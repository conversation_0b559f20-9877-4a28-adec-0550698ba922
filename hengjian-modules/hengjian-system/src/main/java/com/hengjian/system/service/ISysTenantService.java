package com.hengjian.system.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.domain.bo.SysTenantBo;
import com.hengjian.system.domain.vo.ApprovedTenantVo;
import com.hengjian.system.domain.vo.SysTenantVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 租户Service接口
 *
 * <AUTHOR>
 */
public interface ISysTenantService {

    /**
     * 查询租户
     */
    SysTenantVo queryById(Long id);

    /**
     * 基于租户ID查询租户
     */
    SysTenantVo queryByTenantId(String tenantId);

    /**
     * 基于租户ID查询租户无视租户
     */
    SysTenantVo queryByTenantIdNoTenant(String tenantId);

    SysTenant queryOneByTenantId(String tenantId);

    List<SysTenant> queryListByTenantIds(List<String> tenantIds);

    /**
     * 查询租户列表
     */
    TableDataInfo<SysTenantVo> queryPageList(SysTenantBo bo, PageQuery pageQuery);

    /**
     * 查询租户列表（导出用）
     */
    R<Void> queryPageListForExport(SysTenantBo bo);

    /**
     * 查询租户列表
     */
    List<SysTenantVo> queryList(SysTenantBo bo);

    /**
     * 新增租户
     */
    Boolean insertByBo(SysTenantBo bo);

    /**
     * 修改租户
     */
    Boolean updateByBo(SysTenantBo bo);

    /**
     * 修改租户
     */
    Boolean updateByVo(SysTenantVo vo);

    /**
     * 修改租户状态
     */
    int updateTenantStatus(SysTenantBo bo);

    /**
     * 校验租户是否允许操作
     */
    void checkTenantAllowed(String tenantId);

    /**
     * 校验并批量删除租户信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验企业名称是否唯一
     */
    boolean checkCompanyNameUnique(SysTenantBo bo);

    /**
     * 校验账号余额
     */
    boolean checkAccountBalance(String tenantId);

    /**
     * 校验有效期
     */
    boolean checkExpireTime(String tenantId);

    /**
     * 同步租户套餐
     */
    Boolean syncTenantPackage(String tenantId, String packageId);

    /**
     * 是否存在租户Id
     * @param tenantId
     * @return
     */
    Boolean existTenantId(String tenantId);

    /**
     * 根据租户类型获取默认套餐Id
     * @param tenantType
     * @return
     */
    Long getDefaultPackageId(String tenantType);

    /**
     * 根据租户类型获取默认套餐Id
     * @param identityType
     * @param productSourceType
     * @return
     */
    Long getDefaultPackageId(String identityType, String productSourceType);
    /**
     * 判断非管理员
     * @param tenantType
     */
    void checkNotManager(String tenantType);

    /**
     * 根据租户菜单创建租户角色
     *
     * @param tenantId  租户编号
     * @param packageId 租户套餐id
     * @return 角色id
     */
    Long createTenantRole(String tenantId, Long packageId);

    /**
     * 获取我的租户信息
     * @return
     */
    SysTenantVo getMyTenant();

    /**
     * 功能描述：通过第三家商店标志获取租户
     *
     * @param thirdChannelFlag 第三通道标志
     * @param tenantId
     * @return {@link SysTenant }
     * <AUTHOR>
     * @date 2024/01/10
     */
    SysTenant getTenantByThirdChannelFlag(String thirdChannelFlag, String tenantId);

    /**
     * 根据租户ID查询租户信息
     * @param tenantIds
     * @return
     */
    Map<String, SysTenant> getTenantMapByTenantIds(List<String> tenantIds);
    /**
     * @description: 根据租户id/昵称查询已经审核状态的租户
     * @author: Len
     * @date: 2024/9/26 14:31
     * @param: tenantId
     * @param: nickName
     * @return: java.util.List<com.hengjian.system.domain.vo.ApprovedTenantVo>
     **/
    List<ApprovedTenantVo> getApprovedTenant(String tenantId, String nickName,Integer isCalculation);
    /**
     * @description: 更新租户是否支持测算
     * @author: Len
     * @date: 2024/9/27 11:15
     * @param: tenantIds
     * @param: isCalculation
     **/
    void updateTenantIsCalculation(List<String> tenantIds, Integer isCalculation);
    /**
     * @description: 判断分销商是否支持测算
     * @author: Len
     * @date: 2024/9/27 11:15
     * @param: tenantId
     * @param: isCalculation
     * @return: java.lang.Boolean
     **/
    Boolean getIsApprovedTenant(String tenantId,Integer isCalculation);

    Map<String,Map<String,String>> queryByTenantIds(List<String> tenantIds);

    /**
     * 根据租户类型获取租户id
     * @param tenantType
     * @return
     */
    List<String> listTenantIdByTenantType(String tenantType);
    List<SysTenantVo> getSupTenants();
}
