package com.hengjian.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.constant.CacheNames;
import com.hengjian.common.core.exception.ServiceException;
import com.hengjian.common.core.service.OssService;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.core.utils.StreamUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.core.utils.file.FileUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.oss.core.OssClient;
import com.hengjian.common.oss.entity.UploadResult;
import com.hengjian.common.oss.enumd.AccessPolicyType;
import com.hengjian.common.oss.factory.OssFactory;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.*;
import com.hengjian.system.domain.SysOss;
import com.hengjian.system.domain.bo.SysOssBo;
import com.hengjian.system.domain.vo.BatchUploadFile;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.mapper.SysOssMapper;
import com.hengjian.system.service.ISysOssService;
import com.zsmall.common.enums.common.ContentTypeEnum;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.ImagesUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.event.EventListener;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件上传 服务层实现
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysOssServiceImpl implements ISysOssService, OssService {

    private final SysOssMapper baseMapper;
    private final FileProperties fileProperties;
    @Override
    public TableDataInfo<SysOssVo> queryPageList(SysOssBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOss> lqw = buildQueryWrapper(bo);
        Page<SysOssVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<SysOssVo> filterResult = StreamUtils.toList(result.getRecords(), this::matchingUrl);
        result.setRecords(filterResult);
        return TableDataInfo.build(result);
    }

    @Override
    public List<SysOssVo> listByIds(Collection<Long> ossIds) {
        List<SysOssVo> list = new ArrayList<>();
        for (Long id : ossIds) {
            SysOssVo vo = SpringUtils.getAopProxy(this).getById(id);
            if (ObjectUtil.isNotNull(vo)) {
                list.add(this.matchingUrl(vo));
            }
        }
        return list;
    }

    @Override
    public String selectUrlByIds(String ossIds) {
        List<String> list = new ArrayList<>();
        for (Long id : StringUtils.splitTo(ossIds, Convert::toLong)) {
            SysOssVo vo = TenantHelper.ignore(() -> SpringUtils.getAopProxy(this).getById(id));
            if (ObjectUtil.isNotNull(vo)) {
                list.add(this.matchingUrl(vo).getUrl());
            }
        }
        return String.join(StringUtils.SEPARATOR, list);
    }

    private LambdaQueryWrapper<SysOss> buildQueryWrapper(SysOssBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysOss> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), SysOss::getFileName, bo.getFileName());
        lqw.like(StringUtils.isNotBlank(bo.getOriginalName()), SysOss::getOriginalName, bo.getOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileSuffix()), SysOss::getFileSuffix, bo.getFileSuffix());
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), SysOss::getUrl, bo.getUrl());
        lqw.between(params.get("beginCreateTime") != null && params.get("endCreateTime") != null,
            SysOss::getCreateTime, params.get("beginCreateTime"), params.get("endCreateTime"));
        lqw.eq(ObjectUtil.isNotNull(bo.getCreateBy()), SysOss::getCreateBy, bo.getCreateBy());
        lqw.eq(StringUtils.isNotBlank(bo.getService()), SysOss::getService, bo.getService());
        return lqw;
    }

    @Cacheable(cacheNames = CacheNames.SYS_OSS, key = "#ossId")
    @Override
    public SysOssVo getById(Long ossId) {
        return baseMapper.selectVoById(ossId);
    }

    @Override
    public void download(Long ossId, HttpServletResponse response) throws IOException {
        SysOssVo sysOss = SpringUtils.getAopProxy(this).getById(ossId);
        if (ObjectUtil.isNull(sysOss)) {
            throw new ServiceException("文件数据不存在!");
        }
        FileUtils.setAttachmentResponseHeader(response, sysOss.getOriginalName());
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE + "; charset=UTF-8");
        OssClient storage = OssFactory.instance();
        try (InputStream inputStream = storage.getObjectContent(sysOss.getUrl())) {
            int available = inputStream.available();
            IoUtil.copy(inputStream, response.getOutputStream(), available);
            response.setContentLength(available);
        } catch (Exception e) {
            log.error("download error. message: {}", e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public SysOssVo upload(MultipartFile file) {
        String originalfileName = file.getOriginalFilename();
        String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult;
        try {
            uploadResult = storage.uploadSuffix(file.getBytes(), suffix, file.getContentType());
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        // 保存文件信息
        SysOss oss = new SysOss();
        oss.setUrl(uploadResult.getUrl());
        oss.setFileSuffix(suffix);
        oss.setFileName(uploadResult.getFilename());
        oss.setOriginalName(originalfileName);
        oss.setService(storage.getConfigKey());
        baseMapper.insert(oss);
        SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
        sysOssVo.setSavePath(uploadResult.getSavePath());
        return this.matchingUrl(sysOssVo);
    }

    @Override
    public SysOssVo uploadExtend(MultipartFile file, String businessId, String businessNumber) {
        String originalfileName = file.getOriginalFilename();
        String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult;
        try {
            uploadResult = storage.uploadSuffix(file.getBytes(), suffix, file.getContentType());
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        // 保存文件信息
        SysOss oss = new SysOss();
        oss.setUrl(uploadResult.getUrl());
        oss.setFileSuffix(suffix);
        oss.setBusinessId(businessId);
        oss.setBusinessNumber(businessNumber);

        oss.setFileName(uploadResult.getFilename());
        oss.setOriginalName(originalfileName);
        oss.setService(storage.getConfigKey());
        baseMapper.insert(oss);
        SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
        sysOssVo.setSavePath(uploadResult.getSavePath());
        return this.matchingUrl(sysOssVo);
    }

    @Override
    public SysOssVo upload(InputStream file, String fileName) {
        String suffix = StringUtils.substring(fileName, fileName.lastIndexOf("."), fileName.length());
        // 这里切换成tom的url
        // 文件/后缀
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult;
        try {
            uploadResult = storage.uploadSuffix(file, suffix, ContentTypeEnum.getCentTypeBySurffix(suffix));
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        // 保存文件信息
        SysOss oss = new SysOss();
        oss.setUrl(uploadResult.getUrl());
        oss.setFileSuffix(suffix);
        oss.setFileName(uploadResult.getFilename());
        oss.setOriginalName(fileName);
        oss.setService(storage.getConfigKey());
        baseMapper.insert(oss);
        SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
        sysOssVo.setSavePath(uploadResult.getSavePath());
        return this.matchingUrl(sysOssVo);
    }

    @Override
    public SysOssVo upload(InputStream file, String fileName, String businessId, String businessNumber ) {
        String suffix = StringUtils.substring(fileName, fileName.lastIndexOf("."), fileName.length());
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult;
        try {
            uploadResult = storage.uploadSuffix(file, suffix, ContentTypeEnum.getCentTypeBySurffix(suffix));
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        // 保存文件信息
        SysOss oss = new SysOss();
        oss.setUrl(uploadResult.getUrl());
        oss.setFileSuffix(suffix);
        oss.setFileName(uploadResult.getFilename());
        oss.setOriginalName(fileName);
        oss.setBusinessId(businessId);
        oss.setBusinessNumber(businessNumber);
        oss.setService(storage.getConfigKey());
        baseMapper.insert(oss);
        SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
        sysOssVo.setSavePath(uploadResult.getSavePath());
        return this.matchingUrl(sysOssVo);
    }


    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        List<SysOss> list = baseMapper.selectBatchIds(ids);
        for (SysOss sysOss : list) {
            OssClient storage = OssFactory.instance(sysOss.getService());
            storage.delete(sysOss.getUrl());
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 匹配Url
     *
     * @param oss OSS对象
     * @return oss 匹配Url的OSS对象
     */
    private SysOssVo matchingUrl(SysOssVo oss) {
        OssClient storage = OssFactory.instance(oss.getService());
        // 仅修改桶类型为 private 的URL，临时URL时长为120s
        if (AccessPolicyType.PRIVATE == storage.getAccessPolicy()) {
            oss.setUrl(storage.getPrivateUrl(oss.getFileName(), 120));
        }
        return oss;
    }

    public SysOssVo queryByUrl(String url) {
        LambdaQueryWrapper<SysOss> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysOss::getUrl, url);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 读取文件内容，并输出字符串
     *
     * @param savePath 存储桶存储路径
     * @return {@link String} 字符串
     */
    public String getFileContent(String savePath) {
        OssClient storage = OssFactory.instance();
        try (InputStream inputStream = storage.getObjectContent(savePath)) {
            return IoUtil.readUtf8(inputStream);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<SysOssVo> listByOssIds(List<Long> ids) {
        LambdaQueryWrapper<SysOss> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysOss::getOssId,ids);
        return TenantHelper.ignore(()->baseMapper.selectVoList(wrapper));
    }


    /**
     * 事件监听 - 获取OSS存储字符串内容，并返回
     *
     * @param stringContentEvent OSS存储事件
     */
    @EventListener
    public void ossStringContentEventListener(OSSStringContentEvent stringContentEvent) {
        String inSavePath = stringContentEvent.getInSavePath();
        String content = this.getFileContent(inSavePath);
        stringContentEvent.setOutContent(content);
    }
    // tag lty oss
    @EventListener
    public void ossUploadEventListener(OSSUploadEvent ossUploadEvent) {
        MultipartFile file = ossUploadEvent.getFile();
        if (file != null) {
            SysOssVo sysOssVo = this.upload(file);
            ossUploadEvent.setSysOssVo(sysOssVo);
        } else {
            String fileName = ossUploadEvent.getFileName();
            InputStream inputStream = ossUploadEvent.getInputStream();
            SysOssVo sysOssVo = this.upload(inputStream, fileName);
            ossUploadEvent.setSysOssVo(sysOssVo);
        }
    }

    @EventListener
    public void ossUploadBatchEventListener(OSSUploadBatchEvent ossUploadBatchEvent) {
        BatchUploadFile files = ossUploadBatchEvent.getFiles();
        Map<String, MultipartFile> fileMap = files.getFileMap();

        if (CollUtil.isNotEmpty(fileMap) ) {
            Map<String,SysOssVo> sysOssVoMap = this.batchUpload(fileMap);
            ossUploadBatchEvent.setSysOssVoMap(sysOssVoMap);
        }
    }

    private Map<String, SysOssVo> batchUpload(Map<String, MultipartFile> fileMap) {
        ArrayList<SysOss> sysOsses = new ArrayList<>();
        Map<String, SysOssVo> sysOssVoMap = new HashMap<>();
        for (Map.Entry<String, MultipartFile> map : fileMap.entrySet()) {
            String storeId = map.getKey();
            MultipartFile file = map.getValue();
            String originalfileName = file.getOriginalFilename();
            String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
            OssClient storage = OssFactory.instance();
            UploadResult uploadResult;
            try {
                uploadResult = storage.uploadSuffix(file.getBytes(), suffix, file.getContentType());
            } catch (IOException e) {
                throw new ServiceException(e.getMessage());
            }
            SysOss oss = new SysOss();
            oss.setUrl(uploadResult.getUrl());
            oss.setFileSuffix(suffix);
            oss.setFileName(uploadResult.getFilename());
            oss.setOriginalName(originalfileName);
            oss.setService(storage.getConfigKey());
            oss.setStoreId(storeId);
            oss.setSavePath(uploadResult.getSavePath());
            sysOsses.add(oss);
        }

        // 保存文件信息
        baseMapper.insertBatch(sysOsses);
        for (SysOss oss : sysOsses) {
            SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
            this.matchingUrl(sysOssVo);
            sysOssVoMap.put(sysOssVo.getStoreId(),sysOssVo);
        }

        return sysOssVoMap;
    }


    /**
     * 功能描述：下载pdf而非异步
     *
     * @param image          形象
     * @param trackingNumber
     * <AUTHOR>
     * @date 2024/04/22
     */
    public SysOssVo downloadPdfNotAsync(String image, String lineId, String trackingNumber) {
        File file = null;
        SysOssVo sysOssVo = null;
        int maxRetryCount = 3;

        for (int attempt = 0; attempt < maxRetryCount; attempt++) {
            try {
                String tempSavePath = fileProperties.getTempSavePath();
                String fileName = UUID.fastUUID().toString(true) + "." + "pdf";
                String tempPath = tempSavePath + File.separator + "tiktokImport" + File.separator + fileName;
                file = FileUtil.newFile(tempPath);
                int timeout = 10000 + (attempt * 5000);
                HttpUtil.downloadFile(image, file, timeout);
                log.info("http 下载成功");
                InputStream inputStream = FileUtil.getInputStream(file);
                OSSUploadEvent ossUploadEvent = new OSSUploadEvent(inputStream, fileName);
                MultipartFile realFile = ossUploadEvent.getFile();

                if (realFile != null) {
                    sysOssVo = this.uploadExtend(realFile, lineId, trackingNumber);
                } else {
                    String realFileName = ossUploadEvent.getFileName();
                    InputStream realInputStream = ossUploadEvent.getInputStream();
                    sysOssVo = this.upload(realInputStream, realFileName, lineId, trackingNumber);
                }

                // If the operation is successful, break the loop
                if (sysOssVo != null) {
                    break;
                }
            } catch (Exception e) {
                log.error("Attempt {} - Download pdf failed pdf = {}, reason {}", attempt + 1, image, e.getMessage(), e);
            } finally {
                FileUtil.del(file);
            }
        }

        if (sysOssVo == null) {
            throw new ServiceException("Failed to download pdf after " + maxRetryCount + " attempts");
        }
        return sysOssVo;

    }

    /**
     * 功能描述：下载pdf不异步打开
     *
     * @param image          形象
     * @param lineId         线路id
     * @param trackingNumber 查询号
     * @return {@link SysOssVo }
     * <AUTHOR>
     * @date 2024/10/15
     */
    public SysOssVo downloadPdfNotAsyncForOpen(String image, String lineId, String trackingNumber) {
        File file = null;
        SysOssVo sysOssVo = null;
        int maxRetryCount = 3;
        String pdfUrl = null;
        String fileSuffix = null;
        if(StringUtils.isNotEmpty(image)){
            fileSuffix = FileUtil.getSuffix(image);
        }
        for (int attempt = 0; attempt < maxRetryCount; attempt++) {
            try {
                String tempSavePath = fileProperties.getTempSavePath();
                String fileName;
                if(StringUtils.isNotEmpty(fileSuffix)){
                    fileName = UUID.fastUUID().toString(true) + "." + fileSuffix;
                }else {
                    fileName = UUID.fastUUID().toString(true) + "." + "pdf";
                }
                String tempPath = tempSavePath + File.separator + "doc" + File.separator + fileName;
                file = FileUtil.newFile(tempPath);
                int timeout = 600000 + (attempt * 20000);
                log.info("http 开始下载");
                pdfUrl = ImagesUtil.downloadPdf(image);
                HttpUtil.downloadFile(pdfUrl, file, timeout);
                log.info("http 下载成功");
                InputStream inputStream = FileUtil.getInputStream(file);
                OSSUploadEvent ossUploadEvent = new OSSUploadEvent(inputStream, fileName);
                MultipartFile realFile = ossUploadEvent.getFile();

                if (realFile != null) {
                    sysOssVo = this.uploadExtend(realFile, lineId, trackingNumber);
                } else {
                    String realFileName = ossUploadEvent.getFileName();
                    InputStream realInputStream = ossUploadEvent.getInputStream();
                    sysOssVo = this.upload(realInputStream, realFileName, lineId, trackingNumber);
                }

                // If the operation is successful, break the loop
                if (sysOssVo != null) {
                    break;
                }
            } catch (Exception e) {
                log.error("Attempt {} - Download pdf failed pdf = {}, reason {}", attempt + 1, pdfUrl, e.getMessage(), e);
            } finally {
                FileUtil.del(file);
            }
        }

        if (sysOssVo == null) {
            throw new ServiceException("Failed to download pdf after " + maxRetryCount + " attempts");
        }
        return sysOssVo;

    }

    /**
     * 下载和上传文件
     *
     * @param fileUrl 文件urL
     * @param lineId
     * @param trackingNumber
     * @param fileSuffix 文件名后缀
     * @return
     */
    public SysOssVo downloadAndUploadFileNotAsync(String fileUrl, String lineId, String trackingNumber,String fileSuffix) {
        if (StringUtils.isBlank(fileUrl)) {
            log.error("fileUrl cannot be blank");
            throw new IllegalArgumentException("fileUrl cannot be blank");
        }
        File file = null;
        SysOssVo sysOssVo = null;
        int maxRetryCount = 3;
        String pdfUrl = null;
        for (int attempt = 0; attempt < maxRetryCount; attempt++) {
            try {
                String tempSavePath = fileProperties.getTempSavePath();
                String fileName = UUID.fastUUID().toString(true) + "." + fileSuffix;
                String tempPath = tempSavePath + File.separator + "file" + File.separator + fileName;
                file = FileUtil.newFile(tempPath);
                int timeout = 10000 + (attempt * 5000);
                try {
                    pdfUrl = ImagesUtil.downloadPdf(fileUrl);
                    log.info("使用代理下载文件地址 {}", pdfUrl);
                }catch (Exception e){
                    log.error("使用tom代理下载文件失败，使用原始链接下载文件!");
                    pdfUrl = fileUrl;
                }
                if (StringUtils.isBlank(pdfUrl)) {
                    log.error("PDF URL is blank after attempt {}", attempt + 1);
                    throw new ServiceException("PDF URL is blank after attempt " + (attempt + 1));
                }
                HttpUtil.downloadFile(pdfUrl, file, timeout);
                log.info("http 下载成功");
                InputStream inputStream = FileUtil.getInputStream(file);
                OSSUploadEvent ossUploadEvent = new OSSUploadEvent(inputStream, fileName);
                MultipartFile realFile = ossUploadEvent.getFile();

                if (realFile != null) {
                    sysOssVo = this.uploadExtend(realFile, lineId, trackingNumber);
                } else {
                    String realFileName = ossUploadEvent.getFileName();
                    InputStream realInputStream = ossUploadEvent.getInputStream();
                    sysOssVo = this.upload(realInputStream, realFileName, lineId, trackingNumber);
                }
                if (sysOssVo != null) {
                    break;
                }
            } catch (Exception e) {
                log.error("Attempt {} - Download file failed pdf = {}, reason {}", attempt + 1, pdfUrl, e.getMessage(), e);
            } finally {
                FileUtil.del(file);
            }
        }
        if (sysOssVo == null) {
            throw new ServiceException("Failed to download file after " + maxRetryCount + " attempts");
        }
        return sysOssVo;
    }


    @EventListener
    public void ossObtainEventListener(OSSObtainEvent ossObtainEvent) {
        Long ossId = ossObtainEvent.getOssId();
        String url = ossObtainEvent.getUrl();

        if (ossId != null) {
            SysOssVo sysOss = TenantHelper.ignore(() -> SpringUtils.getAopProxy(this).getById(ossId));
            url = sysOss.getUrl();
        }

        OssClient storage = OssFactory.instance();
        try (InputStream inputStream = storage.getObjectContent(url)) {
            byte[] bytes = IoUtil.readBytes(inputStream);
            ossObtainEvent.setBytes(bytes);
        } catch (Exception e) {
            log.error("url {}，获取文件错误，原因: {}", e.getMessage(), e);
        }
    }

    @EventListener
    public void ossUrlLoadEventListener(OSSDownloadEvent downloadEvent) {
        String url = downloadEvent.getUrl();
        SysOssVo sysOssVo = TenantHelper.ignore(() -> this.queryByUrl(url));
        downloadEvent.setSysOssVo(sysOssVo);
    }
}
