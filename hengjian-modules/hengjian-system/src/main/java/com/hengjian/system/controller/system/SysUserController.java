package com.hengjian.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.core.utils.StreamUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.excel.core.ExcelResult;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.extend.event.SettleInEvent;
import com.hengjian.system.domain.bo.SysDeptBo;
import com.hengjian.system.domain.bo.SysUserBo;
import com.hengjian.system.domain.vo.*;
import com.hengjian.system.listener.SysUserImportListener;
import com.hengjian.system.service.*;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.enums.ExtraPerfectionFlag;
import com.zsmall.common.enums.tenantSettleIn.SettleInReviewRecordEnum;
import com.zsmall.system.entity.domain.TenantSupSettleInReviewRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR> Li
 */
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {

    private final ISysUserService userService;
    private final ISysRoleService roleService;
    private final ISysPostService postService;
    private final ISysDeptService deptService;
    private final ISysTenantService tenantService;

    /**
     * 获取用户列表
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/list")
    public TableDataInfo<SysUserVo> list(SysUserBo user, PageQuery pageQuery) {
        return userService.selectPageUserList(user, pageQuery);
    }

    /**
     * 导出用户列表
     */
    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:user:export")
    @PostMapping("/export")
    public void export(SysUserBo user, HttpServletResponse response) {
        List<SysUserVo> list = userService.selectUserList(user);
        List<SysUserExportVo> listVo = MapstructUtils.convert(list, SysUserExportVo.class);
        ExcelUtil.exportExcel(listVo, "用户数据", SysUserExportVo.class, response, false);
    }

    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:user:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<SysUserImportVo> result = ExcelUtil.importExcel(file.getInputStream(), SysUserImportVo.class, new SysUserImportListener(updateSupport));
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "用户数据", SysUserImportVo.class, response, false);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public R<UserInfoVo> getInfo() {
        UserInfoVo userInfoVo = new UserInfoVo();
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (TenantHelper.isEnable() && LoginHelper.isSuperAdmin()) {
            // 超级管理员 如果重新加载用户信息需清除动态租户
            TenantHelper.clearDynamic();
        }
        SysUserVo user = userService.selectUserById(loginUser.getUserId());
        user.setTenantType(LoginHelper.getTenantType());
        // 去除密码
        user.setPassword(null);
        user.setUserName(null);
        userInfoVo.setUser(user);
        userInfoVo.setPermissions(loginUser.getMenuPermission());
        userInfoVo.setRoles(loginUser.getRolePermission());
        userInfoVo.setUserSettleIn(getUserSettleInNew(user.getTenantId()));
        return R.ok(userInfoVo);
    }

    /**
     * 用户入驻状态处理
     * @param tenantId
     * @return
     */
    private SysUserSettleInVo getUserSettleIn(String tenantId) {
        SysTenantVo sysTenantVo = tenantService.queryByTenantId(tenantId);
        String extraPerfectionFlag = sysTenantVo.getExtraPerfectionFlag();
        SettleInEvent settleInEvent = new SettleInEvent();
        SpringUtils.context().publishEvent(settleInEvent);
        String reviewState = settleInEvent.getReviewState();
        //通过
        if (StrUtil.equals(ExtraPerfectionFlag.True.getCode(), extraPerfectionFlag)) {
            SysUserSettleInVo settleInVo = new SysUserSettleInVo(ExtraPerfectionFlag.True.name(), null, reviewState);
            if (StrUtil.equals(reviewState, SettleInReviewRecordEnum.Rejected.name())) {
                Object cacheObject = RedisUtils.getCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId());
                if (cacheObject == null) {
                    settleInVo.setReviewReason(settleInEvent.getReviewReason());
                    settleInVo.setIsSupTip(true);
                }
            }
            return settleInVo;
        }else {
            //未填写
            if (StrUtil.isBlank(reviewState)) {
                return new SysUserSettleInVo(ExtraPerfectionFlag.False.name(), null, null);
            }
            //拒绝
            if (StrUtil.equals(reviewState, SettleInReviewRecordEnum.Rejected.name())) {
                return new SysUserSettleInVo(ExtraPerfectionFlag.Rejected.name(), settleInEvent.getReviewReason(), null);
            }
            //审核中
            if (StrUtil.equalsAny(reviewState, SettleInReviewRecordEnum.Reviewing.name(), SettleInReviewRecordEnum.ReviewedAgain.name())) {
                return new SysUserSettleInVo(ExtraPerfectionFlag.Reviewing.name(), null, null);
            }
            return new SysUserSettleInVo(ExtraPerfectionFlag.False.name(), null, null);
        }
    }

    private SysUserSettleInVo getUserSettleInNew(String tenantId) {
        SysTenantVo sysTenantVo = tenantService.queryByTenantId(tenantId);
        String extraPerfectionFlag = sysTenantVo.getExtraPerfectionFlag();
        SettleInEvent settleInEvent = new SettleInEvent();
        SpringUtils.context().publishEvent(settleInEvent);
        String reviewState = settleInEvent.getReviewState();
        SysUserSettleInVo sysUserSettleInVo = new SysUserSettleInVo();
        sysUserSettleInVo.setReviewState(reviewState);
        sysUserSettleInVo.setExtraPerfectionFlag(ExtraPerfectionFlag.formName(extraPerfectionFlag));
        sysUserSettleInVo.setReviewReason(settleInEvent.getReviewReason());
        sysUserSettleInVo.setUserReviewState(reviewState);
        if (StrUtil.equals(ExtraPerfectionFlag.False.getCode(), extraPerfectionFlag)) {
            // 审核拒绝弹窗判断
            if (StrUtil.equals(reviewState, SettleInReviewRecordEnum.Rejected.name())) {
                Object cacheObject = RedisUtils.getCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + reviewState);
                if (null == cacheObject) {
                    sysUserSettleInVo.setReviewReason(settleInEvent.getReviewReason());
                    sysUserSettleInVo.setIsSupTip(true);
                    return sysUserSettleInVo;
                }
            }
            if (StrUtil.equals(reviewState, SettleInReviewRecordEnum.Reviewing.name())) {
                Object cacheObject = RedisUtils.getCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + reviewState);
                if(null != cacheObject){
                    sysUserSettleInVo.setIsSupTip(true);
                    return sysUserSettleInVo;
                }
            }
        }
        return sysUserSettleInVo;

//        //通过
//        if (StrUtil.equals(ExtraPerfectionFlag.True.getCode(), extraPerfectionFlag)) {
//            SysUserSettleInVo settleInVo = new SysUserSettleInVo(ExtraPerfectionFlag.True.name(), null, reviewState);
//            if (StrUtil.equals(reviewState, SettleInReviewRecordEnum.Rejected.name())) {
//                Object cacheObject = RedisUtils.getCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId());
//                if (cacheObject == null) {
//                    settleInVo.setReviewReason(settleInEvent.getReviewReason());
//                    settleInVo.setIsSupTip(true);
//                }
//            }
//            return settleInVo;
//        }else {
//            //未填写
//            if (StrUtil.isBlank(reviewState)) {
//                return new SysUserSettleInVo(ExtraPerfectionFlag.False.name(), null, null);
//            }
//            //拒绝
//            if (StrUtil.equals(reviewState, SettleInReviewRecordEnum.Rejected.name())) {
//                return new SysUserSettleInVo(ExtraPerfectionFlag.Rejected.name(), settleInEvent.getReviewReason(), null);
//            }
//            //审核中
//            if (StrUtil.equalsAny(reviewState, SettleInReviewRecordEnum.Reviewing.name(), SettleInReviewRecordEnum.ReviewedAgain.name())) {
//                return new SysUserSettleInVo(ExtraPerfectionFlag.Reviewing.name(), null, null);
//            }
//            return new SysUserSettleInVo(ExtraPerfectionFlag.False.name(), null, null);
//        }
    }

    /**
     * 根据用户编号获取详细信息
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:user:query")
    @GetMapping(value = {"/", "/{userId}"})
    public R<SysUserInfoVo> getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        SysUserInfoVo userInfoVo = new SysUserInfoVo();
        List<SysRoleVo> roles = roleService.selectRoleAll();
        userInfoVo.setRoles(LoginHelper.isSuperAdmin(userId) ? roles : StreamUtils.filter(roles, r -> !r.isSuperAdmin()));
        userInfoVo.setPosts(postService.selectPostAll());
        if (ObjectUtil.isNotNull(userId)) {
            SysUserVo sysUser = userService.selectUserById(userId);
            // 去除密码
            sysUser.setPassword(null);
            userInfoVo.setUser(sysUser);
            userInfoVo.setRoleIds(StreamUtils.toList(sysUser.getRoles(), SysRoleVo::getRoleId));
            userInfoVo.setPostIds(postService.selectPostListByUserId(userId));
        }
        return R.ok(userInfoVo);
    }

    /**
     * 新增用户
     */
    @SaCheckPermission("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody SysUserBo user) {
        if (!userService.checkUserNameUnique(user)) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return R.fail("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        if (TenantHelper.isEnable()) {
            if (!tenantService.checkAccountBalance(TenantHelper.getTenantId())) {
                return R.fail("当前租户下用户名额不足，请联系管理员");
            }
        }
        user.setPassword(BCrypt.hashpw(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody SysUserBo user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        if (!userService.checkUserNameUnique(user)) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     *
     * @param userIds 角色ID串
     */
    @SaCheckPermission("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Void> remove(@PathVariable Long[] userIds) {
        if (ArrayUtil.contains(userIds, LoginHelper.getUserId())) {
            return R.fail("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @SaCheckPermission("system:user:resetPwd")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R<Void> resetPwd(@RequestBody SysUserBo user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(BCrypt.hashpw(user.getPassword()));
        return toAjax(userService.resetUserPwd(user.getUserId(), user.getPassword()));
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysUserBo user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        return toAjax(userService.updateUserStatus(user.getUserId(), user.getStatus()));
    }

    /**
     * 根据用户编号获取授权角色
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:user:query")
    @GetMapping("/authRole/{userId}")
    public R<SysUserInfoVo> authRole(@PathVariable Long userId) {
        SysUserVo user = userService.selectUserById(userId);
        List<SysRoleVo> roles = roleService.selectRolesByUserId(userId);
        SysUserInfoVo userInfoVo = new SysUserInfoVo();
        userInfoVo.setUser(user);
        userInfoVo.setRoles(LoginHelper.isSuperAdmin(userId) ? roles : StreamUtils.filter(roles, r -> !r.isSuperAdmin()));
        return R.ok(userInfoVo);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户Id
     * @param roleIds 角色ID串
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public R<Void> insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return R.ok();
    }

    /**
     * 获取部门树列表
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/deptTree")
    public R<List<Tree<Long>>> deptTree(SysDeptBo dept) {
        return R.ok(deptService.selectDeptTreeList(dept));
    }


    @GetMapping("/admin")
    public R<SysTenantAdminSensitiveVo> getSensitiveTenantAdmin() {
        return R.ok(userService.getSensitiveTenantAdmin());
    }

    /**
     * 获取用户登录后待操作信息
     * @return
     */
    @GetMapping("/loginOper")
    public R<SysLoginOperVo> getLoginOperVo() {
        return R.ok(userService.getLoginOperVo());
    }
}
