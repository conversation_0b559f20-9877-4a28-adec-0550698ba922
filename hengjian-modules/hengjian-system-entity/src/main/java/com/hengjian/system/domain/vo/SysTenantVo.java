package com.hengjian.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.hengjian.system.domain.SysTenant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 租户视图对象 sys_tenant
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysTenant.class)
public class SysTenantVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 三方平台标识
     */
    @ExcelProperty(value = "店铺标识")
    private String thirdChannelFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    /**
     * 渠道店铺标识
     */
    @ExcelProperty(value = "渠道店铺标识")
    private String channelFlag;

    /**
     * 租户类型
     */
    @ExcelProperty(value = "租户类型")
    private String tenantType;

    /**
     * 所处国家二位代号
     */
    private String country;

    /**
     * 货源类型
     */
    @ExcelProperty(value = "货源类型")
    private String productSourceType;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String contactUserName;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @ExcelProperty(value = "联系邮箱")
    private String contactEmail;

    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    private String companyName;

    /**
     * 统一社会信用代码
     */
    @ExcelProperty(value = "统一社会信用代码")
    private String licenseNumber;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 域名
     */
    @ExcelProperty(value = "域名")
    private String domain;

    /**
     * 企业简介
     */
    @ExcelProperty(value = "企业简介")
    private String intro;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 租户套餐编号
     */
    @ExcelProperty(value = "租户套餐编号")
    private Long packageId;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    private Date expireTime;

    /**
     * 用户数量（-1不限制）
     */
    @ExcelProperty(value = "用户数量")
    private Long accountCount;

    /**
     * 租户状态（0正常 1停用）
     */
    @ExcelProperty(value = "租户状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 是否已完善信息（1代表已完善 0代表未完善）
     */
    private String extraPerfectionFlag;

    /**
     * 租户下用户列表
     */
    private List<SysUserVo> sysUserList;

    /**
     * 租户下用户默认管理员账户
     */
    private SysUserVo sysUser;
    /**
     *  是否测算 1测算2不测算
     */
    private Integer isCalculation;
}
