package com.hengjian.system.domain.vo;

import com.hengjian.common.translation.annotation.Translation;
import com.hengjian.common.translation.constant.TransConstant;
import com.hengjian.system.domain.SysOss;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * OSS对象存储视图对象 sys_oss
 *
 * <AUTHOR> Li
 */
@Data
@AutoMapper(target = SysOss.class)
public class SysOssVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 对象存储主键
     */
    private Long ossId;
    private String businessNumber;
    private String businessId;
    /**
     * 文件名
     */
    private String fileName;

    /**
     * 原名
     */
    private String originalName;



    /**
     * 文件后缀名
     */
    private String fileSuffix;

    /**
     * URL地址
     */
    private String url;

    /**
     * 保存地址
     */
    private String savePath;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 上传人
     */
    private Long createBy;

    /**
     * 上传人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 服务商
     */
    private String service;

    private String storeId;
}
