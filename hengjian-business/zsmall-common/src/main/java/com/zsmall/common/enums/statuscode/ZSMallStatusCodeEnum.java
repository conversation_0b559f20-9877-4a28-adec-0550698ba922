package com.zsmall.common.enums.statuscode;

import com.hengjian.common.core.domain.RStatusCodeBase;

/**
 * ZSMall状态码枚举（新版）
 *
 * <AUTHOR>
 * @create 2021/7/6 12:19
 */
public enum ZSMallStatusCodeEnum implements RStatusCodeBase {

    /** 请求成功 */
    REQUEST_SUCCESS("0", "zsmall.systemCommon.requestSuccess"),
    /** 上传成功 */
    UPLOAD_SUCCESS("0", "zsmall.systemCommon.uploadSuccess"),
    /** 下单成功！ */
    ORDER_SUCCESS("0", "zsmall.systemCommon.orderSuccess"),
    /** 支付成功！ */
    PAYMENT_SUCCESS("0", "zsmall.systemCommon.paymentSuccess"),
    /** 您的价格修改变更已提交审核，最新价格在三天内生效，请到商品审核页面查看 */
    HAS_PRICE_CHANGE("0", "zsmall.systemCommon.hasPriceChange"),
    /** [API]必需项不能为空 */
    REQUIRED_CANNOT_EMPTY("1001", "zsmall.systemCommon.requiredCannotEmpty"),
    /**
     * [API]非法参数
     */
    ILLEGAL_PARAMETER("1001", "zsmall.systemCommon.illegalParameter"),
    /** 上传文件未响应，请重试 */
    UPLOAD_FILE_NOT_RESPONDING("1005", "zsmall.systemCommon.uploadFileNotResponding"),
    /** 下载文件时出现未知错误 */
    DOWNLOAD_FILE_ERROR("1008", "zsmall.systemCommon.downloadFileError"),
    /** 系统繁忙，请稍候重试 */
    SYSTEM_BUSY("1009", "zsmall.systemCommon.systemBusy"),
    /** 查询日志时出现未知错误 */
    LOG_QUERY_ERROR("1010", "zsmall.systemCommon.logQueryError"),
    /** API缺少必要传值 */
    MISSING_VALUE_IN_THE_INTERFACE("1011", "zsmall.systemCommon.missingValueInTheInterface"),
    /** 该功能未开放 */
    FUNCTION_IS_NOT_OPEN("1012", "zsmall.systemCommon.functionIsNotOpen"),
    /** 系统编号生成失败 */
    BUSINESS_CODE_GENERATE_ERROR("1013", "zsmall.systemCommon.businessCodeGenerateError"),
    /** 填写内容超长，请检查内容过长的字段 */
    DATABASE_DATA_TOO_LONG("1014", "database.DataTooLong"),
    /** 总金额数额过大 */
    TOTAL_AMOUNT_IS_TOO_LARGE("1015", "database.totalAmountTooLarge"),
    /**
     * 相关数据已发生改变，请刷新后重试
     */
    THE_RELEVANT_DATA_HAS_CHANGED("1016", "zsmall.systemCommon.theRelevantDataHasChanged"),
    /**
     * 操作过于频繁，请稍候再试！
     */
    FREQUENT_OPERATIONS("1017", "system.common.frequentOperations"),

    /** 用户相关 - 隐私政策操作 - 10100 */
    /** 根据类型获取隐私政策文本异常 */
    GET_PRIVACY_POLICY_ERROR("10101", "zsmall.privacyPolicy.getPrivacyPolicyError"),
    /** 类型不合法 */
    REQUEST_TYPE_ILLEGAL("10102", "zsmall.privacyPolicy.requestTypeIllegal"),
    /** 隐私政策不存在 */
    PRIVACY_POLICY_NOT_EXIST("10103", "zsmall.privacyPolicy.privacyPolicyNotExist"),
    /** 发送验证邮件异常 */
    USER_EMAIL_SEND_ERROR("10003", "zsmall.user.userEmailSendError"),
    /** 发送短信验证码异常 */
    USER_SEND_SMS_OTP_ERROR("10005", "zsmall.user.userSendSmsOtpError"),

    /** 用户账号不存在或Token已失效，请重新登录 */
    NOT_LOGIN("10016", "zsmall.global.notLogin"),
    /**
     * 无操作权限
     */
    NO_HANDLE_PERMISSION("10017", "no.handle.permission"),
    /** 该账户不是员工账户不能使用 */
    CURRENT_USER_NOT_MD("Z10001", "zsmall.global.currentUserNotMd"),
    /** 登陆账号不是分销商类型,请登陆分销商账号后继续 */
    CURRENT_USER_NOT_BULK("Z10002", "zsmall.global.currentUserNotBulk"),
    /** 当前帐号不是商家账号，无法操作 */
    CURRENT_USER_NOT_SUP("Z10003", "zsmall.global.currentUserNotSup"),
    /** 当前帐号无法使用此功能 */
    CURRENT_USER_CANNOT_USE("Z10004", "zsmall.global.currentUserCannotUse"),
    /** 非员工或非供应商账户无法使用 */
    CURRENT_USER_NOT_MD_OR_NOT_SUP("Z10005", "zsmall.global.currentUserNotMdOrNotSup"),
    /** 账户不存在 */
    USER_ACCOUNT_NOT_EXIST("Z10006", "zsmall.global.userAccountNotExist"),
    /** 删除运费模板出错 */
    UPDATE_USER_MESSAGE_ERROR("Z10007", "zsmall.global.updateUserMessageError"),
    /** 邀请码已经存在 */
    USER_INVITATION_CODE_EXIST("Z10008", "zsmall.global.userInvitationCodeExist"),
    /** 存在非法字符 */
    ILLEGAL_CHARACTERS_EXIST("Z10009", "zsmall.global.illegalCharactersExist"),
    /** 注册信息填写超时，请重新注册 */
    REGISTER_INFORMATION_INPUT_TIMEOUT("Z10010", "zsmall.global.registerInformationInputTimeout"),
    /** 密码信息填写超时，请重新发送邮件 */
    RESET_PASSWORD_EMAIL_TIMEOUT("Z10011", "zsmall.global.resetPasswordEmailTimeout"),
    /** 邮箱信息填写超时，请重新发送邮件 */
    UPDATE_EMAIL_TIMEOUT("Z10012", "zsmall.global.updateEmailTimeout"),
    /** 邮件已失效，请重新发送邮件 */
    EMAIL_LINK_TIMEOUT("Z10013", "zsmall.global.emailLinkTimeout"),
    /** 用户尚未绑定手机号 */
    CURRENT_USER_PHONE_NOT_EXIST("Z10014", "zsmall.global.currentUserPhoneNotExist"),
    /** 验证码已经失效，请重新发送邮件 */
    EMAIL_VERIFY_CODE_TIMEOUT("Z10015", "zsmall.global.emailVerifyCodeTimeout"),
    /** 校验验证码发生未知错误，请稍候重试 */
    EMAIL_VERIFY_CODE_COMPARE_ERROR("Z10016", "zsmall.global.emailVerifyCodeCompareError"),
    /** 验证码不匹配，请重新输入 */
    VERIFY_CODE_IS_NOT_MATCH("Z10017", "zsmall.global.verifyCodeIsNotMatch"),
    /** 邮箱已经存在，请重新输入 */
    MAILBOX_ALREADY_EXISTS("Z10018", "zsmall.global.mailboxAlreadyExists"),
    /** 手机号已经存在，请重新输入 */
    PHONE_ALREADY_EXISTS("Z10019", "zsmall.global.phoneAlreadyExists"),
    /** 下载Excel时出现未知错误 */
    DOWNLOAD_EXCEL_ERROR("Z10020", "zsmall.global.downloadExcelError"),
    /** 上传Excel时出现未知错误 */
    UPLOAD_EXCEL_ERROR("Z10021", "zsmall.global.uploadExcelError"),
    /** 用户不存在 */
    USER_NOT_EXIST("Z10022", "zsmall.global.userNotExist"),
    /** 导出文件发生未知错误 */
    EXPORT_FILE_ERROR("Z10023", "zsmall.global.exportFileError"),
    /** 账号被冻结，请联系管理员 */
    ACCOUNT_IS_FROZEN("Z10024", "zsmall.global.accountIsFrozen"),
    /** 新密码与旧密码不能重复 */
    NEW_OLD_PASSWORD_REPEATED("Z10025", "zsmall.global.newOldPasswordRepeated"),
    /** 用户无菜单权限 */
    MENU_PERMISSION_NOT_EXIST("Z10026", "zsmall.global.menuPermissionNotExist"),
    /** 清除Redis时出现未知错误 */
    DELETE_REDIS_ERROR("Z10027", "zsmall.global.deleteRedisError"),
    /** 系统未知错误，请联系管理员（Z028） */
    SYSTEM_ERROR_E10028("Z10028", "zsmall.global.systemErrorE10028"),
    /** 系统未知错误，请联系管理员（Z029） */
    SYSTEM_ERROR_E10029("Z10029", "zsmall.global.systemErrorE10029"),
    /** 密码错误，请重新输入 */
    PASSWORD_ERROR("Z10030", "zsmall.global.passwordError"),
    /** 帐户正在审核中 */
    USER_NOT_REVIEW_ERROR("Z10031", "zsmall.global.userNotReviewError"),
    /** 两次输入密码不一致 */
    COMFIRM_PASSWORD_IS_NOT_MATCH("Z10032", "zsmall.global.comfirmPasswordIsNotMatch"),
    /** 库存变更时发生未知错误 */
    INVENTORY_CHANGE_ERROR("Z10033", "zsmall.global.inventoryChangeError"),
    /** 保存导入数据时出现未知错误 */
    SAVE_IMPORT_DATA_ERROR("Z10034", "zsmall.global.saveImportDataError"),
    /** 支付失败，请稍候再试 */
    PAYMENT_FAILED("Z10035", "zsmall.global.paymentFailed"),

    /** 查看初学者指南提示异常 */
    QUERY_PASSED_GUIDE_ERROR("10047", "zsmall.userOperation.queryPassedGuideError"),
    /** 通过指定新手引导功能异常 */
    PASS_GUIDE_ERROR("10048", "zsmall.userOperation.passGuideError"),
    /** 切换自动扣款开关异常 */
    SWITCH_AUTO_DEDUCTION_ERROR("10049", "zsmall.userOperation.switchAutoDeductionError"),
    /** 自动扣款出错,请稍后再试 */
    GET_AUTO_DEDUCTION_ERROR("10050", "zsmall.userOperation.getAutoDeductionError"),
    /** 手机号长度不符合规范 */
    PHONE_NUMBER_NOT_MEET_SPECIFICATION("10051", "zsmall.userOperation.phoneNumberNotMeetSpecification"),
    /** 首次重设密码时出现未知错误 */
    FIRST_RESET_PASSWORD_ERROR("10052", "zsmall.userOperation.firstResetPasswordError"),
    /** 您的账号不需要重设密码 */
    NOT_NEED_RESET_PASSWORD("10053", "zsmall.userOperation.notNeedResetPassword"),
    /** 系统维护 */
    SYSTEM_MAINTENANCE("10054", "zsmall.userOperation.systemMaintenance"),

    /** 获取主页产品提示异常 */
    GET_HOME_PAGE_PRODUCT_ERROR("13004", "zsmall.userHomePage.getHomePageProductError"),

    /** 用户提交反馈异常 */
    USER_FEEDBACK_SUBMIT_ERROR("14001", "zsmall.userFeedBack.userFeedbackSubmitError"),
    /** 查询用户提交反馈异常 */
    FIND_USER_FEEDBACK_SUBMIT_ERROR("14002", "zsmall.userFeedBack.findUserFeedbackSubmitError"),
    /** 用户反馈不存在 */
    USER_FEEDBACK_NOT_EXIST("14003", "zsmall.userFeedBack.userFeedbackNotExist"),
    /** 获取用户反馈详情失败 */
    GET_USER_FEEDBACK_DETAIL_ERROR("14004", "zsmall.userFeedBack.getUserFeedbackDetailError"),
    /** 该用户反馈已受理，请勿重新受理 */
    USER_FEEDBACK_IS_ACCEPT("14005", "zsmall.userFeedBack.userFeedbackIsAccept"),
    /** 改变受理状态异常 */
    CHANGE_ACCEPT_STATUS_ERROR("14006", "zsmall.userFeedBack.changeAcceptStatusError"),
    /** 非员工没有权限不能改变受理状态 */
    USER_IS_NOT_MD_PERMISSION_TO_CHANGE_STATUS("14007", "zsmall.userFeedBack.userIsNotMdPermissionToChangeStatus"),

    /** 重新设置商品分类关系时发送异常 */
    RESET_PRODUCT_CATEGORY_ERROR("61013", "zsmall.productCategory.resetProductCategoryError"),
    /** 分类中存在商品，无法删除 */
    PRODUCT_CATEGORY_EXIST_PRODUCT("61014", "zsmall.productCategory.productCategoryExistProduct"),
    /** 查询网站地图分类列表时发生未知错误 */
    QUERY_SITE_MAP_CATEGORY_ERROR("61015", "zsmall.productCategory.querySiteMapCategoryError"),
    /** 分类不存在 */
    PRODUCT_CATEGORY_NOT_FOUND("61016", "zsmall.productCategory.productCategoryNotFound"),
    /**
     * 分类名重复
     */
    PRODUCT_CATEGORY_REPEAT("61017", "zsmall.productCategory.productCategoryRepeat"),

    /** 商品未设置涨价 */
    PRODUCT_SKU_MARK_UP_NOT_SET("63012", "zsmall.productDropShipping.productSkuMarkUpNotSet"),
    /** 涨价不能为0 */
    PRODUCT_SKU_MARK_UP_NOT_ZERO("63015", "zsmall.productDropShipping.productSkuMarkUpNotZero"),
    /** 分页查询sku映射异常 */
    PRODUCT_SKU_MAPPING_QUERY_ERROR("63016", "zsmall.productDropShipping.productSkuMappingQueryError"),
    /** 保存sku映射异常 */
    PRODUCT_SKU_MAPPING_SAVE_ERROR("63017", "zsmall.productDropShipping.productSkuMappingSaveError"),
    /** sku不存在或已被删除 */
    PRODUCT_SKU_NOT_EXIST("63018", "zsmall.productDropShipping.productSkuNotExist"),
    /** 删除sku异常 */
    PRODUCT_SKU_DELETE_ERROR("63019", "zsmall.productDropShipping.productSkuDeleteError"),
    /** 映射sku必填 */
    MAPPING_SKU_REQUIRED("63020", "zsmall.productDropShipping.mappingSkuRequired"),
    /** 同一个店铺不允许出现映射SKU重复 */
    MAPPING_SKU_REPEAT("63021", "zsmall.productDropShipping.mappingSkuRepeat"),
    /** 同步SKU至销售渠道时异常 */
    SYNC_PRODUCT_SKU_ERROR("63022", "zsmall.productDropShipping.syncProductSkuError"),
    /** 未设置Mapping，无法同步至渠道 */
    NOT_SET_MAPPING("63023", "zsmall.productDropShipping.notSetMapping"),
    /** 查询SKU仓库配置时发生未知错误 */
    QUERY_SKU_WAREHOUSE_CONFIGS_ERROR("63024", "zsmall.productDropShipping.querySkuWarehouseConfigsError"),
    /** 获取收藏夹商品发生未知错误 */
    GET_DROPSHIPPING_LIST_ERROR("63025", "zsmall.productDropShipping.getDropshippingListError"),
    /** 导出收藏夹商品发生未知错误 */
    EXPORT_DROPSHIPPING_LIST_ERROR("63026", "zsmall.productDropShipping.exportDropshippingListError"),
    /** 之前已同步的产品将不会再次同步到您的Shopify商店。 */
    MAPPING_SKU_REPEAT_SHOPIFY("63027", "zsmall.productDropShipping.mappingSkuRepeatShopify"),
    /** 产品库存不足，无法同步至Shopify。 */
    OUT_OF_STORE_SYNC_FAILED("63028", "zsmall.productDropShipping.outOfStoreSyncFailed"),
    /** 请至少选择一个渠道店铺。 */
    SELECT_AT_LEAST_ONE_CHANNEL("63029", "zsmall.productDropShipping.selectAtLeastOneChannel"),
    /** 请至少选择一个SKU。 */
    SELECT_AT_LEAST_ONE_SKU("63030", "zsmall.productDropShipping.selectAtLeastOneSku"),
    /** 成本价格发生改变，请刷新页面后重试 */
    COST_PRICE_HAS_CHANGED("63031", "zsmall.productDropShipping.costPriceHasChanged"),
    /** 请先在'编辑商品'中选择乐天品类后再同步商品 */
    PLEASE_SELECT_RAKUTEN_GENRE_FIRST("63032", "zsmall.productDropShipping.pleaseSelectRakutenGenreFirst"),
    /** 此商品的物流方式不适用于渠道 */
    PRODUCT_LOGISTICS_METHOD_NOT_APPLICABLE("63033", "zsmall.productDropShipping.productLogisticsMethodNotApplicable"),

    /** 获取商品详情异常 */
    GET_PRODUCT_DETAIL_ERROR("62004", "zsmall.product.getProductDetailError"),
    /** 没有商品管理权限 */
    NO_HAS_PRODUCT_MANAGEMENT_AUTHORITY("62007", "zsmall.product.noHasProductManagementAuthority"),
    /** 产品sku不匹配 */
    PRODUCT_SKU_NOT_MATCHING("62025", "zsmall.product.productSkuNotMatching"),
    /** SKU重复 */
    PRODUCT_SKU_CODE_REPEAT("62026", "zsmall.product.productSkuCodeExisted"),
    /** UPC重复 */
    PRODUCT_UPC_CODE_REPEAT("62027", "zsmall.product.productUpcCodeExisted"),
    /** 商品sku至少需要一个规格属性 */
    PRODUCT_SKU_REQUIRES_AT_LEAST_ONE_SPECIFICATION("62028", "zsmall.product.productSkuRequiresAtLeastOneSpecification"),
    /** 设置SKU销售状态时发生未知错误 */
    SET_SALE_STATUS_ERROR("62030", "zsmall.product.setSaleStatusError"),
    /** 商品规格属性相关信息必填，请仔细检查并补全 */
    SKU_ATTRIBUTES_REQUIRE("62031", "zsmall.product.skuAttributesRequire"),
    /** SKU相关信息必填，请仔细检查并补全 */
    SKU_INFORMATION_REQUIRE("62032", "zsmall.product.skuInformationRequire"),
    /** 翻页查询商品SKU列表异常 */
    QUERY_PRODUCT_SKU_PAGE_ERROR("62033", "zsmall.product.queryProductSkuPageError"),
    /** SKU至少需要上传一张图片 */
    SKU_REQUIRES_ONE_IMAGE("62034", "zsmall.product.skuRequiresOneImage"),
    /** 商家新增自定义规格时发生未知错误 */
    SUP_ADD_PRODUCT_ATTRIBUTE_ERROR("62035", "zsmall.product.supAddProductAttributeError"),
    /** 分类未找到，无法新增自定义规格 */
    PRODUCT_CATEGORY_NOT_FOUND_NOT_DEFINED_SPECIFICATIONS("62036", "zsmall.product.productCategoryNotFoundNotDefinedSpecifications"),
    /** SKU规格属性值重复 */
    SPEC_VALUES_REPEAT("62037", "zsmall.product.specValuesRepeat"),
    /** 上传商品库存更新Excel时发生未知错误 */
    UPLOAD_PRODUCT_QUANTITY_UPDATE_ERROR("62038", "zsmall.product.uploadProductQuantityUpdateError"),
    /** 上传商品价格更新Excel时发生未知错误 */
    UPLOAD_PRODUCT_PRICE_UPDATE_ERROR("62039", "zsmall.product.uploadProductPriceUpdateError"),
    /** 查询导出字段时发生未知错误 */
    QUERY_EXPORT_FIELD_MD_ERROR("62040", "zsmall.product.queryExportFieldMdError"),
    /** 导出商品信息时发生未知错误 */
    EXPORT_PRODUCT_FILE_MD_ERROR("62041", "zsmall.product.exportProductFileMdError"),
    /** 请选择要导出的字段 */
    NOT_CHOOSE_EXPORT_FIELD_MD_ERROR("62042", "zsmall.product.notChooseExportFieldMdError"),
    /** 此商品所在仓库不支持物流第三方账号 */
    NOT_SUPPORT_3RD_BILLING("62043", "zsmall.product.notSupport3rdBilling"),
    /** 未选择物流模板 */
    LOGISTICS_TEMPLATE_NOT_SELECT("62044", "zsmall.product.logisticsTemplateNotSelect"),
    /** 未识别到仓库类型，请重新选择第一个仓库 */
    WAREHOUSE_TYPE_IS_NOT_RECOGNIZED("62044", "zsmall.product.warehouseTypeIsNotRecognized"),
    /** 查询SkuId.时发生了未知错误 */
    QUERY_ITEM_NO_ERROR("62045", "zsmall.product.queryItemNoError"),
    /** 此商品所在仓库未配置物流模板 */
    NOT_CONFIGURED_LOGISTICS_TEMPLATE("62046", "zsmall.product.notConfiguredLogisticsTemplate"),
    /** 此商品所在仓库没有足够的库存 */
    NOT_HAVE_ENOUGH_STOCK("62047", "zsmall.product.notHaveEnoughStock"),
    /** 提货类型未知 */
    PICK_UP_TYPE_UNKNOWN("62048", "zsmall.product.pickUpTypeUnknown"),
    /** Sku[{}] 单价、一件代发价不能为0 */
    SKU_PICK_UP_DROPSHIPPING_PRICE_NOT_ZERO("62049", "zsmall.product.skuPickUpDropshippingPriceNotZero"),
    /** Sku[{}] 单价不能为0 */
    SKU_PICK_UP_PRICE_NOT_ZERO("62050", "zsmall.product.skuPickUpPriceNotZero"),
    /** Sku[{}] 一件代发价不能为0 */
    SKU_DROPSHIPPING_PRICE_NOT_ZERO("62051", "zsmall.product.skuDropshippingPriceNotZero"),
    /** 新商品正在审核中，无法修改信息 */
    NEW_PRODUCT_VERIFY_PENDING("62052", "zsmall.product.newProductVerifyPending"),
    /** 审核商品时出现未知错误 */
    REVIEW_PRODUCT_ERROR("62053", "zsmall.product.reviewProductError"),
    /** 审核驳回理由不能为空 */
    REJECTION_REASON_CANNOT_BE_EMPTY("62054", "zsmall.product.rejectionReasonCannotBeEmpty"),
    /** 查询商品审核列表时出现未知错误 */
    QUERY_PRODUCT_REVIEW_PAGE_ERROR("62055", "zsmall.product.queryProductReviewPageError"),
    /** 商品不存在 */
    PRODUCT_NOT_EXIST("62056", "zsmall.product.productNotExist"),
    /** 查询商品列表时发生未知错误 */
    QUERY_PRODUCT_ERROR("62057", "zsmall.product.queryProductError"),
    /** 库存不足 */
    OUT_OF_STOCK("62058", "zsmall.product.outOfStock"),
    /** 扣除库存时发生未知错误 */
    DEDUCT_STOCK_ERROR("62058", "zsmall.product.deductStockError"),
    /** 指定日期不能小于或者等于今天 */
    SPECIFY_DATE_NOT_EQUAL_NOW_OR_LESS("62059", "zsmall.product.specifyDateNotEqualNowOrLess"),
    /** 未选择仓库 */
    WAREHOUSE_NOT_SELECTED("62061", "zsmall.product.warehouseNotSelected"),
    /** 商品数据错误，请重试 */
    PRODUCT_DATA_ERROR("62062", "zsmall.product.productDataError"),
    /** 商品查询发生未知错误 */
    PRODUCT_QUERY_LIST_ERROR("62063", "zsmall.product.productQueryListError"),
    /** 更新商品属性发生未知错误 */
    PRODUCT_CATEGORY_UPDATE_ERROR("62064", "zsmall.product.productCategoryUpdateError"),
    /** 下架商品发生未知错误 */
    PRODUCT_OFF_SHELF_ERROR("62065", "zsmall.product.productOffShelfError"),
    /** Sku[{}] 单价不能为0 */
    SKU_UNIT_PRICE_NOT_ZERO("62066", "zsmall.product.skuUnitPriceNotZero"),
    /** Sku[{}] 操作费不能为0 */
    SKU_OPERATION_FEE_NOT_ZERO("62067", "zsmall.product.skuOperationFeeNotZero"),
    /** Sku[{}] 尾程操作费不能为0 */
    SKU_FINAL_DELIVERY_FEE_NOT_ZERO("62068", "zsmall.product.skuFinalDeliveryFeeNotZero"),
    /** Sku[{}] 单价、操作费和尾程操作费不能为0 */
    SKU_UNIT_PRICE_OPERATION_FEE_FINAL_DELIVERY_FEE_NOT_ZERO("62069", "zsmall.product.skuUnitPriceOperationFeeFinalDeliveryFeeNotZero"),
    /** 商品正在审核中，无法修改价格 */
    NEW_PRODUCT_PRICE_VERIFY_PENDING("62059", "zsmall.product.newProductPriceVerifyPending"),
    /** 商品已通过审核，无需再次提交审核 */
    PRODUCT_ACCEPTED("62060", "zsmall.product.productAccepted"),
    /** ItemNo未上架 */
    ITEM_NO_NOT_ON_SHELF("62061", "zsmall.product.itemNoNotOnShelf"),
    /** 设置商品sku涨价异常 */
    SET_PRODUCT_SKU_MARK_UP_ERROR("63004", "zsmall.product.setProductSkuMarkUpError"),
    /**
     * 未审核的商品无法上架
     */
    PRODUCT_NOT_REVIEW_CANNOT_ON_SHELF("63005", "zsmall.product.notReviewCannotOnShelf"),
    /**
     * 属性名重复
     */
    PRODUCT_GLOBAL_ATTRIBUTE_NAME_REPEAT("63006", "zsmall.product.productGlobalAttributeNameRepeat"),
    /** 商品Sku不存在 */
    PRODUCT_SKU_NOT_EXISTS("63007", "zsmall.product.productSkuNotExists"),
    /** 商品Sku库存不足 */
    PRODUCT_SKU_OUT_OF_STOCK("63008", "zsmall.product.productSkuOutOfStock"),

    /** 商品Sku库存调整时出现未知错误 */
    PRODUCT_SKU_ADJUST_STOCK_ERROR("63009", "zsmall.product.productSkuAdjustStockError"),
    /** 存在必填的普通规格 */
    PRODUCT_EXISTS_REQUIRED_SPEC("63010", "zsmall.product.productExistsRequiredSpec"),
    /** 未找到可用库存 */
    NO_AVAILABLE_STOCK_FOUND("63011", "zsmall.product.noAvailableStockFound"),
    /**
     * 产品未存储在仓库中
     */
    PRODUCT_IS_NOT_STORED_IN_THE_WAREHOUSE("63012", "zsmall.product.productIsNotStoredInTheWarehouse"),

    /** 商品价格公式和适用产品保存异常 */
    PRODUCT_SKU_PRICE_RULE_SAVE_ERROR("64101", "zsmall.productPrice.productSkuPriceRuleSaveError"),
    /** 获取适用产品异常 */
    PRODUCT_SKU_APPLICABLE_PRODUCT_QUERY_ERROR("64102", "zsmall.productPrice.productSkuApplicableProductQueryError"),
    /** 删除适用产品异常 */
    PRODUCT_SKU_APPLICABLE_PRODUCT_DELETE_ERROR("64103", "zsmall.productPrice.productSkuApplicableProductDeleteError"),
    /** 获取产品价格信息列表异常 */
    QUERY_PRODUCT_SKU_PRICE_LIST_ERROR("64104", "zsmall.productPrice.queryProductSkuPriceListError"),
    /** 保存价格信息异常 */
    PRODUCT_SKU_PRICE_SAVE_ERROR("64105", "zsmall.productPrice.productSkuPriceSaveError"),
    /** 查询定价公式列表时出现异常 */
    QUERY_PRODUCT_SKU_PRICE_RULE_LIST_ERROR("64106", "zsmall.productPrice.queryProductSkuPriceRuleListError"),
    /** 查询定价公式详情时出现异常 */
    QUERY_PRODUCT_SKU_PRICE_RULE_DETAILS_ERROR("64107", "zsmall.productPrice.queryProductSkuPriceRuleDetailsError"),
    /** 商品价格公式适用数据存在交集 */
    APPLICABLE_VALUE_ERROR("64108", "zsmall.productPrice.applicableValueError"),
    /** 校验商品价格公式适用数据出现异常 */
    CHECK_APPLICABLE_VALUE_ERROR("64109", "zsmall.productPrice.checkApplicableValueError"),
    /** 当前适用商品不能删除 */
    APPLICABLE_PRODUCT_CANNOT_BE_DELETED("64110", "zsmall.productPrice.applicableProductCannotBeDeleted"),
    /** 不能进行乘以0和除以0操作 */
    APPLICABLE_CALCULATION_ERROR("64111", "zsmall.productPrice.applicableCalculationError"),
    /** 基础公式不能修改 */
    APPLICABLE_BASE_RULE_CANNOT_EDIT("64112", "zsmall.productPrice.applicableBaseRuleCannotEdit"),

    /** 获取可用的UPC异常 */
    GEY_USAGE_UPC_ERROR("65001", "zsmall.productUpc.geyUsageUpcError"),
    /** UPC不足 */
    UPC_SHORTAGE("65002", "zsmall.productUpc.upcShortage"),
    /** UPC使用记录更新异常 */
    USAGE_UPC_RECORD_UPDATE_ERROR("65003", "zsmall.productUpc.usageUpcRecordUpdateError"),
    /** UPC已被其他店铺使用 */
    USAGE_UPC_STORE_USED("65004", "zsmall.productUpc.usageUpcStoreUsed"),
    /** UPC已被当前店铺下其他SKU使用 */
    USAGE_UPC_SKU_USED("65005", "zsmall.productUpc.usageUpcSkuUsed"),
    /** UPC未找到或已被使用 */
    USAGE_UPC_NOT_FOUND("65006", "zsmall.productUpc.usageUpcNotFound"),

    /* 分销商商品映射信息 */

    /**
     * 商品映射信息不存在
     * Product mapping info not exists.
     */
    PRODUCT_MAPPING_NOT_EXISTS("66001", "zsmall.productMapping.productMappingNotExists"),



    /* 分销商商品映射信息 */

    /** 支付请求已提交至系统处理，具体结果请详见订单状态 */
    PAY_ORDER_SUCCESS("0", "zsmall.orders.payOrderSuccess"),
    /** 订单不存在 */
    ORDERS_IS_NOT_EXIST("71008", "zsmall.orders.ordersIsNotExist"),
    /** 子订单不存在 */
    ORDER_ITEM_NOT_EXIST("71005", "zsmall.orders.orderItemNotExist"),
    /** 物流信息不存在，无法改变履约状态 */
    LOGISTICS_TRACKING_NOT_EXIST("71011", "zsmall.orders.logisticsTrackingNotExist"),
    /** 下载快递标签异常 */
    SHIPPING_LABEL_DOWNLOAD_ERROR("71012", "zsmall.orders.shippingLabelDownloadError"),
    /** 模板文件不存在 */
    FILE_CANT_BE_NOT_FOUND("71013", "zsmall.orders.fileCantBeNotFound"),
    /** 上传文件为空 */
    UPLOAD_FILE_IS_EMPTY("71014", "zsmall.orders.uploadFileIsEmpty"),
    /** 上传的模板文件标题行不正确，请重新下载模板且勿修改模板标题行。 */
    EXCEL_COLUMN_COUNT_NOT_MATCH("71015", "zsmall.orders.excelColumnCountNotMatch"),
    /** 上传的模板文件数据不能为空 */
    EXCEL_NOT_EXIST_VALID_ROW("71016", "zsmall.orders.excelNotExistValidRow"),
    /** 订单不存在或导入记录已审核 */
    ORDER_NOT_EXIST_OR_REVIEW("71017", "zsmall.orders.orderNotExistOrReview"),
    /** 上传快递标签异常 */
    SHIPPING_LABEL_UPLOAD_ERROR("71018", "zsmall.orders.shippingLabelUploadError"),
    /** 订单状态不为[已支付]，无法确认发货 */
    ORDER_UNPAID_CANT_FULFILL("71019", "zsmall.orders.orderUnpaidCantFulfill"),
    /** 获取支付失败信息时出错,请稍后再试 */
    GET_PAY_FAILED_ORDER_MESSAGE_ERROR("71020", "zsmall.orders.getPayFailedOrderMessageError"),
    /** 订单退款中，无法履约 */
    ORDER_REFUNDING_CANT_FULFILL("71021", "zsmall.orders.orderRefundingCantFulfill"),
    /** 取消订单发生错误 */
    ORDER_CANCEL_ERROR("71022", "zsmall.orders.orderCancelError"),
    /** 手动支付发生错误 */
    PAY_ORDER_ERROR("71023", "zsmall.orders.payOrderError"),
    /** 当前默认设置为自动扣款,如需手动付款请关闭自动扣款功能 */
    AUTOMATICALLY_DEDUCTION_TURNED_ON("71024", "zsmall.orders.automaticallyDeductionTurnedOn"),
    /** 商品导入文件后缀不为xls或xlsx */
    PRODUCT_IMPORT_FILE_SUFFIX_NOT_MATCH("71025", "zsmall.orders.productImportFileSuffixNotMatch"),
    /** 商品导入文件标题不符合标准，请重新下载模板 */
    PRODUCT_IMPORT_FILE_TITLE_NOT_MATCH("71026", "zsmall.orders.productImportFileTitleNotMatch"),
    /** 存在正在导入的Excel，请等待导入结束 */
    PRODUCT_IMPORT_FILE_EXIST_IMPORTING("71027", "zsmall.orders.productImportFileExistImporting"),
    /** 导入商品时发生未知错误 */
    PRODUCT_IMPORT_ERROR("71028", "zsmall.orders.productImportError"),
    /** 查询商品导入记录时发生未知错误 */
    QUERY_PRODUCT_IMPORT_RECORD_ERROR("71029", "zsmall.orders.queryProductImportRecordError"),
    /** 关联自提物流时发生未知错误 */
    CONNECT_OTHER_LOGISTICS_ERROR("71030", "zsmall.orders.connectOtherLogisticsError"),
    /** 订单不存在 */
    ORDER_NOT_EXIST("71031", "zsmall.orders.orderNotExist"),
    /** 订单已支付，无法进行此操作 */
    ORDER_HAS_BEEN_PAID("71032", "zsmall.orders.orderHasNotBeenPaid"),
    /** 文件中无商品数据 */
    PRODUCT_IMPORT_FILE_NOT_ROW("71033", "zsmall.orders.productImportFileNotRow"),
    /** 履约状态不能回到上一状态 */
    FULFILLMENT_STATUS_CANNOT_GO_BACK("71034", "zsmall.orders.fulfillmentStatusCannotGoBack"),
    /** 有多个重复渠道订单号无法匹配快递标签，请单个订单操作上传快递标签 */
    SHIPPING_LABEL_ORDER_TOO_MUCH("71035", "zsmall.orders.shippingLabelOrderTooMuch"),
    /** 订单已填写第三方账号，无需上传快递标签 */
    NOT_NEED_UPLOAD_LABEL("71036", "zsmall.orders.notNeedUploadLabel"),
    /** 不支持的国家 */
    UNSUPPORTED_COUNTRIES("71037", "zsmall.orders.unsupportedCountries"),
    /** 以下文件未匹配到[渠道订单号]：</br>{} */
    UPLOAD_SHIPPING_LABEL_MESSAGE("0", "zsmall.orders.uploadShippingLabelMessage"),
    /** 改变渠道订单号时发生未知错误 */
    CHANGE_CHANNEL_ORDER_ID_ERROR("71038", "zsmall.orders.changeChannelOrderIdError"),
    /** 改变店铺链接时发生未知错误 */
    CHANGE_STORE_LINK_ERROR("71039", "zsmall.orders.changeStoreLinkError"),
    /** 改变订单详情时发生未知错误 */
    CHANGE_ORDER_DETAILS_ERROR("71040", "zsmall.orders.changeOrderDetailsError"),
    /** 商品仅支持代发 */
    PRODUCT_ONLY_SUPPORT_DROPSHIPPING("71041", "zsmall.orders.productOnlySupportDropshipping"),
    /** 商品仅支持自提 */
    PRODUCT_ONLY_SUPPORT_PICK_UP("71042", "zsmall.orders.productOnlySupportPickUp"),
    /** 购物车下单时发生未知错误 */
    PLACE_SHOPPING_CART_ORDER_ERROR("71043", "zsmall.orders.placeShoppingCartOrderError"),
    /** 手动确认收货时发生未知错误 */
    ORDER_CONFIRM_RECEIPT_ERROR("71044", "zsmall.orders.orderConfirmReceiptError"),
    /** 不存在等待收货的订单 */
    ORDER_CANNOT_UPDATE_NOT_BELONG_TO_ME_ERROR("71045", "zsmall.orders.orderCannotUpdateNotBelongToMeError"),
    /** 上传订单附件异常 */
    ORDER_ATTACHMENT_UPLOAD_ERROR("71046", "zsmall.orders.orderAttachmentUploadError"),
    /** 查询订单列表时发生未知错误 */
    QUERY_ORDER_LIST_ERROR("71047", "zsmall.orders.queryOrderListError"),
    /** 查询订单详情时发生未知错误 */
    QUERY_ORDER_DETAIL_ERROR("71048", "zsmall.orders.queryOrderDetailError"),
    /** 当前订单不支持此功能 */
    CURRENT_ORDER_NOT_SUPPORTED("71049", "zsmall.orders.currentOrderNotSupported"),
    /** 订单不存在[订单编号]：*/
    ORDER_NOT_EXIST_FOR_ORDER_NO("71050", "zsmall.orders.orderNotExistForOrderNo"),

    /** 确认发货时发生未知错误 */
    CONFIRM_DISPATCHED_ERROR("71201", "zsmall.orderLogistics.confirmDispatchedError"),
    /** 当前无法操作发货 */
    ORDER_ALREADY_DISPATCHED("71202", "zsmall.orderLogistics.orderAlreadyDispatched"),
    /** 请至少填写一个跟踪单号 */
    FILL_LEAST_ONE_TRACKING_NO("71203", "zsmall.orderLogistics.fillLeastOneTrackingNo"),
    /** 物流信息识别时发生未知错误 */
    DISCERN_LOGISTICS_ERROR("71204", "zsmall.orderLogistics.discernLogisticsError"),
    /** 物流信息识别错误，原因: {} */
    TRACK_17_DISCERN_LOGISTICS_ERROR("71205", "zsmall.orderLogistics.track17DiscernLogisticsError"),
    /** 物流单号必须填写 */
    TRACKING_NO_REQUIRED("71206", "zsmall.orderLogistics.trackingNoRequired"),
    /** 第三方物流异常 */
    THIRD_LOGISTICS_ERROR("71207", "zsmall.orderLogistics.thirdLogisticsError"),

    /** 分页查询物流跟踪单时发生未知错误 */
    QUERY_TRACKING_PAGE_ERROR("71301", "zsmall.orderLogisticsTracking.queryTrackingPageError"),
    /** 导出物流跟踪数据时发生未知错误 */
    EXPORT_TRACKING_ERROR("71302", "zsmall.orderLogisticsTracking.exportTrackingError"),

    /** 退货方式未知 */
    RETURN_TYPE_UNKNOWN("72015", "zsmall.orderRefund.returnTypeUnknown"),
    /** 退款单不存在 */
    ORDER_REFUND_NOT_EXIST("72011", "zsmall.orderRefund.orderRefundNotExist"),
    /** 状态不为退款进行中，无法补全信息 */
    REFUND_ITEM_NOT_REFUNDING("72012", "zsmall.orderRefund.refundItemNotRefunding"),
    /** 订单已退款或者不符合退款标准 */
    NO_CAN_REFUND_ORDER_ITEM("72021", "zsmall.orderRefund.noCanRefundOrderItem"),
    /** 获取退款原因列表出错,请稍后再试 */
    GET_REFUND_REASON_OPTIONS_ERROR("72022", "zsmall.orderRefund.getRefundReasonOptionsError"),
    /** 未发货时只能全数退款 */
    REFUND_ALL_WHEN_UNDISPATCHED("72023", "zsmall.orderRefund.refundAllWhenUndispatched"),
    /** 存在进行中的退款单，请等待完结后重新申请 */
    HAS_REFUNDING_ORDER("72024", "zsmall.orderRefund.hasRefundingOrder"),
    /** 您的订单已超出售后时效，如有售后需求请联系平台 */
    ORDER_EXCEEDED_AFTER_SALES_LIMITATIONS("72025", "zsmall.orderRefund.orderExceededAfterSalesLimitations"),

    /** 查询售后原因列表时发生未知错误 */
    QUERY_REFUND_REASON_LIST_ERROR("73001", "zsmall.orderAfterSales.queryRefundReasonListError"),
    /** 提交售后申请时发生未知错误 */
    SUBMIT_REFUND_REQUEST_ERROR("73002", "zsmall.orderAfterSales.submitRefundRequestError"),
    /** 售后规则不存在，请刷新页面后重新申请 */
    REFUND_RULE_NOT_EXIST("73003", "zsmall.orderAfterSales.refundRuleNotExist"),
    /** 上传售后规则时发生未知错误 */
    UPLOAD_REFUND_RULE_ERROR("73004", "zsmall.orderAfterSales.uploadRefundRuleError"),
    /** 取消售后申请时发生未知错误 */
    CANCEL_REFUND_REQUEST_ERROR("73005", "zsmall.orderAfterSales.cancelRefundRequestError"),
    /** 售后申请记录不存在 */
    REFUND_REQUEST_NOT_EXIST("73006", "zsmall.orderAfterSales.refundRequestNotExist"),
    /** 当前状态无法取消售后申请 */
    CANNOT_CANCEL_THE_REFUND_APPLICATION("73007", "zsmall.orderAfterSales.cannotCancelTheRefundApplication"),
    /** 查询售后单详情时发生未知错误 */
    QUERY_REFUND_DETAIL_ERROR("73008", "zsmall.orderAfterSales.queryRefundDetailError"),
    /** 售后申请处理异常 */
    REFUND_HANDLE_ERROR("73009", "zsmall.orderAfterSales.refundHandleError"),
    /** 当前售后申请状态无法执行该操作 */
    CURRENT_REFUND_CANNOT_BE_PROCESSED("73010", "zsmall.orderAfterSales.currentRefundCannotBeProcessed"),
    /** 确认收到退货时发生未知错误 */
    CONFIRM_RECEIPT_OF_RETURN("73011", "zsmall.orderAfterSales.confirmReceiptOfReturn"),
    /** 退货物流信息不存在 */
    RETURN_LOGISTICS_INFORMATION_NOT_EXIST("73012", "zsmall.orderAfterSales.returnLogisticsInformationNotExist"),
    /** 申请平台介入时发生未知错误 */
    PLATFORM_INTERVENTION_ERROR("73013", "zsmall.orderAfterSales.platformInterventionError"),
    /** 处理平台介入时发生未知错误 */
    HANDLE_PLATFORM_INTERVENTION_ERROR("73014", "zsmall.orderAfterSales.handlePlatformInterventionError"),
    /** 订单已全部退款，无法再次提交售后申请 */
    ORDER_REFUNDED("73015", "zsmall.orderAfterSales.orderRefunded"),
    /** 请提供图片 */
    PLEASE_PROVIDE_PICTURES("73101", "zsmall.orderAfterSales.pleaseProvidePictures"),
    /** 金额不能为零 */
    AMOUNT_CANNOT_BE_ZERO("73102", "zsmall.orderAfterSales.amountCannotBeZero"),
    /** 申请金额超过可退款金额 */
    REQUESTED_AMOUNT_EXCEEDS_REFUNDABLE("73103", "zsmall.orderAfterSales.requestedAmountExceedsRefundable"),
    /** 请选择不可修改金额的退款原因 */
    UNMODIFIABLE_AMOUNT_REASON("73104", "zsmall.orderAfterSales.unmodifiableAmountReason"),

    /** 保存仓库信息异常 */
    SAVE_STORE_WAREHOUSE_ERROR("91009", "zsmall.warehouseManagement.saveStoreWarehouseError"),
    /** 存在相同的仓库编号 */
    THE_SAME_WAREHOUSE_CODE_EXISTS("91010", "zsmall.warehouseManagement.theSameWarehouseIdExists"),
    /** 获取仓库列表异常 */
    GET_STORE_WAREHOUSE_LIST_BY_TYPE_ERROR("91011", "zsmall.warehouseManagement.getStoreWarehouseListByTypeError"),
    /** 获取仓库服务商key配置异常 */
    GET_WAREHOUSE_KEY_LIST_ERROR("91012", "zsmall.warehouseManagement.getWarehouseKeyListError"),
    /** 设置仓库服务商key异常 */
    SET_WAREHOUSE_KEY_ERROR("91013", "zsmall.warehouseManagement.setWarehouseKeyError"),
    /** 存在相同的仓库KeyId */
    THE_SAME_KEY_ID_EXISTS("91014", "zsmall.warehouseManagement.theSameKeyIdExists"),
    /** Key必须为数字 */
    THE_KEY_MUST_BE_NUMBER("91015", "zsmall.warehouseManagement.theKeyMustBeNumber"),
    /** 仓库已关联商品，无法删除 */
    ASSOCIATED_PRODUCT_CANNOT_DELETE("91016", "zsmall.warehouseManagement.associatedProductCannotDelete"),
    /** 仓库已关联物流模板，无法删除 */
    ASSOCIATED_LOGISTICS_CANNOT_DELETE("91017", "zsmall.warehouseManagement.associatedLogisticsCannotDelete"),
    /** 查询Sku库存列表异常 */
    QUERY_PRODUCT_SKU_STOCK_LIST_ERROR("92001", "zsmall.warehouseManagement.queryProductSkuStockListError"),
    /** 编辑库存数量异常 */
    EDIT_STOCK_QUANTITY_ERROR("92002", "zsmall.warehouseManagement.editStockQuantityError"),
    /** 第三方仓库无法编辑库存 */
    BIZARK_WAREHOUSE_CANNOT_EDIT_STOCK("92003", "zsmall.warehouseManagement.bizarkWarehouseCannotEditStock"),
    /** 切换库存销售状态异常 */
    SWITCH_STOCK_SALE_STATUS_ERROR("92004", "zsmall.warehouseManagement.switchStockSaleStatusError"),
    /** 库存状态未知，无法进行操作 */
    STOCK_STATUS_UNKNOWN("92005", "zsmall.warehouseManagement.stockStatusUnknown"),
    /** 查询可用的仓库列表异常 */
    QUERY_VALID_WAREHOUSE_LIST_ERROR("92006", "zsmall.warehouseManagement.queryValidWarehouseListError"),
    /** 该SKU已有库存在该仓库 */
    SKU_EXIST_NOW_WAREHOUSE("92007", "zsmall.warehouseManagement.skuExistNowWarehouse"),
    /** 仓库无效或已删除 */
    WAREHOUSE_NOT_EXIST("92008", "zsmall.warehouseManagement.warehouseNotExist"),
    /** 查询履约仓库配置列表异常 */
    QUERY_FULFILL_WAREHOUSE_LIST_ERROR("92009", "zsmall.warehouseManagement.queryFulfillWarehouseListError"),
    /** 设置履约仓库异常 */
    SETTING_FULFILL_WAREHOUSE_ERROR("92010", "zsmall.warehouseManagement.settingFulfillWarehouseError"),
    /** 查询Sku库存列表异常 */
    QUERY_PRODUCT_SKU_STOCK_ERROR("92011", "zsmall.warehouseManagement.queryProductSkuStockError"),
    /** 查询商品运输方式时发生未知错误 */
    QUERY_TRANSPORT_METHOD_ERROR("92012", "zsmall.warehouseManagement.queryTransportMethodError"),
    /** 查询商品Sku列表异常 */
    QUERY_PRODUCT_SKU_LIST_ERROR("92013", "zsmall.warehouseManagement.queryProductSkuListError"),
    /**
     * 仓库正被商品或促销活动使用，无法删除
     */
    WAREHOUSE_IS_ALREADY_IN_USE("92014", "zsmall.warehouseManagement.warehouseIsAlreadyInUse"),
    /** Shopify应用授权重定向异常 */
    SHOPIFY_REDIRECT_ERROR("12102", "zsmall.thirdPartyPlatforms.shopifyRedirectError"),
    /** Shopify Access Token 不存在 */
    SHOPIFY_ACCESS_TOKEN_NOT_EXIST("12103", "zsmall.thirdPartyPlatforms.shopifyAccessTokenNotExist"),
    /** 该Amazon已关联其他用户，无法再次关联 */
    AMAZON_SHOP_ALREADY_CONNECT_OTHER("12109", "zsmall.thirdParty.amazonShopAlreadyConnectOther"),
    /** Amazon应用授权绑定异常 */
    AMAZON_AUTHENTICATION_BINDING_ERROR("12111", "zsmall.thirdParty.amazonAuthenticationBindingError"),
    /** 获取销售渠道信息异常 */
    GET_CHANNEL_INFO_ERROR("12113", "zsmall.thirdPartyPlatforms.getChannelInfoError"),
    /** 同步商品至销售渠道出现未知异常 */
    SYNC_PRODUCT_UNKNOWN_ERROR("12114", "zsmall.thirdPartyPlatforms.syncProductUnknownError"),
    /** 获取已关联的渠道异常 */
    GET_ENABLE_CHANNEL_ERROR("12115", "zsmall.thirdPartyPlatforms.getEnableChannelError"),
    /** 查询导入记录异常 */
    QUERY_IMPORT_RECORD_ERROR("12116", "zsmall.thirdPartyPlatforms.queryImportRecordError"),
    /** 下载模板文件出错,请稍后再试 */
    DOWNLOAD_SHEET_ERROR("12118", "zsmall.thirdPartyPlatforms.downloadSheetError"),
    /** 审核导入记录异常 */
    REVIEW_IMPORT_RECORD_ERROR("12119", "zsmall.thirdPartyPlatforms.reviewImportRecordError"),
    /** 导入记录无效或不存在 */
    IMPORT_RECORD_NOT_FOUND("12119", "zsmall.thirdPartyPlatforms.importRecordNotFound"),
    /** 导入记录无效或不存在 */
    ORDER_LACK_SHIPPING_LABEL("12120", "zsmall.thirdPartyPlatforms.orderLackShippingLabel"),
    /** 获取导入记录出错,请稍后再试 */
    QUERY_IMPORT_RECORD_DETAIL_ERROR("12121", "zsmall.thirdPartyPlatforms.queryImportRecordDetailError"),
    /** 获取导入订单列表出错,请稍后再试 */
    QUERY_IMPORT_RECORD_ORDER_LIST_ERROR("12122", "zsmall.thirdPartyPlatforms.queryImportRecordOrderListError"),
    /** 获取已关联的渠道分组异常 */
    QUERY_ENABLE_CHANNEL_GROUP_ERROR("12123", "zsmall.thirdPartyPlatforms.queryEnableChannelGroupError"),
    /** 修改渠道信息异常 */
    MODIFY_CHANNEL_INFO_ERROR("12124", "zsmall.thirdPartyPlatforms.modifyChannelInfoError"),
    /** 销售渠道已停用或已删除，无法进行相关操作 */
    SALES_CHANNEL_NOT_EXIST("12125", "zsmall.thirdPartyPlatforms.salesChannelNotExist"),
    /** 销售渠道未启用或已删除，无法同步商品 */
    SALES_CHANNEL_CANNOT_SYNC("12126", "zsmall.thirdPartyPlatforms.salesChannelCannotSync"),
    /** 同步状态不正确，无法同步商品 */
    SYNC_STATUS_NOT_CORRECT("12127", "zsmall.thirdPartyPlatforms.syncStatusNotCorrect"),
    /** 请求参数同步状态未知 */
    REQUEST_SYNC_STATUS_UNKNOWN("12128", "zsmall.thirdPartyPlatforms.requestSyncStatusUnknown"),
    /** 存在相同的渠道店铺名 */
    SAME_CHANNEL_STORE_NAME("12129", "zsmall.thirdPartyPlatforms.sameChannelStoreName"),
    /** 存在相同的渠道店铺信息 */
    SAME_CHANNEL_STORE("12129", "zsmall.thirdPartyPlatforms.sameChannel"),
    /** 获取渠道店铺详细异常 */
    GET_CHANNEL_DETAIL_INFO_ERROR("12130", "zsmall.thirdPartyPlatforms.getChannelDetailInfoError"),
    /** 获取销售渠道信息出错,请稍后再试 */
    QUERY_ALL_CHANNEL_GROUP_ERROR("12131", "zsmall.thirdPartyPlatforms.queryAllChannelGroupError"),
    /** Wayfair沙盒测试出现未知错误 */
    WAYFAIR_SANDBOX_TEST_UNKNOWN_ERROR("12132", "zsmall.thirdPartyPlatforms.wayfairSandboxTestUnknownError"),
    /** Wayfair沙盒测试错误（未查询到有效订单） */
    WAYFAIR_SANDBOX_TEST_ERROR_NOT_ORDERS("12133", "zsmall.thirdPartyPlatforms.wayfairSandboxTestErrorNotOrders"),
    /** 授权信息丢失，请重新授权 */
    AUTHORIZE_INFO_LOST("12134", "zsmall.thirdPartyPlatforms.authorizeInfoLost"),
    /** Shopify店铺已被其它账号连接 */
    SHOPIFY_HAS_CONNECTED("12135", "zsmall.thirdPartyPlatforms.shopifyHasConnected"),
    /** Shopify认证信息已过期 */
    SHOPIFY_AUTHENTICATION_EXPIRED("12136", "zsmall.thirdPartyPlatforms.shopifyAuthenticationExpired"),
    /** 您的Shopify邮箱已注册了一个非分销商账号，仅分销商可以关联销售渠道 */
    SALES_CHANNEL_ONLY_BULK("12137", "zsmall.thirdPartyPlatforms.salesChannelOnlyBulk"),
    /** 订单导入发生未知错误 */
    OTHER_ORDER_IMPORT_ERROR("12138", "zsmall.thirdPartyPlatforms.otherOrderImportError"),
    /** 无法查询到订单，请稍候再试 */
    TEMP_ORDER_NOT_FOUND_ERROR("12139", "zsmall.thirdPartyPlatforms.tempOrderNotFoundError"),
    /** 更新订单信息发生未知错误，请稍候再试 */
    TEMP_ORDER_UPDATE_ERROR("12140", "zsmall.thirdPartyPlatforms.tempOrderUpdateError"),
    /** 洲不存在 */
    TEMP_ORDER_STATE_NOT_EXIST("12141", "zsmall.thirdPartyPlatforms.tempOrderStateNotExist"),
    /** 手机号格式错误 */
    TEMP_ORDER_PHONE_ERROR("12142", "zsmall.thirdPartyPlatforms.tempOrderPhoneError"),
    /** 不支持的国家 */
    TEMP_ORDER_COUNTRY_NOT_EXIST("12143", "zsmall.thirdPartyPlatforms.tempOrderCountryNotExist"),
    /** 收件人名称未填写 */
    TEMP_ORDER_RECIPIENT_NAME_BLANK_ERROR("12144", "zsmall.thirdPartyPlatforms.tempOrderRecipientNameBlankError"),
    /** 手机号格式错误 */
    TEMP_ORDER_PHONE_BLANK_ERROR("12145", "zsmall.thirdPartyPlatforms.tempOrderPhoneBlankError"),
    /** 地址一未填写 */
    TEMP_ORDER_ADDRESS1_BLANK_ERROR("12146", "zsmall.thirdPartyPlatforms.tempOrderAddress1BlankError"),
    /** 城市未填写 */
    TEMP_ORDER_CITY_BLANK_ERROR("12147", "zsmall.thirdPartyPlatforms.tempOrderCityBlankError"),
    /** zipCode未填写 */
    TEMP_ORDER_ZIPCODE_BLANK_ERROR("12148", "zsmall.thirdPartyPlatforms.tempOrderZipcodeBlankError"),
    /** 第三方物流商账号未填写 */
    TEMP_ORDER_LOGISTICS_ACCOUNT_BLANK_ERROR("12149", "zsmall.thirdPartyPlatforms.tempOrderLogisticsAccountBlankError"),
    /** 第三方物流商邮编未填写 */
    TEMP_ORDER_LOGISTICS_ACCOUNT_ZIPCODE_BLANK_ERROR("12150", "zsmall.thirdPartyPlatforms.tempOrderLogisticsAccountZipcodeBlankError"),
    /** 仓库不支持第三方物流 */
    TEMP_ORDER_WAREHOUSE_NOT_SUPPORT_3RDBILLING_ERROR("12151", "zsmall.thirdPartyPlatforms.tempOrderWarehouseNotSupport3rdbillingError"),
    /** 跟踪单号未填写 */
    TEMP_ORDER_TRACKING_NO_BLANK_ERROR("12152", "zsmall.thirdPartyPlatforms.tempOrderTrackingNoBlankError"),
    /** 商店订单ID未填写，无法上传Shipping Label */
    TEMP_ORDER_STORE_ORDER_ID_BLANK_ERROR("12153", "zsmall.thirdPartyPlatforms.tempOrderStoreOrderIdBlankError"),
    /** 暂无订单需要上传快递标签 */
    TEMP_ORDER_STORE_ORDER_ID_BLANK_OR_NOT_PICK_UP_ERROR("12154", "zsmall.thirdPartyPlatforms.tempOrderStoreOrderIdBlankOrNotPickUpError"),
    /** 自提订单未上传快递标签 */
    TEMP_ORDER_PICK_UP_NOT_EXIST_SHIPPING_LABEL_ERROR("12155", "zsmall.thirdPartyPlatforms.tempOrderPickUpNotExistShippingLabelError"),
    /** SkuId.[{}] 已下架或不存在！ */
    TEMP_ORDER_SOME_PRODUCT_NOT_EXIST("12156", "zsmall.thirdPartyPlatforms.tempOrderSomeProductNotExist"),
    /** SkuId.[{}] 不存在于指定仓库（{}）！ */
    TEMP_ORDER_PRODUCT_NOT_EXIST_WAREHOUSE("12157", "zsmall.thirdPartyPlatforms.tempOrderProductNotExistWarehouse"),
    /** 邮编不存在 */
    TEMP_ORDER_ZIPCODE_NOT_EXIST("12158", "zsmall.thirdPartyPlatforms.tempOrderZipcodeNotExist"),
    /** 商店订单ID不能重复 */
    TEMP_ORDER_STORE_ORDER_ID_REPEAT("12159", "zsmall.thirdPartyPlatforms.tempOrderStoreOrderIdRepeat"),
    /** 销售渠道已删除或已断开连接 */
    SALES_CHANNEL_DELETE_DISCONNECT("12160", "zsmall.thirdPartyPlatforms.salesChannelDeleteDisconnect"),
    /** 销售渠道店铺：{}，ItemNo.：{}，信息更新成功 */
    SALES_CHANNEL_UPDATE_SUCCESS("12161", "zsmall.thirdPartyPlatforms.salesChannelUpdateSuccess"),
    /** 销售渠道店铺：{}，ItemNo.：{}，信息更新失败，原因：{} */
    SALES_CHANNEL_UPDATE_FAILED("12162", "zsmall.thirdPartyPlatforms.salesChannelUpdateFailed"),
    /** 商品正在同步至销售渠道，请勿重复操作 */
    PRODUCT_SYNCHRONIZING("12163", "zsmall.thirdPartyPlatforms.productSynchronizing"),
    /** 文件删除错误，请重试 */
    TEMP_ORDER_FILE_DELETE_ERROR("12164", "zsmall.thirdPartyPlatforms.tempOrderFileDeleteError"),
    /** 第三方物流必选承运商 */
    TEMP_ORDER_CARRIER_BLANK_ERROR("12165", "zsmall.thirdPartyPlatforms.tempOrderCarrierBlankError"),
    /** 推送履约信息至第三方渠道店铺出现未知错误 */
    PUSH_FULFILLMENT_TO_SALES_CHANNEL_ERROR("12166", "zsmall.thirdPartyPlatforms.pushFulfillmentToSalesChannelError"),
    /** Shopify履约订单不存在，请确认是否已经手动履约 */
    SHOPIFY_FULFILLMENT_ORDER_NOT_EXISTS("12167", "zsmall.thirdPartyPlatforms.shopifyFulfillmentOrderNotExists"),
    /** Rakuten商品图片上传失败 */
    RAKUTEN_IMAGE_UPLOAD_FAILED("12168", "zsmall.thirdPartyPlatforms.rakutenImageUploadFailed"),



    /** 获取Access Token失败 */
    GET_ACCESS_TOKEN_FAILED("21128", "zsmall.store.getAccessTokenFailed"),
    /** 渠道店铺不存在或已被删除，无法重新授权 */
    CHANNEL_STORE_NOT_EXIST_OR_DELETED("21129", "zsmall.store.channelStoreNotExistOrDeleted"),
    /** WayFair连接失败 */
    WAYFAIR_CONNECT_ERROR("21014", "zsmall.store.wayfairConnectError"),
    /** 绑定渠道账号异常 */
    BINDING_CHANNEL_ACCOUNT_ERROR("21015", "zsmall.store.bindingChannelAccountError"),
    /** 跳转渠道链接异常 jumpToChannel*/
    JUMP_TO_CHANNEL_ERROR("21016", "zsmall.store.jumpToChannelError"),
    /** 查询销售渠道关联情况异常 */
    QUERY_SALES_CHANNEL_STATUS_ERROR("21017", "zsmall.store.querySalesChannelStatusError"),
    /** 查询销售渠道信息异常 */
    QUERY_SALES_CHANNEL_INFO_ERROR("21018", "zsmall.store.querySalesChannelInfoError"),
    /** 解除绑定异常 */
    DISCONNECT_SALES_CHANNEL_ERROR("21020", "zsmall.store.disconnectSalesChannelError"),

    /** 计算交易手续费异常 */
    CALCULATE_HANDLE_FEE_ERROR("24023", "zsmall.payMethod.calculateHandleFeeError"),
    /** 未知支付方式 */
    UNKNOWN_PAYMENT_TYPE("24024", "zsmall.payMethod.unknownPaymentType"),
    /** 检查是否绑定了支付方式异常 */
    CHECK_BIND_PAYMENT_METHOD_ERROR("24025", "zsmall.payMethod.checkBindPaymentMethodError"),
    /** 充值金额小于等于手续费，无法完成充值 */
    COST_LESS_HANDING_FEE("24026", "zsmall.payMethod.costLessHandingFee"),
    /** 员工汇入或者扣除钱包余额时发生未知错误 */
    RECHARGE_OR_DEDUCT_ERROR("24027", "zsmall.payMethod.rechargeOrDeductError"),
    /** 分销商或者供应商不存在 */
    BULK_OR_SUP_NOT_EXIST("24028", "zsmall.payMethod.bulkOrSupNotExist"),
    /** 请选择正确的操作类型 */
    RECHARGE_OR_DEDUCT_OPERATION_TYPE_ERROR("24029", "zsmall.payMethod.rechargeOrDeductOperationTypeError"),
    /** 余额不足 */
    BULK_WALLET_BALANCE_INSUFFICIENT("24030", "zsmall.payMethod.bulkWalletBalanceInsufficient"),
    /** 余额清零发生未知错误 */
    CLEAR_BALANCE_ERROR("24031", "zsmall.payMethod.clearBalanceError"),
    /** 被操作的用户不是供应商或者分销商 */
    USER_NOT_SUP_OR_NOT_BULK("24032", "zsmall.payMethod.userNotSupOrNotBulk"),
    /** 查询钱包余额发生未知错误 */
    QUERY_BALANCE_ERROR("24033", "zsmall.payMethod.queryBalanceError"),
    /** 导出钱包余额发生未知错误 */
    EXPORT_BALANCE_ERROR("24040", "zsmall.payMethod.exportBalanceError"),
    /** 只允许为分销商用户充值 */
    ONLY_ALLOW_RECHARGE_TO_BULK_WALLET("24041", "zsmall.payMethod.onlyAllowRechargeToBulkWallet"),
    /** 分销商不存在 */
    BULK_NOT_EXIST("24034", "zsmall.payMethod.bulkNotExist"),
    /** 钱包已被冻结，无法进行相关操作 */
    WALLET_FREEZE("24035", "zsmall.payMethod.walletFreeze"),
    /** 订单支付暂不支持使用银行卡 */
    NOT_SUPPORTED_BANK_CARDS("24036", "zsmall.payMethod.notSupportedBankCards"),
    /** 支付方式无效，请重新选择 */
    INVALID_PAYMENT_METHOD("24037", "zsmall.payMethod.invalidPaymentMethod"),
    /** 钱包支付时出现未知错误 */
    WALLET_PAY_ERROR("24038", "zsmall.payMethod.walletPayError"),
    /** 退款至钱包时出现未知错误 */
    WALLET_REFUND_ERROR("24039", "zsmall.payMethod.walletRefundError"),
    /** 暂未开通为供货商充值功能 */
    NOT_ALLOW_RECHARGE_TO_SUP("24040", "zsmall.payMethod.notAllowRechargeToSup"),
    /**
     * 钱包更新失败，请重试
     */
    WALLET_UPDATE_FAILURE("24041", "zsmall.payMethod.walletUpdateFailure"),
    WALLET_NOT_FOUND("24042", "zsmall.payMethod.walletNotFound"),

    /** 交易记录不存在 */
    TRANSACTIONS_NOT_EXIST("25003", "zsmall.thirdParty.transactionsNotExist"),
    /** 省不存在 */
    STATE_NOT_EXIST("31002", "zsmall.thirdParty.stateNotExist"),

    /** 获取汇款信息时发生未知错误 */
    GET_REMITTANCE_INFO_ERROR("101001", "zsmall.payExtend.getRemittanceInfoError"),
    /** 提交交易回执单时发生未知错误 */
    SUBMIT_PAYMENT_RECEIPT_ERROR("101002", "zsmall.payExtend.submitPaymentReceiptError"),
    /** 查询交易回执单时发生未知错误 */
    QUERY_PAYMENT_RECEIPT_ERROR("101003", "zsmall.payExtend.queryPaymentReceiptError"),
    /** 审核交易回执单时发生未知错误 */
    REVIEW_PAYMENT_RECEIPT_ERROR("101004", "zsmall.payExtend.reviewPaymentReceiptError"),
    /** 该交易回执单已确认处理，无法再次操作 */
    PAYMENT_RECEIPT_SOLVED("101005", "zsmall.payExtend.paymentReceiptSolved"),
    /** 交易回执单不存在 */
    PAYMENT_RECEIPT_NOT_EXIST("101099", "zsmall.payExtend.paymentReceiptNotExist"),
    /** 获取钱包充值详情异常，请稍候重试 */
    GET_WALLET_DETAILS_LIST_ERROR("101101", "zsmall.payExtend.getWalletDetailsListError"),
    /** 导出钱包充值详情异常，请稍候重试 */
    EXPORT_WALLET_DETAILS_LIST_ERROR("101102", "zsmall.payExtend.exportWalletDetailsListError"),
    /** 查询充值交易回执单发生未知错误 */
    QUERY_PAYMENT_RECEIPT_DEPOSIT_ERROR("101103", "zsmall.payExtend.queryPaymentReceiptDepositError"),
    /** 查询充值总金额发生未知错误 */
    QUERY_PAYMENT_RECEIPT_TOTAL_AMOUNT_ERROR("101104", "zsmall.payExtend.queryPaymentReceiptTotalAmountError"),
    /** 查询提现交易回执单发生未知错误 PAYMENT_RECEIPT_SOLVED
    QUERY_PAYMENT_RECEIPT_WITHDRAWAL_ERROR("101105", "zsmall.payExtend.queryPaymentReceiptWithdrawalError"),
    /** 交易记录不存在 */
    STORE_TRANSACTION_IS_EMPTY("101106", "zsmall.payExtend.storeTransactionIsEmpty"),
    /** 查询充值详情出现未知错误 */
    QUERY_RECHARGE_DETAILS_ERROR("101107", "zsmall.payExtend.queryRechargeDetailsError"),
    /** 导出充值交易回执单发生未知错误 */
    EXPORT_PAYMENT_RECEIPT_DEPOSIT_ERROR("101108", "zsmall.payExtend.exportPaymentReceiptDepositError"),

    /** 产品问题列表的查询提示异常 */
    GET_QUESTIONS_LIST_ERROR("101101", "zsmall.productQA.getQuestionsListError"),
    /** 获取问题列表异常，请稍后重试 */
    GET_QUESTIONS_DETAILS_ERROR("101102", "zsmall.productQA.getQuestionsDetailsError"),
    /** 回答问题异常，请稍后重试 */
    REPLY_QUESTIONS_ERROR("101103", "zsmall.productQA.replyQuestionsError"),
    /** 添加问题异常，请稍后重试 */
    CREATE_QUESTIONS_ERROR("101104", "zsmall.productQA.createQuestionsError"),
    /** 请填写提问信息文本 */
    TEXT_QUESTIONS_ERROR("101105", "zsmall.productQA.textQuestionsError"),
    /** 只有提问者可以继续提问 */
    ADDITIONAL_QUESTIONS_ERROR("101106", "zsmall.productQA.additionalQuestionsError"),
    /** 该问题已不存在 */
    NOT_EXIST_QUESTIONS_ERROR("101107", "zsmall.productQA.notExistQuestionsError"),
    /** 编辑回复信息异常，请稍后重试 */
    EDIT_ANSWERS_ERROR("101108", "zsmall.productQA.editAnswersError"),
    /** 答案不存在或已被关闭 */
    NOT_EXIST_ANSWERS_ERROR("101109", "zsmall.productQA.notExistAnswersError"),
    /** 删除回复信息异常，请稍后重试 */
    DELETE_ANSWERS_ERROR("101110", "zsmall.productQA.deleteAnswersError"),
    /** 删除问题异常，请稍后重试 */
    DELETE_QUESTIONS_ERROR("101111", "zsmall.productQA.deleteQuestionsError"),
    /** 删除失败，对象不是提问信息 */
    NOT_QUESTIONS_ERROR("101112", "zsmall.productQA.notQuestionsError"),
    /** 因产品或规格不存在，获取问题列表失败 */
    QUERY_PRODUCT_NOT_FOUND("101113", "zsmall.productQA.queryProductNotFound"),
    /** 新增追加提问信息发生未知错误 */
    CONTINUE_QUESTIONS_ERROR("101114", "zsmall.productQA.continueQuestionsError"),
    /** 请等待商家答复当前问题后再继续下一个问题 */
    CONTINUE_QUESTIONS_AGAIN_ERROR("101115", "zsmall.productQA.continueQuestionsAgainError"),
    /** 请填写回复信息文本 */
    TEXT_ANSWERS_ERROR("101116", "zsmall.productQA.textAnswersError"),
    /** 获取编辑日志异常，请稍后重试 */
    GET_LOG_EDIT_REPLY_ERROR("101117", "zsmall.productQA.getLogEditReplyError"),
    /** 当前回复没有被编辑过 */
    REPLY_NOT_EDIT_ERROR("101118", "zsmall.productQA.replyNotEditError"),
    /** 商品skuCode不能为空 */
    PRODUCT_SKU_CODE_ISNULL_ERROR("101119", "zsmall.productQA.productSkuCodeIsnullError"),
    /** 报告问题异常，请稍后重试 */
    REPORT_QUESTION_ERROR("101120", "zsmall.productQA.reportQuestionError"),
    /** 该问题已不存在 */
    CLOSED_QUESTIONS_ERROR("101121", "zsmall.productQA.closedQuestionsError"),
    /** 回复不存在 */
    NOT_EXIST_ANSWERS_2_ERROR("101122", "zsmall.productQA.notExistAnswers2Error"),
    /** 追问不存在 */
    NOT_EXIST_CONTINUE_QUESTIONS_ERROR("101123", "zsmall.productQA.notExistContinueQuestionsError"),

    /** 创建文章或编辑时提示异常 */
    CREATE_BLOG_ARTICLE_ERROR("101201", "zsmall.blog.createBlogArticleError"),
    /** 获取博客文章列表出错 */
    QUERY_BLOG_ARTICLE_PAGE_ERROR("101201", "zsmall.blog.queryBlogArticlePageError"),
    /** 获取博客文章详情出错 */
    QUERY_BLOG_ARTICLE_DETAIL_ERROR("101202", "zsmall.blog.queryBlogArticleDetailError"),
    /** 文章不存在或被删除 */
    BLOG_ARTICLE_NOT_EXIST("101203", "zsmall.blog.blogArticleNotExist"),
    /** 更新文章时提示异常 */
    UPDATE_BLOG_ARTICLE_ERROR("101204", "zsmall.blog.updateBlogArticleError"),
    /** 删除文章时提示异常 */
    DELETE_BLOG_ARTICLE_ERROR("101205", "zsmall.blog.deleteBlogArticleError"),
    /** 获取博客文章类型出错 */
    QUERY_BLOG_TYPE_ERROR("101206", "zsmall.blog.queryBlogTypeError"),
    /** 博客分类代码重复 */
    DUPLICATION_BLOG_CATEGORY_CODE("101207", "zsmall.blog.category.code.duplication"),
    /** 博客分类多语种名称必须是JSON格式 */
    BLOG_CATEGORY_OTHER_NAME_NOT_JSON("101208", "zsmall.blog.category.multilingual.notjson"),

    /** 运输服务提供商查询列表信息提示异常 */
    GET_SHIPPING_SERVICE_ERROR("101301", "zsmall.logisticsTemplate.getShippingServiceError"),
    /** 在添加运输商信息时提示异常 */
    ADD_SHIPPING_SERVICE_ERROR("101302", "zsmall.logisticsTemplate.addShippingServiceError"),
    /** 运输商名称不能为空 */
    SHIPPING_NAME_CAN_NOT_NULL("101303", "zsmall.logisticsTemplate.shippingNameCanNotNull"),
    /** 物流商名称不能重复 */
    SHIPPING_NAME_CAN_NOT_REPEAT("101304", "zsmall.logisticsTemplate.shippingNameCanNotRepeat"),
    /** 添加新物流模板出错,请稍后再试或联系客户支持 */
    ADD_LOGISTICS_TEMPLATE_ERROR("101305", "zsmall.logisticsTemplate.addLogisticsTemplateError"),
    /** 获取费率信息出错,请稍后再试或联系客户支持 */
    GET_RATE_TYPE_ERROR("101306", "zsmall.logisticsTemplate.getRateTypeError"),
    /** 获取省/市信息出错,请稍后再试或联系客户支持 */
    GET_COUNTRY_ERROR("101307", "zsmall.logisticsTemplate.getCountryError"),
    /** 获取重量信息出错 */
    GET_WEIGHT_UNIT_ERROR("101308", "zsmall.logisticsTemplate.getWeightUnitError"),
    /** 获取仓库信息发生未知错误 */
    GET_WAREHOUSE_ERROR("101309", "zsmall.logisticsTemplate.getWarehouseError"),
    /** 查询运费模板出错 */
    GET_LOGISTICS_TEMPLATE_LIST_ERROR("101310", "zsmall.logisticsTemplate.getLogisticsTemplateListError"),
    /** 删除运费模板出错 */
    DELETE_LOGISTICS_TEMPLATE_ERROR("101311", "zsmall.logisticsTemplate.deleteLogisticsTemplateError"),
    /** 运费模板不存在 */
    LOGISTICS_TEMPLATE_NOT_EXIST_ERROR("101312", "zsmall.logisticsTemplate.logisticsTemplateNotExistError"),
    /** 更新物流模板信息发生未知错误 */
    LOGISTICS_TEMPLATE_UPDATE_ERROR("101313", "zsmall.logisticsTemplate.logisticsTemplateUpdateError"),
    /** 只能修改属于自己的物流模板 */
    LOGISTICS_TEMPLATE_BELONG_ERROR("101314", "zsmall.logisticsTemplate.logisticsTemplateBelongError"),
    /** 更新服务商名称信息发生未知错误 */
    UPDATE_LOGISTICS_TEMPLATE_SERVICE_ALIAS_ERROR("101315", "zsmall.logisticsTemplate.updateLogisticsTemplateServiceAliasError"),
    /** 更新物流模板目的地信息发生未知错误 */
    UPDATE_LOGISTICS_TEMPLATE_SHIP_TO_ERROR("101316", "zsmall.logisticsTemplate.updateLogisticsTemplateShipToError"),
    /** 物流模板名称必须唯一 */
    LOGISTICS_TEMPLATE_NAME_DUPLICATE_ERROR("101317", "zsmall.logisticsTemplate.logisticsTemplateNameDuplicateError"),
    /** 删除物流模板费用详情发生未知错误 */
    DEL_LOGISTICS_TEMPLATE_ITEM_ERROR("101318", "zsmall.logisticsTemplate.delLogisticsTemplateItemError"),
    /** 获取物流模板详情发生未知错误 */
    GET_LOGISTICS_TEMPLATE_DETAIL_ERROR("101319", "zsmall.logisticsTemplate.getLogisticsTemplateDetailError"),
    /** 邮编没有匹配城市 */
    ZIP_CODE_DOES_NOT_MATCH_CITY("101320", "zsmall.logisticsTemplate.zipCodeDoesNotMatchCity"),
    /** 运费计算发生未知错误 */
    LOGISTICS_FEE_ERROR("101321", "zsmall.logisticsTemplate.logisticsFeeError"),
    /** 目的地城市不支持配送 */
    TARGET_CITY_NOT_SUPPORT_ERROR("101322", "zsmall.logisticsTemplate.targetCityNotSupportError"),
    /** 重量单位不支持 */
    WEIGHT_UNIT_ERROR("101323", "zsmall.logisticsTemplate.weightUnitError"),
    /** 所选仓库不存在 */
    WAREHOUSE_NOT_EXIST_ERROR("101324", "zsmall.logisticsTemplate.warehouseNotExistError"),
    /** 至少保留一条费率规则 */
    LOGISTICS_TEMPLATE_RATE_RULE_LESS_THAN_ONE_ERROR("101325", "zsmall.logisticsTemplate.logisticsTemplateRateRuleLessThanOneError"),
    /** 请选择目的地 */
    LOGISTICS_TEMPLATE_SHIP_TO_NOT_INPUT_ERROR("101326", "zsmall.logisticsTemplate.logisticsTemplateShipToNotInputError"),
    /** 物流时间必须大于等于一天 */
    LOGISTICS_TIME_INPUT_ERROR("101327", "zsmall.logisticsTemplate.logisticsTimeInputError"),
    /** 最大物流时间必须大于等于最小时间 */
    LOGISTICS_TIME_INPUT_2_ERROR("101328", "zsmall.logisticsTemplate.logisticsTimeInput2Error"),
    /** 数量和金额不能是负数 */
    LOGISTICS_NUM_OR_FEE_INPUT_ERROR("101329", "zsmall.logisticsTemplate.logisticsNumOrFeeInputError"),
    /** 物流模板存在关联商品，无法删除 */
    LOGISTICS_TEMPLATE_ASSOCIATED_PRODUCT("101330", "zsmall.logisticsTemplate.logisticsTemplateAssociatedProduct"),

    /** 调用谷歌地图距离矩阵接口超时异常 */
    CALL_GOOGLE_MAP_API_TIME_OUT_ERROR("101401", "zsmall.marketplace.callGoogleMapApiTimeOutError"),
    /** 调用谷歌地图距离矩阵接口失败 */
    CALL_GOOGLE_MAP_API_FAIL_ERROR("101402", "zsmall.marketplace.callGoogleMapApiFailError"),
    /** 接口不支持查询距离信息 */
    CALL_GOOGLE_MAP_API_NOT_SUPPORT_ERROR("101403", "zsmall.marketplace.callGoogleMapApiNotSupportError"),
    /** 调用谷歌地图距离矩阵接口出现未知错误 */
    CALL_GOOGLE_MAP_API_UNKNOWN_ERROR("101404", "zsmall.marketplace.callGoogleMapApiUnknownError"),
    /** 调用谷歌地图经纬度接口失败 */
    CALL_GOOGLE_MAP_GEOCODING_API_FAIL_ERROR("101405", "zsmall.marketplace.callGoogleMapGeocodingApiFailError"),
    /** 调用谷歌地图距离矩阵接口超时异常 */
    CALL_GOOGLE_MAP_GEOCODING_API_TIME_OUT_ERROR("101406", "zsmall.marketplace.callGoogleMapGeocodingApiTimeOutError"),
    /** 获取Marketplace商品详情（针对Shopify铺货）时发生未知错误 */
    GET_MARKETPLACE_PRODUCT_DETAIL_FOR_DETAIL_ERROR("101407", "zsmall.marketplace.getMarketplaceProductDetailForDetailError"),
    /** 查询购物车数量异常 */
    GET_SHOPPING_CART_NUM_ERROR("110007", "zsmall.shoppingCart.getShoppingCartNumError"),
    /** 查询购物车信息异常 */
    GET_SHOPPING_CART_INFO_ERROR("110008", "zsmall.shoppingCart.getShoppingCartInfoError"),
    /** 删除购物车商品sku信息 */
    DELETE_SHOPPING_CART_INFO_ERROR("110009", "zsmall.shoppingCart.deleteShoppingCartInfoError"),
    /** 改变购物车中商品sku数量 */
    CHANGE_SHOPPING_CART_NUM_ERROR("110010", "zsmall.shoppingCart.changeShoppingCartNumError"),
    /** 购物车商品sku信息不存在 */
    SHOPPING_CART_INFO_NOT_EXIST("110011", "zsmall.shoppingCart.shoppingCartInfoNotExist"),
    /** 购物车商品数量不能小于1 */
    SHOPPING_CART_PRODUCT_NUM_AT_LEAST_ONE("110012", "zsmall.shoppingCart.shoppingCartProductNumAtLeastOne"),
    /** 商品sku添加到购物车异常 */
    ADD_TO_SHOPPING_CART_ERROR("110013", "zsmall.shoppingCart.addToShoppingCartError"),
    /** 添加有库存时通知记录异常 */
    ADD_STOCK_NOTIFY_ERROR("110014", "zsmall.shoppingCart.addStockNotifyError"),
    /** 提交锁货信息异常 */
    SUBMIT_STOCK_LOCK_INFO_ERROR("110015", "zsmall.shoppingCart.submitStockLockInfoError"),
    /** 翻页查询marketplace分类商品列表异常 */
    GET_CATEGORY_PRODUCT_PAGE_ERROR("110016", "zsmall.shoppingCart.getCategoryProductPageError"),
    /** 获取支付方式异常 */
    GET_STORE_PAYMENT_METHOD_LIST_ERROR("110017", "zsmall.shoppingCart.getStorePaymentMethodListError"),
    /** marketplace下订单异常 */
    MARKETPLACE_PLACE_ORDER_ERROR("110018", "zsmall.shoppingCart.marketplacePlaceOrderError"),
    /** 商品已下架 */
    PRODUCT_OFF_SHELF("110019", "zsmall.shoppingCart.productOffShelf"),
    /** 商品sku库存不足 */
    PRODUCT_SKU_STOCK_SHORTAGE("110020", "zsmall.shoppingCart.productSkuStockShortage"),
    /** 下载商品资料包时出现未知错误 */
    DOWNLOAD_PRODUCT_ZIP_ERROR("110021", "zsmall.marketplace.downloadProductZipError"),

    /** 物流＆退货政策信息不存在 */
    SHIPPING_RETURNS_NOT_EXIST("101500", "zsmall.shippingReturns.shippingReturnsNotExist"),
    /** 新增物流&退货政策模板信息发生未知错误 */
    SHIPPING_RETURNS_CREATE_ERROR("101501", "zsmall.shippingReturns.shippingReturnsCreateError"),
    /** 更新物流&退货政策模板信息发生未知错误 */
    SHIPPING_RETURNS_UPDATE_ERROR("101502", "zsmall.shippingReturns.shippingReturnsUpdateError"),
    /** 删除物流&退货政策模板信息发生未知错误 */
    SHIPPING_RETURNS_DELETE_ERROR("101503", "zsmall.shippingReturns.shippingReturnsDeleteError"),
    /** 物流&退货政策模板不属于当前用户 */
    SHIPPING_RETURNS_BELONG_ERROR("101504", "zsmall.shippingReturns.shippingReturnsBelongError"),
    /** 查询物流&退货政策模板信息异常 */
    SHIPPING_RETURNS_QUERY_ERROR("101505", "zsmall.shippingReturns.shippingReturnsQueryError"),
    /** 查询物流&退货政策模板详情信息异常 */
    SHIPPING_RETURNS_QUERY_DETAIL_ERROR("101506", "zsmall.shippingReturns.shippingReturnsQueryDetailError"),
    /** 查询物流&退货政策列表时发生未知错误 */
    QUERY_SHIPPING_RETURNS_LIST_ERROR("101521", "zsmall.shippingReturns.queryShippingReturnsListError"),
    /** 审核物流＆退货政策时发生未知错误 */
    REVIEW_SHIPPING_RETURNS_ERROR("101522", "zsmall.shippingReturns.reviewShippingReturnsError"),
    /** 物流＆退货政策内容不能为空 */
    SHIPPING_RETURNS_CONTENT_IS_BLANK("101523", "zsmall.shippingReturns.shippingReturnsContentIsBlank"),

    /** 查询连接渠道店铺及铺货的工作指南发生未知错误 */
    WORK_GUIDE_QUERY_ERROR("101601", "zsmall.workGuidelines.workGuideQueryError"),

    /** 查询下载记录时出现未知错误 */
    QUERY_DOWNLOAD_RECORD_ERROR("120001", "zsmall.downloadCenter.queryDownloadRecordError"),
    /** 删除下载记录时出现未知错误 */
    DELETE_DOWNLOAD_RECORD_ERROR("120002", "zsmall.downloadCenter.deleteDownloadRecordError"),
    /** 存在生成中的导出记录，请等待完成后再试 */
    DOWNLOAD_RECORD_GENERATING("120003", "zsmall.downloadCenter.downloadRecordGenerating"),

    /** 配置类型不存在 */
    HOME_PROPERTIES_TYPE_NOT_EXIST_ERROR("130001", "zsmall.configuration.homePropertiesTypeNotExistError"),
    /** 新增首页配置时异常 */
    HOME_PROPERTIES_ADD_ERROR("130002", "zsmall.configuration.homePropertiesAddError"),
    /** 更新首页配置时异常 */
    HOME_PROPERTIES_UPDATE_ERROR("130003", "zsmall.configuration.homePropertiesUpdateError"),
    /** Existing配置已经达到最大，不可再新增配置 */
    HOME_PROPERTIES_CHANNEL_EXISTING_LENGTH_OUT_ERROR("130004", "zsmall.configuration.homePropertiesChannelExistingLengthOutError"),
    /** Coming soon配置已经达到最大，不可再新增配置 */
    HOME_PROPERTIES_CHANNEL_COMING_SOON_LENGTH_OUT_ERROR("130005", "zsmall.configuration.homePropertiesChannelComingSoonLengthOutError"),
    /** 删除首页配置时异常 */
    HOME_PROPERTIES_DELETE_ERROR("130007", "zsmall.configuration.homePropertiesDeleteError"),
    /** 此页面不支持配置 */
    HOME_PROPERTIES_DEST_TYPE_NOT_SUPPORT_ERROR("130008", "zsmall.configuration.homePropertiesDestTypeNotSupportError"),
    /** 进入首页时发生未知错误 */
    HOME_PROPERTIES_MARKETPLACE_GET_ERROR("130009", "zsmall.configuration.homePropertiesMarketplaceGetError"),
    /** 未能正确读取到首页信息 */
    HOME_PROPERTIES_MARKETPLACE_GET_TYPE_NO_EXIST_ERROR("130010", "zsmall.configuration.homePropertiesMarketplaceGetTypeNoExistError"),
    /** 查询首页配置时发生异常 */
    HOME_PROPERTIES_GET_ERROR("130011", "zsmall.configuration.homePropertiesGetError"),
    /** 保存配置时异常 */
    HOME_PROPERTIES_SAVE_ERROR("130012", "zsmall.configuration.homePropertiesSaveError"),
    /** 商品查询时异常，请稍后重试 */
    HOME_SEARCH_PRODUCTS_ERROR("130013", "zsmall.configuration.homeSearchProductsError"),
    /** Banner右侧广告配置必须是3个 */
    HOME_PROPERTIES_CN_BANNER_RIGHT_AD_ERROR("130014", "zsmall.configuration.homePropertiesCnBannerRightAdError"),
    /** 新增活动配置发生未知异常，请稍后重试 */
    ACTIVITY_PROPERTIES_CREATE_ERROR("130015", "zsmall.configuration.activityPropertiesCreateError"),
    /** 活动名称不能重复，请重新输入 */
    ACTIVITY_NAME_CANNOT_REPEAT("130016", "zsmall.configuration.activityNameCannotRepeat"),
    /** 活动不存在 */
    ACTIVITY_PROPERTIES_NOT_EXISTS("130017", "zsmall.configuration.activityPropertiesNotExists"),
    /** 查询活动配置详情发生未知异常，请稍后重试 */
    ACTIVITY_PROPERTIES_DETAIL_ERROR("130018", "zsmall.configuration.activityPropertiesDetailError"),
    /** 查询活动配置列表发生未知异常，请稍后重试 */
    ACTIVITY_PROPERTIES_LIST_ERROR("130019", "zsmall.configuration.activityPropertiesListError"),
    /** 删除活动配置列表发生未知异常，请稍后重试 */
    ACTIVITY_PROPERTIES_DELETE_ERROR("130020", "zsmall.configuration.activityPropertiesDeleteError"),
    /** 活动配置已经被使用请先解除绑定 */
    ACTIVITY_PROPERTIES_NOT_DELETE_USED_ERROR("130021", "zsmall.configuration.activityPropertiesNotDeleteUsedError"),
    /** 获取首页业务配置时发生错误 */
    HOME_BUSINESS_PARAMETER_ERROR("130022", "zsmall.configuration.homeBusinessParameterError"),


    /** 新增标签时异常 */
    LABEL_ADD_ERROR("140001", "zsmall.label.labelAddError"),
    /** 更新标签时异常 */
    LABEL_UPDATE_ERROR("140002", "zsmall.label.labelUpdateError"),
    /** 删除标签时异常 */
    LABEL_DELETE_ERROR("140003", "zsmall.label.labelDeleteError"),
    /** 查询标签时异常 */
    LABEL_QUERY_ERROR("140004", "zsmall.label.labelQueryError"),
    /** 商品绑定标签时异常 */
    LABEL_BINDING_ERROR("140005", "zsmall.label.labelBindingError"),
    /** 标签不存在 */
    LABEL_NOT_EXIST_ERROR("140006", "zsmall.label.labelNotExistError"),
    /** 标签已存在 */
    LABEL_EXIST_ERROR("140007", "zsmall.label.labelExistError"),
    /** 商品已经绑定过标签 */
    LABEL_PRODUCT_EXIST_ERROR("140008", "zsmall.label.labelProductExistError"),
    /** 商品只能绑定一个标签 */
    LABEL_PRODUCT_ONE_TO_ONE_ERROR("140009", "zsmall.label.labelProductOneToOneError"),
    /** 商品解绑标签时异常 */
    LABEL_UNBIND_ERROR("140010", "zsmall.label.labelUnbindError"),

    /** 分页查询商品管控列表异常 */
    QUERY_PRODUCT_CHANNEL_CONTROL_PAGE_ERROR("150001", "zsmall.channelControl.queryProductChannelControlPageError"),
    /** 设置商品管控异常 */
    SET_PRODUCT_CHANNEL_CONTROL_ERROR("150002", "zsmall.channelControl.setProductChannelControlError"),
    /** 成员ID超过上限(最多: {max}) */
    MEMBER_ID_EXCEED_LIMIT("150003", "zsmall.channelControl.memberIdExceedLimit"),
    /** 切换商品渠道管控类型时发生未知错误 */
    SWITCH_PRODUCT_CHANNEL_CONTROL_TYPE_ERROR("150004", "zsmall.channelControl.switchProductChannelControlTypeError"),

    /** 查询汇入记录时发生未知错误 */
    RECHARGE_QUERY_ERROR("160001", "zsmall.walletManagement.rechargeQueryError"),
    /** 查询交易记录时发生未知错误 */
    TRANSACTIONS_QUERY_ERROR("160002", "zsmall.walletManagement.transactionsQueryError"),
    /** 解冻/冻结钱包发生未知错误 */
    TO_FREEZE_OR_THAW_STORE_WALLET_ERROR("160002", "zsmall.walletManagement.toFreezeOrThawStoreWalletError"),
    /** 时间格式错误 */
    EDIT_EXPIRE_DATE_ERROR("250001", "zsmall.walletManagement.editExpireDateError"),
    /** 编辑银行卡信息时出现异常 */
    EDIT_CARD_INFORMATION_ERROR("250002", "zsmall.walletManagement.editCardInformationError"),

    /** 重新创建WMS系统销售单时发生未知错误 */
    RECREATE_WMS_ORDER_ERROR("170001", "zsmall.orderExtend.recreateWmsOrderError"),
    /** 订单不存在或WMS销售单未出现创建异常 */
    ORDER_NOT_EXIST_OR_WMS_NOT_ERROR("170002", "zsmall.orderExtend.orderNotExistOrWmsNotError"),

    /** Payoneer授权操作异常 */
    PAYONEER_AUTHORIZATION_ERROR("180001", "zsmall.payoneer.payoneerAuthorizationError"),
    /** Payoneer授权操作已失效 */
    PAYONEER_AUTHORIZATION_EXPIRED("180002", "zsmall.payoneer.payoneerAuthorizationExpired"),
    /** 获取应用注册连接异常 */
    PAYONEER_REGISTRATION_CONNECTION_ERROR("180003", "zsmall.payoneer.payoneerRegistrationConnectionError"),
    /** 当前Payoneer账户已被绑定 */
    PAYONEER_ACCOUNT_BEEN_BOUND_ERROR("180004", "zsmall.payoneer.payoneerAccountBeenBoundError"),
    /** Payoneer账户绑定失败 */
    PAYONEER_BINDING_FAILED("180005", "zsmall.payoneer.payoneerBindingFailed"),
    /** Payoneer账户名修改失败 */
    PAYONEER_ACCOUNT_NAME_UPDATE_FAILED("180006", "zsmall.payoneer.payoneerAccountNameUpdateFailed"),
    /** Payoneer账户删除失败 */
    PAYONEER_DELETE_FAILED("180007", "zsmall.payoneer.payoneerDeleteFailed"),
    /** 查询Payoneer账户列表异常 */
    PAYONEER_QUERY_ERROR("180008", "zsmall.payoneer.payoneerQueryError"),
    /** 查询Payoneer余额列表异常 */
    PAYONEER_QUERY_BALANCE_ERROR("180009", "zsmall.payoneer.payoneerQueryBalanceError"),
    /** Payoneer支付异常 */
    PAYONEER_PAYMENT_COMMIT_ERROR("1800010", "zsmall.payoneer.payoneerPaymentCommitError"),
    /** Payoneer不存在 */
    PAYONEER_EMPTY_ERROR("1800011", "zsmall.payoneer.payoneerEmptyError"),
    /** Payoneer余额列表中不存在当前币种账户 */
    PAYONEER_BALANCE_ERROR("1800012", "zsmall.payoneer.payoneerBalanceError"),
    /** 余额不足 */
    PAYONEER_INSUFFICIENT_BALANCE_ERROR("1800013", "zsmall.payoneer.payoneerInsufficientBalanceError"),
    /** Payoneer账户名称长度不能超过20 */
    PAYONEER_ACCOUNT_LENGTH_ERROR("1800014", "zsmall.payoneer.payoneerAccountLengthError"),
    /** Payoneer应用程序未注册或处于活动状态 */
    PAYONEER_APPLICATION_NOT_REGISTERED_ERROR("1800015", "zsmall.payoneer.payoneerApplicationNotRegisteredError"),
    /** Payoneer支付失败，请求已过期 */
    PAYONEER_PAYMENT_TIMEOUT_ERROR("1800016", "zsmall.payoneer.payoneerPaymentTimeoutError"),
    /** Payoneer支付失败 */
    PAYONEER_PAYMENT_FAIL("1800017", "zsmall.payoneer.payoneerPaymentFail"),

    /** 指定活动（{}）不存在或已结束 */
    PRODUCT_ACTIVITY_NOT_EXIST("190000", "zsmall.productActivity.productActivityNotExist"),
    /** 查询活动记录发生未知错误 */
    PRODUCT_ACTIVITY_QUERY_ERROR("190001", "zsmall.productActivity.productActivityQueryError"),
    /** 系统识别到了一个未知的活动状态 */
    ACTIVITY_STATUS_ERROR("190002", "zsmall.productActivity.activityStatusError"),
    /** 活动状态必须是草稿或者审核中 */
    ACTIVITY_STATUS_NOT_DRAFT_UNDERREVIEW_ERROR("190003", "zsmall.productActivity.activityStatusNotDraftUnderreviewError"),
    /** 活动开始时间至少从今天开始 */
    ACTIVITY_START_TIME_ERROR("190004", "zsmall.productActivity.activityStartTimeError"),
    /** 活动期限低于30天 */
    ACTIVITY_TIME_LESS_THEN_30_ERROR("190005", "zsmall.productActivity.activityTimeLessThen30Error"),
    /** 活动类型不匹配 */
    ACTIVITY_TYPE_NOT_MATCH("190006", "zsmall.productActivity.activityTypeNotMatch"),
    /** 活动不存在 */
    ACTIVITY_NOT_EXIST("190007", "zsmall.productActivity.activityNotExist"),
    /** 锁货发生未知错误 */
    DISTRIBUTORS_STOCK_LOCK_ERROR("190008", "zsmall.productActivity.distributorsStockLockError"),
    /** 保存锁货活动发生未知错误 */
    STOCK_LOCK_SAVE_ERROR("190009", "zsmall.productActivity.stockLockSaveError"),
    /** 活动商品总数量小于最小起订量 */
    ACTIVITY_QUANTITY_LESS_THEN_MINIMUM("190010", "zsmall.productActivity.activityQuantityLessThenMinimum"),
    /** 库存不足：（{}） */
    STOCK_LOCK_QUANTITY_NOT_ENOUGH("190011", "zsmall.productActivity.stockLockQuantityNotEnough"),
    /** 锁货活动保存失败，未查询到对应的活动 */
    STOCK_LOCK_NOT_EXIST_ERROR("190012", "zsmall.productActivity.stockLockNotExistError"),
    /** 查询锁货活动发生未知错误 */
    STOCK_LOCK_QUERY_DETAIL_ERROR("190013", "zsmall.productActivity.stockLockQueryDetailError"),
    /** 更新锁货活动发生未知错误 */
    STOCK_LOCK_UPDATE_ERROR("190014", "zsmall.productActivity.stockLockUpdateError"),
    /** 活动状态不存在 */
    ACTIVITY_STATUS_NOT_EXIST("190015", "zsmall.productActivity.activityStatusNotExist"),
    /** 权限不足，无法更新到目标状态 */
    ACTIVITY_CANNOT_UPDATE_STATUS("190016", "zsmall.productActivity.activityCannotUpdateStatus"),
    /** 活动已经关闭 */
    STOCK_LOCK_CLOSED("190017", "zsmall.productActivity.stockLockClosed"),
    /** 所选仓库不存在[{}] */
    ACTIVITY_WAREHOUSE_NOT_EXIST_ERROR("190018", "zsmall.productActivity.activityWarehouseNotExistError"),
    /** 库存不足 */
    ACTIVITY_ALL_QUANTITY_NOT_ENOUGH("190019", "zsmall.productActivity.activityAllQuantityNotEnough"),
    /** 商品下架失败，请先结束活动：（{}） */
    PRODUCT_OFF_SHELF_ERROR_ACTIVITY_NOT_END("190020", "zsmall.productActivity.productOffShelfErrorActivityNotEnd"),
    /** 分销商申请异常 */
    DISTRIBUTOR_APPLY_ERROR("190021", "zsmall.productActivity.distributorApplyError"),
    /** 活动库存不足 */
    ACTIVITY_QUANTITY_NOT_ENOUGH("190022", "zsmall.productActivity.activityQuantityNotEnough"),
    /** 活动仓库不存在 */
    ACTIVITY_WAREHOUSE_NOT_EXIST("190023", "zsmall.productActivity.activityWarehouseNotExist"),
    /** 归还库存时未指定仓库 */
    BACK_INVENTORY_NO_SPECIAL_WAREHOUSE("190024", "zsmall.productActivity.backInventoryNoSpecialWarehouse"),
    /** 商品删除失败，请先结束活动：（{}） */
    PRODUCT_DELETE_ERROR_ACTIVITY_NOT_END("190025", "zsmall.productActivity.productDeleteErrorActivityNotEnd"),
    /** 商品删除失败，活动仍有分销商参与：（{}） */
    PRODUCT_DELETE_ERROR_BULK_ACTIVITY_NOT_END("190026", "zsmall.productActivity.productDeleteErrorBulkActivityNotEnd"),
    /** 当前需求数量小于活动要求的最低数量 */
    QUANTITY_LESS_MINIMUM_QUANTITY("190027", "zsmall.productActivity.quantityLessMinimumQuantity"),
    /** 保存活动时发生未知错误 */
    ACTIVITY_CREATE_ERROR("190028", "zsmall.productActivity.activitySaveError"),
    /** 更新活动状态时发生未知错误 */
    ACTIVITY_STATUS_UPDATE_ERROR("190029", "zsmall.productActivity.activityStatusUpdateError"),
    /** 商品下架失败，活动仍有分销商参与：（{}） */
    PRODUCT_OFF_SHELF_ERROR_ACTIVITY_ITEM_NOT_END("190031", "zsmall.productActivity.productOffShelfErrorActivityItemNotEnd"),
    /** 取消分销商的活动时发生未知错误 */
    CANCEL_BULK_ACTIVITY_ERROR("190032", "zsmall.productActivity.cancelBulkActivityError"),
    /** 活动不存在或已被取消 */
    ACTIVITY_NOT_EXIST_OR_CANCELLED("190033", "zsmall.productActivity.activityNotExistOrCancelled"),
    /** 退还金额不能大于总金额${} */
    REFUND_AMOUNT_CANNOT_BE_GREATER_THAN("190034", "zsmall.productActivity.refundAmountCannotBeGreaterThan"),
    /** 扣除金额不能大于供应商总金额${} */
    DEDUCTION_AMOUNT_CANNOT_BE_GREATER_THAN("190034", "zsmall.productActivity.deductionAmountCannotBeGreaterThan"),
    /** 调整分销商活动时发生未知错误 - 190035 **/
    ADJUST_BULK_ACTIVITY_ERROR("190035", "zsmall.productActivity.adjustBulkActivityError"),
    /** 仓库调整后的库存不能小于已售出的库存 - 190036 **/
    ADJUST_INVENTORY_CANNOT_BE_LESS_THAN("190036", "zsmall.productActivity.adjustInventoryCannotBeLessThan"),
    /** 仓库调整后的剩余库存不能小于0 - 190037 **/
    ADJUST_INVENTORY_CANNOT_BE_LESS_THAN_ZERO("190037", "zsmall.productActivity.adjustInventoryCannotBeLessThanZero"),
    /** 库存未改变 - 190038 **/
    INVENTORY_UNCHANGED("190038", "zsmall.productActivity.inventoryUnchanged"),
    /** 不支持调整此活动的库存 - 190039 **/
    ADJUSTING_INVENTORY_NOT_SUPPORTED("190039", "zsmall.productActivity.adjustingInventoryNotSupported"),
    /** 查询分销商活动库存信息异常 - 190040 **/
    QUERY_BULK_ACTIVITY_INVENTORY_ERROR("190040", "zsmall.productActivity.queryBulkActivityInventoryError"),
    /** 活动不可用 */
    ACTIVITY_NOT_AVAILABLE("190041", "zsmall.productActivity.activityNotAvailable"),
    /** 活动取消成功 */
    ACTIVITY_CANCEL_SUCCESS("0", "zsmall.productActivity.activityCancelSuccess"),
    /** 活动取消申请已提交，请等待审核 */
    ACTIVITY_CANCEL_REQUEST_SUBMITTED("0", "zsmall.productActivity.activityCancelRequestSubmitted"),


    /** 创建圈货活动时发生未知错误 */
    BUYOUT_CREATE_ERROR("190101", "zsmall.buyout.buyoutCreateError"),
    /** 查询圈货活动详情时发生未知错误 */
    QUERY_BUYOUT_DETAIL_ERROR("190102", "zsmall.buyout.queryBuyoutDetailError"),
    /** 分销商参与圈货时发生未知错误 */
    DISTRIBUTORS_BUYOUT_ERROR("190102", "zsmall.buyout.distributorsBuyoutError"),
    /** 查询圈货活动草稿时发生未知错误 */
    QUERY_BUYOUT_DRAFT_ERROR("190103", "zsmall.buyout.queryBuyoutDraftError"),

     /** 查询商品sku统计数据详情异常 */
    STATISTIC_SKU_INFO_ERROR("200001", "zsmall.statistics.statisticSkuInfoError"),
    /** 查询商品sku统计分销商数据详情异常 */
    STATISTIC_SKU_BULK_INFO_ERROR("200002", "zsmall.statistics.statisticSkuBulkInfoError"),
    /** 查询商品sku统计供应商数据详情异常 */
    STATISTIC_SKU_SUP_INFO_ERROR("200003", "zsmall.statistics.statisticSkuSupInfoError"),
    /** 查询商品Sku统计数据列表异常 */
    STATISTIC_SKU_QUERY_ERROR("200004", "zsmall.statistics.statisticSkuQueryError"),
    /** 查询商品Sku统计分销商数据列表异常 */
    STATISTIC_SKU_BULK_QUERY_ERROR("200005", "zsmall.statistics.statisticSkuBulkQueryError"),
    /** 查询商品Sku统计供应商数据列表异常 */
    STATISTIC_SKU_SUP_QUERY_ERROR("200006", "zsmall.statistics.statisticSkuSupQueryError"),
    /**统计类型不能为空 */
    STATISTIC_SKU_SUMMARYTYPE_ERROR("200007", "zsmall.statistics.summaryTypeError"),

    /** 查询清货商品发生未知错误 */
    LIQUIDATION_PRODUCT_QUERY_ERROR("210001", "zsmall.liquidation.liquidationProductQueryError"),
    /** 查询清货活动发生未知错误 */
    LIQUIDATION_ACTIVITY_QUERY_ERROR("210002", "zsmall.liquidation.liquidationActivityQueryError"),
    /** 清货活动不存在 */
    LIQUIDATION_ACTIVITY_NOT_EXIST("210003", "zsmall.liquidation.liquidationActivityNotExist"),
    /** 保存清货议价单发生未知错误 */
    LIQUIDATION_ORDER_SAVE_ERROR("210004", "zsmall.liquidation.liquidationOrderSaveError"),
    /** 清货活动发货方式不存在 */
    LIQUIDATION_DELIVERY_METHOD_NOT_EXIST("210005", "zsmall.liquidation.liquidationDeliveryMethodNotExist"),
    /** 库存不足 */
    LIQUIDATION_QUANTITY_NOT_ENOUGH("210006", "zsmall.liquidation.liquidationQuantityNotEnough"),
    /** 数量与价格区间不匹配：（{}） */
    LIQUIDATION_QUANTITY_AND_PRICE_NOT_MATCH("210007", "zsmall.liquidation.liquidationQuantityAndPriceNotMatch"),
    /** 清货活动信息添加失败 */
    LIQUIDATION_ACTIVITY_SAVE_ERROR("210008", "zsmall.liquidation.liquidationActivitySaveError"),
    /** 获取清货详情发生未知错误 */
    LIQUIDATION_ACTIVITY_DETAIL_ERROR("210009", "zsmall.liquidation.liquidationActivityDetailError"),
    /** 查询清货订单发生未知错误 */
    LIQUIDATION_ORDER_QUERY_ERROR("210010", "zsmall.liquidation.liquidationOrderQueryError"),
    /** 查询清货订单尾款明细发生未知错误 */
    LIQUIDATION_ORDER_BALANCE_ERROR("210011", "zsmall.liquidation.liquidationOrderBalanceError"),
    /** 订单费用设置发生未知错误 */
    LIQUIDATION_ORDER_FEE_SETTING_ERROR("210012", "zsmall.liquidation.liquidationOrderFeeSettingError"),
    /** 活动状态更新发生未知错误 */
    LIQUIDATION_STATUS_UPDATE_ERROR("210013", "zsmall.liquidation.liquidationStatusUpdateError"),
    /** 清货订单不存在 */
    LIQUIDATION_ORDER_NOT_EXIST("210014", "zsmall.liquidation.liquidationOrderNotExist"),
    /** 清货订单状态更新发生未知错误 */
    LIQUIDATION_ORDER_STATUS_UPDATE_ERROR("210015", "zsmall.liquidation.liquidationOrderStatusUpdateError"),
    /** 清货订单扣除库存发生未知错误 */
    LIQUIDATION_ORDER_DEDUCT_INVENTORY_ERROR("210016", "zsmall.liquidation.liquidationOrderDeductInventoryError"),
    /** 清货活动商品不能为空 */
    LIQUIDATION_PRODUCT_SKU_NOT_EMPTY("210017", "zsmall.liquidation.liquidationProductSkuNotEmpty"),
    /** 清货活动商品图片不能为空 */
    LIQUIDATION_PRODUCT_IMAGE_NOT_EMPTY("210018", "zsmall.liquidation.liquidationProductImageNotEmpty"),
    /** 清货活动商品价格及数量范围不能为空 */
    LIQUIDATION_PRODUCT_ATTRIBUTE_NOT_EMPTY("210019", "zsmall.liquidation.liquidationProductAttributeNotEmpty"),
    /** 清货活动商品库存不能小于最小购买数量 */
    LIQUIDATION_PRODUCT_INVENTORY_QUANTITY_ERROR("210020", "zsmall.liquidation.liquidationProductInventoryQuantityError"),
    /** 清货活动订单参数错误 */
    LIQUIDATION_ORDER_PARAM_ERROR("210021", "zsmall.liquidation.liquidationOrderParamError"),
    /** 实际购买数量低于最小购买数量 */
    LIQUIDATION_ORDER_LESS_THEN_MINIMUM_QUANTITY("210022", "zsmall.liquidation.liquidationOrderLessThenMinimumQuantity"),
    /** 仓库编码为空 */
    LIQUIDATION_WAREHOUSE_CODE_EMPTY("210023", "zsmall.liquidation.liquidationWarehouseCodeEmpty"),
    /** 商品名称为空 */
    LIQUIDATION_PRODUCT_NAME_EMPTY("210024", "zsmall.liquidation.liquidationProductNameEmpty"),
    /** 上传文件不合法 */
    LIQUIDATION_UPLOAD_PRODUCT_SKU_FILE_ERROR("210025", "zsmall.liquidation.liquidationUploadProductSkuFileError"),
    /** 托盘尺寸数据不完整 */
    LIQUIDATION_PALLET_SIZE_INCOMPLETE("210026", "zsmall.liquidation.liquidationPalletSizeIncomplete"),
    /** 发货方式为空 */
    LIQUIDATION_DELIVERY_METHOD_EMPTY("210027", "zsmall.liquidation.liquidationDeliveryMethodEmpty"),
    /** 自提操作费为空 */
    LIQUIDATION_PICK_UP_OPERATION_FEE_EMPTY("210028", "zsmall.liquidation.liquidationPickUpOperationFeeEmpty"),
    /** 发送到仓库操作费为空 */
    LIQUIDATION_DELIVERY_TO_WAREHOUSE_OPERATION_FEE_EMPTY("210029", "zsmall.liquidation.liquidationDeliveryToWarehouseOperationFeeEmpty"),
    /** 预计托盘空间为空 */
    LIQUIDATION_PALLET_EXPECTED_SPACE_EMPTY("210030", "zsmall.liquidation.liquidationPalletExpectedSpaceEmpty"),
    /** 托盘费为空 */
    LIQUIDATION_PALLET_FEE_EMPTY("210031", "zsmall.liquidation.liquidationPalletFeeEmpty"),
    /** 商品sku属性不完整 */
    LIQUIDATION_PRODUCT_SKU_PROPERTIES_INCOMPLETE("210032", "zsmall.liquidation.liquidationProductSkuPropertiesIncomplete"),
    /** 商品sku尺寸不完整 */
    LIQUIDATION_PRODUCT_SKU_SIZE_INCOMPLETE("210033", "zsmall.liquidation.liquidationProductSkuSizeIncomplete"),
    /** 商品sku数量为空 */
    LIQUIDATION_PRODUCT_SKU_QUANTITY_EMPTY("210034", "zsmall.liquidation.liquidationProductSkuQuantityEmpty"),
    /** 上传清货活动商品必填信息（{}）不能为空 */
    LIQUIDATION_SKU_UPLOAD_PARAM_CANNOT_EMPTY("210035", "zsmall.liquidation.liquidationSkuUploadParamCannotEmpty"),
    /** 设置打托费需要填写备注 */
    LIQUIDATION_ORDER_PALLET_REMARK_EMPTY("210036", "zsmall.liquidation.liquidationOrderPalletRemarkEmpty"),
    /** 自提单不能设置运费 */
    LIQUIDATION_ORDER_SET_SHIPPING_FEE_ERROR("210037", "zsmall.liquidation.liquidationOrderSetShippingFeeError"),
    /** 上传清货活动商品数值格式错误 */
    LIQUIDATION_SKU_NUMBER_FORMAT_ERROR("210038", "zsmall.liquidation.liquidationSkuNumberFormatError"),
    /** 商品最小数量为空 */
    LIQUIDATION_PRODUCT_MINIMUM_QUANTITY_EMPTY("210039", "zsmall.liquidation.liquidationProductMinimumQuantityEmpty"),
    /** 商品价格信息不完整 */
    LIQUIDATION_PRODUCT_SKU_PRICE_INFORMATION_INCOMPLETE("210040", "zsmall.liquidation.liquidationProductSkuPriceInformationIncomplete"),
    /** 保存出售方式时出现错误 */
    LIQUIDATION_SAVE_SELLING_METHOD_ERROR("210041", "zsmall.liquidation.liquidationSaveSellingMethodError"),
    /** 获取清货商品文件时出现未知错误 */
    LIQUIDATION_PRODUCT_SKU_FILE_ERROR("210042", "zsmall.liquidation.liquidationProductSkuFileError"),
    /** 清货活动商品的数量(价格)范围不正确 */
    LIQUIDATION_PRODUCT_SKU_QUANTITY_RANGES_ERROR("210043", "zsmall.liquidation.liquidationProductSkuQuantityRangesError"),
    /** 清货活动的商品的数量不存在匹配价格 */
    LIQUIDATION_QUANTITY_NOT_MATCH_PRICE_ERROR("210044", "zsmall.liquidation.liquidationQuantityNotMatchPriceError"),

    /** 初次生成账单时发生未知错误 */
    FIRST_GENERATE_BILL_ERROR("230001", "zsmall.bill.firstGenerateBillError"),
    /** 查询账单列表时发生未知错误 */
    QUERY_BILL_PAGE_ERROR("230002", "zsmall.bill.queryBillPageError"),
    /** 查询账单详情时发生未知错误 */
    QUERY_BILL_DETAIL_ERROR("230003", "zsmall.bill.queryBillDetailError"),
    /** 未找到账单 */
    BILL_NOT_FOUND("230004", "zsmall.bill.billNotFound"),
    /** 导出账单列表时发生未知错误 */
    EXPORT_BILL_LIST_ERROR("230005", "zsmall.bill.exportBillListError"),
    /** 生成封账账单时发生未知错误 */
    GENERATE_SEAL_BILL_ERROR("230006", "zsmall.bill.generateSealBillError"),
    /** 查询账单分类列表时发生未知错误 */
    QUERY_BILL_CLASS_ERROR("230007", "zsmall.bill.queryBillClassError"),
    /** 下载账单PDF时发生未知错误 */
    DOWNLOAD_BILL_PDF_ERROR("230008", "zsmall.bill.downloadBillPdfError"),
    /** 账单PDF未生成，请确认账单是否已结算 */
    DOWNLOAD_BILL_PDF_NOT_GENERATED("230009", "zsmall.bill.downloadBillPdfNotGenerated"),
    /** 账单PDF生成中，请稍候再试 */
    DOWNLOAD_BILL_PDF_GENERATING("230010", "zsmall.bill.downloadBillPdfGenerating"),
    /** 账单PDF生成失败，请联系管理员重新生成 */
    DOWNLOAD_BILL_PDF_GENERATED_FAILED("230011", "zsmall.bill.downloadBillPdfGeneratedFailed"),
    /** 补充账单摘要时发生未知错误 */
    SUPPLEMENT_BILL_ABSTRACT_ERROR("230012", "zsmall.bill.supplementBillAbstractError"),
    /** 记账失败，未知的用户 */
    BILLING_FAILURE_UNKNOWN_USER("231001", "zsmall.bill.billingFailureUnknownUser"),
    /** 记账失败，未知摘要类型 */
    BILLING_FAILURE_UNKNOWN_ABSTRACT_TYPE("231002", "zsmall.bill.billingFailureUnknownAbstractType"),
    /** 记账失败，未知关系类型 */
    BILLING_FAILURE_UNKNOWN_RELATION_TYPE("231003", "zsmall.bill.billingFailureUnknownRelationType"),
    /** 记账失败，未找到账单 */
    BILLING_FAILURE_NO_BILL_FOUND("231004", "zsmall.bill.billingFailureNoBillFound"),
    /** 记账失败，订单暂无价格相关字段 */
    BILLING_FAILURE_ORDER_PRICE_FOUND("231004", "zsmall.bill.billingFailureOrderPriceFound"),

    /** 保存收款账户时发生未知错误 */
    RECEIPT_ACCOUNT_SAVE_ERROR("240001", "zsmall.receiptAccount.receiptAccountSaveError"),
    /** 查询收款账户时发生未知错误 */
    RECEIPT_ACCOUNT_QUERY_ERROR("240002", "zsmall.receiptAccount.receiptAccountQueryError"),
    /** 禁用收款账户时发生未知错误 */
    RECEIPT_ACCOUNT_DISABLED_ERROR("240003", "zsmall.receiptAccount.receiptAccountDisabledError"),
    /** 收款账户已经被禁用或者不存在 */
    RECEIPT_ACCOUNT_NOT_EXIST_OR_DISABLED_ERROR("240004", "zsmall.receiptAccount.receiptAccountNotExistOrDisabledError"),
    /** 最多可以添加10个收款账户 */
    RECEIPT_ACCOUNT_GREATER_THAN_10_ERROR("240005", "zsmall.receiptAccount.receiptAccountGreaterThan10Error"),
    /** 收款账户长度不足 */
    RECEIPT_ACCOUNT_LENGTH_NOT_ENOUGH_ERROR("240006", "zsmall.receiptAccount.receiptAccountLengthNotEnoughError"),
    /** 邮箱格式错误 */
    RECEIPT_EMAIL_FORMAT_ERROR("240007", "zsmall.receiptAccount.receiptEmailFormatError"),
    /** 同一个国家不能添加相同的银行卡号 */
    RECEIPT_ACCOUNT_REPEAT_ERROR("240008", "zsmall.receiptAccount.receiptAccountRepeatError"),
    /** 邮箱重复 */
    RECEIPT_ACCOUNT_PAYONEER_EMAIL_REPEAT_ERROR("240008", "zsmall.receiptAccount.receiptAccountPayoneerEmailRepeatError"),
    RECEIPT_ACCOUNT_CREDIT_NULL("240009", "zsmall.receiptAccount.receiptAccountCreditNull"),
    RECEIPT_ACCOUNT_PAYONEER_NULL("240010", "zsmall.receiptAccount.receiptAccountPayoneerNull"),
    RECEIPT_ACCOUNT_CREDIT_PARAMETER_EMPTY("240011", "zsmall.receiptAccount.receiptAccountCreditParameterEmpty"),
    RECEIPT_ACCOUNT_PAYONEER_PARAMETER_EMPTY("240012", "zsmall.receiptAccount.receiptAccountPayoneerParameterEmpty"),


    /** 查询提现记录时发生未知错误 */
    RECEIPT_RECORD_QUERY_ERROR("241001", "zsmall.receiptRecord.receiptRecordQueryError"),
    /** 提现金额必须大于0 */
    RECEIPT_AMOUNT_LESS_THAN_ZERO_ERROR("241002", "zsmall.receiptRecord.receiptAmountLessThanZeroError"),
    /** 未能查询到对应的提现账户 */
    RECEIPT_AMOUNT_NOT_FOUND_ERROR("241003", "zsmall.receiptRecord.receiptAmountNotFoundError"),
    /** 未能查询到提现记录 */
    RECEIPT_RECORD_NOT_FOUND_ERROR("241004", "zsmall.receiptRecord.receiptRecordNotFoundError"),
    /** 保存提现记录发生未知错误 */
    RECEIPT_RECORD_SAVE_ERROR("241005", "zsmall.receiptRecord.receiptRecordSaveError"),
    /** 更新提现记录发生未知错误 */
    RECEIPT_RECORD_UPDATE_ERROR("241006", "zsmall.receiptRecord.receiptRecordUpdateError"),
    /** 提现金额不匹配 */
    RECEIPT_AMOUNT_NOT_MATCH("241007", "zsmall.receiptRecord.receiptAmountNotMatch"),
    /** 添加账单提现交易记录关系信息失败！ */
    RECEIPT_TRANSACTION_RECORD_SAVE_ERROR("241008", "zsmall.receiptRecord.receiptTransactionRecordSaveError"),
    /** 修改账单提现状态信息失败！ */
    BILL_RECEIPT_STATE_UPDATE_ERROR("241009", "zsmall.receiptRecord.billReceiptStateUpdateError"),

    /** 设置支付密码时发生异常 */
    SETTING_PAYMENT_PASSWORD_ERROR("260001", "zsmall.payManagement.settingPaymentPasswordError"),
    /** 未设置支付密码 */
    PAYMENT_PASSWORD_NOT_SET("260002", "zsmall.payManagement.paymentPasswordNotSet"),
    /** 修改支付密码设置时发生错误 */
    CHANGE_PAYMENT_SETTING_ERROR("260003", "zsmall.payManagement.changePaymentSettingError"),
    /** 查询支付设置时发生错误 */
    QUERY_PAYMENT_SETTING_ERROR("260004", "zsmall.payManagement.queryPaymentSettingError"),
    /** 校验支付密码时发生异常 */
    VERIFY_PAYMENT_PASSWORD_ERROR("260005", "zsmall.payManagement.verifyPaymentPasswordError"),
    /** 校验支付设置时发生异常 */
    VERIFY_PAYMENT_SETTING_ERROR("260006", "zsmall.payManagement.verifyPaymentSettingError"),
    /** 支付密码为空 */
    PAYMENT_PASSWORD_EMPTY("260007", "zsmall.payManagement.paymentPasswordEmpty"),
    /** 支付密码不正确 */
    INCORRECT_PAYMENT_PASSWORD("260008", "zsmall.payManagement.incorrectPaymentPassword"),

    /** 供应商入驻信息保存发生异常 */
    /** 名字不能为空 */
    USER_SUP_DISTR_FIRSTNAME_EMPTY("", "zsmall.settled.firstNameNotEmpty"),
    /** 姓氏不能为空 */
    USER_SUP_DISTR_LASTNAME_EMPTY("", "zsmall.settled.lastNameNotEmpty"),
    /** 即时通讯软件类型不能为空 */
    USER_SUP_DISTR_INSTANTMSGAPPTYPE_EMPTY("", "zsmall.settled.instantMsgAppTypeNotEmpty"),
    /** 即时通讯软件账号不能为空 */
    USER_SUP_DISTR_MESSAGINGAPPNUMBER_EMPTY("", "zsmall.settled.instantMsgAppNumberNotEmpty"),
    /** 是否存在公司主体不能为空 */
    USER_SUP_DISTR_HASCOMPANY_EMPTY("", "zsmall.settled.hasCompanyNotEmpty"),
    /** 公司名称不能为空 */
    USER_SUP_DISTR_COMPANYNAME_EMPTY("", "zsmall.settled.companyNameNotEmpty"),
    /** 主营类目不能为空 */
    USER_SUP_DISTR_MAINCATEGORIES_EMPTY("", "zsmall.settled.mainCategoriesNotEmpty"),
    /** 团队规模不能为空 */
    USER_SUP_DISTR_TEAMSIZE_EMPTY("", "zsmall.settled.teamSizeNotEmpty"),
    /** 公司所在州/省id不能为空 */
    USER_SUP_DISTR_COMPANYSTATEID_EMPTY("", "zsmall.settled.companyStateIdNotEmpty"),
    /** 公司所在市不能为空 */
    USER_SUP_DISTR_COMPANYCITYTEXT_EMPTY("", "zsmall.settled.companyCityTextNotEmpty"),
    /** 公司联系地址不能为空 */
    USER_SUP_DISTR_COMPANYCONTACTADDRESS_EMPTY("", "zsmall.settled.companyContactAddressNotEmpty"),
    /** 近一年年销售规模（万）不能为空 */
    USER_SUP_DISTR_RECENTANNUALSALESSCALE_EMPTY("", "zsmall.settled.recentAnnualSalesScaleNotEmpty"),
    /** 跨境电商经验不能为空 */
    USER_SUP_DISTR_CECEXPERIENCE_EMPTY("", "zsmall.settled.cecExperienceNotEmpty"),
    /** 其他线上销售渠道经验不能为空 */
    USER_SUP_DISTR_OTHEREXPERIENCE_EMPTY("", "zsmall.settled.otherExperienceNotEmpty"),

    USER_SUP_SETTLE_IN_SAVE_ERROR("270001", "zsmall.settled.userSupSettleInSaveError"),
    /** 获取员工审核供应商入驻记录信息发生异常 */
    USER_SUP_REVIEW_RECORD_QUERY_ERROR("270002", "zsmall.settled.userSupReviewRecordQueryError"),
    /** 查询供应商公司联系人信息发生异常 */
    USER_SUP_SETTLE_IN_QUERY_CONTACT_ERROR("270003", "zsmall.settled.userSupSettleInQueryContactError"),
    /** 查询供应商入驻基础信息发生异常 */
    USER_SUP_SETTLE_IN_QUERY_BASIC_ERROR("270004", "zsmall.settled.userSupSettleInQueryBasicError"),
    /** 查询供应商入驻信息发生异常 */
    USER_SUP_SETTLE_IN_QUERY_ERROR("270005", "zsmall.settled.userSupSettleInQueryError"),
    /** 修改供应商入驻公司联系人信息发生异常 */
    USER_SUP_SETTLE_IN_UPDATE_CONTACT_ERROR("270006", "zsmall.settled.userSupSettleInUpdateContactError"),
    /** 修改供应商入驻基础信息发生异常 */
    USER_SUP_SETTLE_IN_UPDATE_BASIC_ERROR("270007", "zsmall.settled.userSupSettleInUpdateBasicError"),
    /** 分销商入驻信息保存发生异常 */
    USER_BULK_INFO_SAVE_ERROR("270008", "zsmall.settled.userBulkInfoSaveError"),
    /** 查询分销商入驻信息发生异常 */
    USER_BULK_INFO_QUERY_ERROR("270009", "zsmall.settled.userBulkInfoQueryError"),
    /** 修改员工审核供应商入驻记录信息发生异常 */
    USER_SUP_REVIEW_RECORD_UPDATE_ERROR("270010", "zsmall.settled.userSupReviewRecordUpdateError"),
    /** 获取员工入驻信息是否完善状态发生异常 */
    USER_SUP_IS_PERFECT_INFO_STATUS_ERROR("270011", "zsmall.settled.userSupIsPerfectInfoStatusError"),
    /** 分销商入驻信息未完善 */
    USER_BULK_INFORMATION_IS_NOT_PERFECT("270012", "zsmall.settled.userBulkInformationIsNotPerfect"),
    /** 供应商入驻信息审核中，请勿重复提交 */
    USER_SUP_SETTLE_IN_TO_BE_REVIEWED("270013", "zsmall.settled.userSupSettleInToBeReviewed"),
    /** 供应商入驻信息不存在 */
    USER_SUP_SETTLE_IN_NOT_EXISTS("270014", "zsmall.settled.userSupSettleInNotExists"),

    /** 查询资金总览统计时发生错误 */
    QUERY_FUNDS_OVERVIEW_STATISTICS_ERROR("280001", "zsmall.fundManagement.queryFundsOverviewStatisticsError"),
    /** 查询资金流水时发生错误 */
    QUERY_FUND_FLOW_ERROR("280002", "zsmall.fundManagement.queryFundFlowError"),
    /** 导出资金流水时发生错误 */
    EXPORT_FUNDS_OVERVIEW_ERROR("280003", "zsmall.fundManagement.exportFundsOverviewError"),

    /** 查询国内现货商品列表时发生未知错误 */
    QUERY_CHINA_SPOT_PRODUCT_LIST_ERROR("290001", "zsmall.chinaSpotProduct.queryChinaSpotProductListError"),
    /** 上传国内现货商品Excel时发生未知错误 */
    UPLOAD_CHINA_SPOT_PRODUCT_EXCEL_ERROR("290002", "zsmall.chinaSpotProduct.uploadChinaSpotProductExcelError"),
    /** 查询国内现货商品详情时发生未知错误 */
    QUERY_CHINA_SPOT_PRODUCT_DETAIL_ERROR("290003", "zsmall.chinaSpotProduct.queryChinaSpotProductDetailError"),
    /** 国内现货商品不存在 */
    QUERY_CHINA_SPOT_PRODUCT_NOT_EXIST("290004", "zsmall.chinaSpotProduct.queryChinaSpotProductNotExist"),
    /** 删除国内现货商品时发生未知错误 */
    DELETE_CHINA_SPOT_PRODUCT_ERROR("290005", "zsmall.chinaSpotProduct.deleteChinaSpotProductError"),
    /** 导出国内现货商品列表时发生未知错误 */
    EXPORT_CHINA_SPOT_PRODUCT_LIST_ERROR("290006", "zsmall.chinaSpotProduct.exportChinaSpotProductListError"),

    /** 批发价格设置不规范 */
    WHOLESALE_PRICE_SETUP_ERROR("300001", "zsmall.wholesaleProduct.wholesalePriceSetupError"),
    /** 批发商品阶梯价格不存在 */
    WHOLESALE_TIERED_PRICE_NOT_EXIST("300002", "zsmall.wholesaleProduct.wholesaleTieredPriceNotExist"),
    /** 批发商品SKU价格不存在 */
    WHOLESALE_SKU_PRICE_NOT_EXIST("300003", "zsmall.wholesaleProduct.wholesaleSkuPriceNotExist"),
    /** 批发商品发货类型不存在 */
    WHOLESALE_DELIVERY_TYPE_NOT_EXIST("300004", "zsmall.wholesaleProduct.wholesaleDeliveryTypeNotExist"),
    /** 批发商品总库存不能小于最小起订量 */
    WHOLESALE_INVENTORY_ERROR("300005", "zsmall.wholesaleProduct.wholesaleInventoryError"),
    /** SKU或UPC（{}）已存在或重复 */
    WHOLESALE_SKU_REPEAT("300006", "zsmall.wholesaleProduct.wholesaleSkuRepeat"),
    /** 国外现货批发商品查询时异常，请稍后重试 */
    HOME_SEARCH_WHOLESALE_PRODUCTS_ERROR("300007", "zsmall.wholesaleProduct.homeSearchWholesaleProductsError"),
    /** 批发商品不存在，请稍后重试 */
    WHOLESALE_SKU_NOT_EXIST("300008", "zsmall.wholesaleProduct.wholesaleSkuNotExist"),
    /** 该商品无法加入购物车 */
    WHOLESALE_CANNOT_ADD_TO_CART("300009", "zsmall.wholesaleProduct.wholesaleCannotAddToCart"),
    /** 该商品存在进行中的意向单，无法删除 */
    HAS_INTENTION_NOT_DEL_ERROR("300010", "zsmall.wholesaleProduct.hasIntentionError"),
    /** 撤销批发订单成功 */
    CANCEL_ORDER_SUCCESS("0", "zsmall.wholesaleProduct.cancelOrderSuccess"),
    /** 保存成功，价格变更已提交审核，请前往[商品审核]菜单查看审核结果 */
    WHOLESALE_UPDATE_PRICE_SUCCESS("0", "zsmall.wholesaleProduct.wholesaleUpdatePriceSuccess"),
    /** 支付成功！平台将在48小时之内与您联系，协助您为您的客户进行一件代发 */
    ZSMALL_DROPSHIPPING_PAY_SUCCESS("0", "zsmall.wholesaleProduct.zsmallDropshippingPaySuccess"),
    /** 下单时发生未知错误 */
    WHOLESALE_PLACE_ORDER_ERROR("301001", "zsmall.wholesaleProduct.wholesalePlaceOrderError"),
    /** 未达到最小起订数量要求 */
    MINIMUM_QUANTITY_NOT_REACHED("301002", "zsmall.wholesaleProduct.minimumQuantityNotReached"),
    /** SkuId.[{}] 库存不足！ */
    PRODUCT_SKU_INSUFFICIENT_INVENTORY("301003", "zsmall.wholesaleProduct.productSkuInsufficientInventory"),
    /**
     * tiktok包不存在
     */
//    tiktok相关
    TIKTOK_PACKAGE_NOT_EXISTS("401000", "zsmall.tiktok.packageNotExists"),
    /**
     * tiktok trackingNo为null  或 对应的物流详情还未生成
     */
    TIKTOK_TRACKING_NO_NOT_EXISTS("401001", "zsmall.tiktok.trackingNoNotExists"),
    /**
     * tiktok包url不存在
     */
    TIKTOK_PACKAGE_URL_NOT_EXISTS("401002", "zsmall.tiktok.packageUrlNotExists"),
//    tiktok相关

    /** 所选收货地址不存在或已删除，请重试 */
    SHIPPING_ADDRESS_NOT_EXIST("301004", "zsmall.wholesaleProduct.shippingAddressNotExist"),
    /** 批发订单录入价格时发生未知错误 */
    ENTER_PRICE_ERROR("301005", "zsmall.wholesaleProduct.enterPriceError"),
    /** 批发订单不存在 */
    WHOLESALE_ORDER_NOT_EXIST("301006", "zsmall.wholesaleProduct.wholesaleOrderNotExist"),
    /** 当前订单状态不需要录入价格 */
    NOT_NEED_ENTER_PRICE("301008", "zsmall.wholesaleProduct.notNeedEnterPrice"),
    /** 取消批发订单时发生未知错误 */
    WHOLESALE_CANCEL_ORDER_ERROR("301009", "zsmall.wholesaleProduct.wholesaleCancelOrderError"),
    /** 当前订单状态无法执行该操作 */
    OPERATION_CANNOT_BE_PERFORMED("301010", "zsmall.wholesaleProduct.operationCannotBePerformed"),
    /** 查询批发订单时发生未知错误 */
    QUERY_WHOLESALE_ORDER_ERROR("301011", "zsmall.wholesaleProduct.queryWholesaleOrderError"),
    /** 查询批发订单列表时发生未知错误 */
    QUERY_WHOLESALE_ORDER_PAGE_ERROR("301012", "zsmall.wholesaleProduct.queryWholesaleOrderPageError"),
    /** 不支持选择的物流方式 */
    LOGISTICS_METHOD_NOT_SUPPORTED("301013", "zsmall.wholesaleProduct.logisticsMethodNotSupported"),
    /** 物流信息不完整 */
    INCOMPLETE_LOGISTICS_INFORMATION("301014", "zsmall.wholesaleProduct.incompleteLogisticsInformation"),
    /** 请上传提货单 */
    PLEASE_UPLOAD_SHIPPING_LABEL("301015", "zsmall.wholesaleProduct.pleaseUploadShippingLabel"),
    /** 下单支付尾款时发生未知错误 */
    PLACE_ORDER_PAY_BALANCE("301016", "zsmall.wholesaleProduct.placeOrderPayBalance"),
    /** 该商品无法铺货至第三方销售渠道 */
    CANNOT_IMPORT_TO_SALES_CHANNEL("301017", "zsmall.wholesaleProduct.cannotImportToSalesChannel"),
    /** 商品未映射 */
    PRODUCT_MAPPING_EXCEPTION("301018","zsmall.product.productMappingException"),
    /**
     * 商品已下架
     */
    PRODUCT_DELISTED("66002", "zsmall.product.delisted"),
    /**
     * 此商品已下架
     */
    PRODUCT_DELIST("66003", "zsmall.product.delist"),
    /**
     * 物流方式不能为空
     */
    SUPPORTED_LOGISTICS_IS_NOT_NUll("66004", "zsmall.product.supportedLogisticsIsNotNull"),
    /**
     * 错误的物流方式
     */
    ERROR_SUPPORTED_LOGISTICS("66005", "zsmall.product.errorSupportedLogistics"),
    /**
     * 商品数量超出限制
     */
    COMMODITY_QUANTITY_OVERSHOOT("66006", "zsmall.product.commodityQuantityOvershoot"),
    /**
     * 尾程配送费异常
     */
    FINAL_DELIVERY_FEE_EXCEPTION("70000","zsmall.finalDeliveryFee.exception"),
    /**
     * 测量异常
     */
    MEASUREMENT_ANOMALY("70001","zsmall.finalDeliveryFee.measurementAnomaly"),
    /**
     * 商品不存在
     */
    PRODCUT_NOT_FOUND_ERROR("70002","zsmall.finalDeliveryFee.productNotFoundError"),
    /**
     * 不支持的物流方式
     */
    SITE_INFORMATION_A_SCENE("80000","zsmall.siteInformation.aScene"),
    UNSUPPORTED_LOGISTICS_METHODS("70003","zsmall.finalDeliveryFee.unsupportedLogisticsMethods"),
    /**
     * 验证码发送失败
     */
    VERIFY_CODE_SEND_FAIL("7000403", "zsmall.verifyCode.sendFail"),

    /**
     * 交易子类型不可为空
     */
    TRANSACTION_SUB_TYPE_NOT_NULL("7000419","zsmall.transaction.transactionSubTypeNotNull"),
    ONLY_EXCEL_IS_SUPPORTED("710110","zsmall.excel.onlyExcelIsSupported" ),
    PRODUCT_MIGRATION_ENCOUNTERED_UNKNOWN_EXCEPTION("8838392", "zsmall.product.migrationEncounteredUnknownException"),

    LTL_ORDER_BOL_NEED("720110", "zsmall.order.ltlOrderMustNeedBol"),
    OPEN_ORDER_REVIEW_CONSENT_SUCCESS("20010", "zsmall.order.openOrderReviewConsentSuccess" ),
    OPEN_ORDER_REVIEW_CONSENT_FAILED("20011", "zsmall.order.openOrderReviewConsentFailed" ),
    OPEN_ORDER_REVIEW_REFUSE_SUCCESS("20020", "zsmall.order.openOrderReviewRefuseSuccess" ),
    ABNORMAL_SKU_INVENTORY_IS_INSUFFICIENT("50010", "zsmall.order.abnormalSkuInventoryIsInsufficient" ),
    ABNORMAL_CREATED_FOR_THE_SHIPPING_ORDER("50020", "zsmall.order.abnormalCreatedForTheShippingOrder" ),
    OPEN_ORDER_REVIEW_REFUSE_FAILED("20021", "zsmall.order.openOrderReviewRefuseFailed" );
    /**
     * 响应子编号
     */
    private String subCode;

    /**
     * 信息编号，用于在messages.properties等文件中获取国际化信息
     */
    private String messageCode;

    private Object[] args;

    ZSMallStatusCodeEnum(String subCode, String messageCode) {
        this.subCode = subCode;
        this.messageCode = messageCode;
    }

    @Override
    public String getSubCode() {
        return this.subCode;
    }

    @Override
    public String getMessageCode() {
        return this.messageCode;
    }

    public ZSMallStatusCodeEnum args(Object... args) {
        this.args = args;
        return this;
    }

    @Override
    public Object[] getArgs() {
        return this.args;
    }
}
