<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrdersMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.order.entity.domain.Orders">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="orderType" column="order_type" jdbcType="VARCHAR"/>
        <result property="logisticsType" column="logistics_type" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="channelType" column="channel_type" jdbcType="VARCHAR"/>
        <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
        <result property="channelAlias" column="channel_alias" jdbcType="VARCHAR"/>
        <result property="channelOrderNo" column="channel_order_no" jdbcType="VARCHAR"/>
        <result property="channelOrderName" column="channel_order_name" jdbcType="VARCHAR"/>
        <result property="channelOrderTime" column="channel_order_time" jdbcType="TIMESTAMP"/>
        <result property="fulfillmentProgress" column="fulfillment_progress" jdbcType="VARCHAR"/>
        <result property="orderState" column="order_state" jdbcType="VARCHAR"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="payErrorMessage" column="pay_error_message" jdbcType="OTHER"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="totalQuantity" column="total_quantity" jdbcType="INTEGER"/>
        <result property="originalTotalProductAmount" column="original_total_product_amount" jdbcType="DECIMAL"/>
        <result property="originalTotalOperationFee" column="original_total_operation_fee" jdbcType="DECIMAL"/>
        <result property="originalTotalFinalDeliveryFee" column="original_total_final_delivery_fee" jdbcType="DECIMAL"/>
        <result property="originalTotalPickUpPrice" column="original_total_pick_up_price" jdbcType="DECIMAL"/>
        <result property="originalTotalDropShippingPrice" column="original_total_drop_shipping_price"
                jdbcType="DECIMAL"/>
        <result property="originalPayableTotalAmount" column="original_payable_total_amount" jdbcType="DECIMAL"/>
        <result property="originalPrepaidTotalAmount" column="original_prepaid_total_amount" jdbcType="DECIMAL"/>
        <result property="originalActualTotalAmount" column="original_actual_total_amount" jdbcType="DECIMAL"/>
        <result property="originalRefundExecutableAmount" column="original_refund_executable_amount"
                jdbcType="DECIMAL"/>
        <result property="platformTotalProductAmount" column="platform_total_product_amount" jdbcType="DECIMAL"/>
        <result property="platformTotalOperationFee" column="platform_total_operation_fee" jdbcType="DECIMAL"/>
        <result property="platformTotalFinalDeliveryFee" column="platform_total_final_delivery_fee" jdbcType="DECIMAL"/>
        <result property="platformTotalPickUpPrice" column="platform_total_pick_up_price" jdbcType="DECIMAL"/>
        <result property="platformTotalDropShippingPrice" column="platform_total_drop_shipping_price"
                jdbcType="DECIMAL"/>
        <result property="platformPayableTotalAmount" column="platform_payable_total_amount" jdbcType="DECIMAL"/>
        <result property="platformPrepaidTotalAmount" column="platform_prepaid_total_amount" jdbcType="DECIMAL"/>
        <result property="platformActualTotalAmount" column="platform_actual_total_amount" jdbcType="DECIMAL"/>
        <result property="platformRefundExecutableAmount" column="platform_refund_executable_amount"
                jdbcType="DECIMAL"/>
        <result property="importRecordId" column="import_record_id" jdbcType="BIGINT"/>
        <result property="orderNote" column="order_note" jdbcType="VARCHAR"/>
        <result property="trackingFlag" column="tracking_flag" jdbcType="INTEGER"/>
        <result property="exceptionCode" column="exception_code" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,tenant_id,order_type,
        logistics_type,order_no,channel_type,
        channel_id,channel_alias,channel_order_no,
        channel_order_name,channel_order_time,fulfillment_progress,
        order_state,pay_time,pay_error_message,
        total_quantity,original_total_product_amount,original_total_operation_fee,
        original_total_final_delivery_fee,original_total_pick_up_price,original_total_drop_shipping_price,
        original_payable_total_amount,original_prepaid_total_amount,original_actual_total_amount,
        original_refund_executable_amount,platform_total_product_amount,platform_total_operation_fee,
        platform_total_final_delivery_fee,platform_total_pick_up_price,platform_total_drop_shipping_price,
        platform_payable_total_amount,platform_prepaid_total_amount,platform_actual_total_amount,
        platform_refund_executable_amount,import_record_id,order_note,tracking_flag,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>

    <update id="updateOrderTrackingFlag">
        update orders set tracking_flag = #{trackingFlag}
        where tenant_id = #{tenantId}
        and channel_order_no = #{channelOrderNo}
        and del_flag = 0
    </update>

    <select id="queryPageList" resultMap="BaseResultMap">
        SELECT o.*
        FROM orders o
                join  order_item oi ON oi.order_id = o.id
                left join  order_item_product_sku ops ON ops.order_item_id = oi.id
        WHERE o.del_flag = '0'
          AND oi.del_flag = '0'
        <if test="dto.isShow == null">
            AND o.is_show  is null
        </if>
       <if test="dto.isShow != null and dto.isShow ==2">
           AND o.is_show=2
       </if>
       <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderStates)">
            AND o.order_state IN
            <foreach collection="dto.orderStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelIds)">
            AND o.channel_id IN
            <foreach collection="dto.channelIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelType)">
            AND o.channel_type = #{dto.channelType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.logisticsType)">
            AND o.logistics_type = #{dto.logisticsType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.orderNo)">
            AND o.order_no = #{dto.orderNo}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelOrderNo)">
            AND o.channel_order_name LIKE CONCAT('%', #{dto.channelOrderNo}, '%')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.sku)">
            AND ops.sku LIKE CONCAT('%', #{dto.sku}, '%')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.productName)">
            AND ops.product_name LIKE CONCAT('%', #{dto.productName}, '%')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.itemNo)">
            AND ops.product_sku_code LIKE CONCAT('%', #{dto.itemNo}, '%')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.trackingNo)">
            AND EXISTS(SELECT 1
                       FROM order_item_tracking_record oitr
                       WHERE oitr.del_flag = '0'
                         AND oitr.order_no = o.order_no
                         AND oitr.logistics_tracking_no LIKE CONCAT('%', #{dto.trackingNo}, '%'))
        </if>
        <if test="dto.tenantType == 'Supplier'">
            AND oi.supplier_tenant_id = #{dto.tenantId}
        </if>

        <if test="dto.tenantType == 'Manager' and dto.supplierTenantId != null">
            AND oi.supplier_tenant_id = #{dto.supplierTenantId}
        </if>

        <if test="dto.tenantType == 'Distributor'">
            AND o.tenant_id = #{dto.tenantId}
        </if>
        <if test="dto.tenantType == 'Manager' and dto.distributorTenantId != null">
            AND o.tenant_id = #{dto.distributorTenantId}
        </if>
        <if test="dto.startDate != null and dto.endDate != null">
            AND o.create_time BETWEEN #{dto.startDate} AND #{dto.endDate}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.fulfillmentType)">
            AND o.fulfillment_progress = #{dto.fulfillmentType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.activityCode)">
            AND EXISTS(SELECT 1
                       FROM order_item oi
                                JOIN product_activity_item pai ON oi.activity_code = pai.activity_code
            WHERE oi.order_id = o.id
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(dto.tenantType, 'Distributor')">
                AND pai.activity_code = #{dto.activityCode}
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equalsAny(dto.tenantType, 'Supplier', 'Manager')">
                AND pai.activity_code_parent = #{dto.activityCode}
            </if>
            )
        </if>
        <if test="dto.exceptionCode != null">
            and o.exception_code= #{dto.exceptionCode}
        </if>
        GROUP BY o.id
    </select>

    <select id="existsOrderNo" resultType="java.lang.Boolean">
        SELECT COUNT(o.id)
        FROM orders o
        WHERE o.order_no = #{orderNo}
    </select>

    <select id="getByOrderNoAndSupplier" resultMap="BaseResultMap">
        SELECT o.*
        from orders o
        join order_item oi on o.id = oi.order_id
        where oi.del_flag = '0'
        and o.del_flag = '0'
        and o.order_no = #{orderNo}
        and oi.supplier_tenant_id = #{supplierTenantId}
        group by o.id
    </select>

    <select id="getShippingOrder" resultMap="BaseResultMap">
        SELECT o.*
        FROM orders o
        WHERE o.del_flag = '0'
        AND o.order_no = #{orderNo}
        AND o.order_state = #{orderState}
        AND EXISTS(SELECT 1 FROM order_item oi WHERE oi.del_flag = '0' AND oi.shipping_order_state =
        #{shippingOrderState})
    </select>

    <select id="queryChannelSalesStatisticsForMag"
            resultType="com.zsmall.order.entity.domain.vo.statistics.ChannelSalesStatisticsVo">
        SELECT o.channel_type                      AS 'channelType',
        SUM(o.platform_payable_total_amount) AS 'salesAmount',
        COUNT(o.id)                         AS 'orderQuantity'
        FROM orders o
        WHERE o.del_flag = '0'
        AND o.order_state IN ('Paid', 'Verifying')
        AND o.pay_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY o.channel_type
    </select>

    <select id="queryChannelSalesStatisticsForDis"
            resultType="com.zsmall.order.entity.domain.vo.statistics.ChannelSalesStatisticsVo">
        SELECT o.channel_type                      AS 'channelType',
               SUM(o.platform_payable_total_amount) AS 'salesAmount',
               COUNT(o.id)                         AS 'orderQuantity'
        FROM orders o
        WHERE o.del_flag = '0'
          AND o.order_state IN ('Paid', 'Verifying')
          AND o.pay_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY o.channel_type
    </select>

    <select id="queryChannelSalesStatisticsForSup"
            resultType="com.zsmall.order.entity.domain.vo.statistics.ChannelSalesStatisticsVo">
        SELECT order_union.channelType        AS 'channelType',
               SUM(order_union.salesAmount)   AS 'salesAmount',
               SUM(order_union.orderQuantity) AS 'orderQuantity'
        FROM ((SELECT o.channel_type                        AS 'channelType',
                      SUM(oi.original_payable_total_amount) AS 'salesAmount',
                      COUNT(DISTINCT o.id)                  AS 'orderQuantity'
               FROM orders o
                        JOIN order_item oi on o.id = oi.order_id
               WHERE o.del_flag = '0'
                 AND oi.supplier_tenant_id = #{supplierId}
                 AND o.order_state IN ('Paid', 'Verifying')
                 AND oi.order_state IN ('Paid', 'Verifying')
                 AND o.pay_time BETWEEN #{startDate} AND #{endDate}
                 AND o.order_type != 'Wholesale'
               GROUP BY o.channel_type)
              UNION
              (SELECT o.channel_type                       AS 'channelType',
                      SUM(o.original_payable_total_amount) AS 'salesAmount',
                      COUNT(DISTINCT o.id)                 AS 'orderQuantity'
               FROM orders o
               WHERE o.del_flag = '0'
                 AND o.order_state IN ('Paid', 'Verifying')
                 AND o.pay_time BETWEEN #{startDate} AND #{endDate}
                 AND o.order_type = 'Wholesale'
                 AND EXISTS (SELECT 1
                             FROM order_item oi
                             WHERE oi.order_id = o.id
                               AND oi.supplier_tenant_id = #{supplierId})
               GROUP BY o.channel_type)) AS order_union
        GROUP BY order_union.channelType
    </select>

    <select id="statsPlatformOrderPaymentAmount4Wholesale" resultType="java.math.BigDecimal">
        SELECT SUM(o.platform_payable_total_amount)
        FROM orders o
        WHERE o.order_type = 'Wholesale'
          AND o.fulfillment_progress = 'Fulfilled'
        <if test="startDate != null">
            AND EXISTS (SELECT 1 FROM order_item oi WHERE oi.order_id = o.id AND oi.fulfillment_time IS NOT NULL AND oi.fulfillment_time &gt;= #{startDate})
        </if>
        <if test="endDate != null">
            AND EXISTS (SELECT 1 FROM order_item oi WHERE oi.order_id = o.id AND oi.fulfillment_time IS NOT NULL AND oi.fulfillment_time &lt;= #{endDate})
        </if>
    </select>
    <select id="getSysTenantByThirdChannelFlag" resultType="com.hengjian.system.domain.SysTenant">
        select * from sys_tenant where third_channel_flag  = #{channelFlag}
    </select>

    <select id="selectOrderDetailByOrderNos" resultMap="BaseResultMap">
        SELECT
        o.*
        FROM orders o
        JOIN order_item oi ON oi.order_id = o.id
        JOIN order_item_product_sku ops ON ops.order_item_id = oi.id
        WHERE o.del_flag = '0'
        AND oi.del_flag = '0'
        AND ops.del_flag = '0'
        AND o.tenant_id =#{tenantId,jdbcType=VARCHAR}
        AND  o.order_no in
        <foreach collection="orderNoSet" item="item" index="index" open="(" close=")" separator=",">
            #{item, jdbcType=VARCHAR}
        </foreach>

    </select>

    <select id="getOrderExportList" resultType="com.zsmall.order.entity.domain.bo.order.OrderExportListDTO">
        SELECT
            o.id,
            o.order_no as orderNo,
            o.order_state as orderState,
            o.fulfillment_progress as fulfillmentProgress,
            o.channel_order_no as channelOrderNo,
            o.latest_delivery_time as latestDeliveryTime,
            o.original_total_operation_fee as originalTotalOperationFee,
            o.original_total_final_delivery_fee as originalTotalFinalDeliveryFee,
            o.original_payable_total_amount as originalPayableTotalAmount,
            o.original_actual_total_amount as originalActualTotalAmount,
            o.original_total_product_amount as originalTotalProductAmount,
            o.platform_total_final_delivery_fee as platformTotalFinalDeliveryFee,
            o.platform_total_operation_fee as platformTotalOperationFee,
            o.platform_payable_total_amount as platformPayableTotalAmount,
            o.platform_actual_total_amount as platformActualTotalAmount,
            o.platform_total_product_amount as platformTotalProductAmount,
            o.original_total_pick_up_price as originalTotalPickUpPrice,
            o.original_total_drop_shipping_price as originalTotalDropShippingPrice,
            o.platform_refund_executable_amount as originalPrepaidTotalAmount,
            o.original_prepaid_total_amount as platformPrepaidTotalAmount,
            o.logistics_type as logisticsType,
            o.channel_alias as channelName,
            o.channel_id,
            o.channel_alias,
            o.exception_code,
            o.create_time as createTime,
            o.currency as currencyCode,
            o.pay_time as payTime,
            oi.id as orderItemId,
            oi.total_quantity as quantity,
            oi.original_payable_unit_price as originalPayableUnitPrice,
            oi.platform_payable_unit_price as platformPayableUnitPrice,
            oi.product_sku_code as productSkuCode,
            oi.channel_type as channelType,
            oi.tenant_id as tenantId,
            oi.supplier_tenant_id as supplierTenantId,
            oi.channel_sku as channelSku,
            o.shipment_exception as shipmentException
        FROM orders o
        inner join  order_item oi ON oi.order_id = o.id
        WHERE  o.del_flag = '0'
        AND oi.del_flag = '0'
        <if test="dto.isShowOrder != null and dto.isShowOrder==true ">
            AND o.is_show=2
        </if>
        <if test="dto.isShowOrder != null and dto.isShowOrder ==false ">
            AND o.is_show  is null
        </if>
        <if test="dto.orderSource != null">
            AND o.order_source=#{dto.orderSource}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderStates)">
            AND o.order_state IN
            <foreach collection="dto.orderStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelIds)">
            AND o.channel_id IN
            <foreach collection="dto.channelIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelType)">
            AND o.channel_type = #{dto.channelType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.logisticsType)">
            AND o.logistics_type = #{dto.logisticsType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.orderNo)">
            AND o.order_no LIKE CONCAT('%', #{dto.orderNo}, '%')
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderNos)">
            AND o.order_no in
            <foreach collection="dto.orderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelOrderNos)">
            AND o.channel_order_name in
            <foreach collection="dto.channelOrderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelOrderNo)">
            AND o.channel_order_name LIKE CONCAT('%', #{dto.channelOrderNo}, '%')
        </if>
        <if test="dto.tenantType == 'Distributor'">
            AND oi.tenant_id = #{dto.tenantId}
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                and oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.tenantType == 'Supplier'">
            AND oi.supplier_tenant_id = #{dto.tenantId}
            <if test="dto.distributorTenantId!= null and dto.distributorTenantId!= ''">
                and oi.tenant_id = #{dto.distributorTenantId}
            </if>
        </if>
        <if test="dto.tenantType == 'Manager'   ">
            <if test="dto.distributorTenantId != null and dto.distributorTenantId != ''">
                AND oi.tenant_id = #{dto.distributorTenantId}
            </if>
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                AND oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.itemNo != null and dto.itemNo != ''">
            and oi.product_sku_code=#{dto.itemNo}
        </if>
        <if test="dto.startDate != null and dto.startDate != ''">
            and o.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null and dto.endDate != ''">
            and o.create_time &lt;= #{dto.endDate}
        </if>
        <if test="dto.channelOrderStartDate != null and dto.channelOrderStartDate != ''">
            and o.channel_order_time &gt;=#{dto.channelOrderStartDate}
        </if>
        <if test="dto.channelOrderEndDate != null and dto.channelOrderEndDate != ''">
            and o.channel_order_time &lt;=#{dto.channelOrderEndDate}
        </if>
        <if test="dto.dispatchedStartTime != null and dto.dispatchedStartTime != ''">
            and oi.dispatched_time &gt;=#{dto.dispatchedStartTime}
        </if>
        <if test="dto.dispatchedEndTime != null and dto.dispatchedEndTime != ''">
            and oi.dispatched_time &lt;=#{dto.dispatchedEndTime}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.fulfillmentType)">
            AND o.fulfillment_progress = #{dto.fulfillmentType}
        </if>
        <if test="dto.exceptionCode != null">
            and o.exception_code= #{dto.exceptionCode}
        </if>
        <if test="dto.currencyCode != null and dto.currencyCode != ''">
            and o.currency = #{dto.currencyCode}
        </if>
        <if test="dto.cancelStatus != null">
            and o.cancel_status=#{dto.cancelStatus}
        </if>
        <if test="dto.shipmentException != null">
            and o.shipment_exception=#{dto.shipmentException}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.name LIKE CONCAT('%', #{dto.productName}, '%'))
        </if>
        <if test="dto.sku!= null and dto.sku!= ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.sku = #{dto.sku})
        </if>
        <if test="dto.trackingNo!= null and dto.trackingNo!= ''">
            and exists(select 1
            from order_item_tracking_record oitr
            where oitr.order_no = oi.order_no
            and oitr.logistics_tracking_no LIKE CONCAT('%', #{dto.trackingNo}, '%'))
        </if>
        <if test="dto.activityCode != null and dto.activityCode != ''">
            and exists(select 1
            from product_activity_item pai
            where pai.activity_code = oi.activity_code
            and pai.activity_code = #{dto.activityCode})
        </if>
        <!--        GROUP BY o.id-->
        order by ${dto.sortValue}
        limit #{page},#{pageSize}
    </select>

    <select id="getOrderExportCount" resultType="java.lang.Integer">
        SELECT count(o.id)
        FROM orders o
        inner join order_item oi ON oi.order_id = o.id
        WHERE
        o.del_flag = '0'
        AND oi.del_flag = '0'
        <if test="dto.isShowOrder != null and dto.isShowOrder==true ">
            AND o.is_show=2
        </if>
        <if test="dto.isShowOrder != null and dto.isShowOrder ==false ">
            AND o.is_show  is null
        </if>
        <if test="dto.orderSource != null">
            AND o.order_source=#{dto.orderSource}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderStates)">
            AND o.order_state IN
            <foreach collection="dto.orderStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelIds)">
            AND o.channel_id IN
            <foreach collection="dto.channelIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelType)">
            AND o.channel_type = #{dto.channelType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.logisticsType)">
            AND o.logistics_type = #{dto.logisticsType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.orderNo)">
            AND o.order_no LIKE CONCAT('%', #{dto.orderNo}, '%')
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderNos)">
            AND o.order_no in
            <foreach collection="dto.orderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelOrderNos)">
            AND o.channel_order_name in
            <foreach collection="dto.channelOrderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelOrderNo)">
            AND o.channel_order_name LIKE CONCAT('%', #{dto.channelOrderNo}, '%')
        </if>

        <if test="dto.tenantType == 'Distributor'">
            AND o.tenant_id = #{dto.tenantId}
        </if>
        <if test="dto.tenantType == 'Supplier'">
            AND oi.supplier_tenant_id = #{dto.supplierTenantId}
        </if>

        <if test="dto.tenantType == 'Manager'   ">
            <if test="dto.distributorTenantId != null and dto.distributorTenantId != ''">
                AND oi.tenant_id = #{dto.distributorTenantId}
            </if>
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                AND oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.startDate != null and dto.startDate != ''">
            and o.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null and dto.endDate != ''">
            and o.create_time &lt;= #{dto.endDate}
        </if>
        <if test="dto.channelOrderStartDate != null and dto.channelOrderStartDate != ''">
            and o.channel_order_time >= #{dto.channelOrderStartDate}
        </if>
        <if test="dto.channelOrderEndDate != null and dto.channelOrderEndDate != ''">
            and o.channel_order_time &lt;= #{dto.channelOrderEndDate}
        </if>
        <if test="dto.dispatchedStartTime != null and dto.dispatchedStartTime != ''">
            and oi.dispatched_time &gt;=#{dto.dispatchedStartTime}
        </if>
        <if test="dto.dispatchedEndTime != null and dto.dispatchedEndTime != ''">
            and oi.dispatched_time &lt;=#{dto.dispatchedEndTime}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.fulfillmentType)">
            AND o.fulfillment_progress = #{dto.fulfillmentType}
        </if>
        <if test="dto.exceptionCode != null">
            and o.exception_code= #{dto.exceptionCode}
        </if>
        <if test="dto.itemNo != null and dto.itemNo != ''">
            and oi.product_sku_code=#{dto.itemNo}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.name LIKE CONCAT('%', #{dto.productName}, '%'))
        </if>
        <if test="dto.sku!= null and dto.sku!= ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.sku = #{dto.sku})
        </if>
        <if test="dto.trackingNo!= null and dto.trackingNo!= ''">
            and exists(select 1
            from order_item_tracking_record oitr
            where oitr.order_no = oi.order_no
            and oitr.logistics_tracking_no LIKE CONCAT('%', #{dto.trackingNo}, '%'))
        </if>
        <if test="dto.activityCode != null and dto.activityCode != ''">
            and exists(select 1
            from product_activity_item pai
            where pai.activity_code = oi.activity_code
            and pai.activity_code = #{dto.activityCode})
        </if>
        order by o.id desc
    </select>




    <update id="updateOrderTrackingFlagByChannelOrderNo">
        update orders set tracking_flag = #{trackingFlag}
        where channel_order_no in
              <foreach collection="channelOrderNoList" item="channelOrderNo" separator="," open="(" close=")">
                  #{channelOrderNo}
              </foreach>
          and del_flag = 0
    </update>
    <update id="updateOrderTrackingFlagByChannelOrderNoAndChannelId">
        update orders set tracking_flag = #{trackingFlag}
        where channel_id = #{channelId}
          and channel_order_no = #{channelOrderNo}
          and del_flag = 0
    </update>

    <update id="updateShipmentExceptionCodeNullById">
        update orders set shipment_exception = NULL
        where id = #{orderId}
          and del_flag = 0
    </update>


    <select id="selectOrderAddressInfo" resultType="com.zsmall.order.entity.domain.bo.order.OrderExportListDTO">
        select  oai.order_id as orderId,
        CONCAT_WS(',',
        oai.recipient,
        oai.zip_code,
        oai.phone_number,
        oai.address1,
        oai.city,
        oai.state_code,
        oai.country_code)           AS address
        from  order_address_info oai
        inner  join ${tableName} as sss
        on sss.value1=oai.order_id
        where oai.del_flag='0'
    </select>

    <select id="selectOrderLogisticsInfo" resultType="com.zsmall.order.entity.domain.bo.order.OrderExportListDTO">
        SELECT ali.order_id,ali.logistics_account, ali.logistics_account_zip_code
        FROM order_logistics_info ali
        WHERE ali.del_flag='0'
        AND EXISTS (
        SELECT 1
        FROM  ${tableName} sss
        WHERE sss.value1 = ali.order_id
        );

    </select>

    <select id="selectOrderAddressAndLogisticsInfo" resultType="com.zsmall.order.entity.domain.bo.order.OrderExportListDTO">
        SELECT
            s.value1 as id,
            CONCAT_WS(',', oai.recipient, oai.zip_code, oai.phone_number, oai.address1,oai.address2,oai.address3,  oai.city, oai.state_code, oai.country_code) AS address,
            oai.recipient as recipient,
            oai.zip_code as zipCode,
            oai.phone_number as phoneNumber,
            oai.city as city,
            oai.country_code as country,
            oai.state_code as state
<!--            li.logistics_account,-->
<!--            li.logistics_account_zip_code-->
        FROM
            ${tableName} s
                LEFT JOIN order_address_info oai ON s.value1 = oai.order_id
<!--                LEFT JOIN order_logistics_info li ON s.value1 = li.order_id-->
        WHERE
           oai.del_flag = '0'
<!--          AND li.del_flag = '0'-->
    </select>


    <select id="selectOrderTrackingRecord" resultType="com.zsmall.order.entity.domain.bo.order.OrderExportListDTO">
        SELECT oitr.order_no,
        oitr.logistics_carrier as logisticsCarrier,
        oitr.logistics_tracking_no as logisticsTrackingNo,
        oitr.dispatched_time as dispatchedTime
        FROM order_item_tracking_record oitr
        WHERE
        oitr.del_flag='0' and
        EXISTS (
        SELECT 1
        FROM ${tableName} sss
        WHERE sss.value3 = oitr.order_no
        );


    </select>

    <select id="selectOrderSkuName" resultType="com.zsmall.order.entity.domain.bo.order.OrderExportListDTO">
        SELECT ps.product_sku_code, ps.name,ps.sku as erpSku
        FROM product_sku ps
        WHERE  ps.del_flag='0' and  EXISTS (
        SELECT 1
        FROM   ${tableName}  sss
        WHERE sss.value4 = ps.product_sku_code
        );

    </select>

    <select id="getByChannelSkuAndExceptionCode" resultType="com.zsmall.order.entity.domain.Orders">
        SELECT
        t1.*
        FROM
        orders t1
        LEFT JOIN order_item t2 ON t1.id = t2.order_id
        AND t2.del_flag = 0
        WHERE
        t1.del_flag = 0
        <if test="null != exceptionCode">
            AND t1.exception_code = #{exceptionCode}
        </if>
          <if test="null != orderSource">
          and t1.order_source = #{orderSource}
          </if>
        AND t1.order_state = #{orderState}
        AND t2.channel_sku in
        <foreach collection="channelSkuList" item="channelSku" open="(" close=")" separator=",">
            #{channelSku}
        </foreach>
    </select>

    <select id="getByChannelSkuAndExceptionCodePage" resultType="com.zsmall.order.entity.domain.Orders">
        SELECT
        t1.*
        FROM
        orders t1
        LEFT JOIN order_item t2 ON t1.id = t2.order_id
        AND t2.del_flag = 0
        WHERE
        t1.del_flag = 0
        and (t1.is_show IS NULL OR t1.is_show = 1)
        <if test="null != exceptionCode">
            AND t1.exception_code = #{exceptionCode}
        </if>
        <if test="null != orderSource">
            and t1.order_source = #{orderSource}
        </if>
        AND t1.order_state = #{orderState}
        AND t2.channel_sku in
        <foreach collection="channelSkuList" item="channelSku" open="(" close=")" separator=",">
            #{channelSku}
        </foreach>
        LIMIT #{pageSize}
        OFFSET #{offset}
    </select>

    <select id="selectOrderItemPrice" resultType="com.zsmall.order.entity.domain.OrderItemPrice">
        SELECT oip.*,oi.order_no as orderItemNo
        FROM order_item_price oip
                inner join order_item oi on oip.order_item_id = oi.id
                 INNER JOIN ${tableName}  sss
                            ON sss.value2 =oip.order_item_id
        WHERE oip.del_flag = '0';

    </select>

    <select id="selectOrderWarehouseCode" resultType="com.zsmall.order.entity.domain.bo.order.OrderExportListDTO">
        select w.warehouse_code as logisticsWarehouse,
        oips.order_no as orderNo
        from ${tableName}  sss
            inner join    order_item_product_sku oips on sss.value2=oips.order_item_id
        inner join warehouse w on w.warehouse_system_code=oips.warehouse_system_code

    </select>

    <select id="selectOrderItem" resultType="com.zsmall.order.entity.domain.OrderItem">
        select oi.*
        from order_item oi
                 inner join ${tableName} sss
                            on sss.value2 = oi.id
        where oi.del_flag = '0'
    </select>

    <select id="getByChannelSkuAndExceptionCodeNum" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        orders t1
        LEFT JOIN order_item t2 ON t1.id = t2.order_id
        AND t2.del_flag = 0
        WHERE
        t1.del_flag = 0
        and (t1.is_show IS NULL OR t1.is_show = 1)
        <if test="null != exceptionCode">
            AND t1.exception_code = #{exceptionCode}
        </if>
        <if test="null != orderSource">
            and t1.order_source = #{orderSource}
        </if>
        AND t1.order_state = #{orderState}
        AND t2.channel_sku in
        <foreach collection="channelSkuList" item="channelSku" open="(" close=")" separator=",">
            #{channelSku}
        </foreach>
    </select>

    <select id="getByOrderNoListAndExceptionCodePage" resultType="com.zsmall.order.entity.domain.Orders">
        SELECT
        t1.*
        FROM
        orders t1
        LEFT JOIN order_item t2 ON t1.id = t2.order_id
        AND t2.del_flag = 0
        WHERE
        t1.del_flag = 0
        and (t1.is_show IS NULL OR t1.is_show = 1)
        <if test="null != exceptionCode">
            AND t1.exception_code = #{exceptionCode}
        </if>
        <if test="null != orderSource">
            and t1.order_source = #{orderSource}
        </if>
        AND t1.order_state = #{orderState}
        AND t1.order_no in
        <foreach collection="orderNoList" item="orderNo" open="(" close=")" separator=",">
            #{orderNo}
        </foreach>
        LIMIT #{pageSize}
        OFFSET #{offset}
    </select>

    <resultMap id="orderListVoResultMap" type="com.zsmall.order.entity.domain.vo.OrderListVo">
        <id column="orderId" property="orderId"/>
        <result column="channelOrderId" property="channelOrderId"/>
        <result column="orderState" property="orderState"/>
        <result column="fulfillmentProgress" property="fulfillment"/>
        <result column="orderType" property="orderType"/>
        <result column="createTime" property="createTime"/>
        <result column="channelOrderTime" property="channelOrderTime"/>
        <result column="logisticsType" property="logisticsType"/>
        <result column="platform_actual_total_amount" property="platformActualTotalAmount"/>
        <result column="original_actual_total_amount" property="originalActualTotalAmount"/>
        <result column="original_total_drop_shipping_price" property="originalTotalDropShippingPrice"/>
        <result column="original_total_pick_up_price" property="originalTotalPickUpPrice"/>
        <result column="payFailedMessage" property="payFailedMessage" jdbcType="VARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="trackingFlag" property="trackingFlag"/>
        <result column="exception_code" property="exceptionCode"/>
        <result column="order_source" property="orderSource"/>
        <result column="is_show" property="isShow"/>
        <result column="channel_id" property="channelId"/>
        <result column="channel_type" property="channelType"/>
        <result column="supplier_tenant_id" property="supplierIds"/>
        <result column="tenant_id" property="distributorId"/>
        <result column="create_by" property="createBy"/>
        <result column="cancel_status" property="cancelStatus"/>
        <result column="currency" property="currency"/>
        <result column="currency_symbol" property="currencySymbol"/>
        <result column="shipment_exception" property="shipmentException"/>
        <collection  property="orderItems" ofType="com.zsmall.order.entity.domain.vo.OrderListVo$OrderItems">
<!--            <result column="imageShowUrl" property="imageShowUrl"/>-->
<!--            <result column="productName" property="productName"/>-->
<!--            <result column="sku" property="sku"/>-->
            <result column="itemNo" property="itemNo"/>
            <result column="num" property="num"/>
            <result column="order_state" property="orderState"/>
            <result column="channel_sku" property="channelSku"/>
        </collection >
    </resultMap>


    <select id="queryOrderListVoPageList"  resultMap="orderListVoResultMap">
        SELECT
        o.order_no as orderId,
        o.channel_order_no as channelOrderId,
        o.order_type as orderType,
        o.create_time as createTime,
        o.channel_order_time as channelOrderTime,
        o.order_state as orderState ,
        o.fulfillment_progress as fulfillmentProgress,
        o.logistics_type as logisticsType,
        o.pay_error_message as payFailedMessage,
        o.tracking_flag as trackingFlag,
        o.exception_code,
        o.is_show,
        o.order_source,
        o.channel_id,
        o.platform_actual_total_amount,
        o.original_actual_total_amount,
        o.original_total_drop_shipping_price,
        o.original_total_pick_up_price,
        o.channel_type ,
        o.tenant_id,
        o.create_by,
        o.currency,
        o.currency_symbol,
        o.cancel_status,
        oi.total_quantity as num,
        oi.order_state,
        oi.supplier_tenant_id,
        oi.channel_sku,
        oi.product_sku_code as itemNo,
        o.shipment_exception
<!--        ops.product_sku_code as itemNo,-->
<!--        ops.image_show_url as imageShowUrl,-->
<!--        ops.product_name as productName,-->
<!--        ops.sku-->
        FROM orders o
        inner join  order_item oi ON oi.order_id = o.id
<!--        <if test="dto.isJoin==true ">-->
<!--            inner JOIN ${temporaryTable} t ON o.id = t.value1-->
<!--        </if>-->
        WHERE  o.del_flag = '0'
        AND oi.del_flag = '0'
        <if test="dto.isShowOrder != null and dto.isShowOrder==true ">
            AND o.is_show=2
        </if>
        <if test="dto.isShowOrder != null and dto.isShowOrder ==false ">
            AND o.is_show  is null
        </if>
        <if test="dto.orderSource != null">
            AND o.order_source=#{dto.orderSource}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderStates)">
            AND o.order_state IN
            <foreach collection="dto.orderStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelIds)">
            AND o.channel_id IN
            <foreach collection="dto.channelIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
<!--        <if test="dto.orderIds != null and dto.orderIds.size() != 0">-->
<!--            and o.id in-->
<!--            <foreach collection="dto.orderIds" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelType)">
            AND o.channel_type = #{dto.channelType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.logisticsType)">
            AND o.logistics_type = #{dto.logisticsType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.orderNo)">
            AND o.order_no LIKE CONCAT('%', #{dto.orderNo}, '%')
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderNos)">
            AND o.order_no in
            <foreach collection="dto.orderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelOrderNos)">
            AND o.channel_order_name in
            <foreach collection="dto.channelOrderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelOrderNo)">
            AND o.channel_order_name LIKE CONCAT('%', #{dto.channelOrderNo}, '%')
        </if>
        <if test="dto.tenantType == 'Distributor'">
            AND oi.tenant_id = #{dto.tenantId}
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                and oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.tenantType == 'Supplier'">
            AND oi.supplier_tenant_id = #{dto.tenantId}
            <if test="dto.distributorTenantId!= null and dto.distributorTenantId!= ''">
                and oi.tenant_id = #{dto.distributorTenantId}
            </if>
        </if>
        <if test="dto.tenantType == 'Manager'   ">
            <if test="dto.distributorTenantId != null and dto.distributorTenantId != ''">
                AND oi.tenant_id = #{dto.distributorTenantId}
            </if>
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                AND oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.itemNo != null and dto.itemNo != ''">
            and oi.product_sku_code=#{dto.itemNo}
        </if>
        <if test="dto.startDate != null and dto.startDate != ''">
            and o.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null and dto.endDate != ''">
            and o.create_time &lt;= #{dto.endDate}
        </if>
        <if test="dto.channelOrderStartDate != null and dto.channelOrderStartDate != ''">
            and o.channel_order_time &gt;=#{dto.channelOrderStartDate}
        </if>
        <if test="dto.channelOrderEndDate != null and dto.channelOrderEndDate != ''">
            and o.channel_order_time &lt;=#{dto.channelOrderEndDate}
        </if>
        <if test="dto.dispatchedStartTime != null and dto.dispatchedStartTime != ''">
            and oi.dispatched_time &gt;=#{dto.dispatchedStartTime}
        </if>
        <if test="dto.dispatchedEndTime != null and dto.dispatchedEndTime != ''">
            and oi.dispatched_time &lt;=#{dto.dispatchedEndTime}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.fulfillmentType)">
            AND o.fulfillment_progress = #{dto.fulfillmentType}
        </if>
        <if test="dto.exceptionCode != null">
            and o.exception_code= #{dto.exceptionCode}
        </if>
        <if test="dto.cancelStatus != null">
            and o.cancel_status=#{dto.cancelStatus}
        </if>
        <if test="dto.currencyCode != null and dto.currencyCode != ''">
            and o.currency = #{dto.currencyCode}
        </if>
        <if test="dto.cancelStatus != null">
            and o.cancel_status=#{dto.cancelStatus}
        </if>
        <if test="dto.shipmentException != null and dto.shipmentException != ''">
            and o.shipment_exception = #{dto.shipmentException}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.name LIKE CONCAT('%', #{dto.productName}, '%'))
        </if>
        <if test="dto.sku!= null and dto.sku!= ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.sku = #{dto.sku})
        </if>
        <if test="dto.trackingNo!= null and dto.trackingNo!= ''">
            and exists(select 1
            from order_item_tracking_record oitr
            where oitr.order_no = oi.order_no
            and oitr.logistics_tracking_no LIKE CONCAT('%', #{dto.trackingNo}, '%'))
        </if>
        <if test="dto.activityCode != null and dto.activityCode != ''">
            and exists(select 1
            from product_activity_item pai
            where pai.activity_code = oi.activity_code
            and pai.activity_code = #{dto.activityCode})
        </if>
<!--        GROUP BY o.id-->
        order by ${dto.sortValue}
        limit #{page},#{limit}
    </select>

    <select id="getOrderIdBySkuQuery" resultType="java.lang.Long">
        select oi.order_id
        from product_sku ps inner join order_item oi
        on oi.product_sku_code=ps.product_sku_code
        where oi.del_flag=0
        <if test="productName != null and productName != ''">
            AND ps.name LIKE CONCAT('%', #{productName}, '%')
        </if>
        <if test="sku != null and sku != ''">
            and ps.sku =#{sku}
        </if>
    </select>

    <select id="getOrderIdByActivityQuery" resultType="java.lang.Long">
        select oi.order_id from order_item oi
                            inner JOIN product_activity_item pai ON oi.activity_code = pai.activity_code
        where oi.del_flag=0
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(tenantType, 'Distributor')">
            AND pai.activity_code = #{activityCode}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equalsAny(tenantType, 'Supplier', 'Manager')">
            AND pai.activity_code_parent = #{activityCode}
        </if>
    </select>

    <select id="getOrderIdByTrackingNoQuery" resultType="java.lang.Long">
        SELECT oi.order_id from order_item oi
        inner join  order_item_tracking_record oitr on oitr.order_no=oi.order_no
        WHERE oi.del_flag = '0'
          <if test="trackingNo != null and trackingNo != ''">
              AND oitr.logistics_tracking_no LIKE CONCAT('%', #{trackingNo}, '%')
          </if>

    </select>

    <select id="countByOrderListVo" resultType="java.lang.Integer">
        SELECT count(o.id)
        FROM orders o
                 inner join order_item oi ON oi.order_id = o.id
        WHERE
           o.del_flag = '0'
        AND oi.del_flag = '0'
        <if test="dto.isShowOrder != null and dto.isShowOrder==true ">
            AND o.is_show=2
        </if>
        <if test="dto.isShowOrder != null and dto.isShowOrder ==false ">
            AND o.is_show  is null
        </if>
        <if test="dto.orderSource != null">
            AND o.order_source=#{dto.orderSource}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderStates)">
            AND o.order_state IN
            <foreach collection="dto.orderStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelIds)">
            AND o.channel_id IN
            <foreach collection="dto.channelIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelType)">
            AND o.channel_type = #{dto.channelType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.logisticsType)">
            AND o.logistics_type = #{dto.logisticsType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.orderNo)">
            AND o.order_no LIKE CONCAT('%', #{dto.orderNo}, '%')
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderNos)">
            AND o.order_no in
            <foreach collection="dto.orderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelOrderNos)">
            AND o.channel_order_name in
            <foreach collection="dto.channelOrderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelOrderNo)">
            AND o.channel_order_name LIKE CONCAT('%', #{dto.channelOrderNo}, '%')
        </if>

        <if test="dto.tenantType == 'Distributor'">
            AND oi.tenant_id = #{dto.tenantId}
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                and oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.tenantType == 'Supplier'">
            AND oi.supplier_tenant_id = #{dto.tenantId}
            <if test="dto.distributorTenantId!= null and dto.distributorTenantId!= ''">
                and oi.tenant_id = #{dto.distributorTenantId}
            </if>
        </if>

        <if test="dto.tenantType == 'Manager'   ">
            <if test="dto.distributorTenantId != null and dto.distributorTenantId != ''">
                AND oi.tenant_id = #{dto.distributorTenantId}
            </if>
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                AND oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.startDate != null and dto.startDate != ''">
            and o.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null and dto.endDate != ''">
            and o.create_time &lt;= #{dto.endDate}
        </if>
        <if test="dto.channelOrderStartDate != null and dto.channelOrderStartDate != ''">
            and o.channel_order_time >= #{dto.channelOrderStartDate}
        </if>
        <if test="dto.channelOrderEndDate != null and dto.channelOrderEndDate != ''">
            and o.channel_order_time &lt;= #{dto.channelOrderEndDate}
        </if>
        <if test="dto.dispatchedStartTime != null and dto.dispatchedStartTime != ''">
            and oi.dispatched_time &gt;=#{dto.dispatchedStartTime}
        </if>
        <if test="dto.dispatchedEndTime != null and dto.dispatchedEndTime != ''">
            and oi.dispatched_time &lt;=#{dto.dispatchedEndTime}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.fulfillmentType)">
            AND o.fulfillment_progress = #{dto.fulfillmentType}
        </if>
        <if test="dto.exceptionCode != null">
            and o.exception_code= #{dto.exceptionCode}
        </if>
        <if test="dto.itemNo != null and dto.itemNo != ''">
            and oi.product_sku_code=#{dto.itemNo}
        </if>
        <if test="dto.cancelStatus != null">
            and o.cancel_status =#{dto.cancelStatus}
        </if>
        <if test="dto.currencyCode != null and dto.currencyCode != ''">
            and o.currency = #{dto.currencyCode}
        </if>
        <if test="dto.cancelStatus != null">
            and o.cancel_status=#{dto.cancelStatus}
        </if>
        <if test="dto.shipmentException != null and dto.shipmentException != ''">
            and o.shipment_exception = #{dto.shipmentException}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            and exists(select 1
                       from product_sku ps
                       where ps.product_sku_code = oi.product_sku_code
                         and ps.name LIKE CONCAT('%', #{dto.productName}, '%'))
        </if>
        <if test="dto.sku!= null and dto.sku!= ''">
            and exists(select 1
                       from product_sku ps
                       where ps.product_sku_code = oi.product_sku_code
                         and ps.sku = #{dto.sku})
        </if>
        <if test="dto.trackingNo!= null and dto.trackingNo!= ''">
            and exists(select 1
                       from order_item_tracking_record oitr
                       where oitr.order_no = oi.order_no
                         and oitr.logistics_tracking_no LIKE CONCAT('%', #{dto.trackingNo}, '%'))
        </if>
        <if test="dto.activityCode != null and dto.activityCode != ''">
            and exists(select 1
                       from product_activity_item pai
                       where pai.activity_code = oi.activity_code
                         and pai.activity_code = #{dto.activityCode})
        </if>
    </select>

    <select id="countChannelOrderNo" resultType="java.lang.Integer">
        SELECT count(*)
        FROM orders
        WHERE
        del_flag = 0
        <if test="isShow != null and isShow == false ">
            AND (is_show IS NULL OR is_show = 1)
        </if>
        <if test="isShow != null and isShow == true ">
            AND is_show = 2
        </if>
        <if test="null != channelOrderNo and channelOrderNo != ''">
            AND channel_order_no = #{channelOrderNo}
        </if>
        <if test="null != orderState and orderState != ''">
            AND order_state != #{orderState}
        </if>
    </select>

    <select id="countOrderAttachment" resultType="java.lang.Integer">
        SELECT count(o.id)
        FROM orders o
        inner join order_item oi ON oi.order_id = o.id
        inner join order_attachment oa on oa.order_no=o.order_no
        WHERE
        o.del_flag = '0'
        AND oi.del_flag = '0'
        and oa.del_flag=0
        and oa.attachment_type='ShippingLabel'
        and oa.attachment_show_url is not null
        <if test="dto.isShowOrder != null and dto.isShowOrder==true ">
            AND o.is_show=2
        </if>
        <if test="dto.isShowOrder != null and dto.isShowOrder ==false ">
            AND o.is_show  is null
        </if>
        <if test="dto.currencyCode != null and dto.currencyCode != ''">
            and o.currency = #{dto.currencyCode}
        </if>
        <if test="dto.orderSource != null">
            AND o.order_source=#{dto.orderSource}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderStates)">
            AND o.order_state IN
            <foreach collection="dto.orderStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelIds)">
            AND o.channel_id IN
            <foreach collection="dto.channelIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelType)">
            AND o.channel_type = #{dto.channelType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.logisticsType)">
            AND o.logistics_type = #{dto.logisticsType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.orderNo)">
            AND o.order_no LIKE CONCAT('%', #{dto.orderNo}, '%')
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderNos)">
            AND o.order_no in
            <foreach collection="dto.orderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelOrderNos)">
            AND o.channel_order_name in
            <foreach collection="dto.channelOrderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelOrderNo)">
            AND o.channel_order_name LIKE CONCAT('%', #{dto.channelOrderNo}, '%')
        </if>

        <if test="dto.tenantType == 'Distributor'">
            AND oi.tenant_id = #{dto.tenantId}
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                and oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.tenantType == 'Supplier'">
            AND oi.supplier_tenant_id = #{dto.tenantId}
            <if test="dto.distributorTenantId!= null and dto.distributorTenantId!= ''">
                and oi.tenant_id = #{dto.distributorTenantId}
            </if>
        </if>

        <if test="dto.tenantType == 'Manager'   ">
            <if test="dto.distributorTenantId != null and dto.distributorTenantId != ''">
                AND oi.tenant_id = #{dto.distributorTenantId}
            </if>
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                AND oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.startDate != null and dto.startDate != ''">
            and o.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null and dto.endDate != ''">
            and o.create_time &lt;= #{dto.endDate}
        </if>
        <if test="dto.channelOrderStartDate != null and dto.channelOrderStartDate != ''">
            and o.channel_order_time >= #{dto.channelOrderStartDate}
        </if>
        <if test="dto.channelOrderEndDate != null and dto.channelOrderEndDate != ''">
            and o.channel_order_time &lt;= #{dto.channelOrderEndDate}
        </if>
        <if test="dto.dispatchedStartTime != null and dto.dispatchedStartTime != ''">
            and oi.dispatched_time &gt;=#{dto.dispatchedStartTime}
        </if>
        <if test="dto.dispatchedEndTime != null and dto.dispatchedEndTime != ''">
            and oi.dispatched_time &lt;=#{dto.dispatchedEndTime}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.fulfillmentType)">
            AND o.fulfillment_progress = #{dto.fulfillmentType}
        </if>
        <if test="dto.exceptionCode != null">
            and o.exception_code= #{dto.exceptionCode}
        </if>
        <if test="dto.itemNo != null and dto.itemNo != ''">
            and oi.product_sku_code=#{dto.itemNo}
        </if>
        <if test="dto.cancelStatus != null">
            and o.cancel_status=#{dto.cancelStatus}
        </if>
        <if test="dto.shipmentException != null and dto.shipmentException != ''">
            and o.shipment_exception = #{dto.shipmentException}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.name LIKE CONCAT('%', #{dto.productName}, '%'))
        </if>
        <if test="dto.sku!= null and dto.sku!= ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.sku = #{dto.sku})
        </if>
        <if test="dto.trackingNo!= null and dto.trackingNo!= ''">
            and exists(select 1
            from order_item_tracking_record oitr
            where oitr.order_no = oi.order_no
            and oitr.logistics_tracking_no LIKE CONCAT('%', #{dto.trackingNo}, '%'))
        </if>
        <if test="dto.activityCode != null and dto.activityCode != ''">
            and exists(select 1
            from product_activity_item pai
            where pai.activity_code = oi.activity_code
            and pai.activity_code = #{dto.activityCode})
        </if>
        order by o.id desc
    </select>

    <select id="selectOrderAttachment"
            resultType="com.zsmall.order.entity.domain.bo.order.OrderAttachmentExportDto">
        SELECT
        oa.id as orderAttachmentId,
        oa.order_no as orderNo,
        oa.attachment_show_url as  ossUrl
        FROM orders o
        inner join order_item oi ON oi.order_id = o.id
        inner join order_attachment oa on oa.order_no=o.order_no
        WHERE
        o.del_flag = '0'
        AND oi.del_flag = '0'
        and oa.del_flag=0
        and oa.attachment_type='ShippingLabel'
        and oa.attachment_show_url is not null
        <if test="dto.isShowOrder != null and dto.isShowOrder==true ">
            AND o.is_show=2
        </if>
        <if test="dto.isShowOrder != null and dto.isShowOrder ==false ">
            AND o.is_show  is null
        </if>
        <if test="dto.currencyCode != null and dto.currencyCode != ''">
            and o.currency = #{dto.currencyCode}
        </if>
        <if test="dto.orderSource != null">
            AND o.order_source=#{dto.orderSource}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderStates)">
            AND o.order_state IN
            <foreach collection="dto.orderStates" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelIds)">
            AND o.channel_id IN
            <foreach collection="dto.channelIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelType)">
            AND o.channel_type = #{dto.channelType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.logisticsType)">
            AND o.logistics_type = #{dto.logisticsType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.orderNo)">
            AND o.order_no LIKE CONCAT('%', #{dto.orderNo}, '%')
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.orderNos)">
            AND o.order_no in
            <foreach collection="dto.orderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.channelOrderNos)">
            AND o.channel_order_name in
            <foreach collection="dto.channelOrderNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.channelOrderNo)">
            AND o.channel_order_name LIKE CONCAT('%', #{dto.channelOrderNo}, '%')
        </if>

        <if test="dto.tenantType == 'Distributor'">
            AND oi.tenant_id = #{dto.tenantId}
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                and oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.tenantType == 'Supplier'">
            AND oi.supplier_tenant_id = #{dto.tenantId}
            <if test="dto.distributorTenantId!= null and dto.distributorTenantId!= ''">
                and oi.tenant_id = #{dto.distributorTenantId}
            </if>
        </if>

        <if test="dto.tenantType == 'Manager'   ">
            <if test="dto.distributorTenantId != null and dto.distributorTenantId != ''">
                AND oi.tenant_id = #{dto.distributorTenantId}
            </if>
            <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
                AND oi.supplier_tenant_id = #{dto.supplierTenantId}
            </if>
        </if>
        <if test="dto.startDate != null and dto.startDate != ''">
            and o.create_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate != null and dto.endDate != ''">
            and o.create_time &lt;= #{dto.endDate}
        </if>
        <if test="dto.channelOrderStartDate != null and dto.channelOrderStartDate != ''">
            and o.channel_order_time >= #{dto.channelOrderStartDate}
        </if>
        <if test="dto.channelOrderEndDate != null and dto.channelOrderEndDate != ''">
            and o.channel_order_time &lt;= #{dto.channelOrderEndDate}
        </if>
        <if test="dto.dispatchedStartTime != null and dto.dispatchedStartTime != ''">
            and oi.dispatched_time &gt;=#{dto.dispatchedStartTime}
        </if>
        <if test="dto.dispatchedEndTime != null and dto.dispatchedEndTime != ''">
            and oi.dispatched_time &lt;=#{dto.dispatchedEndTime}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(dto.fulfillmentType)">
            AND o.fulfillment_progress = #{dto.fulfillmentType}
        </if>
        <if test="dto.exceptionCode != null">
            and o.exception_code= #{dto.exceptionCode}
        </if>
        <if test="dto.itemNo != null and dto.itemNo != ''">
            and oi.product_sku_code=#{dto.itemNo}
        </if>
        <if test="dto.cancelStatus != null">
            and o.cancel_status=#{dto.cancelStatus}
        </if>
        <if test="dto.shipmentException != null and dto.shipmentException != ''">
            and o.shipment_exception = #{dto.shipmentException}
        </if>
        <if test="dto.productName != null and dto.productName != ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.name LIKE CONCAT('%', #{dto.productName}, '%'))
        </if>
        <if test="dto.sku!= null and dto.sku!= ''">
            and exists(select 1
            from product_sku ps
            where ps.product_sku_code = oi.product_sku_code
            and ps.sku = #{dto.sku})
        </if>
        <if test="dto.trackingNo!= null and dto.trackingNo!= ''">
            and exists(select 1
            from order_item_tracking_record oitr
            where oitr.order_no = oi.order_no
            and oitr.logistics_tracking_no LIKE CONCAT('%', #{dto.trackingNo}, '%'))
        </if>
        <if test="dto.activityCode != null and dto.activityCode != ''">
            and exists(select 1
            from product_activity_item pai
            where pai.activity_code = oi.activity_code
            and pai.activity_code = #{dto.activityCode})
        </if>
        order by o.id desc
        limit #{startRow} ,#{finalPageSize}
    </select>



    <select id="selectOrderCountWithOpenApi" resultType="int">
        SELECT COUNT(DISTINCT o.order_extend_id)
        FROM orders o
        INNER JOIN order_item oi ON o.id = oi.order_id
        WHERE o.del_flag = 0
        AND oi.del_flag = 0
        <if test="dto.distributorTenantId != null and dto.distributorTenantId != ''">
            AND oi.tenant_id = #{dto.distributorTenantId}
        </if>
        <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
            AND oi.supplier_tenant_id = #{dto.supplierTenantId}
        </if>
        <if test="dto.orderExtendIdList != null and dto.orderExtendIdList.size() != 0">
            AND o.order_extend_id IN
            <foreach collection="dto.orderExtendIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.channelOrderNoList != null and dto.channelOrderNoList.size() != 0">
            AND o.channel_order_no IN
            <foreach collection="dto.channelOrderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.createTimeStart != null">
            AND o.create_time &gt;= #{dto.createTimeStart}
        </if>
        <if test="dto.createTimeEnd != null">
            AND o.create_time &lt;= #{dto.createTimeEnd}
        </if>

    </select>

    <select id="selectPageOrderWithOpenApi" resultType="java.lang.String">
        SELECT t.order_extend_id
        FROM (
        SELECT o.order_extend_id, MAX(o.id) as max_id
        FROM orders o
        INNER JOIN order_item oi ON o.id = oi.order_id
        WHERE o.del_flag = 0
        AND oi.del_flag = 0
        <if test="dto.distributorTenantId != null and dto.distributorTenantId != ''">
            AND oi.tenant_id = #{dto.distributorTenantId}
        </if>
        <if test="dto.supplierTenantId != null and dto.supplierTenantId != ''">
            AND oi.supplier_tenant_id = #{dto.supplierTenantId}
        </if>
        <if test="dto.orderExtendIdList != null and dto.orderExtendIdList.size() >0">
            AND o.order_extend_id IN
            <foreach collection="dto.orderExtendIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.channelOrderNoList != null and dto.channelOrderNoList.size() >0">
            AND o.channel_order_no IN
            <foreach collection="dto.channelOrderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.createTimeStart != null">
            AND o.create_time &gt;= #{dto.createTimeStart}
        </if>
        <if test="dto.createTimeEnd != null">
            AND o.create_time &lt;= #{dto.createTimeEnd}
        </if>
        GROUP BY o.order_extend_id
        ) t
        ORDER BY t.max_id DESC
        LIMIT #{dto.offset},#{dto.size}
    </select>

    <select id="selectOrderDetailWithOpenApi" resultMap="BaseResultMap">
        select o.*
        from orders o
        inner join order_item oi on o.id = oi.order_id
        where o.del_flag=0
        and oi.del_flag=0
        <if test="dto.distributorTenantId != null">
            and oi.tenant_id=#{dto.distributorTenantId}
        </if>
        <if test="dto.supplierTenantId != null">
            and oi.supplier_tenant_id=#{dto.supplierTenantId}
        </if>
        <if test=" dto.orderExtendIdList != null and dto.orderExtendIdList.size() > 0">
            and o.order_extend_id in
            <foreach collection="dto.orderExtendIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test=" dto.channelOrderNoList != null and dto.channelOrderNoList.size() > 0">
            and o.channel_order_no in
            <foreach collection="dto.channelOrderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="getOrderNoByOrderExtendId" resultMap="BaseResultMap">
        select o.*
        from orders o
        inner join order_item oi on o.id = oi.order_id
        where o.del_flag=0
        and oi.del_flag=0
        and o.order_extend_id=#{orderExtendId}
        and oi.supplier_tenant_id=#{tenantId}
    </select>



    <select id="selectOrderAttach"
            resultType="com.zsmall.order.entity.domain.bo.order.OrderAttachmentExportDto">
        SELECT
        oa.id as orderAttachmentId,
        oa.order_no as orderNo,
        oa.attachment_show_url as  ossUrl
        FROM order_item  oi
        inner join order_attachment oa on oa.order_no=oi.order_no
        WHERE  oi.del_flag = 0
        and oa.del_flag=0
        and oa.attachment_type='ShippingLabel'
        and oa.attachment_show_url is not null
        AND EXISTS (
        SELECT 1
        FROM  ${tableName} sss
        WHERE sss.value3 = oa.order_no
        );
    </select>

    <select id="countOrder" resultType="int">
        select count(*)
        from orders o
        where del_flag=0
    </select>

    <select id="getOrders" resultType="com.zsmall.order.entity.domain.mq.EsOrdersDTO">
        select o.*,
        oi.order_item_no,
        oi.tenant_id,
        oi.supplier_tenant_id,
        oi.dispatched_time,
        oi.product_sku_code,
        oi.channel_sku,
        oips.sku,
        oips.upc,
        oips.erp_sku,
        oips.mapping_sku,
        oips.product_name,
        oips.image_show_url,
        oips.warehouse_system_code,
        oip.original_unit_price,
        oip.original_operation_fee,
        oip.original_final_delivery_fee,
        oip.platform_unit_price,
        oip.platform_operation_fee,
        oip.platform_final_delivery_fee
        from orders o
        left join order_item oi on o.id = oi.order_id and oi.del_flag=0
        left join order_item_product_sku oips on oi.id = oips.order_item_id and oips.del_flag=0
        left join order_item_price oip on oi.id = oip.order_item_id and oip.del_flag=0
        <where>
            o.del_flag=0
            <if test="orderNos != null and orderNos.size() != 0">
                and o.order_no in
                <foreach collection="orderNos" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by o.id desc
        <if test="pageSize != 0">
            limit #{page},#{pageSize}
        </if>

    </select>

    <select id="selectOrderItemTrackingRecord"
            resultType="com.zsmall.order.entity.domain.OrderItemTrackingRecord">
        select oitr.*
        from order_item_tracking_record oitr
        inner join ${tableName}  sss
        WHERE sss.value3 = oitr.order_no
        and oitr.del_flag=0
    </select>

    <select id="selectOrderLogisticsInfoByEsInsert"
            resultType="com.zsmall.order.entity.domain.OrderLogisticsInfo">
        select oli.*
        from order_logistics_info oli
        inner join ${tableName}  sss
        WHERE sss.value3 = oli.order_no
        and oli.del_flag=0
    </select>

    <select id="getOrderNos" resultType="java.lang.String">
        select orders.order_no
        from orders
        where del_flag=0
        order by id desc
        limit #{page},#{pageSize}
    </select>


</mapper>
