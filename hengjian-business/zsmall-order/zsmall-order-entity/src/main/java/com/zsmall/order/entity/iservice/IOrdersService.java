package com.zsmall.order.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderItem.ShippingOrderStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.OrderUpdateBo;
import com.zsmall.order.entity.domain.bo.order.OrdersBo;
import com.zsmall.order.entity.domain.bo.order.OrdersPageBo;
import com.zsmall.order.entity.domain.vo.OrderListVo;
import com.zsmall.order.entity.domain.vo.statistics.ChannelSalesStatisticsVo;
import com.zsmall.order.entity.mapper.OrdersMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 主订单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IOrdersService extends ServiceImpl<OrdersMapper, Orders> {

    private final OrdersMapper baseMapper;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderItemService iOrderItemService;
    private final IOrderRefundService iOrderRefundService;
    /**
     * 查询主订单
     */
    public Orders queryById(Long id) {
        return TenantHelper.ignore(() -> baseMapper.selectById(id), TenantType.Supplier, TenantType.Manager);
    }

    /**
     * 查询主订单列表
     */
    public Page<Orders> queryPageList(OrdersPageBo dto, Page<Orders> page) {
        return TenantHelper.ignore(() -> baseMapper.queryPageList(dto, page), TenantType.Supplier, TenantType.Manager);
    }

    /**
     * 根据主单ID集合查询主订单信息集合
     *
     * @param orderIds
     * @return
     */
    public List<Orders> queryListByIds(Set<Long> orderIds) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.in(Orders::getId, orderIds);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw), TenantType.Supplier, TenantType.Manager);
    }

    /**
     * 新增主订单
     */
    public Boolean insertByBo(OrdersBo bo) {
        Orders add = MapstructUtils.convert(bo, Orders.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    public boolean batchSaveOrUpdate(List<Orders> ordersList) {
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        if (tenantTypeEnum != null && tenantTypeEnum.equals(TenantType.Distributor)) {
            return this.saveOrUpdateBatch(ordersList);
        }
        return TenantHelper.ignore(() -> this.saveOrUpdateBatch(ordersList));
    }

    /**
     * 修改主订单
     */
    public Boolean updateByBo(OrdersBo bo) {
        Orders update = MapstructUtils.convert(bo, Orders.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    public Boolean updateNoTenant(Orders orders) {
        return TenantHelper.ignore(() -> baseMapper.updateById(orders)) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Orders entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除主订单
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 查询渠道订单号是否重复（租户维度查重）
     *
     * @param channelOrderNo
     * @param excludeState
     * @return
     */
    public boolean existsChannelOrderNo(String channelOrderNo, OrderStateType excludeState) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.eq(Orders::getChannelOrderNo, channelOrderNo);
        if (excludeState != null) {
            lqw.ne(Orders::getOrderState, excludeState);
        }
        return baseMapper.exists(lqw);
    }

    /**
     * 根据渠道订单号是否重复，指定订单状态及展示订单
     *
     * @param channelOrderNo
     * @param excludeState
     * @param isShow
     * @return
     */
    public Integer existsChannelOrderNoAndOrderStateAndIsShow(String channelOrderNo, OrderStateType excludeState, Boolean isShow) {
        return baseMapper.countChannelOrderNo(channelOrderNo, excludeState.getValue(), isShow);
    }

    public boolean existsOrderNo(String orderNo) {
        return baseMapper.existsOrderNo(orderNo);
    }

    /**
     * 根据orderNo获取orders信息
     *
     * @param orderNo
     * @return
     */
    public Orders getByOrderNo(String orderNo) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.eq(Orders::getOrderNo, orderNo);
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            baseMapper.selectOne(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 根据orderNo获取orders信息
     *
     * @param orderNo
     * @return
     */
    public List<Orders> listByOrderNo(String orderNo) {
        Orders order = getByOrderNo(orderNo);
        String orderExtendId = order.getOrderExtendId();
        return TenantHelper.ignore(() -> getByOrderExtendId(orderExtendId));
    }

    /**
     * 根据订单编号和订单状态获取orders信息（无视租户）
     *
     * @param orderNo
     * @return
     */
    public Orders queryByOrderNoAndStateNotTenant(String orderNo, OrderStateType orderStateType) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.eq(Orders::getOrderNo, orderNo);
        lqw.eq(Orders::getOrderState, orderStateType);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 根据订单编号和订单状态获取orders信息
     *
     * @param orderNoList
     * @param orderStateType
     * @return
     */
    public List<Orders> queryByOrderNoListAndStateIn(List<String> orderNoList, OrderStateType... orderStateType) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.in(Orders::getOrderNo, orderNoList);
        lqw.in(Orders::getOrderState, orderStateType);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据订单号获取Orders信息
     *
     * @param orderNoList
     * @return
     */
    public List<Orders> queryByOrderNoList(List<String> orderNoList) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.in(Orders::getOrderNo, orderNoList).eq(Orders::getDelFlag,0);
        return baseMapper.selectList(lqw);
    }

    public List<Orders> queryByOrderNoListPage(int pageSize, int offset,List<String> orderNoList) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.in(Orders::getOrderNo, orderNoList).eq(Orders::getDelFlag,0).orderByDesc(Orders::getId).orderByDesc(Orders::getCreateTime);
        lqw.last("limit " + pageSize + " offset " + offset);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据订单编号和供应商住户ID获取订单（订单批量发货调用）
     *
     * @param orderNo
     * @param supplierTenantId
     * @return
     */
    public Orders getByOrderNoAndSupplier(String orderNo, String supplierTenantId) {
        return TenantHelper.ignore(() -> baseMapper.getByOrderNoAndSupplier(orderNo, supplierTenantId));
    }

    /**
     * 根据订单编号集合查询指定状态的订单集合
     */
    public List<Orders> findByOrderNoInAndOrderStateIn(List<String> orderNoList, List<OrderStateType> orderStateTypeList) {

        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.in(CollUtil.isNotEmpty(orderNoList), Orders::getOrderNo, orderNoList)
            .in(CollUtil.isNotEmpty(orderStateTypeList), Orders::getOrderState, orderStateTypeList);
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)) {
            return baseMapper.selectList(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 根据渠道订单号获取订单集合
     *
     * @param channelOrderNoList
     * @return
     */
    public List<Orders> findByChannelOrderNoIn(List<String> channelOrderNoList) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.in(CollUtil.isNotEmpty(channelOrderNoList), Orders::getChannelOrderNo, channelOrderNoList);
        lqw.eq(Orders::getDelFlag,0);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 根据渠道订单号和租户id修改 trackingFlag
     *
     * @param channelOrderNo
     * @param tenantId
     */
    public void updateOrderTrackingFlag(String channelOrderNo, String tenantId, Integer trackingFlag){
        baseMapper.updateOrderTrackingFlag(channelOrderNo,trackingFlag,tenantId);
    }

    /**
     * 根据渠道订单号和channelId修改 trackingFlag
     *
     * @param channelOrderNo
     * @param channelId
     * @param trackingFlag
     */
    public void updateOrderTrackingFlagByChannelOrderNoAndChannelId(String channelOrderNo, Long channelId, Integer trackingFlag){
        baseMapper.updateOrderTrackingFlagByChannelOrderNoAndChannelId(channelOrderNo, channelId, trackingFlag);
    }

    /**
     * 根据渠道订单号集合修改 trackingFlag
     *
     * @param channelOrderNoList
     * @param trackingFlag
     */
    public void updateOrderTrackingFlagByChannelOrderNo(List<String> channelOrderNoList,Integer trackingFlag){
        baseMapper.updateOrderTrackingFlagByChannelOrderNo(channelOrderNoList,trackingFlag);

    }

    /**
     * 根据参数查询有效的订单（非取消的）
     * @param channelId
     * @param tenantId
     * @param channelOrderNo
     * @return
     */
    public Orders queryValidOrder(Long channelId, String tenantId, String channelOrderNo) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.eq(Orders::getTenantId, tenantId)
        .eq(Orders::getChannelId, channelId)
        .eq(Orders::getChannelOrderNo, channelOrderNo)
        .ne(Orders::getOrderState, OrderStateType.Canceled);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    public Orders getShippingOrder(String orderNo, OrderStateType orderState, ShippingOrderStateEnum shippingOrderState) {
        return TenantHelper.ignore(() -> baseMapper.getShippingOrder(orderNo, orderState.name(), shippingOrderState.name()));
    }

    @InMethodLog(value = "根据下单时间和状态获取订单数据集合")
    public List<Orders> getOrdersByDate(Date date, List<OrderStateType> orderStateTypes) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.lt(Orders::getCreateTime, date)
            .in(Orders::getOrderState, orderStateTypes);
        if (LoginHelper.getTenantTypeEnum() != null && LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)) {
            return baseMapper.selectList(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    @InMethodLog(value = "根据时间区间查询渠道销售统计（For 管理员）")
    public List<ChannelSalesStatisticsVo> queryChannelSalesStatisticsForMag(Date startDate, Date endDate) {
        return baseMapper.queryChannelSalesStatisticsForMag(startDate, endDate);
    }

    @InMethodLog(value = "根据时间区间查询渠道销售统计（For 分销商）")
    public List<ChannelSalesStatisticsVo> queryChannelSalesStatisticsForDis(Date startDate, Date endDate) {
        return baseMapper.queryChannelSalesStatisticsForDis(startDate, endDate);
    }

    @InMethodLog(value = "根据时间区间查询渠道销售统计（For 供货商）")
    public List<ChannelSalesStatisticsVo> queryChannelSalesStatisticsForSup(String supplierId, Date startDate, Date endDate) {
        return baseMapper.queryChannelSalesStatisticsForSup(supplierId, startDate, endDate);
    }

    @InMethodLog(value = "统计平台应付金额（批发订单）")
    public BigDecimal statsPlatformOrderPaymentAmount4Wholesale(Date startDate, Date endDate) {
        return TenantHelper.ignore(() -> baseMapper.statsPlatformOrderPaymentAmount4Wholesale(startDate, endDate));
    }

    @InMethodLog(value = "根据时间区间查询统计订单信息")
    public List<Orders> getByQuery(OrdersPageBo dto) {
        //AND o.create_time BETWEEN #{dto.startDate} AND #{dto.endDate}
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.between(Orders::getCreateTime,dto.getStartDate(),dto.getEndDate());

        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Distributor)||ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Supplier)) {
            baseMapper.selectList(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    @InMethodLog(value = "根据渠道sku和异常状态获取订单信息")
    public List<Orders> getByChannelSkuAndExceptionCode(List<String> channelSkuList, Integer exceptionCode, OrderStateType orderStateType,OrderSourceEnum orderSourceEnum){
        if(CollUtil.isEmpty(channelSkuList)){
            return null;
        }
        return baseMapper.getByChannelSkuAndExceptionCode(channelSkuList, exceptionCode,orderStateType.getValue(), orderSourceEnum.getValue());
    }

    @InMethodLog(value = "根据渠道sku和异常状态获取订单数量")
    public Integer getByChannelSkuAndExceptionCodeNum(List<String> channelSkuList, Integer exceptionCode, OrderStateType orderStateType,OrderSourceEnum orderSourceEnum){
        if(CollUtil.isEmpty(channelSkuList)){
            return null;
        }
        return baseMapper.getByChannelSkuAndExceptionCodeNum(channelSkuList, exceptionCode,orderStateType.getValue(), orderSourceEnum.getValue());
    }

    @InMethodLog(value = "根据渠道sku和异常状态获取订单信息分页版")
    public List<Orders> getByChannelSkuAndExceptionCodePage(int pageSize, int offset,List<String> channelSkuList, Integer exceptionCode, OrderStateType orderStateType, OrderSourceEnum orderSourceEnum){
        if(CollUtil.isEmpty(channelSkuList)){
            return null;
        }
        return baseMapper.getByChannelSkuAndExceptionCodePage(pageSize,offset,channelSkuList, exceptionCode,orderStateType.getValue(), orderSourceEnum.getValue());
    }

    @InMethodLog(value = "根据订单号和异常状态获取订单信息分页版")
    public List<Orders> getByOrderNoListAndExceptionCodePage(int pageSize, int offset,List<String> orderNoList, Integer exceptionCode, OrderStateType orderStateType, OrderSourceEnum orderSourceEnum){
        if(CollUtil.isEmpty(orderNoList)){
            return null;
        }
        return baseMapper.getByOrderNoListAndExceptionCodePage(pageSize,offset,orderNoList, exceptionCode,orderStateType.getValue(), orderSourceEnum.getValue());
    }


    @InMethodLog("根据订单信息修改订单的异常状态")
    public void updateExceptionCode(List<Orders> ordersList, OrderExceptionEnum orderExceptionEnum){
        LambdaUpdateWrapper<Orders> lqw = Wrappers.lambdaUpdate();
        lqw.set(Orders::getExceptionCode,orderExceptionEnum.product_mapping_exception.getValue())
         .in(Orders::getOrderNo,ordersList.stream().map(Orders::getOrderNo).collect(Collectors.toList()));
        baseMapper.update(null,lqw);
        batchSaveOrUpdate(ordersList);
    }

    @InMethodLog("根据订单id集合和订单来源获取订单信息")
    public List<Orders> getListByIdListAndOrderSource(List<Long> orderIdList,OrderSourceEnum orderSourceEnum,OrderExceptionEnum orderExceptionEnum){
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.in(Orders::getId, orderIdList);
        lqw.eq(Orders::getOrderSource, orderSourceEnum.getValue());
        lqw.eq(Orders::getDelFlag, "0");
        if(null != orderExceptionEnum){
            lqw.eq(Orders::getExceptionCode,orderExceptionEnum.getValue());
        }
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 功能描述：将批更新为null
     *
     * @param ids 没有最终运费的编号
     * <AUTHOR>
     * @date 2024/07/31
     */
    @InMethodLog("根据订单id集合将批更新为null")
    public void updateBatchToNull(List<Long> ids,Boolean coveredActualPayment) {

        LambdaUpdateWrapper<Orders> ordersLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        // 如果是渠道店铺PlatformActualTotalAmount 不能设置为null
        ordersLambdaUpdateWrapper.set(Orders::getOriginalTotalFinalDeliveryFee,null)
                                 .set(Orders::getOriginalTotalDropShippingPrice,null)
                                 .set(Orders::getOriginalPayableTotalAmount,null)
                                 .set(Orders::getOriginalActualTotalAmount,null)
                                 .set(Orders::getOriginalRefundExecutableAmount,null)
                                 .set(Orders::getPlatformTotalFinalDeliveryFee,null)
                                 .set(Orders::getPlatformTotalDropShippingPrice,null)
                                 .set(Orders::getPlatformPayableTotalAmount,null)
                                 .set(Orders::getPlatformRefundExecutableAmount,null);


        if(coveredActualPayment){
            ordersLambdaUpdateWrapper.set(Orders::getPlatformActualTotalAmount,null);
        }
        ordersLambdaUpdateWrapper.in(Orders::getId,ids);
        baseMapper.update(null,ordersLambdaUpdateWrapper);

    }


    /**
     * 功能描述：更新批次或设置为空
     *
     * @param updateOrderList 更新订单列表
     * @param codesMap        代码映射
     * <AUTHOR>
     * @date 2024/08/01
     */
    @InMethodLog("异常订单价格更新为null by updateBatchOrSetNull")
    public void updateBatchOrSetNull(List<Orders> updateOrderList, HashMap<String, Integer> codesMap) {
        if(CollUtil.isNotEmpty(updateOrderList)){
            updateOrderList = updateOrderList.stream().distinct().collect(Collectors.toList());
            updateBatchById(updateOrderList);
        }
        //updateOrderList转换为map,key是orderNo,value是对应元素
        Map<String, Orders> orderMap = updateOrderList.stream()
                                                      .collect(Collectors.toMap(Orders::getOrderNo, Function.identity()));
        ArrayList<Orders> orders = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : codesMap.entrySet()) {
            // 考虑一点,库存异常实际也是没有尾程派送费的 后续会补充 tag lty todo 自提的库存异常是不用额外操作的
            if (OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(entry.getValue())
                ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(entry.getValue())
                ||OrderExceptionEnum.measurement_anomaly.getValue().equals(entry.getValue())) {
                orders.add(orderMap.get(entry.getKey()));
            }
        }
        if (CollUtil.isNotEmpty(updateOrderList)) {
            updateBatchById(updateOrderList);
        }
        // 二次更新订单状态
        if (CollUtil.isNotEmpty(orders)) {
            // 筛选出orders中orderSource不等于1或者4返回元素的id加入List<Long> ids
            List<Long> idsYes = orders.stream()
                                   .filter(order -> order.getOrderSource() != 1 && order.getOrderSource() != 4)
                                   .map(Orders::getId)
                                   .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(idsYes)) {
                updateBatchToNull(idsYes,true);
            }
            List<Long> idsNo = orders.stream()
                                   .filter(order -> order.getOrderSource() == 1 || order.getOrderSource() == 4)
                                   .map(Orders::getId)
                                   .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(idsNo)) {
                updateBatchToNull(idsNo,false);
            }
        }
    }

    /**
     * 功能描述：更新或设置为空,orderService内有
     *
     * @param orders             订单
     * @param orderExceptionCode 订单例外代码
     * <AUTHOR>
     * @date 2024/08/14
     */
    @InMethodLog("异常订单价格更新为null by updateOrSetNull")
    public void updateOrSetNull(List<Orders> orders, Integer orderExceptionCode) {
        List<Long> idsYes = new ArrayList<>();
        List<Long> idsNo = new ArrayList<>();

        orders.forEach(s->{
            if (OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(orderExceptionCode)
                ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(orderExceptionCode)
                ||OrderExceptionEnum.measurement_anomaly.getValue().equals(orderExceptionCode)) {
                if(!s.getOrderSource().equals(OrderSourceEnum.INTERFACE_ORDER.getValue())&&!s.getOrderSource().equals(OrderSourceEnum.OPEN_API_ORDER.getValue())){
                    idsYes.add(s.getId());
                }
                if(s.getOrderSource().equals(OrderSourceEnum.INTERFACE_ORDER.getValue())||s.getOrderSource().equals(OrderSourceEnum.OPEN_API_ORDER.getValue())){
                    idsNo.add(s.getId());
                }
            }
        });
        if(ObjectUtil.isNotEmpty(idsYes)){
            if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(orderExceptionCode)
                ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(orderExceptionCode)
                ||OrderExceptionEnum.measurement_anomaly.getValue().equals(orderExceptionCode)){
                updateBatchToNull(idsYes,true);
            }
        }
        if(ObjectUtil.isNotEmpty(idsNo)){
            if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(orderExceptionCode)
                ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(orderExceptionCode)
                ||OrderExceptionEnum.measurement_anomaly.getValue().equals(orderExceptionCode)){
                updateBatchToNull(idsNo,false);
            }
        }

    }


    @InMethodLog("异常订单价格更新为null没引用测试")
    public void updateOrSetNull(Orders orders) {
        if(ObjectUtil.isNotEmpty(orders)&&(
            OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(orders.getExceptionCode()))
            ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(orders.getExceptionCode())
            ||OrderExceptionEnum.measurement_anomaly.getValue().equals(orders.getExceptionCode())){
            Integer orderSource = orders.getOrderSource();
            if(!OrderSourceEnum.INTERFACE_ORDER.getValue().equals(orderSource)&&!OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource)){
                updateBatchToNull(Collections.singletonList(orders.getId()),true);
            }
            if(OrderSourceEnum.INTERFACE_ORDER.getValue().equals(orderSource)||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource)){
                updateBatchToNull(Collections.singletonList(orders.getId()),false);
            }
        }else {
            // 更新订单价格信息
            if (ObjectUtil.isNotEmpty(orders)) {
                updateNoTenant(orders);
            }
        }
    }
/**
 * @description:  订单列表查询
 * @author: len
*  @date: 2024/8/14 17:54
 * @param: dto
 * @param: page
 * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.zsmall.order.entity.domain.vo.OrderListVo>
 **/
    public List<OrderListVo> queryOrderListVoPageList(OrdersPageBo dto,Integer page, Integer limit) {
        return TenantHelper.ignore(() -> baseMapper.queryOrderListVoPageList(dto, page,limit));
    }

    public List<Long> getOrderIdBySkuQuery(String sku, String productName) {
        return TenantHelper.ignore(()->baseMapper.getOrderIdBySkuQuery(sku,productName));
    }

    public List<Long> getOrderIdByActivityQuery(String tenantType,String activityCode) {
        return TenantHelper.ignore(()->baseMapper.getOrderIdByActivityQuery(tenantType,activityCode)) ;
    }

    public List<Long> getOrderIdByTrackingNoQuery(String trackingNo) {
        return TenantHelper.ignore(()->baseMapper.getOrderIdByTrackingNoQuery(trackingNo)) ;

    }

    public Integer countByOrderListVo(OrdersPageBo dto) {
        return TenantHelper.ignore(()->baseMapper.countByOrderListVo(dto)) ;
    }

    /**
     * 上传快递标签
     *
     * @param bo
     * @return
     */
    public R<Void> uploadShippingLabel(OrderUpdateBo bo) {
        String orderNo = bo.getOrderNo();
        Orders order = this.getByOrderNo(orderNo);
        if (order == null) {
            return R.fail(ZSMallStatusCodeEnum.ORDER_NOT_EXIST_OR_REVIEW);
        }
        OrderLogisticsInfo orderLogisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
        boolean isUpload = bo.isUpload();
        MultipartFile multipartFile;
        SysOssVo sysOssVo =null;
        if(!isUpload){
            multipartFile= bo.getMultipartFile();
            if (multipartFile == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }
            OSSUploadEvent ossUploadEvent = new OSSUploadEvent(multipartFile);
            SpringUtils.context().publishEvent(ossUploadEvent);
            sysOssVo = ossUploadEvent.getSysOssVo();
        }else{
            sysOssVo = bo.getSysOssVo();
        }

        String originalName = sysOssVo.getOriginalName();
        String suffix = FileUtil.getSuffix(originalName);

        OrderAttachment newOrderAttachment = new OrderAttachment();
        newOrderAttachment.setOssId(sysOssVo.getOssId());
        newOrderAttachment.setOrderNo(orderNo);
        newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
        newOrderAttachment.setAttachmentOriginalName(originalName);
        newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
        newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
        newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
        newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.ShippingLabel);

        iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
        iOrderAttachmentService.save(newOrderAttachment);

        orderLogisticsInfo.setShippingLabelExist(true);
        iOrderLogisticsInfoService.updateById(orderLogisticsInfo);
        return R.ok();
    }
    public List<Orders> getByOrderExtendId(String orderExtendId) {
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<Orders>().eq(Orders::getOrderExtendId, orderExtendId)
                                                                             .eq(Orders::getDelFlag, 0);
        return TenantHelper.ignore(()->baseMapper.selectList(wrapper)) ;
    }

    public void cancelOrder(String orderNo) {
        log.info("取消订单,订单号:{},当前时间:{}", orderNo, LocalDateTime.now()
                                                                         .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        LambdaUpdateWrapper<Orders> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Orders::getCancelStatus, OrderCancelStateEnum.Canceling)
                     .eq(Orders::getOrderExtendId, orderNo);
        update(updateWrapper);

    }

    /**
     * 功能描述：按订单号获取供应商id
     *
     * @param orderNo 订单号
     * @return {@link String }
     * <AUTHOR>
     * @date 2025/03/12
     */
    public String getSupplierIdByOrderNo(String orderNo) {
        List<Orders> byOrderExtendId = getByOrderExtendId(orderNo);
        Orders order = byOrderExtendId.get(0);

        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(order.getId());
        String supplierTenantId = orderItems.get(0).getSupplierTenantId();
        return supplierTenantId;
    }

    /**
     * 功能描述：取消失败/拒绝
     *
     * @param orderExtendId 订单扩展id
     * <AUTHOR>
     * @date 2025/03/13
     */
    public void cancelFailed(String orderExtendId) {
        LambdaUpdateWrapper<Orders> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Orders::getCancelStatus,3);
        updateWrapper.in(Orders::getOrderExtendId,orderExtendId);
        update(updateWrapper);
    }

    /**
     * 功能描述：取消成功
     *
     * @param orderExtendId 订单扩展id
     * <AUTHOR>
     * @date 2025/03/13
     */
    public void cancelSuccess(String orderExtendId) {
        LambdaUpdateWrapper<Orders> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Orders::getCancelStatus,2);
        updateWrapper.in(Orders::getOrderExtendId,orderExtendId);
        update(updateWrapper);
    }

    /**
     * 功能描述：取消异常
     *
     * @param orderExtendId 订单扩展id
     * <AUTHOR>
     * @date 2025/03/26
     */
    public void cancelAbnormal(String orderExtendId) {
        LambdaUpdateWrapper<Orders> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Orders::getCancelStatus,4);
        updateWrapper.in(Orders::getOrderExtendId,orderExtendId);
        update(updateWrapper);
    }


    public List<Orders> getListByChannelOrderNoAndTenantId(List<String> channelNos, String tenantId) {
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.in(Orders::getChannelOrderNo, channelNos);
        lqw.eq(Orders::getTenantId, tenantId);
        lqw.eq(Orders::getDelFlag, 0);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));

    }

    public Orders getByOrderRefundNo(String orderRefundNo) {
        OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
        String orderNo = orderRefund.getOrderNo();
        LambdaQueryWrapper<Orders> lqw = Wrappers.lambdaQuery();
        lqw.eq(Orders::getOrderNo, orderNo);
        lqw.eq(Orders::getDelFlag, 0);
        return baseMapper.selectOne(lqw);
    }
    /**
     * 功能描述：批量更新跟踪to已履约
     *
     * @param distinctOrderNos 不同订单号
     * <AUTHOR>
     * @date 2025/03/31
     */
    public void batchUpdateTracking2Success(List<String> distinctOrderNos) {
        LambdaUpdateWrapper<Orders> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(Orders::getOrderNo, distinctOrderNos);
        wrapper.set(Orders::getFulfillmentProgress, LogisticsProgress.Dispatched);
        TenantHelper.ignore(()->update(wrapper));
    }

    public Boolean isAllPaid(String orderExtendId) {
        // 如果是LTL订单会存在子单,子单的业务情况需要统一,都为支付,未支付或支付失败,,如果存在
        List<Orders> orders = getByOrderExtendId(orderExtendId);
        if (CollUtil.isNotEmpty(orders)) {
            boolean allPaid = orders.stream()
                                    .allMatch(order -> order.getOrderState() == OrderStateType.Paid);
            Set<OrderStateType> allowedStates = Set.of(
                OrderStateType.UnPaid,
                OrderStateType.Failed
            );

            // 3. 校验所有订单状态是否合法
            boolean hasInvalidState = orders.stream()
                                                   .map(Orders::getOrderState)
                                                   .allMatch(allowedStates::contains);

            if (allPaid) {
                // 全部为已支付状态的业务逻辑
                return true;
            } else if (hasInvalidState) {
                // 全部为未支付或失败状态的业务逻辑
                return false;
            } else {
                // 混合状态报错处理
                throw new IllegalStateException("子订单状态不一致,请先同步子订单状态");
            }
        } else {
            throw new RuntimeException("订单信息异常");
        }

    }

    /**
     * 功能描述：按顺序获取扩展ID
     *
     * @param orderExtendIds 订单扩展ID
     * @return {@link List }<{@link Orders }>
     * <AUTHOR>
     * @date 2025/08/01
     */
    public List<Orders> getByOrderExtendIdsNoTenant(List<String> orderExtendIds) {
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Orders::getOrderExtendId,orderExtendIds);
        wrapper.eq(Orders::getDelFlag,0);
        return TenantHelper.ignore(()->baseMapper.selectList(wrapper));
    }

    /**
     * 功能描述：获取订单号和通道id映射
     *
     * @param orderNoList 订单编号
     * @return {@link Map }<{@link String }, {@link Long }>
     * <AUTHOR>
     * @date 2025/08/11
     */
    public Map<String, Long> getOderNoAndChannelIdMap(List<String> orderNoList) {
        LambdaQueryWrapper<Orders> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Orders::getOrderNo, orderNoList);
        List<Orders> orders = baseMapper.selectList(wrapper);
        Map<String, Long> resultMap = new HashMap<>();
        if (CollUtil.isEmpty(orders)) {
            return resultMap;
        }

        for (Orders order : orders) {
            if (ObjectUtil.isNotEmpty(order.getChannelId())) {
                // 直接put，但添加日志记录重复情况（可选）
                if (resultMap.containsKey(order.getOrderNo())) {
                    log.warn("发现重复订单号: {}，将覆盖旧值", order.getOrderNo());
                }
                resultMap.put(order.getOrderNo(), order.getChannelId());
            }
        }
        return resultMap;
    }
}
