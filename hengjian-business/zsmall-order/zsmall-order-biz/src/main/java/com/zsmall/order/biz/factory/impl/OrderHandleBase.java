package com.zsmall.order.biz.factory.impl;

import com.zsmall.activity.entity.iservice.IProductActivityService;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.biz.service.OrderRefundRuleService;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.iservice.IProductSkuAttributeService;
import com.zsmall.product.entity.iservice.IProductSkuDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 订单处理基类
 *
 * <AUTHOR>
 * @date 2023/3/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderHandleBase {

    private static final String dateFormat = "yyyy-MM-dd HH:mm:ss";

    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderRefundService iOrderRefundService;
    private final IOrderRefundItemService iOrderRefundItemService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final OrderRefundRuleService orderRefundRuleService;


    private final IProductActivityService iProductActivityService;
    // 业务支持类
    private final BusinessParameterService businessParameterService;

}
