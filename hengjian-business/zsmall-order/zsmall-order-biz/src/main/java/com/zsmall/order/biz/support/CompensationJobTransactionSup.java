package com.zsmall.order.biz.support;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrderItemShippingRecordService;
import com.zsmall.order.entity.iservice.IOrdersService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/10/29 15:30
 */
@Service
@RequiredArgsConstructor
public class CompensationJobTransactionSup {

    private final IOrdersService iOrdersService;
    private final IOrderItemShippingRecordService iOrderItemShippingRecordService;
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchNeedCompensationOrderNos(List<String> needCompensationOrderNos) {
        // 更新主表 needCompensationOrderNos内的是orderExtendId
        LambdaUpdateWrapper<Orders> updateStatusWrapper = new LambdaUpdateWrapper<Orders>().in(Orders::getOrderExtendId, needCompensationOrderNos)
                                                                           .set(Orders::getChannelReceiptStatus, "Success")
                                                                           .set(Orders::getFulfillmentProgress, "UnDispatched")
                                                                           .set(Orders::getExceptionCode,0 );
        LambdaQueryWrapper<Orders> ordersWrapper = new LambdaQueryWrapper<Orders>()
            .in(Orders::getOrderExtendId, needCompensationOrderNos);

        List<Orders> orders = iOrdersService.list(ordersWrapper);
        List<String> orderNos = orders.stream().map(Orders::getOrderNo).collect(Collectors.toList());

        LambdaQueryWrapper<OrderItemShippingRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OrderItemShippingRecord::getOrderNo, orderNos);

        List<OrderItemShippingRecord> list = iOrderItemShippingRecordService.list(lambdaQueryWrapper);

        List<Long> ids = list.stream().map(OrderItemShippingRecord::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<OrderItemShippingRecord> unDispatched = new LambdaUpdateWrapper<OrderItemShippingRecord>().in(OrderItemShippingRecord::getId, ids)
                                                                                                                      .set(OrderItemShippingRecord::getShippingErrorCode, null)
                                                                                                                      .set(OrderItemShippingRecord::getShippingState, ShippingStateEnum.Unshipped)
                                                                                                                      .set(OrderItemShippingRecord::getShippingErrorMessage, null)
                                                                                                                      .set(OrderItemShippingRecord::getInCreateJson, null);
        iOrdersService.update(updateStatusWrapper);
        // 更新发货推送表
        iOrderItemShippingRecordService.update(unDispatched);
    }
}
