package com.zsmall.order.biz.anno.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokDistrictInfo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.OpenApiEnum;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.ImagesUtil;
import com.zsmall.common.util.PDFUtil;
import com.zsmall.order.biz.anno.annotaion.OpenApiParamCheck;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.system.entity.domain.ConfZip;
import com.zsmall.system.entity.iservice.IConfZipService;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/13 14:25
 */
@Aspect
@Component
@RequiredArgsConstructor
public class OpenApiDTOCheckAspect {
    private final FileProperties fileProperties;
    @Resource
    private IConfZipService iConfZipService;
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;
    @Before("@annotation(openApiParamCheck)")
    public void before(JoinPoint joinPoint, OpenApiParamCheck openApiParamCheck) throws NoSuchAlgorithmException {
        OpenApiEnum value = openApiParamCheck.value();
        if (ObjectUtil.isEmpty(value)) {
            throw new RuntimeException("value is empty!");
        }
        if (OpenApiEnum.CREATE_ORDER.equals(value)) {
            createOrderFlowCheck(joinPoint);
        }
        if (OpenApiEnum.RECEIVE_ORDER_ATTACHMENT.equals(value)) {
            receiveOrderAttachmentCheck(joinPoint);
        }
    }

    /**
     * 功能描述：接收订单附件检查
     *
     * @param joinPoint 连接点
     * <AUTHOR>
     * @date 2025/01/09
     */
    private void receiveOrderAttachmentCheck(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (ObjectUtil.isNotEmpty(arg) && arg instanceof List) {
                List<OrderReceiveAttachmentDTO> orderAttachmentDTOS = (List<OrderReceiveAttachmentDTO>) arg;
                // 订单号集合模糊查询大于等于
                if (CollUtil.isEmpty(orderAttachmentDTOS)) {
                    throw new RuntimeException("The attachment is empty!");
                }
                // 如果 orderAttachmentDTOS内存在fileType为2的类型且数量大于1,则报错

                for (OrderReceiveAttachmentDTO orderAttachmentDTO : orderAttachmentDTOS) {
                    String orderNo = orderAttachmentDTO.getOrderNo();
                    List<OrderAttachmentDTO> attachments = orderAttachmentDTO.getAttachments();
                    if(CollUtil.isEmpty(attachments)){
                        throw new RuntimeException("Attachment information required !OrderNo:"+orderNo);
                    }
                    // attachments内

                    if (attachments.stream()
                                   .filter(Objects::nonNull)
                                   .mapToInt(OrderAttachmentDTO::getFileType)
                                   .filter(type -> type == 2)
                                   .limit(2)    // 当找到两个时停止
                                   .count() > 1) {
                        throw new RuntimeException("Only one bol type attachment is allowed to be uploaded");
                    }

                    boolean exists = attachments.stream()
                                                .map(OrderAttachmentDTO::getFileType)
                                                .anyMatch(fileType -> Arrays.stream(OrderAttachmentTypeEnum.values())
                                                                            .anyMatch(enumValue -> enumValue.getCode().equals(fileType)));
                    if (!exists) {
                        throw new RuntimeException("The specified label does not exist ,OrderNo:"+orderNo);
                    }
                    // 判断附件url是否为空,用stream流
                    boolean hasEmptyUrl = attachments.stream()
                                                     .map(OrderAttachmentDTO::getUrl)
                                                     .anyMatch(ObjectUtil::isEmpty);
                    if (hasEmptyUrl) {
                        throw new RuntimeException("url is empty!OrderNo:"+orderNo);
                    }
                    // 判断附件名称是否存在
                    boolean allAttachmentsHaveNames = attachments.stream()
                                                                 .allMatch(attachment -> ObjectUtil.isNotEmpty(attachment.getName()));

                    if (!allAttachmentsHaveNames) {
                        throw new RuntimeException("Attachment name is required ,OrderNo:" + orderNo);
                    }
                }
                List<String> orderNos = orderAttachmentDTOS.stream().map(OrderReceiveAttachmentDTO::getOrderNo)
                                                          .collect(Collectors.toList());

                LambdaQueryWrapper<Orders> queryWrapper = Wrappers.lambdaQuery();
                if (CollUtil.isNotEmpty(orderNos)) {
                    // 转换 orderNos 集合为以 OR 连接的条件字符串
                    String orConditions = orderNos.stream()
                                                  .map(orderNo -> "(order_no = '" + orderNo + "' OR order_extend_id = '" + orderNo + "')")
                                                  .collect(Collectors.joining(" OR "));

                    // 应用转换后的 OR 条件字符串到 queryWrapper
                    queryWrapper.apply(orConditions);
                }
                // 查询订单
                List<Orders> orders = TenantHelper.ignore(()->iOrdersService.list(queryWrapper));
                if (CollUtil.isEmpty(orders)) {
                    throw new RuntimeException("Please check your order number!");
                }
                if(orders.size()<orderNos.size()){
                    throw new RuntimeException("Please check your order number!");
                }
            }
        }
    }


    /**
     * 功能描述：创建订单流检查
     *
     * @param joinPoint 连接点
     * <AUTHOR>
     * @date 2025/01/09
     */
    private void createOrderFlowCheck(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        String methodName = joinPoint.getSignature().getName();
        for (Object arg : args) {

            if(ObjectUtil.isNotEmpty(arg)&& arg instanceof OrderReceiveFromThirdDTO){

                String orderNo = ((OrderReceiveFromThirdDTO) arg).getOrderNo();
                if (ObjectUtil.isEmpty(orderNo)){
                    throw new RuntimeException("orderNo is empty!");
                }
                ChannelTypeEnum channelType = ((OrderReceiveFromThirdDTO) arg).getChannelType();
                if (ObjectUtil.isEmpty(channelType)){
                    throw new RuntimeException("The channel is empty or not supported!");
                }

                boolean exists = Arrays.stream(ChannelTypeEnum.values())
                                       .anyMatch(e -> e.name().equals(channelType.name()));

                if(!exists){
                    throw new RuntimeException("The specified name does not exist in the ChannelTypeEnum.");
                }

                int totalQuantity = ((OrderReceiveFromThirdDTO) arg).getTotalQuantity();
                if (ObjectUtil.isEmpty(totalQuantity)||totalQuantity <= 0){
                    throw new RuntimeException("totalQuantity is less than or equal to 0!");
                }

                String currencyCode = ((OrderReceiveFromThirdDTO) arg).getCurrencyCode();
                if (ObjectUtil.isEmpty(currencyCode)){
                    throw new RuntimeException("currencyCode is empty!");
                }

//                Date createTime = ((OrderReceiveFromThirdDTO) arg).getCreateTime();
                String logisticsType = ((OrderReceiveFromThirdDTO) arg).getLogisticsType();
                if(ObjectUtil.isEmpty(logisticsType)){
                    throw new RuntimeException("logisticsType is empty!");
                }
                boolean logistics = Arrays.stream(LogisticsTypeEnum.values())
                                       .anyMatch(e -> e.name().equals(logisticsType));
                if(!logistics){
                    throw new RuntimeException("Unsupported logistics channels!");
                }

                SaleOrderDetailDTO saleOrderDetails = ((OrderReceiveFromThirdDTO) arg).getSaleOrderDetails();

                String carrier = saleOrderDetails.getCarrier();
                if("XPO".equalsIgnoreCase(carrier)){
                    throw new RuntimeException("不支持XPO发货方式");
                }

                String logisticsTrackingNo = saleOrderDetails.getLogisticsTrackingNo();
                List<AttachInfo> attachInfoItems = saleOrderDetails.getAttachInfoItems();
                Integer isNeedDelivery = saleOrderDetails.getIsNeedDelivery();
                if(ObjectUtil.isEmpty(isNeedDelivery)){
                    throw new RuntimeException("isNeedDelivery is empty!");
                }

                List<SaleOrderItemDTO> saleOrderItemsList = ((OrderReceiveFromThirdDTO) arg).getSaleOrderItemsList();
                if("PickUp".equals(logisticsType)){
                    if(ChannelTypeEnum.BestBuy.equals(channelType)){
                        if (!CarrierTypeEnum.FedEx.equalsIgnoreCase(carrier)){
                            throw new RuntimeException("The carrier is incorrect. The default carrier for the BestBuy channel is Fedex");
                        }
                    }
                    if("LTL".equals(carrier)){
                        // 此处校验需要兼容放开的场景
//                        if(attachInfoItems.size()<3){
//                            throw new RuntimeException("LTL carrier's order, must have cartonlabel, palletlabel, itemlabel!");
//                        }
                        int countLabel = 0;
                        int countBol = 0;
                        int countCarton = 0;
                        int countPallet = 0;
                        int countItem = 0;
                        if(CollUtil.isNotEmpty(attachInfoItems)){
                            Iterator<AttachInfo> iterator = attachInfoItems.iterator();
                            while (iterator.hasNext()) {
                                AttachInfo attachInfoItem = iterator.next();
                                if(ObjectUtil.isEmpty(attachInfoItem.getUrl())){
                                    iterator.remove();
                                }
                                if(ObjectUtil.isEmpty(attachInfoItem.getFileType())){
                                    attachInfoItem.setFileType(OrderAttachmentTypeEnum.ShippingLabel.getCode());
                                }
                                String url = attachInfoItem.getUrl();
                                if(ObjectUtil.isNotEmpty(attachInfoItem.getFileType())){
                                    if(ObjectUtil.isNotEmpty(attachInfoItem.getFileType())){
                                        if(OrderAttachmentTypeEnum.ShippingLabel.getCode().equals(attachInfoItem.getFileType())){
                                            countLabel++;
                                        }
                                    }
                                    if(ObjectUtil.isNotEmpty(attachInfoItem.getFileType())){
                                        if(OrderAttachmentTypeEnum.BOL.getCode().equals(attachInfoItem.getFileType())){
                                            countBol++;
                                        }
                                    }
                                    if(OrderAttachmentTypeEnum.CartonLabel.getCode().equals(attachInfoItem.getFileType())){
                                        if(StrUtil.isNotEmpty(url)){
                                            countCarton++;
                                        }else {
                                            continue;
                                        }

                                    }
                                    if(OrderAttachmentTypeEnum.PalletLabel.getCode().equals(attachInfoItem.getFileType())){
                                        if(StrUtil.isNotEmpty(url)){
                                            countPallet++;
                                        }else {
                                            continue;
                                        }

                                    }
                                    if(OrderAttachmentTypeEnum.ItemLabel.getCode().equals(attachInfoItem.getFileType())){
                                        if(StrUtil.isNotEmpty(url)){
                                            countItem++;
                                        }else {
                                            continue;
                                        }
                                    }
                                }
                            }
                            if(countBol>1){
                                throw new RuntimeException("Only one Bol attachment is supported");
                            }
                        }

                        // 此处校验需要兼容放开的场景
//                        if(countCarton==0||countPallet==0||countItem==0){
//                            throw new RuntimeException("For LTL orders, you must have must have cartonlabel, palletlabel, itemlabel !");
//                        }

                    }else{
                        if(saleOrderItemsList.size()>1){
                            throw new RuntimeException("For non-LTL carriers, only one SKU is supported");
                        }
                        // po小件改造要求放开附件数量校验
//                        if(CollUtil.isNotEmpty(attachInfoItems)&&attachInfoItems.size()>1){
//                            throw new RuntimeException("Only single attachments are allowed to be uploaded!");
//                        }
                        // 如果附件没有类型,默认给0
                        // 附件总页数要和sku数量一致
                        SaleOrderItemDTO itemDTO = saleOrderItemsList.get(0);
                        Integer quantity = itemDTO.getQuantity();
                        int pageSize = 0;
                        int countCarton = 0;
                        int countPallet = 0;
                        int countItem = 0;
                        ArrayList<AttachInfo> newLists = new ArrayList<>();
                        // 如果路径为空,移除
                        if(CollUtil.isEmpty(attachInfoItems)){
                            throw new RuntimeException("For non-LTL orders, attachments are necessary");
                        }
                        Iterator<AttachInfo> iterator = attachInfoItems.iterator();
                        while (iterator.hasNext()) {
                            AttachInfo attachInfoItem = iterator.next();
                            if(ObjectUtil.isEmpty(attachInfoItem.getUrl())){
                                iterator.remove();
                            }
                            if(ObjectUtil.isEmpty(attachInfoItem.getFileType())){
                                attachInfoItem.setFileType(OrderAttachmentTypeEnum.ShippingLabel.getCode());
                            }
                            String url = attachInfoItem.getUrl();
                            if(ObjectUtil.isNotEmpty(attachInfoItem.getFileType())){
                                if(OrderAttachmentTypeEnum.CartonLabel.getCode().equals(attachInfoItem.getFileType())){
                                    if(StrUtil.isNotEmpty(url)){
                                        countCarton++;
                                    }else {
                                        continue;
                                    }

                                }
                                if(OrderAttachmentTypeEnum.PalletLabel.getCode().equals(attachInfoItem.getFileType())){
                                    if(StrUtil.isNotEmpty(url)){
                                        countPallet++;
                                    }else {
                                        continue;
                                    }

                                }
                                if(OrderAttachmentTypeEnum.ItemLabel.getCode().equals(attachInfoItem.getFileType())){
                                    if(StrUtil.isNotEmpty(url)){
                                        countItem++;
                                    }else {
                                        continue;
                                    }
                                }
                            }
                            //
                            if(OrderAttachmentTypeEnum.ShippingLabel.getCode().equals(attachInfoItem.getFileType())){
                                // todo 线上放开
//                                String pdfUrl = ImagesUtil.downloadPdf(url);
                                InputStream shippingLabel ;
                                try {
                                    shippingLabel = ImagesUtil.downloadPdfAsStream(url);
                                } catch (Exception e) {
                                    throw new RuntimeException("File download failure!");
                                }

                                List<String> base64List = null;
                                try {
                                    base64List = PDFUtil.splitPdfToBase64(shippingLabel, 1);
                                    pageSize = pageSize+ base64List.size();
                                } catch (Exception e) {
                                    throw new RuntimeException("Failed to split the PDF file!");
                                }
                            }
                            newLists.add(attachInfoItem);
                        }
                        if(!Objects.equals(quantity, pageSize)){
                            throw new RuntimeException("The shippingLabel does not match the number of SKUs!");
                        }
                        if(countCarton>1){
                            throw new RuntimeException("A maximum of one CartonLabel attachment is supported!");
                        }
                        if(countPallet>1){
                            throw new RuntimeException("A maximum of one PalletLabel attachment is supported!");
                        }
                        if(countItem>1){
                            throw new RuntimeException("A maximum of one ItemLabel attachment is supported!");
                        }

                    }
                }else {
                    if(ChannelTypeEnum.BestBuy.equals(channelType)){
                        saleOrderDetails.setCarrier(CarrierTypeEnum.FedEx.name());
                    }
                    if(saleOrderItemsList.size()>1){
                        throw new RuntimeException("For non-LTL carriers, only one SKU is supported");
                    }
                    if(CollUtil.isNotEmpty(attachInfoItems)){
                        throw new RuntimeException("DropShipping mode does not require attachment information");
                    }
                }

                for (SaleOrderItemDTO itemDTO : saleOrderItemsList) {

                    String erpSku = itemDTO.getErpSku();
                    if(ObjectUtil.isEmpty(erpSku)){
                        throw new RuntimeException("erpSku is empty!");
                    }
                    String regex = "^HJ\\d{6,8}$";
                    Pattern pattern = Pattern.compile(regex);
                    boolean matches = pattern.matcher(erpSku).matches();
                    if (!matches){
                        throw new RuntimeException("erpSku is not in the correct format!");
                    }
                    Integer quantity = itemDTO.getQuantity();
                    if(ObjectUtil.isEmpty(quantity)||quantity <= 0){
                        throw new RuntimeException("quantity is less than or equal to 0!");
                    }

                    BigDecimal unitPrice = itemDTO.getUnitPrice();
                    if(ObjectUtil.isEmpty(unitPrice)||unitPrice.compareTo(BigDecimal.ZERO) < 0){
                        throw new RuntimeException("The unit price cannot be lower than 0!");
                    }
                }

                TikTokRecipientAddress address = ((OrderReceiveFromThirdDTO) arg).getAddress();
                String addressLine1 = address.getAddressLine1();
                String name = address.getName();
                if (ObjectUtil.isEmpty(name)||"null".equals(name)){
                    address.setName("xxx");
//                    throw new RuntimeException("name is empty!");
                }
                if (ObjectUtil.isEmpty(addressLine1)||"null".equals(addressLine1)){
                    address.setAddressLine1("xxx");
//                    throw new RuntimeException("addressLine1 is empty!");
                }

                if (ObjectUtil.isEmpty(address)){
                    throw new RuntimeException("address is empty!");
                }
                String phoneNumber = address.getPhoneNumber();
                if (ObjectUtil.isEmpty(phoneNumber)){
                    throw new RuntimeException("phoneNumber is empty!");
                }

                List<TikTokDistrictInfo> districtInfo = address.getDistrictInfo();
                if("PickUp".equals(logisticsType)&&ObjectUtil.isEmpty(districtInfo)){
                    throw new RuntimeException("Pickup mode, requires address information!");
                }
                for (TikTokDistrictInfo tikTokDistrictInfo : districtInfo) {
                    String addressLevelName = tikTokDistrictInfo.getAddressLevelName();
                    String addressName = tikTokDistrictInfo.getAddressName();
//                    country/state/Federal District/county/city

                    if (LogisticsTypeEnum.DropShipping.name().equals(logisticsType)){
                        String regionCode = address.getRegionCode();
                        if(StrUtil.isNotEmpty(regionCode)&&"US".equals(regionCode)){
                            // 判断地区是否是美国,非美国不校验
                            if (ObjectUtil.isEmpty(addressLevelName)){
                                throw new RuntimeException("addressLevelName is empty!");
                            }
                            List<String> addressLevels = Arrays.asList("country", "state", "Federal District", "county", "city");
                            if (!addressLevels.contains(addressLevelName)){
                                throw new RuntimeException("addressLevelName is not in the correct format!");
                            }
                            if("city".equals(addressLevelName)&&ObjectUtil.isEmpty(addressName)&&null==addressName){
                                throw new RuntimeException("city is empty!");
                            }
                            if("state".equals(addressLevelName)){
                                // state 的要查表
                                LambdaQueryWrapper<ConfZip> wrapper = new LambdaQueryWrapper<ConfZip>().eq(ConfZip::getStateCode, addressName);
                                List<ConfZip> list = iConfZipService.list(wrapper);
                                if (CollUtil.isEmpty(list)){
                                    throw new RuntimeException("state is not yet included!");
                                }
                            }
                            if("state".equals(addressLevelName)&&ObjectUtil.isEmpty(addressName)&&null==addressName){
                                throw new RuntimeException("state is empty!");
                            }
                        }

                    }

                }

                if(LogisticsTypeEnum.DropShipping.name().equals(logisticsType)){
                    String regionCode = address.getRegionCode();
                    // 判断地区是否是美国,非美国不校验
                    if(StrUtil.isNotEmpty(regionCode)&&"US".equals(regionCode)){
                        String postalCode = address.getPostalCode();
                        if (ObjectUtil.isEmpty(postalCode)){
                            throw new RuntimeException("postalCode is empty!");
                        }
                        boolean containsAsterisk = postalCode.contains("*");
                        if(!containsAsterisk&&!"PickUp".equals(logisticsType)){
                            String regex = "^\\d{5}(-\\d{4})?$";
                            boolean isValid = postalCode.matches(regex);
                            if (!isValid){
                                throw new RuntimeException("postalCode is not in the correct format!");
                            }
                        }


                        if (ObjectUtil.isEmpty(regionCode)){
                            throw new RuntimeException("regionCode is empty!");
                        }
                        ConfZip confZip = iConfZipService.getByCountry(regionCode);
                        if (ObjectUtil.isEmpty(confZip)){
                            throw new RuntimeException("regionCode is not yet included!");
                        }
                    }

                }

                String subTotal = ((OrderReceiveFromThirdDTO) arg).getSubTotal();
                if (ObjectUtil.isEmpty(subTotal)){
                    throw new RuntimeException("subTotal is empty!");
                }
                String totalAmount = ((OrderReceiveFromThirdDTO) arg).getTotalAmount();
                if (ObjectUtil.isEmpty(totalAmount)){
                    throw new RuntimeException("totalAmount is empty!");
                }

                // 新增附件校验
                if (LogisticsTypeEnum.PickUp.name().equals(logisticsType)) {
//                    if (StrUtil.isEmpty(warehouseCode)) {
//                        throw new AppRuntimeException("自提订单,仓库编码不能为空！");
//                    }
                    if (StrUtil.isEmpty(carrier)) {
                        throw new AppRuntimeException("自提订单,承运商不能为空！");
                    }
                    if (StrUtil.isEmpty(logisticsTrackingNo)&&!"LTL".equals(carrier)){
                        throw new AppRuntimeException("自提订单,物流单号不能为空！");
                    }
                    //校验承运商合法性
                    List<String> names = EnumUtil.getNames(CarrierTypeEnum.class);
                    boolean containsIgnoreCase = names.stream().anyMatch(s -> s.equalsIgnoreCase(carrier));
                    if (!containsIgnoreCase) {
                        throw new RuntimeException(StrUtil.format("非法的承运商：{}", carrier));
                    }

                    if (ObjectUtil.isEmpty(attachInfoItems) || attachInfoItems.isEmpty()){
//                        throw new RuntimeException("The attachment is empty!");
                        return;
                    }
                    // 其他附件类型校验
                    Iterator<AttachInfo> iterator = attachInfoItems.iterator();
                    while (iterator.hasNext()) {
                        AttachInfo next = iterator.next();
                        if(null != next.getFileType() && next.getFileType().equals(OrderAttachmentTypeEnum.Other.getCode())){
                            // 文件格式只能是excel校验
                            List<String> excelFileSuffix = Arrays.asList("xls", "xlsx","csv");
                            String url = next.getUrl();
                            // 取url的后缀
                            if(StringUtils.isEmpty(url)){
                                throw new RuntimeException("文件格式不正确");
                            }
                            String fileSuffix = FileUtil.getSuffix(url);
                            if (!excelFileSuffix.contains(fileSuffix)){
                                throw new RuntimeException("文件格式不正确");
                            }
                        }
                    }
                }
            }

        }
    }
}
