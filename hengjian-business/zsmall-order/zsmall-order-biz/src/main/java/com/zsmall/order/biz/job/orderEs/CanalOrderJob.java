package com.zsmall.order.biz.job.orderEs;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.service.ISysConfigService;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.order.biz.mq.CanalOrderSupper;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.entity.domain.OrderEsProcessingFailed;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.mq.CanalOrdersMqDTO;
import com.zsmall.order.entity.domain.mq.EsOrdersDTO;
import com.zsmall.order.entity.esmapper.EsOrderMapper;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.mapper.OrderEsProcessingFailedMapper;
import com.zsmall.order.entity.mapper.OrdersMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.conditions.update.LambdaEsUpdateWrapper;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.ScanParams;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Component
@RestController
@RequestMapping("/order/canalEs")
public class CanalOrderJob {
    @Resource
    private OrderEsProcessingFailedMapper orderEsProcessingFailedMapper;
    @Resource
    private CanalOrderSupper canalOrderSupper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private EsOrderMapper esOrderMapper;
    @Resource
    private OrdersService ordersService;
    @Resource
    private IOrderItemService orderItemService;
    @Resource
    private JedisPool jedisPool;
    @Resource
    private ISysConfigService sysConfigService;

    /**
     * 内部类，用于封装校验结果
     */
    private static class CheckResult {
        final List<Map<String, Object>> inconsistencies;
        final int totalProcessedCount;

        CheckResult(List<Map<String, Object>> inconsistencies, int totalProcessedCount) {
            this.inconsistencies = inconsistencies;
            this.totalProcessedCount = totalProcessedCount;
        }

        public List<Map<String, Object>> getInconsistencies() {
            return inconsistencies;
        }

        public int getTotalProcessedCount() {
            return totalProcessedCount;
        }
    }

    /**
     * 处理Canal消息失败表,并用mysql最新数据覆盖es数据
     * @throws Exception 异常
     */
    @GetMapping("/orderEsProcessingFailedHandler")
    @XxlJob("orderEsProcessingFailedHandler")
    public void orderEsProcessingFailedHandler() {
        // 1.查询订单ES消息处理失败表
        LambdaQueryWrapper<OrderEsProcessingFailed> q = new LambdaQueryWrapper<>();
        q.le(OrderEsProcessingFailed::getProcessingNum, 3);
        q.eq(OrderEsProcessingFailed::getProcessingStatus, 0);
        q.orderByAsc(OrderEsProcessingFailed::getId);
        List<OrderEsProcessingFailed> orderEsProcessingFailedList = orderEsProcessingFailedMapper.selectList(q);
        if (CollUtil.isEmpty(orderEsProcessingFailedList)){
            return;
        }
        orderEsProcessingFailedList.forEach(s->{
            try{
                Set<String> orderNos=new HashSet<>();
                Message message = JSON.parseObject(s.getMessageObject(), Message.class);
                String messageContext = new String(message.getBody());
              //  messageContext = canalOrderSupper.decodeCanalMessageIfNeeded(messageContext);
                CanalOrdersMqDTO canalOrdersDTO = JSON.parseObject(messageContext, CanalOrdersMqDTO.class);
                if (ObjectUtil.isNotNull(canalOrdersDTO)){
                    canalOrdersDTO.getData().forEach(esOrdersDTO->{
                        //尝试获取订单号
                        String orderNo = esOrdersDTO.getOrderNo();
                        if (StrUtil.isEmpty(orderNo)){
                            String orderItemNo = esOrdersDTO.getOrderItemNo();
                            if (StrUtil.isEmpty(orderItemNo)){
                                throw new RuntimeException("订单号和订单子项号都为空");
                            }
                            //查询订单号
                            OrderItem byOrderItemNo = orderItemService.getByOrderItemNo(orderItemNo);
                            if (ObjectUtil.isNotNull(byOrderItemNo)){
                                orderNo=byOrderItemNo.getOrderNo();
                            }
                        }
                        if (StrUtil.isEmpty(orderNo)){
                            throw new RuntimeException("订单号为空");
                        }
                        orderNos.add(orderNo);
                    });
                }
                if (CollUtil.isNotEmpty(orderNos)){
                    List<EsOrdersDTO> esOrderDateByOrderNo = ordersService.getEsOrderDateByOrderNo(orderNos);
                    if (CollUtil.isNotEmpty(esOrderDateByOrderNo)){
                        esOrderDateByOrderNo.forEach(esOrdersDTO->{
                            LambdaEsUpdateWrapper<EsOrdersDTO> esUpdateWrapper = new LambdaEsUpdateWrapper<>();
                            esUpdateWrapper.eq(EsOrdersDTO::getOrderNo, esOrdersDTO.getOrderNo());
                            esOrderMapper.update(esOrdersDTO,esUpdateWrapper);
                        });
                    }
                }
                s.setProcessingNum(1);
                s.setProcessingStatus(1);
            }catch (Exception e) {
                //更新操作
                s.setProcessingStatus(0);
                s.setProcessingNum(s.getProcessingNum() + 1);
                s.setErrorMessage(e.getMessage());
                log.error("订单ES消息处理失败表处理失败,更新失败,orderEsProcessingFailed:{}", JSON.toJSONString(s));
                //如果处理三次还是失败，发送邮件
                throw new RuntimeException(e);
            }finally{
                s.setUpdateTime(new Date());
                orderEsProcessingFailedMapper.updateById(s);
            }
        });

    }

    /**
     * 校验数据订单数量和ES数量是否一致
     * 如果不一致，分页比对并输出差异订单号（每页4万）
     */
    @SaIgnore
    @GetMapping("/checkOrderNum")
    @XxlJob("checkOrderNum")
    public void checkOrderNum() {
        long dbCount = TenantHelper.ignore(()->ordersMapper.countOrder());
        LambdaEsQueryWrapper<EsOrdersDTO> wa = new LambdaEsQueryWrapper<>();
        wa.eq(EsOrdersDTO::getDelFlag,0);
        long esCount  = esOrderMapper.selectCount(wa);
        if (dbCount != esCount) {
            // 查询差异订单号
            int pageSize = 40000;
            List<String> diffOrderNos = findDiffOrderNos(dbCount, pageSize);
            String errorMsg = StrUtil.format("[Canal校验数据DB订单数量和ES数量是否一致] 失败: 订单数量不一致, dbCount:{}, esCount:{}, 差异订单号数量:{}, 差异订单号:{}", dbCount, esCount, diffOrderNos.size(), JSON.toJSON(diffOrderNos));
            log.error(errorMsg);
            // 如需全部输出可调整此处
            // log.error("全部差异订单号: {}", diffOrderNos);
            throw new RuntimeException(errorMsg);
        }
        log.info("[Canal校验数据DB订单数量和ES数量是否一致] 成功: 订单数量一致, dbCount:{}, esCount:{}", dbCount, esCount);
    }



    @GetMapping("/deleteRedisKeys/{keyPrefix}")
    public R deleteRedisKeys(@PathVariable String keyPrefix) {
      //  String keyPrefix = GlobalConstants.ORDER_CANAL_CHECK;
        String pattern = keyPrefix.endsWith("*") ? keyPrefix : keyPrefix + "*";
        int deletedCount = 0;
        int scanCount = 1000;

        try (Jedis jedis = jedisPool.getResource()) {
            String cursor = "0";
            ScanParams scanParams = new ScanParams();
            scanParams.match(pattern);
            scanParams.count(scanCount);
            do {
                redis.clients.jedis.ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                cursor = scanResult.getCursor();
                List<String> keys = scanResult.getResult();

                if (CollUtil.isNotEmpty(keys)) {
                    // 批量删除keys
                    String[] keysArray = keys.toArray(new String[0]);
                    Long count = jedis.del(keysArray);
                    if (count != null) {
                        deletedCount += count;
                    }
                    log.info("已删除{}个以{}开头的Redis键", count, keyPrefix);
                }
            } while (!cursor.equals("0"));

            log.info("删除Redis键完成，共删除{}个以{}开头的键", deletedCount, keyPrefix);
            return R.ok(StrUtil.format("删除Redis键完成，共删除{}个以{}开头的键", deletedCount, keyPrefix));
        } catch (Exception e) {
            log.error("删除Redis键时发生异常: {}", e.getMessage(), e);
            return R.fail(StrUtil.format("删除Redis键时发生异常: {}", e.getMessage()));
        }
    }

    /**
     * 校验数据库中订单数和ES中的订单数据是否一致
     */
    @SaIgnore
    @GetMapping("/verifyEsAndDbOrderData")
    @XxlJob("verifyEsAndDbOrderData")
    public R<List<Map<String, Object>>> verifyEsAndDbOrderData() {
        CheckResult checkResult = checkEsAndOrderData(GlobalConstants.ORDER_CANAL_CHECK);
        List<Map<String, Object>> inconsistencies = checkResult.getInconsistencies();
        int totalCount = checkResult.getTotalProcessedCount();
        int inconsistentCount = inconsistencies.size();
        String message = StrUtil.format("总订单 {} 个, 不一致订单 {} 个", totalCount, inconsistentCount);
        log.info("[校验ES与DB订单数据] {}", message);
        return R.ok(message, inconsistencies);
    }

    /**
     * 校验数据库中订单号和ES中的订单数据是否一致
     * @param keyPrefix 键前缀
     * @return 包含不一致信息和总处理数的校验结果对象
     */
    public CheckResult checkEsAndOrderData(String keyPrefix) {
        Boolean isOrderEsSearch = TenantHelper.ignore(()->Boolean.parseBoolean(sysConfigService.selectConfigByKey("Is_Order_Es_Date_Update")));
        String pattern = keyPrefix.endsWith("*") ? keyPrefix : keyPrefix + "*";
        int batchCount = 0;
        int scanCount = 5000;
        long startTime = System.currentTimeMillis();
        final AtomicInteger totalProcessedKeys = new AtomicInteger(0);
        // 用于收集所有不一致信息
        List<Map<String, Object>> allInconsistencies = Collections.synchronizedList(new ArrayList<>());
        // 使用try-with-resources确保Jedis连接正确释放
        try (Jedis jedis = jedisPool.getResource()) {
            // 游标初始值为0
            String cursor = "0";
            // SCAN命令参数
            ScanParams scanParams = new ScanParams();
            // 设置匹配模式
            scanParams.match(pattern);
            // 设置每批大约的键数量
            scanParams.count(scanCount);

            // 创建固定大小为5的线程池用于并行处理数据
            int threadCount = 5;
            ExecutorService executor = Executors.newFixedThreadPool(threadCount, new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "order-es-validator-" + counter.getAndIncrement());
                    thread.setDaemon(true);
                    return thread;
                }
            });
            List<Future<?>> futures = Collections.synchronizedList(new ArrayList<>());
            do {
                // 执行SCAN命令获取一批键
                redis.clients.jedis.ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                // 获取新的游标位置
                cursor = scanResult.getCursor();
                // 获取当前批次的键
                List<String> keys = scanResult.getResult();
                if (keys.isEmpty()) {
                    continue;
                }
                final int currentBatch = batchCount++;
                totalProcessedKeys.addAndGet(keys.size());
                log.info("正在处理第 {} 批，获取到 {} 个订单号", currentBatch, keys.size());

                // 复制当前批次的键，并在新线程中处理
                List<String> batchKeys = new ArrayList<>(keys);
                Future<?> future = executor.submit(() -> {
                    // 为每个任务创建独立的Jedis连接
                    try (Jedis taskJedis = jedisPool.getResource()) {
                        processKeyBatch(taskJedis, batchKeys, currentBatch, allInconsistencies,isOrderEsSearch);
                    } catch (Exception e) {
                        log.error("处理批次时Jedis连接异常: {}", e.getMessage(), e);
                    }
                });
                futures.add(future);
                // 如果积累了太多未完成的任务，等待一些任务完成
                // 控制任务队列长度，不超过线程数的2倍
                if (futures.size() >= threadCount * 2) {
                    waitForSomeFutures(futures);
                }

                // 无需每次都休眠，只有在需要时放慢速度
                if (batchCount % 10 == 0) {
                    Thread.sleep(5);
                }

            } while (!cursor.equals("0"));

            // 等待所有剩余任务完成
            waitForAllFutures(futures);
            executor.shutdown();
            try {
                // 等待线程池终止，最多等待2分钟
                if (!executor.awaitTermination(2, TimeUnit.MINUTES)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }

            long endTime = System.currentTimeMillis();
            log.info("校验完成，共处理 {} 批数据，总计 {} 个订单号，耗时 {} 毫秒", batchCount, totalProcessedKeys.get(), (endTime - startTime));
        } catch (Exception e) {
            log.error("批量获取值时发生异常: {}", e.getMessage(), e);
        }
        // 返回包含列表和总数的结果对象
        return new CheckResult(allInconsistencies, totalProcessedKeys.get());
    }

    /**
     * 处理一批键的数据校验
     * @param jedis Redis连接
     * @param keys 待处理的键列表
     * @param batchNo 批次号，用于日志
     * @param inconsistencies 用于收集不一致信息的列表
     */
    private void processKeyBatch(Jedis jedis, List<String> keys, int batchNo, List<Map<String, Object>> inconsistencies,Boolean isOrderEsSearch) {
        try {
            if (CollUtil.isEmpty(keys)) {
                return;
            }
            // 批量获取值
            String mget = jedis.mget(keys.toArray(new String[0])).toString();
            List<String> values = JSON.parseArray(mget, String.class);

            // 过滤掉空值
            List<String> validValues = values.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            if (validValues.isEmpty()) {
                return;
            }
            // 转换为Set以移除重复值
            Set<String> valueSet = new HashSet<>(validValues);
            int totalOrderCount = valueSet.size();
            log.info("批次 {} 开始处理: 共有 {} 个有效订单号", batchNo, totalOrderCount);

            // 先从ES查询数据
            LambdaEsQueryWrapper<EsOrdersDTO> q = new LambdaEsQueryWrapper<>();
            q.in(EsOrdersDTO::getOrderNo, valueSet);
            q.eq(EsOrdersDTO::getDelFlag,0);
            List<EsOrdersDTO> esOrders = esOrderMapper.selectList(q);

            if (CollUtil.isEmpty(esOrders)) {
                log.info("批次 {} 无需处理: 没有找到ES中的订单数据", batchNo);
                return;
            }

            // 提取ES中存在的订单号，只对这些订单进行数据库查询
            Set<String> existingOrderNos = esOrders.stream()
                .map(EsOrdersDTO::getOrderNo)
                .collect(Collectors.toSet());

            // 如果ES中不存在订单数据，无需继续处理
            if (existingOrderNos.isEmpty()) {
                return;
            }



            // 从数据库获取订单数据，使用Map直接收集，避免List的内存开销
            Map<String, EsOrdersDTO> dbOrderMap = new HashMap<>();
            int batchSize = 5000;
            final AtomicInteger totalDbOrderCount = new AtomicInteger(0);

            // 将订单号分批处理
            List<List<String>> batches = new ArrayList<>();
            List<String> currentBatch = new ArrayList<>();

            for (String orderNo : existingOrderNos) {
                currentBatch.add(orderNo);
                if (currentBatch.size() >= batchSize) {
                    batches.add(new ArrayList<>(currentBatch));
                    currentBatch.clear();
                }
            }

            if (!currentBatch.isEmpty()) {
                batches.add(currentBatch);
            }

            log.info("批次 {}: 订单号分为 {} 个小批次处理", batchNo, batches.size());
            // 分批获取数据库订单数据
            for (int i = 0; i < batches.size(); i++) {
                List<String> batch = batches.get(i);
                Set<String> batchSet = new HashSet<>(batch);

                try {
                    // 分批次查询数据库数据
                    List<EsOrdersDTO> batchDbOrders = ordersService.getEsOrderDateByOrderNo(batchSet);
                    if (CollUtil.isNotEmpty(batchDbOrders)) {
                        // 直接放入Map，避免List的中间存储和后续转换
                        batchDbOrders.forEach(order -> {
                            if (dbOrderMap.putIfAbsent(order.getOrderNo(), order) == null) {
                                totalDbOrderCount.incrementAndGet(); // 只有新增的才计数，避免重复计算
                            }
                        });
                    }
                    log.info("批次 {} 小批次 {}/{}: 从数据库获取到 {} 条订单数据",
                             batchNo, i + 1, batches.size(), batchDbOrders != null ? batchDbOrders.size() : 0);
                } catch (Exception e) {
                    log.error("批次 {} 小批次 {}/{} 处理异常: {}", batchNo, i + 1, batches.size(), e.getMessage(), e);
                }
            }

            if (dbOrderMap.isEmpty()) {
                log.info("批次 {} 无需处理: 从数据库中未获取到订单数据", batchNo);
                return;
            }

            final AtomicInteger mismatchCount = new AtomicInteger(0);
            // 遍历ES中的订单进行比较
            for (EsOrdersDTO esOrder : esOrders) {
                try {
                    EsOrdersDTO dbOrdersDTO = dbOrderMap.get(esOrder.getOrderNo());
                    Map<String, Map<String, String>> diffMap = new HashMap<>();
                    try {
                        if (ObjectUtil.notEqual(esOrder, dbOrdersDTO)) {
                            mismatchCount.incrementAndGet();
                            String dbJson = JSON.toJSONString(dbOrdersDTO);
                            String esJson = JSON.toJSONString(esOrder);

                            // 将JSON转为Map进行比较
                            Map<String, Object> dbMap = JSON.parseObject(dbJson, Map.class);
                            Map<String, Object> esMap = JSON.parseObject(esJson, Map.class);

                            // 记录不同的字段
                            for (String key : dbMap.keySet()) {
                                Object dbValue = dbMap.get(key);
                                Object esValue = esMap.get(key);
                                // 不直接使用equals，而是先转为字符串再比较
                                String dbValueStr = dbValue == null ? "null" : dbValue.toString();
                                String esValueStr = esValue == null ? "null" : esValue.toString();

                                if (!dbValueStr.equals(esValueStr)) {
                                    Map<String, String> valueDiff = new HashMap<>();
                                    valueDiff.put("DB", dbValueStr);
                                    valueDiff.put("ES", esValueStr);
                                    diffMap.put(key, valueDiff);
                                }
                            }

                            // 检查ES中有但DB中没有的字段
                            for (String key : esMap.keySet()) {
                                if (!dbMap.containsKey(key)) {
                                    Map<String, String> valueDiff = new HashMap<>();
                                    valueDiff.put("DB", null);
                                    valueDiff.put("ES", esMap.get(key) == null ? "null" : esMap.get(key).toString());
                                    diffMap.put(key, valueDiff);
                                }
                            }
                            if (isOrderEsSearch==true){
                                //如果不一致,使用DB数据更新到ES
                                esOrderMapper.updateById(dbOrdersDTO);
                            }
                        }
                        if (!diffMap.isEmpty()) {
                            log.error("订单数据不一致,orderNo:{},具体差异:\n{}", esOrder.getOrderNo(), JSONUtil.toJsonStr(diffMap));
                            // 将不一致信息添加到列表中
                            Map<String, Object> inconsistencyInfo = new HashMap<>();
                            inconsistencyInfo.put("orderNo", esOrder.getOrderNo());
                            inconsistencyInfo.put("differences", diffMap);
                            inconsistencies.add(inconsistencyInfo);
                        }
                    } catch (Exception e) {
                        log.error("比较订单数据时发生异常:{}, orderNo:{}", e.getMessage(), esOrder.getOrderNo());
                    }
                } catch (Exception e) {
                    log.error("处理单个订单时发生异常: {}", e.getMessage(), e);
                }
            }
            log.error("订单数据一致，批次 {} 处理完成: ES订单 {} 个,DB订单{}个， 不一致订单 {} 个",
                batchNo, esOrders.size(), totalDbOrderCount.get(), mismatchCount.get());
        } catch (Exception e) {
            log.error("处理键批次 {} 时发生异常: {}", batchNo, e.getMessage(), e);
        }
    }

    /**
     * 等待部分Future完成以控制并发量
     * @param futures Future列表
     */
    private void waitForSomeFutures(List<Future<?>> futures) {
        synchronized (futures) {
            List<Future<?>> completedFutures = new ArrayList<>();
            for (Future<?> future : futures) {
                if (future.isDone()) {
                    completedFutures.add(future);
                    try {
                        future.get(); // 检查是否有异常
                    } catch (Exception e) {
                        log.error("任务执行异常: {}", e.getMessage(), e);
                    }
                }
            }
            futures.removeAll(completedFutures);

            // 如果没有完成的任务，等待第一个完成
            if (completedFutures.isEmpty() && !futures.isEmpty()) {
                try {
                    Future<?> first = futures.get(0);
                    first.get(100, TimeUnit.MILLISECONDS);
                    futures.remove(0);
                } catch (TimeoutException te) {
                    // 超时就继续，不需要处理
                } catch (Exception e) {
                    log.error("等待任务完成时发生异常: {}", e.getMessage(), e);
                    futures.remove(0);
                }
            }
        }
    }

    /**
     * 等待所有Future完成
     * @param futures Future列表
     */
    private void waitForAllFutures(List<Future<?>> futures) {
        synchronized (futures) {
            for (Iterator<Future<?>> iterator = futures.iterator(); iterator.hasNext();) {
                Future<?> future = iterator.next();
                try {
                    future.get();
                } catch (Exception e) {
                    log.error("等待任务完成时发生异常: {}", e.getMessage(), e);
                } finally {
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 分页比对DB和ES订单号，返回DB有但ES无的订单号列表
     * @param dbCount 总订单数
     * @param pageSize 每页数量
     * @return 差异订单号列表
     * 说明：只找出DB有但ES无的订单号，如需双向比对可扩展
     */
    private List<String> findDiffOrderNos(long dbCount, int pageSize) {
        List<String> diffOrderNos = new ArrayList<>();
        int totalPage = (int) ((dbCount + pageSize - 1) / pageSize);
        for (int page = 1; page <= totalPage; page++) {
            // 分页获取DB订单号
            int finalPage = page;
            Set<String> dbOrderNos = TenantHelper.ignore(() -> ordersMapper.getOrderNos(finalPage, pageSize));
            if (CollUtil.isEmpty(dbOrderNos)) {
                continue;
            }
            LambdaEsQueryWrapper<EsOrdersDTO> wa = new LambdaEsQueryWrapper<>();
            wa.eq(EsOrdersDTO::getDelFlag, 0);
            wa.select(EsOrdersDTO::getOrderNo);
            wa.orderByDesc(EsOrdersDTO::getId);
            wa.limit(page, pageSize);
            List<EsOrdersDTO> esOrders = esOrderMapper.selectList(wa);
            Set<String> esOrderNos = esOrders.stream().map(EsOrdersDTO::getOrderNo).collect(Collectors.toSet());
            dbOrderNos.removeAll(esOrderNos);
            if (CollUtil.isNotEmpty(dbOrderNos)){
                diffOrderNos.addAll(dbOrderNos);
            }
        }
        return diffOrderNos;
    }
}
