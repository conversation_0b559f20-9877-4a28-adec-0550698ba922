package com.zsmall.order.biz.factory.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.activity.entity.domain.ProductActivity;
import com.zsmall.activity.entity.iservice.IProductActivityService;
import com.zsmall.common.domain.bo.TrackingNoBo;
import com.zsmall.common.domain.vo.IntactAddressInfoVo;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.ConditionType;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.order.biz.factory.OrderHandleInterface;
import com.zsmall.order.biz.service.OrderRefundRuleService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.vo.OrderDetailsAttachmentVo;
import com.zsmall.order.entity.domain.vo.order.*;
import com.zsmall.order.entity.iservice.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 国外现货订单-实现类
 *
 * <AUTHOR>
 * @date 2023/3/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholesaleOrder implements OrderHandleInterface {

    public static final String dateFormat = "yyyy-MM-dd HH:mm:ss";

    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final BusinessParameterService businessParameterService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final OrderRefundRuleService orderRefundRuleService;
    private final IOrderRefundItemService iOrderRefundItemService;
    private final IOrderRefundService iOrderRefundService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final OrderSupport orderSupport;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final IWholesaleIntentionOrderService iWholesaleIntentionOrderService;
    private final IWholesaleIntentionOrderItemService iWholesaleIntentionOrderItemService;
    private final IProductActivityService iProductActivityService;



    /**
     * 构建订单Body
     *
     * @param orders
     * @param activityType
     * @return
     */
    @Override
    public OrderPageVo buildOrderBody(Orders orders, String activityType) {
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        String tenantId = LoginHelper.getTenantId();
        OrderType orderType = orders.getOrderType();
        OrderPageVo orderBody = new OrderPageVo();
        orderBody.setTrackingFlag(orders.getTrackingFlag());
        orderBody.setExceptionCode(orders.getExceptionCode());
        orderBody.setCurrency(orders.getCurrency());
        orderBody.setCurrencySymbol(orders.getCurrencySymbol());
        if (Objects.equals(tenantType, TenantType.Manager)) {
            orderBody.setDistributorId(orders.getTenantId());
            orderBody.setCreateBy(orders.getCreateBy());
        }

        // 收益
        BigDecimal margin = BigDecimal.ZERO;

        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orders.getId());
        List<OrderItemVo> orderItemBodies = new ArrayList<>();
        Boolean canConfirmReceiptOrder = false;
        StringBuilder WMSFailMessage = new StringBuilder();
        List<String> supplierCodes = new ArrayList<>();
        BigDecimal totalAmount = NumberUtil.toBigDecimal(orders.getPlatformPayableTotalAmount());
        if (Objects.equals(tenantType, TenantType.Supplier)) {
            totalAmount = NumberUtil.toBigDecimal(orders.getOriginalPayableTotalAmount());
        }

        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OrderItem orderItem : orderItems) {

                String orderItemNo = orderItem.getOrderItemNo();
                BigDecimal saleTotalPrice = orderItem.getChannelSaleTotalAmount();
                BigDecimal totalPrice = orderItem.getPlatformPayableTotalAmount();

                if (saleTotalPrice != null && !NumberUtil.equals(saleTotalPrice, BigDecimal.ZERO)) {
                    BigDecimal itemMargin = NumberUtil.sub(saleTotalPrice, totalPrice);
                    log.info("orderItemNo = {} itemMargin = {}", orderItemNo, itemMargin);
                    margin = NumberUtil.add(margin, itemMargin);
                    log.info("margin = {}", margin);
                }

                String supplierTenantId = orderItem.getSupplierTenantId();
                if (StrUtil.isNotBlank(supplierTenantId)) {
                    supplierCodes.add(supplierTenantId);
                }

                OrderItemVo orderItemBody = new OrderItemVo();
                OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                BigDecimal platformActualTotalAmount = orderItem.getPlatformActualTotalAmount();
                BigDecimal originalActualTotalAmount = orderItem.getOriginalActualTotalAmount();
                Integer totalQuantity = orderItem.getTotalQuantity();

                OrderStateType orderItemStatus = orderItem.getOrderState();
                LogisticsProgress fulfillment = orderItem.getFulfillmentProgress();
                log.info("发货时间：{}", orderItem.getDispatchedTime());
                if (ObjectUtil.isNotNull(orderProductSku)) {
                    orderItemBody.setItemNo(orderProductSku.getProductSkuCode());
                    orderItemBody.setSku(orderProductSku.getSku());
                    orderItemBody.setImageShowUrl(orderProductSku.getImageShowUrl());
                    orderItemBody.setProductName(orderProductSku.getProductName());
                    orderItemBody.setProductCode(orderProductSku.getProductCode());
                }

                orderItemBody.setCanConfirmReceipt(false);
                orderItemBody.setOrderItemNo(orderItemNo);
                orderItemBody.setNum(orderItem.getTotalQuantity());
//                OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItemNo);
                if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
//                    BigDecimal originalDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice() == null ? BigDecimal.ZERO : orderItemPrice.getOriginalDepositUnitPrice();
//                    BigDecimal originalDepositTotalPrice = NumberUtil.mul(originalDepositUnitPrice, orderItem.getTotalQuantity());
//                    BigDecimal productTotalPriceSup = NumberUtil.add(orderItem.getOriginalActualTotalAmount(), originalDepositTotalPrice);
                    BigDecimal productTotalPriceSup = orderItem.getOriginalActualTotalAmount();
                    orderItemBody.setProductTotalPrice(productTotalPriceSup);
                } else {
//                    BigDecimal depositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice() == null ? BigDecimal.ZERO : orderItemPrice.getPlatformDepositUnitPrice();
//                    BigDecimal depositTotalPrice = NumberUtil.mul(depositUnitPrice, orderItem.getTotalQuantity());
//                    BigDecimal productTotalPrice = NumberUtil.add(orderItem.getPlatformActualTotalAmount(), depositTotalPrice);
                    BigDecimal productTotalPrice = orderItem.getPlatformActualTotalAmount();
                    orderItemBody.setProductTotalPrice(productTotalPrice);
                }
                orderItemBodies.add(orderItemBody);
            }
        }

        if (TenantType.Manager.equals(tenantType) && CollUtil.isNotEmpty(supplierCodes)) {
            String supplierIds = String.join(",", supplierCodes);
            orderBody.setSupplierIds(supplierIds);
        }

        if (CollUtil.isNotEmpty(orderItemBodies)) {
            orderBody.setOrderItems(orderItemBodies);
        }
        orderBody.setCanConfirmReceipt(canConfirmReceiptOrder);
        orderBody.setOrderId(orders.getOrderNo());
        orderBody.setChannelOrderId(orders.getChannelOrderName());
        orderBody.setOrderType(orders.getOrderType().name());
        orderBody.setChannelAlias(orders.getChannelAlias());

        if (!Objects.equals(tenantType, TenantType.Supplier)) {
            orderBody.setTotalNumber(orders.getPlatformActualTotalAmount());
        }
        orderBody.setTotal(DecimalUtil.bigDecimalToString(totalAmount));
//        orderBody.setCustomer(userName);
        orderBody.setItem(orders.getTotalQuantity());
        if (ObjectUtil.isNotNull(orders.getChannelType())) {
            orderBody.setSalesChannel(orders.getChannelType().toString());
        }
        orderBody.setStartTime(DateUtil.format(orders.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        orderBody.setOrderChannelTime(DateUtil.format(orders.getChannelOrderTime(), "yyyy-MM-dd HH:mm:ss"));
        orderBody.setTimeDifference(DateUtil.between(orders.getCreateTime(), new Date(), DateUnit.HOUR));

        if (ObjectUtil.isNotNull(orders.getOrderState())) {
            List<OrderStateType> orderStatusTypes = new ArrayList<>();
            orderStatusTypes.add(OrderStateType.UnPaid);
            orderStatusTypes.add(OrderStateType.Failed);
            orderStatusTypes.add(OrderStateType.Canceled);
            orderStatusTypes.add(OrderStateType.Refunded);
            orderStatusTypes.add(OrderStateType.Pending);
            if (orderStatusTypes.contains(orders.getOrderState())) {
                orderBody.setOrderStatus(orders.getOrderState().name());
            } else if (ObjectUtil.isNotNull(orders.getFulfillmentProgress())) {
                orderBody.setOrderStatus(orders.getFulfillmentProgress().name());
            }

        }
        if (ObjectUtil.isNotNull(orders.getFulfillmentProgress())) {
            orderBody.setFulfillment(orders.getFulfillmentProgress().name());
        }
        orderBody.setPayFailedMessage(orders.getPayErrorMessage());
        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderId(orders.getId());
        if (ObjectUtil.isNotNull(logisticsInfo)) {
            if (ObjectUtil.isNotNull(logisticsInfo.getLogisticsType())) {
                orderBody.setLogisticsType(logisticsInfo.getLogisticsType().name());
            }
        }
        return orderBody;
    }

    /**
     * 构建订单详情
     *
     * @param orders
     * @return
     */
    @Override
    public OrderDetailVo buildOrderDetail(Orders orders) {
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        OrderDetailVo detailVo = new OrderDetailVo();
        Long ordersId = orders.getId();

        String orderNo = orders.getOrderNo();
        OrderType orderType = orders.getOrderType();
        OrderAddressInfo orderAddressInfo = iOrderAddressInfoService.getByOrderNo(orderNo);
        String phoneNumber = orderAddressInfo.getPhoneNumber();
        ChannelTypeEnum channelType = orders.getChannelType();
        Date orderChannelTime = orders.getChannelOrderTime();
        OrderStateType orderStatus = orders.getOrderState();

        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderId(ordersId);
        WholesaleIntentionOrder wiOrder = iWholesaleIntentionOrderService.queryByOrderNo(orderNo);
        List<WholesaleIntentionOrderItem> wiOrderItems = iWholesaleIntentionOrderItemService.queryWIOrderId(wiOrder.getId());

        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(ordersId);
        LogisticsTypeEnum logisticsType = logisticsInfo.getLogisticsType();

        // 子订单分组
        Map<String, OrderItemDetailVo> logisticsAndCategory = orderItemsGrouping(orderItems, logisticsInfo);

        List<OrderItemDetailVo> categoryList = new ArrayList<>();
        Set<String> keySet = logisticsAndCategory.keySet();
        for (String key : keySet) {
            log.info("getOrderDetail - map key = {}", key);
            OrderItemDetailVo categoryBody = logisticsAndCategory.get(key);
            log.info("getOrderDetail - categoryBody == null", categoryBody == null);
            List<OrderItemVo> orderItemsCate = categoryBody.getOrderItems();
            if (CollectionUtils.isNotEmpty(orderItemsCate)) {
                categoryList.add(categoryBody);
            }
        }

        OrderPageVo orderPageVo = new OrderPageVo();
        if (StringUtils.isNotBlank(phoneNumber)) {
            orderPageVo.setPhoneNumber(phoneNumber);
        }

        // 处理地址信息
        OrderAddressType addressType = orderAddressInfo.getAddressType();
        if (ObjectUtil.equals(addressType, OrderAddressType.ShipAddress)) {
            orderPageVo.setShipTo(orderSupport.addressToBody(orderAddressInfo));
        }
        if (ObjectUtil.equals(addressType, OrderAddressType.BillAddress)) {
            orderPageVo.setBillTo(orderSupport.addressToBody(orderAddressInfo));
        }

        orderPageVo.setLogisticsType(logisticsType.name());
        orderPageVo.setCanConfirmReceipt(false);
        orderPageVo.setChannelType(channelType.name());
        orderPageVo.setChannelOrderId(orders.getChannelOrderName());
        IntactAddressInfoVo billTo = orderPageVo.getBillTo();
        orderPageVo.setBillingAddress(ObjectUtil.isNotNull(billTo) ? billTo.getIntactAddress() : null);
        orderPageVo.setContactInformation(phoneNumber);
        orderPageVo.setCustomerName(orders.getTenantId());
        orderPageVo.setNotes(orders.getOrderNote());
        orderPageVo.setFulfillment(orders.getFulfillmentProgress().name());
        if (ObjectUtil.isNotNull(orderStatus)) {
            List<OrderStateType> orderStatusTypes = new ArrayList<>();
            orderStatusTypes.add(OrderStateType.UnPaid);
            orderStatusTypes.add(OrderStateType.Failed);
            orderStatusTypes.add(OrderStateType.Canceled);
            orderStatusTypes.add(OrderStateType.Refunded);
            if (orderStatusTypes.contains(orderStatus)) {
                orderPageVo.setOrderStatus(orderStatus.name());
            } else if (ObjectUtil.isNotNull(orders.getFulfillmentProgress())) {
                orderPageVo.setOrderStatus(orders.getFulfillmentProgress().name());
            }
        }

//        OrderAttachment orderAttachment = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.Zip);
//        if (orderAttachment != null) {
//            orderPageVo.setAttachmentShowUrl(orderAttachment.getAttachmentShowUrl());
//            orderPageVo.setAttachmentName(orderAttachment.getAttachmentOriginalName());
//        }

        Integer num = CollUtil.size(orderItems);
        BigDecimal totalPrice = wiOrder.getOrderTotalAmountPlatform();
        BigDecimal productTotalPrice = wiOrder.getProductAmountPlatform();
        BigDecimal shippingFee = wiOrder.getFinalShippingFeePlatform();
        BigDecimal operationFee = wiOrder.getFinalOperationFeePlatform();
        BigDecimal depositTotalPrice = wiOrder.getOrderDepositAmountPlatform();

        if (TenantType.Supplier.equals(tenantType)) {
            totalPrice = wiOrder.getOrderTotalAmount();
            productTotalPrice = wiOrder.getProductAmount();
            shippingFee = wiOrder.getFinalShippingFee();
            operationFee = wiOrder.getFinalOperationFee();
            depositTotalPrice = wiOrder.getOrderDepositAmount();
        }

        for (OrderItemDetailVo orderItemCategoryBody : categoryList) {
            List<OrderItemVo> orderItemsBodies = orderItemCategoryBody.getOrderItems();
            for (OrderItemVo orderItemsBody : orderItemsBodies) {
                BigDecimal unitPrice = orderItemsBody.getUnitPrice();
                orderItemsBody.setUnitPrice(unitPrice);
            }
        }

        // 自提订单处理快递标签
//        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//            detailVo.setHasLabel(true);
//            OrderAttachment shippingLabel = iOrderAttachmentService.queryByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
//            if (shippingLabel != null) {
//                detailVo.setHasLabel(true);
//                String showUrl = shippingLabel.getAttachmentShowUrl();
//                detailVo.setShippingLabelShowUrl(showUrl);
//                String attachmentName = shippingLabel.getAttachmentOriginalName();
//                if (StringUtils.isNotBlank(attachmentName) /*&& Objects.equals(tenantType, TenantType.Distributor)*/) {
//                    attachmentName = StrUtil.addSuffixIfNot(attachmentName, ".pdf");
//                    detailVo.setLabelFileName(attachmentName);
//                } else {
//                    detailVo.setLabelFileName(orderNo + ".pdf");
//                }
//            }
//        }

        LambdaQueryWrapper<OrderAttachment> oo = new LambdaQueryWrapper<>();
        oo.eq(OrderAttachment::getOrderNo,orders.getOrderNo());
        List<OrderAttachment> orderAttachments = iOrderAttachmentService.getBaseMapper().selectList(oo);
        List<OrderDetailsAttachmentVo> orderDetailsAttachmentVos = BeanUtil.copyToList(orderAttachments, OrderDetailsAttachmentVo.class);
        detailVo.setOrderDetailsAttachmentVos(orderDetailsAttachmentVos);

        orderPageVo.setOrderId(orderNo);
        orderPageVo.setOrderType(orderType.name());
        orderPageVo.setTotalNumber(totalPrice);
        orderPageVo.setTotalAmount(DecimalUtil.bigDecimalToString(totalPrice));
        orderPageVo.setTotal(DecimalUtil.bigDecimalToString(totalPrice));
        orderPageVo.setShippingCost(DecimalUtil.bigDecimalToString(shippingFee));
        orderPageVo.setOperationFee(DecimalUtil.bigDecimalToString(operationFee));
        orderPageVo.setProductAmount(DecimalUtil.bigDecimalToString(productTotalPrice));
        orderPageVo.setTotalDeposit(DecimalUtil.bigDecimalToString(depositTotalPrice));
        orderPageVo.setItem(num);
        orderPageVo.setTotalQuantity(orders.getTotalQuantity());

        WholesaleIntentionOrderItem wiOrderItem = wiOrderItems.get(0);
        orderPageVo.setProductName(wiOrderItem.getProductName());
        orderPageVo.setImageShowUrl(wiOrderItem.getImageShowUrl());

        orderPageVo.setStartDate(DateUtil.format(orders.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        orderPageVo.setPayDate(DateUtil.format(orders.getPayTime(), "yyyy-MM-dd HH:mm:ss"));

        BigDecimal refund = iOrderRefundService.sumByRefundPrice(orders.getId(), ObjectUtil.equals(tenantType, TenantType.Supplier));
        if (refund != null) {
            orderPageVo.setRefundAmount(DecimalUtil.bigDecimalToString(refund));
        }

        // 打包售后信息
        packageRefund:
        {
            // 查询最近一条退款记录
            List<OrderRefundItem> lastRefundItems = iOrderRefundItemService.getByOrderId(ordersId);
            if (CollUtil.isNotEmpty(lastRefundItems)) {
                OrderRefundItem lastRefundItem = lastRefundItems.get(0);
                OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(lastRefundItem.getOrderRefundNo());
                OrderRefundStateType refundStatus = orderRefund.getRefundState();
                if (ObjectUtil.equals(refundStatus, OrderRefundStateType.Reject)) {
                    orderPageVo.setOrderRefundStatus(OrderRefundStateType.Reject.name());
                } else if (ObjectUtil.equals(refundStatus, OrderRefundStateType.Refunded)) {
                    orderPageVo.setOrderRefundStatus(OrderRefundStateType.Refunded.name());
                } else if (ObjectUtil.equals(refundStatus, OrderRefundStateType.Canceled)) {
                    break packageRefund;
                } else {
                    orderPageVo.setOrderRefundStatus(OrderRefundStateType.Refunding.name());
                }

                String refundStatusStr = orderPageVo.getOrderRefundStatus();
                String orderRefundNo = orderRefund.getOrderRefundNo();
                Date refundApplyTime = orderRefund.getRefundApplyTime();
                String refundRuleReason = orderRefund.getRefundRuleReason();

                String orderRefundItemNo = lastRefundItem.getOrderRefundItemNo();
                BigDecimal platformRefundAmount = orderRefund.getPlatformRefundAmount();
                Date managerReviewTime = orderRefund.getManagerReviewTime();
                String managerReviewOpinion = orderRefund.getManagerReviewOpinion();

                Date supplierReviewTime = orderRefund.getSupplierReviewTime();
                String supplierReviewOpinion = orderRefund.getSupplierReviewOpinion();

                OrderRefundItemBody refundInfo = new OrderRefundItemBody();
                refundInfo.setOrderRefundNo(orderRefundNo);
                refundInfo.setOrderRefundItemNo(orderRefundItemNo);
                refundInfo.setRefundReason(refundRuleReason);
                refundInfo.setRefundPrice(DecimalUtil.bigDecimalToString(platformRefundAmount));
                refundInfo.setApplicationTime(DateUtil.format(refundApplyTime, dateFormat));
                refundInfo.setRefundStatus(refundStatusStr);
                refundInfo.setHandleTime(DateUtil.format(supplierReviewTime, dateFormat));
                if (supplierReviewTime != null) {
                    refundInfo.setHandleTime(DateUtil.format(supplierReviewTime, dateFormat));
                } else if (managerReviewTime != null) {
                    refundInfo.setHandleTime(DateUtil.format(managerReviewTime, dateFormat));
                }

                if (OrderRefundStateType.Reject.name().equals(refundStatusStr)) {
                    if (StrUtil.isNotBlank(supplierReviewOpinion)) {
                        refundInfo.setVerifyOpinion(supplierReviewOpinion);
                    } else if (StrUtil.isNotBlank(managerReviewOpinion)) {
                        refundInfo.setVerifyOpinion(managerReviewOpinion);
                    }
                }
                orderPageVo.setOrderRefundInfo(refundInfo);
            }
        }
        BigDecimal refundExecutableAmount = orders.getPlatformRefundExecutableAmount();
        orderPageVo.setRefundExecutableAmount(refundExecutableAmount);

        detailVo.setOrderItemsCategory(categoryList);
        detailVo.setOrderBody(orderPageVo);
        return detailVo;
    }

    /**
     * 子订单分组
     *
     * @param logisticsInfo
     * @param orderItems
     */
    protected Map<String, OrderItemDetailVo> orderItemsGrouping(List<OrderItem> orderItems, OrderLogisticsInfo logisticsInfo) {
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        Map<String, OrderItemDetailVo> logisticsAndCategory = new HashMap<>();
        String hours =
            businessParameterService.getValueFromString(BusinessParameterType.CONFIRM_DISPATCHED_HOURS);
        // 员工最大可以重复确认发货的时间（小时）
        Long maxHours = Long.parseLong(hours);

        for (OrderItem orderItem : orderItems) {
            String orderItemNo = orderItem.getOrderItemNo();
            String supplierTenantId = orderItem.getSupplierTenantId();
            OrderStateType orderItemState = orderItem.getOrderState();
            LogisticsProgress fulfillmentProgress = orderItem.getFulfillmentProgress();
            // 如果是供货商用户调用，则不是自己的商品就跳过
            if (TenantType.Supplier.equals(tenantType) && !StringUtils.equals(LoginHelper.getTenantId(), supplierTenantId)) {
                continue;
            }

            StringBuilder mapKey = new StringBuilder();
            String key_orderItem = "[" + orderItemNo + "]";
            mapKey.append(key_orderItem);

            String key_fulfillment = "[" + fulfillmentProgress.name() + "]";
            mapKey.append(key_fulfillment);

            OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());

            String productName = orderProductSku.getProductName();
            String carrier = "";
            String allTrackingNo = "";
            boolean systemManaged = false;
            if (logisticsInfo != null) {
                carrier = logisticsInfo.getLogisticsCompanyName();
            }

            List<OrderItemTrackingRecord> trackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
            List<TrackingNoBo> trackingInfoList = new ArrayList<>();
            List<String> trackingNoList = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(trackingList)) {
                List<Integer> callingApiList = new ArrayList<>();
                for (OrderItemTrackingRecord tracking : trackingList) {
                    String trackingNo = tracking.getLogisticsTrackingNo();
                    // 组合标签
                    if (StringUtils.isNotBlank(tracking.getLogisticsCarrier())) {
                        carrier = tracking.getLogisticsCarrier();
                    }

                    if (StringUtils.isNotBlank(trackingNo)) {
                        trackingNoList.add(trackingNo);
                        trackingInfoList.add(new TrackingNoBo(tracking.getLogisticsCarrier(), trackingNo));
                    }

                    // 若systemManaged已经为True了，不能再修改，因为Tracking有多条，应该所有systemManaged状态都为False才为False
                    if (!systemManaged) {
                        systemManaged = tracking.getSystemManaged();
                    }
                }
                if (StringUtils.isNotBlank(carrier)) {
                    mapKey.append("[").append(carrier).append("]");
                } else {
                    mapKey.append("[").append("NoCarrier").append("]");
                }

                if (CollectionUtils.isNotEmpty(trackingNoList)) {
                    allTrackingNo = CollUtil.join(trackingNoList, ";");
                    mapKey.append("[").append(allTrackingNo).append("]");
                } else {
                    mapKey.append("[").append("NoTracking").append("]");
                }
            }

            if (logisticsInfo != null && StringUtils.isNotBlank(logisticsInfo.getLogisticsAccount())) {
                mapKey.append("[").append(logisticsInfo.getLogisticsAccount()).append("]");
            } else {
                mapKey.append("[").append("No3rdBilling").append("]");
            }

            // 组合标签
            String warehouseSystemCode = orderProductSku.getWarehouseSystemCode();
            if (StringUtils.isNotBlank(warehouseSystemCode)) {
                mapKey.append("[").append(warehouseSystemCode).append("]");
            } else {
                mapKey.append("[").append("NoWarehouse").append("]");
            }

            if (Objects.equals(systemManaged, ConditionType.True)) {
                mapKey.append("[SystemManaged]");
            } else {
                mapKey.append("[NoSystemManaged]");
            }

            // 将各种状态组合成标签，每一个组合后的标签代表一个分组，展示在前端是一个订单区域
            OrderItemDetailVo categoryBody = logisticsAndCategory.get(mapKey.toString());
            if (categoryBody == null) {
                categoryBody = new OrderItemDetailVo();
                categoryBody.setFulfillment(fulfillmentProgress);
                if (StrUtil.isNotBlank(carrier)) {
                    categoryBody.setCarrier(carrier);
                } else {
                    categoryBody.setCarrier("");
                }
                categoryBody.setTrackingNo(StrUtil.replace(allTrackingNo, ";", "\n"));
                categoryBody.setTrackingInfoList(trackingInfoList);

                if (StrUtil.isNotBlank(warehouseSystemCode)) {
                    categoryBody.setWarehouseCode(warehouseSystemCode);
                } else {
                    categoryBody.setWarehouseCode("");
                }

                if (logisticsInfo != null) {
                    String logisticsAccount = logisticsInfo.getLogisticsAccount();
                    if (StringUtils.isNotBlank(logisticsAccount)) {
                        categoryBody.setLogisticsAccount(logisticsAccount);
                    }
                }

                // 是否显示确认发货按钮
                Boolean showDispatchedBtn = true;

                // 履约状态不是未发货，将禁用确认发货按钮
                if (ObjectUtil.notEqual(fulfillmentProgress, LogisticsProgress.UnDispatched)) {
                    showDispatchedBtn = false;

                    // 例外条件，如果已发货，且当前用户为员工账号，且确认发货在指定的时间内（目前48小时内），员工可以操作再次确认发货
                    if (ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Dispatched) && ObjectUtil.equals(tenantType, TenantType.Manager)) {
                        Date dispatchedTime = orderItem.getDispatchedTime();
                        if (dispatchedTime != null) {
                            long betweenHours = DateUtil.between(dispatchedTime, new Date(), DateUnit.HOUR);
                            if (NumberUtil.isLess(NumberUtil.toBigDecimal(betweenHours), NumberUtil.toBigDecimal(maxHours))) {
                                showDispatchedBtn = true;
                            }
                        }
                    }
                }
                categoryBody.setShowBtn(showDispatchedBtn);
            }

            List<OrderItemVo> orderItemBodies = categoryBody.getOrderItems();
            if (orderItemBodies == null) {
                orderItemBodies = new ArrayList<>();
            }

            // 处理子订单数据
            OrderItemVo orderItemBody = new OrderItemVo();
            if (orderProductSku != null) {
                OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItem.getOrderItemNo());
                Integer orderItemNum = orderItem.getTotalQuantity();
                BigDecimal originalUnitPrice = orderItemPrice.getOriginalUnitPrice();
                BigDecimal productTotalPriceSup = orderItem.getOriginalPayableTotalAmount();
                BigDecimal originalPayableTotalAmount = orderItem.getOriginalPayableTotalAmount();
                BigDecimal productTotalPrice = orderItem.getPlatformPayableTotalAmount();
                BigDecimal platformPayableTotalAmount = orderItem.getPlatformPayableTotalAmount();

                OrderProductSkuBo orderProductSkuBo = new OrderProductSkuBo();
                String description = orderProductSku.getDescription();
                if (StringUtils.isNotBlank(description)) {
                    description = HtmlUtil.cleanHtmlTag(description);
                    orderProductSkuBo.setDesc(description);
                }
                orderProductSkuBo.setSalesChannelOrderId(orderItemNo);
                orderProductSkuBo.setSku(orderProductSku.getSku());
                orderProductSkuBo.setItemNo(orderProductSku.getProductSkuCode());
                orderProductSkuBo.setWarehouseSystemCode(orderProductSku.getWarehouseSystemCode());
                orderProductSkuBo.setImgUrl(orderProductSku.getImageShowUrl());
                orderItemBody.setProductName(productName);
                orderItemBody.setImageShowUrl(orderProductSku.getImageShowUrl());
                orderItemBody.setNum(orderItemNum);
                orderItemBody.setOrderId(orderItemNo);
                orderItemBody.setOrderItemNo(orderItemNo);
                orderItemBody.setProductSku(orderProductSkuBo);

                BigDecimal depositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
                BigDecimal depositTotalPrice = BigDecimal.ZERO;
                if (depositUnitPrice != null) {
                    //如果是供应商，而且是在定价之后开启活动，替换订金单价
                    BigDecimal originalDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice();
                    if (TenantType.Supplier.equals(tenantType) && originalDepositUnitPrice != null) {
                        depositUnitPrice = originalDepositUnitPrice;
                    }
                    depositTotalPrice = NumberUtil.mul(depositUnitPrice, orderItemNum);
                    orderItemBody.setDepositUnitPrice(depositUnitPrice);
                    orderItemBody.setDepositTotalPrice(depositTotalPrice);
                }

                //如果是供应商，而且是在定价之后开启活动
                if (TenantType.Supplier.equals(tenantType) && originalUnitPrice != null) {
                    orderItemBody.setUnitPrice(originalUnitPrice);
                    orderItemBody.setProductTotalPrice(productTotalPriceSup);
                    orderItemBody.setTotalPrice(originalPayableTotalAmount);
                } else {
                    orderItemBody.setUnitPrice(orderItemPrice.getPlatformUnitPrice());
                    orderItemBody.setProductTotalPrice(productTotalPrice);
                    orderItemBody.setTotalPrice(platformPayableTotalAmount);
                }

                Date dispatchedTime = orderItem.getDispatchedTime();
                if (ObjectUtil.isNotNull(dispatchedTime)) {
                    String formatDate2Str = DateUtil.format(dispatchedTime, dateFormat);
                    orderItemBody.setDeliveryTime(formatDate2Str);
                }

                Boolean canRefund;
                // 查询最近一条退款记录
                List<OrderRefundItem> lastRefundItems = iOrderRefundItemService.queryListByOrderItemId(orderItem.getId());
                List<Long> orderRefundIds = lastRefundItems.stream().map(OrderRefundItem::getOrderRefundId).collect(Collectors.toList());
                OrderRefund orderRefund = null;
                if (CollUtil.isNotEmpty(orderRefundIds)) {
                    orderRefund = iOrderRefundService.queryByIdNotTenant(orderRefundIds.get(0));
                }
                if (!Objects.equals(orderItemState, OrderStateType.Paid)) {
                    canRefund = false;
                } else {
                    if (ObjectUtil.isNotNull(orderRefund)) {
                        canRefund = orderRefundRuleService.compareRefundRules(orderItem, true);
                    } else {
                        canRefund = false;
                    }
                }

                // 获取活动编码
                if (ObjectUtil.isNotNull(orderProductSku.getActivityType())) {
                    if (TenantType.Distributor.equals(tenantType)) {
                        orderItemBody.setActivityCode(orderProductSku.getActivityCode());
                    } else {
                        ProductActivity productActivity = iProductActivityService.queryActivityByActivityItemNo(orderProductSku.getActivityCode());
                        orderItemBody.setActivityCode(productActivity.getActivityCode());
                    }
                }

                // 打包售后信息
                packageRefund:
                {
                    if (CollUtil.isNotEmpty(lastRefundItems)) {
                        OrderRefundItem lastRefundItem = lastRefundItems.get(0);
                        OrderRefundStateType refundState = orderRefund.getRefundState();
                        if (ObjectUtil.equals(refundState, OrderRefundStateType.Reject)) {
                            orderItemBody.setRefundStatus(OrderRefundStateType.Reject.name());
                        } else if (ObjectUtil.equals(refundState, OrderRefundStateType.Refunded)) {
                            orderItemBody.setRefundStatus(OrderRefundStateType.Refunded.name());
                        } else if (ObjectUtil.equals(refundState, OrderRefundStateType.Canceled)) {
                            break packageRefund;
                        } else {
                            orderItemBody.setRefundStatus(OrderRefundStateType.Refunding.name());
                        }

                        String refundStatusStr = orderItemBody.getRefundStatus();
                        Date refundApplyTime = orderRefund.getRefundApplyTime();
                        String refundRuleReason = orderRefund.getRefundRuleReason();

                        String orderRefundItemNo = lastRefundItem.getOrderRefundItemNo();
                        BigDecimal refundPrice = lastRefundItem.getPlatformPayableTotalAmount();
                        Date managerReviewTime = orderRefund.getManagerReviewTime();
                        String managerReviewOpinion = orderRefund.getManagerReviewOpinion();
                        String supplierUserId = orderRefund.getSupplierUserId();
                        String supplierReviewOpinion = orderRefund.getSupplierReviewOpinion();
                        Date supplierReviewTime = orderRefund.getSupplierReviewTime();

                        OrderRefundItemBody refundInfo = new OrderRefundItemBody();
                        refundInfo.setOrderRefundItemNo(orderRefundItemNo);
                        refundInfo.setRefundReason(refundRuleReason);
                        refundInfo.setRefundPrice(DecimalUtil.bigDecimalToString(refundPrice));
                        refundInfo.setApplicationTime(DateUtil.format(refundApplyTime, "yyyy-MM-dd HH:mm:ss"));
                        refundInfo.setRefundStatus(refundStatusStr);
                        if (supplierReviewTime != null) {
                            refundInfo.setHandleTime(DateUtil.format(supplierReviewTime, "yyyy-MM-dd HH:mm:ss"));
                        } else if (managerReviewTime != null) {
                            refundInfo.setHandleTime(DateUtil.format(managerReviewTime, "yyyy-MM-dd HH:mm:ss"));
                        }

                        if (OrderRefundStateType.Reject.name().equals(refundStatusStr)) {
                            if (StrUtil.isNotBlank(supplierReviewOpinion)) {
                                refundInfo.setVerifyOpinion(supplierReviewOpinion);
                            } else if (StrUtil.isNotBlank(managerReviewOpinion)) {
                                refundInfo.setVerifyOpinion(managerReviewOpinion);
                            }
                        }
                        orderItemBody.setRefundInfo(refundInfo);
                    }
                }
                BigDecimal platformRefundExecutableAmount = orderItem.getPlatformRefundExecutableAmount();
                orderItemBody.setRefundExecutableAmount(platformRefundExecutableAmount);

                Boolean canConfirmReceipt = ObjectUtil.equals(orderItemState, OrderStateType.Paid) && ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Dispatched);
                orderItemBody.setCanRefund(canRefund);
                orderItemBody.setCanConfirmReceipt(canConfirmReceipt);
                orderItemBodies.add(orderItemBody);
            }
            categoryBody.setOrderItems(orderItemBodies);
            categoryBody.setNum(orderItemBodies.size());
            logisticsAndCategory.put(mapKey.toString(), categoryBody);
        }
        return logisticsAndCategory;
    }


    /**
     * 验证订单类型
     *
     * @param orderType
     * @return
     */
    @Override
    public boolean isThisImpl(OrderType orderType) {
        return OrderType.Wholesale.equals(orderType);
    }

    @Override
    public OrderDetailVo buildOrderDetailForExport(Orders item) {
        return null;
    }
}
