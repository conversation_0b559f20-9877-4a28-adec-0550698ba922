package com.zsmall.order.biz.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.core.exception.base.BaseException;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.domain.SysInfEnum;
import com.hengjian.openapi.domain.vo.SysInfVo;
import com.hengjian.openapi.service.ISysInfService;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.activity.entity.iservice.IProductActivityPriceItemService;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.bma.open.member.iservice.IMemberRuleRelationV2ServiceImpl;
import com.zsmall.bma.open.member.service.MemberRuleRelationV2Service;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.calculate.entity.support.DeliveryFeeSupport;
import com.zsmall.calculate.entity.util.DeliveryFeeV2Utils;
import com.zsmall.common.domain.DeliveryFeeByErpRequest;
import com.zsmall.common.domain.DeliveryFeeByErpResponse;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemPrice;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrderAddressInfoService;
import com.zsmall.order.entity.iservice.IOrderItemPriceService;
import com.zsmall.order.entity.iservice.IOrderLogisticsInfoService;
import com.zsmall.product.biz.support.MemberSupport;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuPriceService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/24 09:58
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Deprecated
public class PriceSupport {

    private final ISysConfigService sysConfigService;

    private final IProductActivityPriceItemService iProductActivityPriceItemService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuService iProductSkuService;
    private final IProductService iProductService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final MemberRuleRelationV2Service iMemberRuleRelationService;
    private final IMemberRuleRelationV2ServiceImpl memberRuleRelationService;
    private final MemberSupport memberSupport;
    private final IMemberLevelV2ServiceImpl iMemberLevelService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final DeliveryFeeV2Utils deliveryFeeUtils;
    private final ProductSkuStockMapper productSkuStockMapper;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final DeliveryFeeSupport deliveryFeeSupport;
    private final IWarehouseService iWarehouseService;
    private final ISysInfService sysInfService;
    private final ISysTenantService sysTenantService;
    private final IProductSkuService productSkuService;

    /**
     * 功能描述：重新计算旧订单金额
     *
     * @param order              秩序
     * @param orderItemPriceList 订购商品价格表
     * <AUTHOR>
     * @date 2024/08/22
     */
    @Deprecated
    public void recalculateOrderAmountOld(Orders order, List<OrderItemPrice> orderItemPriceList) {
        log.info("准备开始重新计算订单金额 子订单价格数据 = {}", JSONUtil.toJsonStr(orderItemPriceList));
        LogisticsTypeEnum logisticsType = order.getLogisticsType();
        BigDecimal originalTotalProductAmount = BigDecimal.ZERO;
        BigDecimal originalTotalOperationFee = BigDecimal.ZERO;
        BigDecimal originalTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal originalTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal originalTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal originalPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal originalPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal originalActualTotalAmount = BigDecimal.ZERO;
        BigDecimal originalRefundExecutableAmount = BigDecimal.ZERO;

        BigDecimal platformTotalProductAmount = BigDecimal.ZERO;
        BigDecimal platformTotalOperationFee = BigDecimal.ZERO;
        BigDecimal platformTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal platformTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal platformTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal platformPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal platformPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal platformActualTotalAmount = BigDecimal.ZERO;
        BigDecimal platformRefundExecutableAmount = BigDecimal.ZERO;

        for (OrderItemPrice orderItemPrice : orderItemPriceList) {
            Integer totalQuantity = orderItemPrice.getTotalQuantity();
            // 会员定价逻辑
            ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, orderItemPrice.getProductSkuCode())));
            Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));
            String tenantId = null;
            if (ObjectUtil.isNotEmpty(order.getTenantId())) {
                tenantId = order.getTenantId();
            } else {
                tenantId = LoginHelper.getTenantId();
            }
//            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), tenantId, productSku.getId(), );
            BigDecimal originalPickUpPrice = null;
            BigDecimal originalDropShippingPrice = null;
            originalPickUpPrice = orderItemPrice.getOriginalPickUpPrice();

            originalDropShippingPrice = orderItemPrice.getOriginalDropShippingPrice();
            BigDecimal platformPickUpPrice = null;
            platformPickUpPrice = orderItemPrice.getPlatformPickUpPrice();

            BigDecimal platformDropShippingPrice = null;
            platformDropShippingPrice = orderItemPrice.getPlatformDropShippingPrice();

            BigDecimal platformFinalDeliveryFee = null;
            platformFinalDeliveryFee = orderItemPrice.getPlatformFinalDeliveryFee();
            BigDecimal originalUnitPrice = null;
            originalUnitPrice = orderItemPrice.getOriginalUnitPrice();
            BigDecimal originalOperationFee = null;
            originalOperationFee = orderItemPrice.getOriginalOperationFee();
            BigDecimal originalFinalDeliveryFee = null;
            originalFinalDeliveryFee = orderItemPrice.getOriginalFinalDeliveryFee();

            BigDecimal platformUnitPrice = null;
            platformUnitPrice = orderItemPrice.getPlatformUnitPrice();
//          会员计价
            BigDecimal platformOperationFee = null;
            platformOperationFee = orderItemPrice.getPlatformOperationFee();

//            if (ObjectUtil.isNotEmpty(memberPrice)) {
//                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
//                    originalPickUpPrice = memberPrice.getOriginalPickUpPrice();
//                    if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//                        orderItemPrice.setOriginalBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
//                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
//                    }
//                }
//                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalDropShippingPrice())) {
//                    originalDropShippingPrice = memberPrice.getOriginalDropShippingPrice();
//                    originalFinalDeliveryFee = memberPrice.getOriginalFinalDeliveryFee();
//                    if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
//                        orderItemPrice.setOriginalBalanceUnitPrice(memberPrice.getOriginalDropShippingPrice());
//                    }
//                }
//                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformPickUpPrice())) {
//                    if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
//                        orderItemPrice.setPlatformBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
//                    }
//                    platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
//                }
//                if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
//                    if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                        orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
//                        orderItemPrice.setPlatformBalanceUnitPrice(memberPrice.getPlatformDropShippingPrice());
//                    }
//                    // tag lty
//                    platformDropShippingPrice = memberPrice.getPlatformDropShippingPrice();
//                    platformFinalDeliveryFee = memberPrice.getPlatformFinalDeliveryFee();
//                }
//                if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice()) || ObjectUtil.isNotEmpty(memberPrice.getOriginalDropShippingPrice())) {
//                    originalUnitPrice = memberPrice.getOriginalUnitPrice();
//
//                    originalOperationFee = memberPrice.getOriginalOperationFee();
//                    platformOperationFee = memberPrice.getPlatformOperationFee();
//                }
//            }
//            会员定价逻辑  OriginalBalanceUnitPrice  OriginalBalanceUnitPrice

            BigDecimal originalDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice();
            BigDecimal originalBalanceUnitPrice = orderItemPrice.getOriginalBalanceUnitPrice();

            BigDecimal platformDepositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
            BigDecimal platformBalanceUnitPrice = orderItemPrice.getPlatformBalanceUnitPrice();

            BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
            BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);

                // 代发时，应付单价需要加上尾程派送费
                // originalBalanceUnitPrice = NumberUtil.add(originalBalanceUnitPrice, orderItemPrice.getOriginalFinalDeliveryFee());
                // platformBalanceUnitPrice = NumberUtil.add(platformBalanceUnitPrice, orderItemPrice.getPlatformFinalDeliveryFee());
            }

            originalTotalProductAmount = originalTotalProductAmount.add(NumberUtil.mul(originalUnitPrice, totalQuantity));
            originalTotalOperationFee = originalTotalOperationFee.add(NumberUtil.mul(originalOperationFee, totalQuantity));
            originalTotalFinalDeliveryFee = originalTotalFinalDeliveryFee.add(NumberUtil.mul(originalFinalDeliveryFee, totalQuantity));
            originalTotalPickUpPrice = originalTotalPickUpPrice.add(NumberUtil.mul(originalPickUpPrice, totalQuantity));
            originalTotalDropShippingPrice = originalTotalDropShippingPrice.add(NumberUtil.mul(originalDropShippingPrice, totalQuantity));

            originalPayableTotalAmount = originalPayableTotalAmount.add(NumberUtil.mul(originalPayableUnitPrice, totalQuantity));
            originalPrepaidTotalAmount = originalPrepaidTotalAmount.add(NumberUtil.mul(originalDepositUnitPrice, totalQuantity));
            originalActualTotalAmount = originalActualTotalAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));
            originalRefundExecutableAmount = originalRefundExecutableAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));

            platformTotalProductAmount = platformTotalProductAmount.add(NumberUtil.mul(platformUnitPrice, totalQuantity));
            platformTotalOperationFee = platformTotalOperationFee.add(NumberUtil.mul(platformOperationFee, totalQuantity));
            platformTotalFinalDeliveryFee = platformTotalFinalDeliveryFee.add(NumberUtil.mul(platformFinalDeliveryFee, totalQuantity));
            platformTotalPickUpPrice = platformTotalPickUpPrice.add(NumberUtil.mul(platformPickUpPrice, totalQuantity));
            platformTotalDropShippingPrice = platformTotalDropShippingPrice.add(NumberUtil.mul(platformDropShippingPrice, totalQuantity));

            platformPayableTotalAmount = platformPayableTotalAmount.add(NumberUtil.mul(platformPayableUnitPrice, totalQuantity));
            platformPrepaidTotalAmount = platformPrepaidTotalAmount.add(NumberUtil.mul(platformDepositUnitPrice, totalQuantity));
            platformActualTotalAmount = platformActualTotalAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
            platformRefundExecutableAmount = platformRefundExecutableAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
        }

        order.setOriginalTotalProductAmount(originalTotalProductAmount);
        order.setOriginalTotalOperationFee(originalTotalOperationFee);
        order.setOriginalTotalFinalDeliveryFee(originalTotalFinalDeliveryFee);
        order.setOriginalTotalPickUpPrice(originalTotalPickUpPrice);
        order.setOriginalTotalDropShippingPrice(originalTotalDropShippingPrice);
        order.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        order.setOriginalPrepaidTotalAmount(originalPrepaidTotalAmount);
        order.setOriginalActualTotalAmount(originalActualTotalAmount);
        order.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);

        order.setPlatformTotalProductAmount(platformTotalProductAmount);
        order.setPlatformTotalOperationFee(platformTotalOperationFee);
        order.setPlatformTotalFinalDeliveryFee(platformTotalFinalDeliveryFee);

        order.setPlatformTotalPickUpPrice(platformTotalPickUpPrice);

        order.setPlatformTotalDropShippingPrice(platformTotalDropShippingPrice);
        order.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        order.setPlatformPrepaidTotalAmount(platformPrepaidTotalAmount);
        // 渠道订单,此处不赋予值的变更
        Integer orderSource = order.getOrderSource();
        ChannelTypeEnum channelType = order.getChannelType();
        // 数据清洗后基本上满足条件
        if(ObjectUtil.isNotEmpty(orderSource)&& ObjectUtil.isNotEmpty(channelType)){
            // 接口订单与openApi订单不需要再次对销售额金额进行重新计算,但后续可能会有其他逻辑此处保留
            if((OrderSourceEnum.INTERFACE_ORDER.getValue().equals(orderSource)||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource))){

            }else{
                order.setPlatformActualTotalAmount(platformActualTotalAmount);
            }

        }else{
            order.setPlatformActualTotalAmount(platformActualTotalAmount);
        }

        order.setPlatformRefundExecutableAmount(platformRefundExecutableAmount);
        log.info("重新计算后的订单金额 = {}", JSONUtil.toJsonStr(order));
    }
    /**
     * 主订单重新计算订单金额,包含会员价,内部循环,原因是单子目前都是单个的,不会有多个
     * 适用场景: 1. 支付前置检查 2. 临时单转换正式单 3. 分销商改变订单详情
     * 影响业务: Orders (要带channelType)
     * 必备参数: orders: logisticsType tenantId   List<OrderItemPrice>  totalQuantity,productSkuCode
     * 业务前言: orderItemPriceList 为子订单价格数据 由calculationOrderItemPrice 方法生成,两个方法需在同一作用域
     * 禁用业务: tiktok订单
     * 禁用Demo:
     * if (ChannelTypeEnum.TikTok.equals(order.getChannelType())) {
     * } else {
     * recalculateOrderAmount(order, orderItemPriceList);
     * }
     *
     * @param order
     * @param orderItemPriceList
     */
    public void recalculateOrderAmount(Orders order, List<OrderItemPrice> orderItemPriceList) {
        log.info("准备开始重新计算订单金额 子订单价格数据 = {}", JSONUtil.toJsonStr(orderItemPriceList));
        LogisticsTypeEnum logisticsType = order.getLogisticsType();
        BigDecimal originalTotalProductAmount = BigDecimal.ZERO;
        BigDecimal originalTotalOperationFee = BigDecimal.ZERO;
        BigDecimal originalTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal originalTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal originalTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal originalPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal originalPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal originalActualTotalAmount = BigDecimal.ZERO;
        BigDecimal originalRefundExecutableAmount = BigDecimal.ZERO;

        BigDecimal platformTotalProductAmount = BigDecimal.ZERO;
        BigDecimal platformTotalOperationFee = BigDecimal.ZERO;
        BigDecimal platformTotalFinalDeliveryFee = BigDecimal.ZERO;
        BigDecimal platformTotalPickUpPrice = BigDecimal.ZERO;
        BigDecimal platformTotalDropShippingPrice = BigDecimal.ZERO;
        BigDecimal platformPayableTotalAmount = BigDecimal.ZERO;
        BigDecimal platformPrepaidTotalAmount = BigDecimal.ZERO;
        BigDecimal platformActualTotalAmount = BigDecimal.ZERO;
        BigDecimal platformRefundExecutableAmount = BigDecimal.ZERO;


        // 分销商id
        String tenantId = order.getTenantId();
        if (!ObjectUtil.isNotEmpty(tenantId)) {
            tenantId = LoginHelper.getTenantId();
        }

        for (OrderItemPrice orderItemPrice : orderItemPriceList) {
            Integer totalQuantity = orderItemPrice.getTotalQuantity();
            // 会员定价逻辑
            ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, orderItemPrice.getProductSkuCode())));
            String supTenantId = productSku.getTenantId();
            String productSkuCode = productSku.getProductSkuCode();
            Product product = TenantHelper.ignore(() -> iProductService.getById(productSku.getProductId()));


//            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), tenantId, productSku.getId(), );
            BigDecimal originalPickUpPrice = null;
            BigDecimal originalDropShippingPrice = null;
            originalPickUpPrice = orderItemPrice.getOriginalPickUpPrice();
            //
            originalDropShippingPrice = orderItemPrice.getOriginalDropShippingPrice();
            BigDecimal platformPickUpPrice = null;
            platformPickUpPrice = orderItemPrice.getPlatformPickUpPrice();

            BigDecimal platformDropShippingPrice = null;
            platformDropShippingPrice = orderItemPrice.getPlatformDropShippingPrice();

            BigDecimal platformFinalDeliveryFee = null;
            // 此处需要调用erp了,如果下面的erp调用的结果不一致,则需要重新计算
            platformFinalDeliveryFee = orderItemPrice.getPlatformFinalDeliveryFee();
            BigDecimal originalUnitPrice = null;
            originalUnitPrice = orderItemPrice.getOriginalUnitPrice();
            BigDecimal originalOperationFee = null;
            originalOperationFee = orderItemPrice.getOriginalOperationFee();
            BigDecimal originalFinalDeliveryFee = null;
            // 这里实际上是从erp获取的,此处已经做好了erp配送费的金额计算了
            originalFinalDeliveryFee = orderItemPrice.getOriginalFinalDeliveryFee();
            BigDecimal platformUnitPrice = null;
            BigDecimal platformOperationFee = null;
            BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
            BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);

            BigDecimal originalDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice();
            BigDecimal originalBalanceUnitPrice = orderItemPrice.getOriginalBalanceUnitPrice();

            BigDecimal platformDepositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
            BigDecimal platformBalanceUnitPrice = orderItemPrice.getPlatformBalanceUnitPrice();
            if(ObjectUtil.isEmpty(originalFinalDeliveryFee)){
                // todo 所有相关的代发价格设置为null
                originalTotalProductAmount = originalTotalProductAmount.add(NumberUtil.mul(originalUnitPrice, totalQuantity));
                originalTotalOperationFee = originalTotalOperationFee.add(NumberUtil.mul(originalOperationFee, totalQuantity));

                originalTotalFinalDeliveryFee = null;
                originalTotalPickUpPrice = originalTotalPickUpPrice.add(NumberUtil.mul(originalPickUpPrice, totalQuantity));
                originalTotalDropShippingPrice = null;
                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                    if(ObjectUtil.isNotEmpty(originalDropShippingPrice)){
                        originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                    }else {
                        originalPayableUnitPrice = null;
                    }
                    if(ObjectUtil.isNotEmpty(platformDropShippingPrice)){
                        platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
                    }else {
                        platformPayableUnitPrice = null;
                    }

                }
                if(ObjectUtil.isNotEmpty(originalPayableUnitPrice)){
                    originalPayableTotalAmount = originalPayableTotalAmount.add(NumberUtil.mul(originalPayableUnitPrice, totalQuantity));
                }else {
                    originalPayableTotalAmount = null;
                }
                // zero
                if (ObjectUtil.isNotEmpty(originalDepositUnitPrice)){
                    originalPrepaidTotalAmount = originalPrepaidTotalAmount.add(NumberUtil.mul(originalDepositUnitPrice, totalQuantity));
                }else {
                    originalPrepaidTotalAmount = null;
                }

                // 如果是自提对应的就是自提价 如果是代发对应的就是代发价
                if(ObjectUtil.isNotEmpty(originalBalanceUnitPrice)){
                    originalActualTotalAmount = originalActualTotalAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));
                    originalRefundExecutableAmount = originalRefundExecutableAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));
                }else {
                    originalActualTotalAmount = null;
                    originalRefundExecutableAmount = null;
                }


                platformTotalProductAmount = platformTotalProductAmount.add(NumberUtil.mul(platformUnitPrice, totalQuantity));
                platformTotalOperationFee = platformTotalOperationFee.add(NumberUtil.mul(platformOperationFee, totalQuantity));

                platformTotalFinalDeliveryFee = null;

                platformTotalPickUpPrice = platformTotalPickUpPrice.add(NumberUtil.mul(platformPickUpPrice, totalQuantity));
                platformTotalDropShippingPrice = null;

                if(ObjectUtil.isNotEmpty(platformPayableUnitPrice)){
                    platformPayableTotalAmount = platformPayableTotalAmount.add(NumberUtil.mul(platformPayableUnitPrice, totalQuantity));
                }else {
                    platformPayableTotalAmount = null;
                }

                platformPrepaidTotalAmount = platformPrepaidTotalAmount.add(NumberUtil.mul(platformDepositUnitPrice, totalQuantity));
                if(ObjectUtil.isNotEmpty(platformBalanceUnitPrice)){
                    platformActualTotalAmount = platformActualTotalAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
                    platformRefundExecutableAmount = platformRefundExecutableAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
                }else {
                    platformActualTotalAmount = null;
                    platformRefundExecutableAmount = null;
                }

            }else{
                platformUnitPrice = orderItemPrice.getPlatformUnitPrice();
                platformDropShippingPrice = platformPickUpPrice.add(platformFinalDeliveryFee);
                platformOperationFee = orderItemPrice.getPlatformOperationFee();
                // 如果是分销商拿到会员等级的运费折扣,计算价格接口出异常,订单增加异常种类,相关计算的价格置为null
                BigDecimal finalDeliveryFeeFromErp;
                // 这里的费用已经算过会员价了
                finalDeliveryFeeFromErp = originalFinalDeliveryFee;
//                if (ObjectUtil.isNotEmpty(memberPrice)&&(ObjectUtil.isNotEmpty(memberPrice.getOriginalUnitPrice())&&ObjectUtil.isNotEmpty(memberPrice.getOriginalOperationFee()))) {
//                    if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
//                        originalPickUpPrice = memberPrice.getOriginalPickUpPrice();
//                        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//                            orderItemPrice.setOriginalBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
//                            orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
//                        }
//                    }
//
//                    // 会员派送费的折扣逻辑
//                    BigDecimal unitPrice = memberPrice.getOriginalUnitPrice();
//                    BigDecimal operationFee = memberPrice.getOriginalOperationFee();
//
//                    BigDecimal memberDropShippingPrice = unitPrice.add(operationFee).add(finalDeliveryFeeFromErp);
//                    // 这里的价格为null,说明走的是默认的单价和操作费
//                    if (ObjectUtil.isNotEmpty(unitPrice) && ObjectUtil.isNotEmpty(operationFee)) {
//                        originalDropShippingPrice = memberDropShippingPrice;
//
//                        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                            orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
//                            orderItemPrice.setOriginalBalanceUnitPrice(originalDropShippingPrice);
//                        }
//                    }
//                    if (ObjectUtil.isNotEmpty(memberPrice.getPlatformPickUpPrice())) {
//                        if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
//                            orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
//                            orderItemPrice.setPlatformBalanceUnitPrice(memberPrice.getOriginalPickUpPrice());
//                        }
//                        platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
//                    }
//                    if (ObjectUtil.isNotEmpty(memberDropShippingPrice)) {
//                        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
//                            orderItemPrice.setOriginalUnitPrice(memberPrice.getOriginalUnitPrice());
//                            // 这个既有代发也有自提,价格统一放这里
//                            orderItemPrice.setPlatformBalanceUnitPrice(memberDropShippingPrice);
//                        }
//                        // tag lty
//                        platformDropShippingPrice = memberDropShippingPrice;
//                        platformFinalDeliveryFee = finalDeliveryFeeFromErp;
//                    }
//                    if (ObjectUtil.isNotEmpty(memberPrice.getOriginalPickUpPrice())) {
//                        originalUnitPrice = memberPrice.getOriginalUnitPrice();
//
//                        originalOperationFee = memberPrice.getOriginalOperationFee();
//                        platformOperationFee = memberPrice.getPlatformOperationFee();
//                    }
//                }
//            会员定价逻辑  OriginalBalanceUnitPrice  OriginalBalanceUnitPrice
                if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);

                    // 代发时，应付单价需要加上尾程派送费
                    // originalBalanceUnitPrice = NumberUtil.add(originalBalanceUnitPrice, orderItemPrice.getOriginalFinalDeliveryFee());
                    // platformBalanceUnitPrice = NumberUtil.add(platformBalanceUnitPrice, orderItemPrice.getPlatformFinalDeliveryFee());
                }

                originalTotalProductAmount = originalTotalProductAmount.add(NumberUtil.mul(originalUnitPrice, totalQuantity));
                originalTotalOperationFee = originalTotalOperationFee.add(NumberUtil.mul(originalOperationFee, totalQuantity));

                originalTotalFinalDeliveryFee = originalTotalFinalDeliveryFee.add(NumberUtil.mul(finalDeliveryFeeFromErp, totalQuantity));
                originalTotalPickUpPrice = originalTotalPickUpPrice.add(NumberUtil.mul(originalPickUpPrice, totalQuantity));
                originalTotalDropShippingPrice = originalTotalDropShippingPrice.add(NumberUtil.mul(originalDropShippingPrice, totalQuantity));

                originalPayableTotalAmount = originalPayableTotalAmount.add(NumberUtil.mul(originalPayableUnitPrice, totalQuantity));
                originalPrepaidTotalAmount = originalPrepaidTotalAmount.add(NumberUtil.mul(originalDepositUnitPrice, totalQuantity));
                originalActualTotalAmount = originalActualTotalAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));
                originalRefundExecutableAmount = originalRefundExecutableAmount.add(NumberUtil.mul(originalBalanceUnitPrice, totalQuantity));

                platformTotalProductAmount = platformTotalProductAmount.add(NumberUtil.mul(platformUnitPrice, totalQuantity));
                platformTotalOperationFee = platformTotalOperationFee.add(NumberUtil.mul(platformOperationFee, totalQuantity));
                platformTotalFinalDeliveryFee = platformTotalFinalDeliveryFee.add(NumberUtil.mul(platformFinalDeliveryFee, totalQuantity));
                platformTotalPickUpPrice = platformTotalPickUpPrice.add(NumberUtil.mul(platformPickUpPrice, totalQuantity));
                platformTotalDropShippingPrice = platformTotalDropShippingPrice.add(NumberUtil.mul(platformDropShippingPrice, totalQuantity));

                platformPayableTotalAmount = platformPayableTotalAmount.add(NumberUtil.mul(platformPayableUnitPrice, totalQuantity));
                platformPrepaidTotalAmount = platformPrepaidTotalAmount.add(NumberUtil.mul(platformDepositUnitPrice, totalQuantity));
                platformActualTotalAmount = platformActualTotalAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
                platformRefundExecutableAmount = platformRefundExecutableAmount.add(NumberUtil.mul(platformBalanceUnitPrice, totalQuantity));
            }
            // 此处时order 有尾程派送费
        }

        order.setOriginalTotalProductAmount(originalTotalProductAmount);
        order.setOriginalTotalOperationFee(originalTotalOperationFee);
        order.setOriginalTotalFinalDeliveryFee(originalTotalFinalDeliveryFee);
        order.setOriginalTotalPickUpPrice(originalTotalPickUpPrice);
        order.setOriginalTotalDropShippingPrice(originalTotalDropShippingPrice);
        order.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        order.setOriginalPrepaidTotalAmount(originalPrepaidTotalAmount);
        order.setOriginalActualTotalAmount(originalActualTotalAmount);
        order.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);

        order.setPlatformTotalProductAmount(platformTotalProductAmount);
        order.setPlatformTotalOperationFee(platformTotalOperationFee);
        order.setPlatformTotalFinalDeliveryFee(platformTotalFinalDeliveryFee);

        order.setPlatformTotalPickUpPrice(platformTotalPickUpPrice);

        order.setPlatformTotalDropShippingPrice(platformTotalDropShippingPrice);
        order.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        order.setPlatformPrepaidTotalAmount(platformPrepaidTotalAmount);
        // 渠道订单,此处不赋予值的变更
        Integer orderSource = order.getOrderSource();
        ChannelTypeEnum channelType = order.getChannelType();
        // 数据清洗后基本上满足条件
        if(ObjectUtil.isNotEmpty(orderSource)&& ObjectUtil.isNotEmpty(channelType)){
            // 接口订单与openApi订单不需要再次对销售额金额进行重新计算,但后续可能会有其他逻辑此处保留
            if((OrderSourceEnum.INTERFACE_ORDER.getValue().equals(orderSource)||OrderSourceEnum.OPEN_API_ORDER.getValue().equals(orderSource))){

            }else{
                order.setPlatformActualTotalAmount(platformActualTotalAmount);
            }

        }else{
            order.setPlatformActualTotalAmount(platformActualTotalAmount);
        }

        order.setPlatformRefundExecutableAmount(platformRefundExecutableAmount);
        log.info("重新计算后的订单金额 = {}", JSONUtil.toJsonStr(order));
    }

    /**
     * 功能描述：
     * 功能描述：收到原始最终运费后,4位精度
     *
     * @param ruleCustomizerTenantId  规则定制器租户id
     * @param ruleFollowerTenantId    规则跟随者租户id
     * @param finalDeliveryFeeFromErp erp原始最终交付费
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/09/06
     */
    public BigDecimal getOriginalFinalDeliveryFeeAfterDiscount(String ruleCustomizerTenantId, String ruleFollowerTenantId, BigDecimal finalDeliveryFeeFromErp) {
        // 折扣系数实际要落在等级,所以要先找等级
        LambdaQueryWrapper<MemberRuleRelation> wrapper = new LambdaQueryWrapper<MemberRuleRelation>()
            .eq(MemberRuleRelation::getRuleCustomizerTenantId, ruleCustomizerTenantId)
            .eq(MemberRuleRelation::getRuleFollowerTenantId, ruleFollowerTenantId)
            .eq(MemberRuleRelation::getDelFlag, 0);
        MemberRuleRelation one = memberRuleRelationService.getOne(wrapper);
        BigDecimal sum =BigDecimal.ZERO;
        // 有会员价
        if(ObjectUtil.isNotEmpty(one)){
            Long levelId = one.getLevelId();
            MemberLevel memberLevel = TenantHelper.ignore(()->iMemberLevelService.getById(levelId));
            Integer status = memberLevel.getStatus();
            if(status==1){
                sum = finalDeliveryFeeFromErp;
            }else {
                Long dictCode = memberLevel.getDictCode();
                //获取折扣系数
                BigDecimal discountFactor = memberSupport.getDiscountFactor(dictCode);
                log.info(StrUtil.format("[获取ERP尾程派送费],计算会员尾程派送费，ERP返回派送费：{}，折扣系数：{}",finalDeliveryFeeFromErp,discountFactor ));
                if(ObjectUtil.isEmpty(discountFactor)){
                    throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费],计算会员尾程派送费异常,未获取到会员尾程派送费折扣系数"));
                }
                discountFactor = discountFactor.divide(new BigDecimal("100"),2, RoundingMode.HALF_UP);
                sum = finalDeliveryFeeFromErp.add(finalDeliveryFeeFromErp.multiply(discountFactor)).setScale(2, RoundingMode.HALF_UP);
            }
        }else{
            log.info(StrUtil.format("[获取ERP尾程派送费],计算尾程派送费，ERP返回派送费：{}，折扣系数：{}",finalDeliveryFeeFromErp,13 ));
            sum = finalDeliveryFeeFromErp.multiply(BigDecimal.valueOf(1.13)).setScale(2, RoundingMode.HALF_UP);
        }
        return sum;
    }


//    /**
//     * 功能描述：一口价
//     *
//     * @param ruleCustomizerTenantId  规则定制器租户id
//     * @param ruleFollowerTenantId    规则跟随者租户id
//     * @param finalDeliveryFeeFromErp erp最终交付费用
//     * @param isDistributionCalculate 是否计算分布
//     * @return {@link BigDecimal }
//     * <AUTHOR>
//     * @date 2024/09/06
//     */
//    public BigDecimal getOriginalFinalDeliveryFeeAfterDiscount(String ruleCustomizerTenantId, String ruleFollowerTenantId, BigDecimal finalDeliveryFeeFromErp,
//                                                               boolean isDistributionCalculate) {
//        BigDecimal sum =BigDecimal.ZERO;
//        sum = finalDeliveryFeeFromErp;
//
//
//        return sum;
//
//    }

    /**
     * 功能描述：
     * 功能描述：从erp获取原始最终交付费用 该接口如果没有返回价格,则尾程派送费异常
     * example : null 未获取到费用,可能是接口异常 或是 库存不足
     *
     * @param stashList      仓库清单
     * @param zipCode        邮政编码
     * @param productSkuCode 产品sku代码
     * @param dTenantId       分销商id
     * @param sTenantId
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/07/30
     */
    public DeliveryFeeByErpResponse getDeliveryFeeFromErp(List<String> stashList, String zipCode, String productSkuCode, List<String> logisticsCode,
                                                          String dTenantId, String sTenantId) {
        List<DeliveryFeeByErpResponse> deliveryFeeByErp ;
        DeliveryFeeByErpResponse deliveryFeeByErpResponse = null;
        DeliveryFeeByErpRequest deliveryFeeByErpRequest = getDeliveryFeeErpRequest(stashList,zipCode,productSkuCode,logisticsCode, dTenantId, sTenantId);
        // 测算就把下面的放开
        try{
            deliveryFeeByErp = deliveryFeeUtils.getDeliveryFeeByErp(Collections.singletonList(deliveryFeeByErpRequest),null);
            deliveryFeeByErpResponse = deliveryFeeByErp.get(0);
        }catch (Exception e){
            int i = TenantHelper.ignore(()->Integer.parseInt(sysConfigService.selectConfigByKey("IS_DeliveryFee_ABLE")));
            if(i==1){
                log.error("获取erp尾程派送费失败,尝试使用备用接口");
                //todo theo
                List<DeliveryFeeByErpResponse> deliveryFeeByErpByError = deliveryFeeSupport.getDeliveryFeeByErpByError(Collections.singletonList(deliveryFeeByErpRequest),null);
                return deliveryFeeByErpByError.get(0);
            }else {
                throw new RuntimeException("获取erp尾程派送费失败");
            }

        }
        List<String> carrierCodes = deliveryFeeByErpRequest.getCarrierCodes();
        String carrier =null;
        if(CollUtil.isNotEmpty(carrierCodes)){
            carrier = carrierCodes.get(0);
        }
        // 把这个注释 todo theo
        List<DeliveryFeeByErpResponse> deliveryFeeByErpByError = deliveryFeeSupport.getDeliveryFeeByErpByError(Collections.singletonList(deliveryFeeByErpRequest), carrier);
        return deliveryFeeByErpByError.get(0);

    }
    @NotNull
    private DeliveryFeeByErpRequest getDeliveryFeeErpRequest(List<String> stashList, String postalCode, String productSkuCode, List<String> carrierCodes,
                                                             String dTenantId, String sTenantId) {

        ProductSku one = TenantHelper.ignore(()->iProductSkuService.getOne(Wrappers.<ProductSku>lambdaQuery()
                                                                                   .eq(ProductSku::getProductSkuCode, productSkuCode)
                                                                                   .eq(ProductSku::getDelFlag, 0)));
        DeliveryFeeByErpRequest deliveryFeeByErpRequest = new DeliveryFeeByErpRequest();

        String channelFlag = TenantHelper.ignore(()->productSkuStockMapper.getChannelFlag(dTenantId));
        deliveryFeeByErpRequest.setChannelFlag(channelFlag);
        deliveryFeeByErpRequest.setDistributorTenantId(dTenantId);
        deliveryFeeByErpRequest.setSupplierTenantId(sTenantId);
        deliveryFeeByErpRequest.setOrgWarehouseCodeList(stashList);
        deliveryFeeByErpRequest.setPostcode(postalCode);
        DeliveryFeeByErpRequest.ProductItem productItem = new DeliveryFeeByErpRequest.ProductItem();
        productItem.setErpSku(one.getSku());
        productItem.setQuantity(1);

        deliveryFeeByErpRequest.setSkuList(Collections.singletonList(productItem));
        deliveryFeeByErpRequest.setCarrierCodes(carrierCodes);
        return deliveryFeeByErpRequest;
    }
    private DeliveryFeeByErpRequest getDeliveryFeeByErpRequest(List<ProductSkuStock> productSkuStocks, String postalCode, String productSkuCode, String logisticsCode) {
        List<String> collect = productSkuStocks.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                               .collect(Collectors.toList());
        List<String> codes = productSkuStockMapper.getWarehouseCode(collect);
        ProductSku one = iProductSkuService.getOne(Wrappers.<ProductSku>lambdaQuery()
                                                           .eq(ProductSku::getProductSkuCode, productSkuCode)
                                                           .eq(ProductSku::getDelFlag, 0));
        ProductSkuStock productSkuStock = productSkuStocks.get(0);
        DeliveryFeeByErpRequest deliveryFeeByErpRequest = new DeliveryFeeByErpRequest();
        // hengjian
        String tenantId = productSkuStock.getTenantId();
        String channelFlag = TenantHelper.ignore(()->productSkuStockMapper.getChannelFlag(tenantId));
        deliveryFeeByErpRequest.setChannelFlag(channelFlag);
        deliveryFeeByErpRequest.setOrgWarehouseCodeList(codes);
        deliveryFeeByErpRequest.setPostcode(postalCode);
        DeliveryFeeByErpRequest.ProductItem productItem = new DeliveryFeeByErpRequest.ProductItem();
        productItem.setErpSku(one.getSku());
        productItem.setQuantity(1);

        deliveryFeeByErpRequest.setSkuList(Collections.singletonList(productItem));
        deliveryFeeByErpRequest.setCarrierCodes(Collections.singletonList(logisticsCode));
        return deliveryFeeByErpRequest;
    }

    private void calculateComputeFailed(Orders order, ProductSkuPrice productSkuPrice, OrderItem orderItem,
                                        OrderItemPrice orderItemPrice, RuleLevelProductPrice memberPrice,
                                        BigDecimal finalDeliveryFeeFromErp, Integer totalQuantity, LogisticsTypeEnum logisticsType,
                                        LocaleMessage localeMessage, String productSkuCode) {
        BigDecimal originalPickUpPrice = null;
        BigDecimal originalDropShippingPrice = null;
        originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();

        BigDecimal platformPickUpPrice = null;
        // 需要判断渠道
        if(OrderSourceEnum.MALL_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.EXCEL_ORDER.getValue().equals(order.getOrderSource())){
            platformPickUpPrice = getPlatformPickUpPriceNotChannelType(orderItem, productSkuPrice);
        }else {
            platformPickUpPrice = getPlatformPickUpPrice(orderItem, productSkuPrice);
        }


        BigDecimal platformDropShippingPrice = null;
        // 非折扣单价+操作费+派送费

        platformDropShippingPrice = null;
        orderItemPrice.setOriginalDropShippingPrice(null);
        orderItemPrice.setPlatformDropShippingPrice(null);

        BigDecimal platformFinalDeliveryFee = null;
        // 不从产品价格获取,实时调用erp
//            platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
        if (ObjectUtil.isNotEmpty(memberPrice)) {
            BigDecimal originalOperationFee1 = memberPrice.getOriginalOperationFee();
            BigDecimal originalUnitPrice1 = memberPrice.getOriginalUnitPrice();
            BigDecimal originalPickUpPrice1 = memberPrice.getOriginalPickUpPrice();

            BigDecimal platformUnitPrice = memberPrice.getPlatformUnitPrice();
            BigDecimal platformOperationFee = memberPrice.getPlatformOperationFee();

            if (ObjectUtil.isNotEmpty(originalOperationFee1) && ObjectUtil.isNotEmpty(originalUnitPrice1) ){
                if(ObjectUtil.isNotEmpty(finalDeliveryFeeFromErp)){
                    originalDropShippingPrice = originalOperationFee1.add(originalUnitPrice1).add(finalDeliveryFeeFromErp);
                }else{
                    originalDropShippingPrice = null;
                }

            }
            if (ObjectUtil.isNotEmpty(originalOperationFee1)&&ObjectUtil.isNotEmpty(originalPickUpPrice1)) {
                originalPickUpPrice = originalPickUpPrice1;
                orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
                orderItemPrice.setOriginalOperationFee(originalOperationFee1);
                orderItemPrice.setOriginalUnitPrice(originalUnitPrice1);
                orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
                orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
            }

            if (ObjectUtil.isNotEmpty(platformUnitPrice)&&ObjectUtil.isNotEmpty(platformOperationFee)) {
                platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
                orderItemPrice.setPlatformUnitPrice(originalUnitPrice1);
                if(ObjectUtil.isNotEmpty(finalDeliveryFeeFromErp)){
                    platformDropShippingPrice = platformUnitPrice.add(platformOperationFee).add(finalDeliveryFeeFromErp) ;
                }else{
                    platformDropShippingPrice = null;
                }

                orderItemPrice.setPlatformOperationFee(platformOperationFee);
                orderItemPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeFromErp);
                orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
            }

            if (ObjectUtil.isNotEmpty(memberPrice.getPlatformUnitPrice())) {
                orderItemPrice.setPlatformUnitPrice(platformUnitPrice);
            }
        }

        // 平台自提价（平台+分销商，产品单价+操作费）

//            BigDecimal platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();


        BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
        BigDecimal platformPayableUnitPrice = null;
        if(ObjectUtil.isNotEmpty(platformPickUpPrice)){
            platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
            originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
        }

        BigDecimal originalPayableTotalAmount = null;
        // 原始实付总金额
        BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
        // 平台应付总金额
        BigDecimal platformPayableTotalAmount = null;
        // 平台应付总金额
        BigDecimal platformActualTotalAmount = null;
        // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
            if(ObjectUtil.isNotEmpty(platformFinalDeliveryFee)){
                if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
                    // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
                    localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
                }
            }else {
                if(ObjectUtil.isNotEmpty(originalDropShippingPrice)){
                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
                    // 普通订单不需要支付订金，所以应付金额和实付金额是一样的

                    originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
                    originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
                    platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
                    platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
                }else {
                    originalPayableUnitPrice = null;
                    platformPayableUnitPrice = null;
                    originalActualTotalAmount = null;
                }


            }
        }else if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
            originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
            platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
            platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
        }

        // 供货商应得收入就是原始应付总金额 tag lty
        orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
        orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);

        orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);

        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
        orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        //平台实际支付单价（平台、分销商）
        orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        //平台实际支付总金额（平台、分销商）
        orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);

        orderItem.setPlatformRefundExecutableAmount(platformActualTotalAmount);

        orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
    }

    private void calculateCompute(String tenantId, Orders order, BigDecimal finalDeliveryFeeFromErp, ProductSku productSku,
                                  OrderItemPrice orderItemPrice, ProductSkuPrice productSkuPrice, OrderItem orderItem,
                                  RuleLevelProductPrice memberPrice, LogisticsTypeEnum logisticsType,
                                  LocaleMessage localeMessage, String productSkuCode, Integer totalQuantity) {
        finalDeliveryFeeFromErp = getOriginalFinalDeliveryFeeAfterDiscount(productSku.getTenantId(), tenantId, finalDeliveryFeeFromErp);
        orderItemPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeFromErp);
        orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
        BigDecimal originalPickUpPrice = null;
        BigDecimal originalDropShippingPrice = null;
        BigDecimal platformPickUpPrice = null;
        originalPickUpPrice = productSkuPrice.getOriginalPickUpPrice();
//                platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
        // 会员价逻辑
        BigDecimal originalUnitPrice = productSkuPrice.getOriginalUnitPrice();
        BigDecimal originalOperationFee = productSkuPrice.getOriginalOperationFee();
        // 此处要加入会员逻辑
        originalDropShippingPrice = originalUnitPrice.add(originalOperationFee).add(finalDeliveryFeeFromErp);
        // todo 自提需要 优化
        if(OrderSourceEnum.MALL_ORDER.getValue().equals(order.getOrderSource())||OrderSourceEnum.EXCEL_ORDER.getValue().equals(order.getOrderSource())){
            platformPickUpPrice = getPlatformPickUpPriceNotChannelType(orderItem, productSkuPrice);
        }else {
            platformPickUpPrice = getPlatformPickUpPrice(orderItem, productSkuPrice);
        }

        BigDecimal platformDropShippingPrice = null;
        // 非折扣单价+操作费+派送费
        platformDropShippingPrice = productSkuPrice.getPlatformPickUpPrice().add(finalDeliveryFeeFromErp);

        BigDecimal platformFinalDeliveryFee = finalDeliveryFeeFromErp;
        // 不从产品价格获取,实时调用erp
//            platformFinalDeliveryFee = productSkuPrice.getPlatformFinalDeliveryFee();
        if (ObjectUtil.isNotEmpty(memberPrice)) {
            BigDecimal originalOperationFee1 = memberPrice.getOriginalOperationFee();
            BigDecimal originalUnitPrice1 = memberPrice.getOriginalUnitPrice();
            BigDecimal originalPickUpPrice1 = memberPrice.getOriginalPickUpPrice();

            BigDecimal platformUnitPrice = memberPrice.getPlatformUnitPrice();
            BigDecimal platformOperationFee = memberPrice.getPlatformOperationFee();

            if (ObjectUtil.isNotEmpty(originalOperationFee1) && ObjectUtil.isNotEmpty(originalUnitPrice1) ){
                originalDropShippingPrice = originalOperationFee1.add(originalUnitPrice1).add(finalDeliveryFeeFromErp);
            }
            if (ObjectUtil.isNotEmpty(originalOperationFee1)&&ObjectUtil.isNotEmpty(originalPickUpPrice1)) {
                originalPickUpPrice = originalPickUpPrice1;
                orderItemPrice.setOriginalPickUpPrice(originalPickUpPrice);
                orderItemPrice.setOriginalOperationFee(originalOperationFee1);
                orderItemPrice.setOriginalUnitPrice(originalUnitPrice1);
                orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
                orderItemPrice.setOriginalFinalDeliveryFee(finalDeliveryFeeFromErp);
            }

            if (ObjectUtil.isNotEmpty(platformUnitPrice)&&ObjectUtil.isNotEmpty(platformOperationFee)) {
                platformPickUpPrice = memberPrice.getPlatformPickUpPrice();
                orderItemPrice.setPlatformPickUpPrice(platformPickUpPrice);
                orderItemPrice.setPlatformUnitPrice(originalUnitPrice1);
                platformDropShippingPrice = platformUnitPrice.add(platformOperationFee).add(finalDeliveryFeeFromErp) ;
                orderItemPrice.setPlatformOperationFee(platformOperationFee);

                orderItemPrice.setPlatformDropShippingPrice(platformDropShippingPrice);
            }

            if (ObjectUtil.isNotEmpty(memberPrice.getPlatformDropShippingPrice())) {
                orderItemPrice.setPlatformUnitPrice(platformUnitPrice);
            }
        }

        // 平台自提价（平台+分销商，产品单价+操作费）

//            BigDecimal platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();


        BigDecimal originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
        BigDecimal platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
        // 当前商品需要代发，但尾程派送费为零或者未设置，提示仅支持自提
        if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
            if (NumberUtil.isLess(platformFinalDeliveryFee, BigDecimal.ZERO)) {
                // 提示错误后，价格还要继续计算，只能只用自提价，但是要控制住订单不能支付
                localeMessage.append(OrderStatusCodeEnum.ONLY_SUPPORTS_PICK_UP.args(productSkuCode));
            } else {
                originalPayableUnitPrice = NumberUtil.toBigDecimal(originalDropShippingPrice);
                platformPayableUnitPrice = NumberUtil.toBigDecimal(platformDropShippingPrice);
            }
        }
//                if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
//                    originalPayableUnitPrice = NumberUtil.toBigDecimal(originalPickUpPrice);
//                    platformPayableUnitPrice = NumberUtil.toBigDecimal(platformPickUpPrice);
//                }
        // 普通订单不需要支付订金，所以应付金额和实付金额是一样的
        // 原始应付总金额
        BigDecimal originalPayableTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 原始实付总金额
        BigDecimal originalActualTotalAmount = NumberUtil.mul(originalPayableUnitPrice, totalQuantity);
        // 普通订单不需要支付定价，所以应付金额和实付金额是一样的
        // 平台应付总金额
        BigDecimal platformPayableTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);
        // 平台应付总金额
        BigDecimal platformActualTotalAmount = NumberUtil.mul(platformPayableUnitPrice, totalQuantity);

        // 供货商应得收入就是原始应付总金额 tag lty
        orderItem.setSupplierIncomeEarned(originalPayableTotalAmount);
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalPayableTotalAmount(originalPayableTotalAmount);
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setOriginalActualUnitPrice(originalPayableUnitPrice);
        orderItem.setOriginalActualTotalAmount(originalActualTotalAmount);
        orderItem.setOriginalRefundExecutableAmount(originalActualTotalAmount);
        orderItemPrice.setOriginalDropShippingPrice(originalDropShippingPrice);
        orderItemPrice.setOriginalDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setOriginalBalanceUnitPrice(originalPayableUnitPrice);

        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);
        orderItem.setPlatformPayableTotalAmount(platformPayableTotalAmount);
        //平台实际支付单价（平台、分销商）
        orderItem.setPlatformActualUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        //平台实际支付总金额（平台、分销商）
        orderItem.setPlatformActualTotalAmount(platformActualTotalAmount);

        orderItem.setPlatformRefundExecutableAmount(platformActualTotalAmount);

        orderItemPrice.setPlatformDepositUnitPrice(BigDecimal.ZERO);
        orderItemPrice.setPlatformBalanceUnitPrice(platformPayableUnitPrice);
    }

    /**
     * 功能描述：按渠道获取订单商品价格
     *
     * @param productSkuPrice 产品SKU价格
     * @param orderItem       订单项
     * @param orderItemPrice
     * @return {@link OrderItemPrice }
     * <AUTHOR>
     * @date 2024/02/08
     */
    private OrderItemPrice getOrderItemPriceByChannel(ProductSkuPrice productSkuPrice, OrderItem orderItem,
                                                      OrderItemPrice orderItemPrice) {
        if (ChannelTypeEnum.Erp.equals(orderItem.getChannelType())) {
            BigDecimal upPrice = orderItemPrice.getPlatformPickUpPrice();
            BeanUtil.copyProperties(productSkuPrice, orderItemPrice, "id", "delFlag", "createBy", "createTime", "updateBy", "updateTime,platformPickUpPrice", "platformUnitPrice", "platformDropShippingPrice");
            orderItemPrice.setPlatformPickUpPrice(upPrice);
        } else {
            BeanUtil.copyProperties(productSkuPrice, orderItemPrice, "id", "delFlag", "createBy", "createTime", "updateBy", "updateTime","platformDropShippingPrice","originalFinalDeliveryFee",
                "platformFinalDeliveryFee",
                "originalDropShippingPrice"
                );
        }
        return orderItemPrice;
    }

    /**
     * 功能描述：获取平台取货价格 tiktok和erp取货价格为平台实际支付单价 其他渠道取货价格为产品sku价格,需要根据订单来源进行判断
     *
     * @param orderItem       订单项
     * @param productSkuPrice 产品SKU价格
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/02/07
     */
    private BigDecimal getPlatformPickUpPrice(OrderItem orderItem, ProductSkuPrice productSkuPrice) {

        BigDecimal platformPickUpPrice = BigDecimal.ZERO;
        platformPickUpPrice = orderItem.getPlatformActualUnitPrice();
        return platformPickUpPrice;
    }

    private BigDecimal getPlatformPickUpPriceNotChannelType(OrderItem orderItem, ProductSkuPrice productSkuPrice) {
        BigDecimal platformPickUpPrice = BigDecimal.ZERO;
        platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
        return platformPickUpPrice;
    }
    /**
     * 订单模块调用ERP接口获取尾程派送费
     * @param deliveryFeeByErpRequest 请求对象
     * @return {@link DeliveryFeeByErpResponse }
     */
    public List<DeliveryFeeByErpResponse> getOrderDeliveryFeeByErp(List<DeliveryFeeByErpRequest> deliveryFeeByErpRequest){
        log.info(StrUtil.format("[获取ERP尾程派送费],请求参数:{}",JSONUtil.toJsonStr(deliveryFeeByErpRequest)));
        TimeInterval timer = DateUtil.timer();
        try {
            DeliveryFeeByErpRequest delivery = deliveryFeeByErpRequest.get(0);
            if (ObjectUtil.isNull(delivery.getSupplierTenantId())){
                throw  new RuntimeException("[获取ERP尾程派送费],失败,订单询价供应商ID不能为空]");
            }
            if (ObjectUtil.isNull(delivery.getDistributorTenantId())){
                throw  new RuntimeException("[获取ERP尾程派送费],失败,订单询价分销商ID不能为空]");
            }
            //校验请求值
            validDeliveryFeeByErpRequest(deliveryFeeByErpRequest);
            SysInfVo sysInfVo = sysInfService.queryByInfNote(SysInfEnum.GET_DELIVERY_FEE_BY_ERP);
            if (ObjectUtil.isNull(sysInfVo)){
                throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{} 未获取接口相关配置","GET_DELIVERY_FEE_BY_ERP"));
            }
            String url=null;
            if (sysInfVo.getIsTest()==1){
                url=sysInfVo.getInfTestUrl();
            }
            if (sysInfVo.getIsTest()==2){
                url=sysInfVo.getInfUrl();
            }
            String infParameters = sysInfVo.getInfParameters();
            JSONObject jsonObject = JSONUtil.parseObj(infParameters);
            String headerApiKey = jsonObject.getStr("x-api-key");
            log.error(StrUtil.format("[获取ERP尾程派送费]请求参数：{}",deliveryFeeByErpRequest));
            String result2 = HttpRequest.post(url)
                                        .header("x-api-key", headerApiKey)
                                        .body(JSONUtil.toJsonStr(deliveryFeeByErpRequest))
                                        .timeout(10000)//超时，毫秒
                                        .execute().body();
            log.error(StrUtil.format("[获取ERP尾程派送费]返回参数：{}",ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", "")));
            JSONObject object = JSONUtil.parseObj(result2);
            if (object.getInt("statusCode")==200){
               // throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费] 询价失败，ERP返回：{},",result2));
                String jsonStr = JSONUtil.toJsonStr(object.getObj("data"));
                //转换
                List<DeliveryFeeByErpResponse> lists = com.alibaba.fastjson.JSONObject.parseArray(jsonStr, DeliveryFeeByErpResponse.class);
                //校验返回值
                validDeliveryFeeByErpResponse(deliveryFeeByErpRequest,lists);
                log.info(StrUtil.format("[获取ERP尾程派送费]耗时：{} 毫秒",timer.interval()));
                return lists ;
            }else {
                log.error("[获取ERP尾程派送费]报错,ERP询价失败,返回报错,请求参数：{},返回参数：{}",
                    JSONUtil.toJsonStr(deliveryFeeByErpRequest),ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", ""));
                throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,ERP询价失败,返回报错,返回参数：{}",ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", "")));
            }
        }catch (Exception e){
            log.error(String.valueOf(e));
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{},请求参数：{}",e.getMessage(),JSONUtil.toJsonStr(deliveryFeeByErpRequest)));
        }

    }

    /**
     * 商城调用ERP接口获取尾程派送费
     * @param deliveryFeeByErpRequest 请求对象
     * @return {@link DeliveryFeeByErpResponse }
     */
    public List<DeliveryFeeByErpResponse> getMallDeliveryFeeByErp(List<DeliveryFeeByErpRequest> deliveryFeeByErpRequest){
        log.info(StrUtil.format("[获取ERP尾程派送费],请求参数:{}",JSONUtil.toJsonStr(deliveryFeeByErpRequest)));
        TimeInterval timer = DateUtil.timer();
        try {
            //校验请求值
            validDeliveryFeeByErpRequest(deliveryFeeByErpRequest);
            SysInfVo sysInfVo = sysInfService.queryByInfNote(SysInfEnum.GET_DELIVERY_FEE_BY_ERP);
            if (ObjectUtil.isNull(sysInfVo)){
                throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{} 未获取接口相关配置","GET_DELIVERY_FEE_BY_ERP"));
            }
            String url=null;
            if (sysInfVo.getIsTest()==1){
                url=sysInfVo.getInfTestUrl();
            }
            if (sysInfVo.getIsTest()==2){
                url=sysInfVo.getInfUrl();
            }
            String infParameters = sysInfVo.getInfParameters();
            JSONObject jsonObject = JSONUtil.parseObj(infParameters);
            String headerApiKey = jsonObject.getStr("x-api-key");
            log.error(StrUtil.format("[获取ERP尾程派送费]请求参数：{}",deliveryFeeByErpRequest));
            String result2 = HttpRequest.post(url)
                    .header("x-api-key", headerApiKey)
                    .body(JSONUtil.toJsonStr(deliveryFeeByErpRequest))
                    .timeout(10000)//超时，毫秒
                    .execute().body();
            log.error(StrUtil.format("[获取ERP尾程派送费]返回参数：{}",ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", "")));
            JSONObject object = JSONUtil.parseObj(result2);
            if (object.getInt("statusCode")!=200){
                throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费] 询价失败，ERP返回：{},",ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", "")));
            }
            String jsonStr = JSONUtil.toJsonStr(object.getObj("data"));
            //转换
            List<DeliveryFeeByErpResponse> lists = com.alibaba.fastjson.JSONObject.parseArray(jsonStr, DeliveryFeeByErpResponse.class);
            //校验返回值
            validDeliveryFeeByErpResponse(deliveryFeeByErpRequest,lists);
            log.info(StrUtil.format("[获取ERP尾程派送费]耗时：{} 毫秒",timer.interval()));
            return lists ;
        }catch (Exception e){
            log.error(String.valueOf(e));
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{},请求参数：{}",e.getMessage(),JSONUtil.toJsonStr(deliveryFeeByErpRequest)));
        }

    }



    /**
     * @description:  订单单个商品单次询价
     * @author: len
    *  @date: 2024/8/8 14:44
     * @param: sku
     * @param: num
     * @param: sendWarehouseCode
     * @param: postCode
     * @return: com.zsmall.product.entity.domain.DeliveryFeeByErpResponse
     **/
    public DeliveryFeeByErpResponse getOrderDeliveryFeeByErp(String productSKuCode,Integer num,List<String> sendWarehouseCode,String postCode,
                                                             String distributorTenantId,String supplierTenantId){
        if (ObjectUtil.isNull(supplierTenantId)){
            throw  new RuntimeException("[获取ERP尾程派送费],失败,订单询价供应商ID不能为空]");
        }
        if (ObjectUtil.isNull(distributorTenantId)){
            throw  new RuntimeException("[获取ERP尾程派送费],失败,订单询价分销商ID不能为空]");
        }

        List<DeliveryFeeByErpRequest> deliveryFeeByErpRequests = null;
        ProductSku productSku = productSkuService.queryByProductSkuCode(productSKuCode);
        if (ObjectUtil.isNull(productSku)){
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：商品信息不存在{} ",productSKuCode));
        }
        String sku=productSku.getSku();
        if (ObjectUtil.isNull(sku)){
           throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{} ","SKU不能为空"));
       }
        if (ObjectUtil.isNull(num)){
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{} ","SKU数量不能为空"));
        }
        if (ObjectUtil.isNull(postCode)){
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{} ","收件人邮编不能为空"));
        }
        if (CollectionUtil.isEmpty(sendWarehouseCode)){
            List<String> productSkuStock = TenantHelper.ignore(() -> iProductSkuService.getProductSkuStockBySku(sku, 1));
            if (CollectionUtil.isEmpty(productSkuStock)) {
                throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,当前商品:{} 所属库存无库存", sku));
            }
            sendWarehouseCode=productSkuStock;
        }
        if (ObjectUtil.isNull(distributorTenantId)){
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,当前租户:{} 不能为空"));
        }
        TimeInterval timer = DateUtil.timer();
        try {
            //校验请求值
            DeliveryFeeByErpRequest request=new DeliveryFeeByErpRequest();
            request.setPostcode(postCode);
            request.setOrgWarehouseCodeList(sendWarehouseCode);
            request.setChannelFlag(postCode);

                SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(distributorTenantId);
                if (ObjectUtil.isNull(sysTenantVo)) {
                    throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,店铺信息不存在,租户ID:{}", distributorTenantId));
                }
                if (ObjectUtil.isEmpty(sysTenantVo.getThirdChannelFlag())) {
                    throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,分销商:{}店铺信息不存在", distributorTenantId));
                }
                //封装渠道店铺信息
                request.setChannelFlag(sysTenantVo.getThirdChannelFlag());

            DeliveryFeeByErpRequest.ProductItem productItem=new DeliveryFeeByErpRequest.ProductItem();
            productItem.setErpSku(sku);
            productItem.setQuantity(num);
            request.setSkuList(List.of(productItem));
             deliveryFeeByErpRequests = List.of(request);
            validDeliveryFeeByErpRequest(deliveryFeeByErpRequests);
            SysInfVo sysInfVo = sysInfService.queryByInfNote(SysInfEnum.GET_DELIVERY_FEE_BY_ERP);
            if (ObjectUtil.isNull(sysInfVo)){
                throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{} 未获取接口相关配置","GET_DELIVERY_FEE_BY_ERP"));
            }
            String url=null;
            if (sysInfVo.getIsTest()==1){
                url=sysInfVo.getInfTestUrl();
            }
            if (sysInfVo.getIsTest()==2){
                url=sysInfVo.getInfUrl();
            }
            String infParameters = sysInfVo.getInfParameters();
            JSONObject jsonObject = JSONUtil.parseObj(infParameters);
            String headerApiKey = jsonObject.getStr("x-api-key");
            log.error(StrUtil.format("[获取ERP尾程派送费]请求参数：{}",deliveryFeeByErpRequests));
            String result2 = HttpRequest.post(url)
                    .header("x-api-key", headerApiKey)
                    .body(JSONUtil.toJsonStr(deliveryFeeByErpRequests))
                    .timeout(10000)//超时，毫秒
                    .execute().body();
            log.error(StrUtil.format("[获取ERP尾程派送费]返回参数：{}",ObjectUtil.isNull(result2)? "": result2.replaceAll("\\r?\\n", "")));
            JSONObject object = JSONUtil.parseObj(result2);

            if (object.getInt("statusCode")==200){
                // throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费] 询价失败，ERP返回：{},",result2));
                String jsonStr = JSONUtil.toJsonStr(object.getObj("data"));
                //转换
                List<DeliveryFeeByErpResponse> lists = com.alibaba.fastjson.JSONObject.parseArray(jsonStr, DeliveryFeeByErpResponse.class);
                //校验返回值
                validDeliveryFeeByErpResponse(deliveryFeeByErpRequests,lists);
                log.info(StrUtil.format("[获取ERP尾程派送费]耗时：{} 毫秒",timer.interval()));
                return lists.get(0) ;
            }else {
                throw new BaseException("50001",StrUtil.format("[获取ERP尾程派送费] 询价失败，ERP返回：{},",result2));
            }
        }catch (Exception e){
            log.error(String.valueOf(e));
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,错误原因：{},请求参数：{}",e.getMessage(),JSONUtil.toJsonStr(deliveryFeeByErpRequests)));
        }

    }

    /**
     * 功能描述：erp误收运费
     *
     * @param productSKuCode      产品sku代码
     * @param num                 数字
     * @param sendWarehouseCode   发送仓库代码
     * @param postCode            邮政编码
     * @param distributorTenantId 分销商租户id
     * @param supplierTenantId    供应商租户id
     * @param logisticsCarrier
     * @return {@link DeliveryFeeByErpResponse }
     * <AUTHOR>
     * @date 2024/09/05
     */
    public DeliveryFeeByErpResponse getDeliveryFeeByErpByError(String productSKuCode, Integer num, List<String> sendWarehouseCode, String postCode,
                                                               String distributorTenantId, String supplierTenantId,
                                                               String logisticsCarrier){
        ProductSku productSku = productSkuService.queryByProductSkuCode(productSKuCode);
        String sku = productSku.getSku();
        DeliveryFeeByErpRequest request=new DeliveryFeeByErpRequest();
        request.setPostcode(postCode);
        request.setDistributorTenantId(distributorTenantId);
        request.setSupplierTenantId(supplierTenantId);
        request.setOrgWarehouseCodeList(sendWarehouseCode);
        request.setChannelFlag(postCode);
        List<DeliveryFeeByErpRequest> deliveryFeeByErpRequests = null;
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(distributorTenantId);
        if (ObjectUtil.isNull(sysTenantVo)) {
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,店铺信息不存在,租户ID:{}", distributorTenantId));
        }
        if (ObjectUtil.isEmpty(sysTenantVo.getThirdChannelFlag())) {
            throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费]报错,分销商:{}店铺信息不存在", distributorTenantId));
        }
        //封装渠道店铺信息
        request.setChannelFlag(sysTenantVo.getThirdChannelFlag());

        DeliveryFeeByErpRequest.ProductItem productItem=new DeliveryFeeByErpRequest.ProductItem();
        productItem.setErpSku(sku);
        productItem.setQuantity(num);
        request.setSkuList(List.of(productItem));
        deliveryFeeByErpRequests = List.of(request);
        List<DeliveryFeeByErpResponse> deliveryFeeByErpByError = deliveryFeeSupport.getDeliveryFeeByErpByError(deliveryFeeByErpRequests,logisticsCarrier );
        return deliveryFeeByErpByError.get(0);
    }


    /**
     * @description:  请求ERP询价接口前 校验参数
     * @author: len
    *  @date: 2024/8/4 15:50
     * @param: deliveryFeeRequests
     **/
    void validDeliveryFeeByErpRequest(List<DeliveryFeeByErpRequest>  deliveryFeeRequests){
        for (DeliveryFeeByErpRequest deliveryFeeRequest : deliveryFeeRequests) {
            if (ObjectUtil.isNull(deliveryFeeRequest.getRequestId())){
                deliveryFeeRequest.setRequestId(IdUtil.getSnowflakeNextIdStr());
            }
            if (ObjectUtil.isEmpty(deliveryFeeRequest.getChannelFlag())){
                throw  new RuntimeException("[获取ERP尾程派送费],分销商店铺信息不能为空!");
            }
            if ("US".equals(deliveryFeeRequest.getCountryCode()) && ObjectUtil.isEmpty(deliveryFeeRequest.getPostcode())) {
                throw new RuntimeException("[获取ERP尾程派送费], US国家测算,邮编不能为空!");
            }
            if (CollectionUtil.isEmpty(deliveryFeeRequest.getOrgWarehouseCodeList())){
                throw  new RuntimeException("[获取ERP尾程派送费],发货仓库编码不能为空");
            }
            if (CollectionUtil.isEmpty(deliveryFeeRequest.getSkuList())){
                throw new RuntimeException("[获取ERP尾程派送费],商品信息不能为空");
            }
            deliveryFeeRequest.getSkuList().forEach(s->{
                if (ObjectUtil.isEmpty(s.getErpSku())){
                    throw new RuntimeException("[获取ERP尾程派送费],ErpSku编码不能为空");
                }
                //查询SKU所属的商品信息是否存在
                ProductSku productSku = iProductSkuService.queryBySku(s.getErpSku());
                if (ObjectUtil.isNull(productSku)){
                    throw new RuntimeException(StrUtil.format("[获取ERP尾程派送费],Sku:{}对应的商品信息不存在",s.getErpSku()));
                }
                if (ObjectUtil.isEmpty(s.getQuantity())){
                    throw new RuntimeException("[获取ERP尾程派送费],ErpSku商品数量不能为空");
                }
                if (s.getQuantity()==0){
                    throw new RuntimeException("[获取ERP尾程派送费],ErpSku商品数量不能为0");
                }
            });
        }

    }



    /**
     * @description:  ERP询价结果校验
     * @author: len
    *  @date: 2024/8/4 15:50
     * @param: requests 询价请求参数
     * @param: response  询价返回参数
     **/
    void validDeliveryFeeByErpResponse(List<DeliveryFeeByErpRequest> requests, List<DeliveryFeeByErpResponse> response) {
        if (CollectionUtil.isEmpty(response)) {
            throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回结果为空，返回参数：{}", JSONUtil.toJsonStr(response)));
        }
        //如果询价前后数量不对，抛出异常
        if (requests.size() != response.size()) {
            throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价前后数量不对，请求参数：{}，返回参数：{}", JSONUtil.toJsonStr(requests), JSONUtil.toJsonStr(response)));
        }
        response.forEach(s -> {
            if (ObjectUtil.isEmpty(s.getOrgWarehouseCode())) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回仓库编码为空，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            if (CollectionUtil.isEmpty(s.getSkuList())) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回商品信息为空，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            if (ObjectUtil.isEmpty(s.getShippingFee())) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回运费为空，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            //校验返回的商品信息
            for (DeliveryFeeByErpResponse.ProductItem productItem : s.getSkuList()) {
                String sku =productItem.getErpSku();
                if (ObjectUtil.isEmpty(sku)){
                    throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回商品信息为空，返回参数：{}", JSONUtil.toJsonStr(s)));
                }
                ProductSku productSku = iProductSkuService.queryBySku(sku);
                if (ObjectUtil.isNull(productSku)) {
                    throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回SKU信息分销系统不存在，返回参数：{}", JSONUtil.toJsonStr(s)));
                }
                if (productItem.getQuantity()==0){
                    throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价返回SKU数量为0，返回参数：{}", JSONUtil.toJsonStr(s)));
                }
                //判断返回的仓库信息在分销系统是否存在
                LambdaQueryWrapper<Warehouse> q = new LambdaQueryWrapper<>();
                q.eq(Warehouse::getWarehouseCode, s.getOrgWarehouseCode());
                q.eq(Warehouse::getWarehouseState, 1);
                q.eq(Warehouse::getTenantId, productSku.getTenantId());
                Warehouse one = TenantHelper.ignore(() -> iWarehouseService.getOne(q));
                if (ObjectUtil.isNull(one)) {
                    throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，返回的仓库编码分销系统不存在，租户ID:{},返回参数：{}", LoginHelper.getTenantId(), JSONUtil.toJsonStr(s)));
                }
            }
            Map<String, DeliveryFeeByErpRequest> collect = requests.stream()
                    .collect(Collectors.toMap(
                            DeliveryFeeByErpRequest::getRequestId,
                            request -> request,
                            (existing, replacement) -> replacement
                    ));
   //         校验询价前后，ERP返回的商品数量是否一致
            DeliveryFeeByErpRequest deliveryFeeByErpRequest = collect.get(s.getRequestId());
            if (ObjectUtil.isEmpty(deliveryFeeByErpRequest)) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价前后requestId不能为空，返回参数：{}", JSONUtil.toJsonStr(s)));
            }
            if (deliveryFeeByErpRequest.getSkuList().size() != s.getSkuList().size()) {
                throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价前后商品数量不对，请求参数：{}，返回参数：{}", JSONUtil.toJsonStr(deliveryFeeByErpRequest), JSONUtil.toJsonStr(s)));
            }
        });
        //获取返回的EepSku信息
        List<String> responseRrpSkus = response.stream().map(DeliveryFeeByErpResponse::getSkuList).filter(Objects::nonNull).flatMap(List::stream)
                                               .map(DeliveryFeeByErpResponse.ProductItem::getErpSku) // 提取sku
                                               .collect(Collectors.toList());
        //获取返回的EepSku信息
        List<String> requestRrpSkus = requests.stream().map(DeliveryFeeByErpRequest::getSkuList).flatMap(List::stream)
                .map(DeliveryFeeByErpRequest.ProductItem::getErpSku)
                .collect(Collectors.toList());
        if (!responseRrpSkus.equals(requestRrpSkus)) {
            throw new RuntimeException(StrUtil.format("[校验ERP尾程派送费返回值不通过],询价失败，询价前后erpSku数据异常，请求参数：{}，返回参数：{}", JSONUtil.toJsonStr(requests), JSONUtil.toJsonStr(response)));
        }
    }

    /**
     * 功能描述：会员后获得底价,如果没有会员价就返回原始价格
     *
     * @param stenantId    供应商
     * @param tenantId     分销商租户id
     * @param productSkuId 产品sku id
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/08/13
     */
    public BigDecimal getBasePriceAfterMember(String stenantId, String tenantId, Long productSkuId) {
//        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(stenantId, tenantId, productSkuId, );
//        if(ObjectUtil.isNotEmpty(memberPrice)){
//            return memberPrice.getPlatformPickUpPrice();
//        }
        // 此类已废弃
//        ProductSkuPrice price = TenantHelper.ignore(()->iProductSkuPriceService.queryByProductSkuId(productSkuId));
//        if (ObjectUtil.isNotEmpty(price)){
//            return price.getPlatformPickUpPrice();
//        }
        return null;
    }
}
