package com.zsmall.order.biz.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.SaleOrderDetailDTO;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import com.zsmall.common.domain.dto.ThirdReceiptDTO;
import com.zsmall.common.domain.tiktok.domain.dto.address.SiteMsgBo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.erp.ErpExceptionEnums;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseChannelCodeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.common.handler.AbstractOrderOperationHandler;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.order.biz.manager.OrderAndItemManager;
import com.zsmall.order.biz.service.IProductAboutManger;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.impl.OrderItemProductSkuThirdServiceImpl;
import com.zsmall.order.biz.service.impl.OrderItemThirdServiceImpl;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.biz.support.ThirdPartyLogisticsSupport;
import com.zsmall.order.biz.support.WholesaleOrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductChannelControlService;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuAttachmentService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.WorldLocation;
import com.zsmall.system.entity.domain.vo.TenantShippingAddressVo;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.iservice.ITenantShippingAddressService;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/9 15:07
 */
@Slf4j
@Lazy
@Component("erpOperationHandler")
public class ErpOrderOperationHandler extends AbstractOrderOperationHandler<String, OrderReceiveFromThirdDTO, Map<String, Object>, Orders, Object> {

    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;
    @Resource
    private BillSupport billSupport;
    @Resource
    private IOrderRefundService iOrderRefundService;
    @Resource
    private WholesaleOrderSupport wholesaleSupports;
    @Resource
    private ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;
    @Resource
    private IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;

    @Resource
    private IProductAboutManger productAboutManger;

    @Resource
    private IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    //    @XxlConf(value = "distribution.shipping.address.id.erp",defaultValue = "1704748687534034946")
    @Value("${distribution.shipping.address.id.erp}")
    public String addressId;

    @Resource
    private ITenantShippingAddressService tenantShippingAddressService;

    //    @XxlConf(value = "distribution.specify.warehouse.id.hj",defaultValue = "BG94930")
    @Value("${distribution.specify.warehouse.id.hj}")
    public String warehouseSystemCode;
    @Resource
    private IOrderItemShippingRecordService iOrderItemShippingRecordService;
    @Resource
    private OrderItemService orderItemService;

    @Resource
    private OrderSupport orderSupport;

    @Resource
    private IProductSkuService iProductSkuService;

    @Resource
    private ISysTenantService sysTenantService;

    @Resource
    private IProductService iProductService;

    @Resource
    private OrderAndItemManager orderAndItemManager;

    @Resource
    private IOrdersService iOrdersService;

    @Resource
    private OrdersService ordersService;

    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;


    @Resource
    private BusinessParameterService businessParameterService;

    @Autowired
    ApplicationContext applicationContext;

    @Resource
    private IWorldLocationService iWorldLocationService;

    @Resource
    private OrderCodeGenerator orderCodeGenerator;


    @Resource
    private IProductSkuAttachmentService iProductSkuAttachmentService;


    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private OrderItemThirdServiceImpl iOrderItemThirdService;


    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private IOrderItemPriceService iOrderItemPriceService;


    @Resource
    private IProductSkuService productSkuService;

    @Resource
    private OrderItemProductSkuThirdServiceImpl orderItemProductSkuThirdService;


    @Override
    public void initialMessageSave(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {

    }

    @Override
    public OrderReceiveFromThirdDTO parseThirdData(String json) {
        OrderReceiveFromThirdDTO erpDTO = JSONObject.parseObject(json, OrderReceiveFromThirdDTO.class);
        if (ObjectUtil.isEmpty(erpDTO)) {
            throw new AppRuntimeException("订单数据不能为null");
        }
        return erpDTO;
    }

    @Override
    public Boolean msgVerify(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        String parameterSupport = businessParameterService.getValueFromString(BusinessParameterType.SUPPORT_COUNTRY);
        // 支持的国家
        List<String> SUPPORT_COUNTRY = JSONUtil.toList(parameterSupport, String.class);
        // 支持的州
        List<String> SUPPORT_STATE = iWorldLocationService.queryChildCodeList("US");
        // 渠道单号重复Set
        orderReceiveFromThirdDTO.getSaleOrderItemsList();

        Set<String> channelOrderNoSet = new HashSet<>();

        List<SaleOrderItemDTO> orderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();

        for (SaleOrderItemDTO itemDTO : orderItemsList) {

            // 仓库号,可以通过渠道写死 提前配置好仓库
            ProductSku productSku = iProductSkuService.queryByProductSkuCodeAndWarehouseSystemCode(itemDTO.getErpSku(), WarehouseChannelCodeEnum.OTHER_CHANNEL.getWarehouseSystemCode());
            if (productSku == null) {

                continue;
            } else {
                // 如果商品被管控，则报商品不存在
                String tenantId = "1";
                boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, itemDTO.getErpSku(), ChannelTypeEnum.Others.name());

                if (!checkUserAllow) {
                    // 日志记录商品不存在
                    return Boolean.FALSE;
                }
            }

            Product product = iProductService.queryByProductSkuCode(itemDTO.getErpSku());
            if (StrUtil.isNotBlank(orderReceiveFromThirdDTO.getOrderNo())) {
                if (channelOrderNoSet.contains(orderReceiveFromThirdDTO.getOrderNo())) {
                    // ?
                } else {
                    // 查询此账户所有订单判断是否有重复的，排除Canceled的
                    boolean orderExists = iOrdersService.existsChannelOrderNo(orderReceiveFromThirdDTO.getOrderNo(), OrderStateType.Canceled);
                    // 查询导入缓存表
                    if (orderExists) {
                        return Boolean.FALSE;
                        // 存在同一个订单
                    } else {
                        channelOrderNoSet.add(orderReceiveFromThirdDTO.getOrderNo());
                    }
                }
            }
            // 规则校验
            if (!RegexUtil.matchQuantity(itemDTO.getQuantity().toString())) {
                // 数量异常-正则表达式
                return Boolean.FALSE;
            }


            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

            // 三方默认代发
            String logisticsType = "DropShipping";
            if (StrUtil.equals(logisticsType, "DropShipping")) {
                // 仅支持自提
                if (SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
                    // 商品仅支持自提
                    log.info("订单与商品物流类型冲突,订单号:{},商品sku:{},商品仅支持自提", orderReceiveFromThirdDTO.getOrderNo(), itemDTO.getErpSku());
                    return Boolean.FALSE;
                }
            }


        }
        return Boolean.TRUE;
    }


    /**
     * 功能描述：录入正式订单数据相关
     *
     * @param map 地图
     * <AUTHOR>
     * @date 2024/01/09
     */
    @Override
    public void formalOrderAboutEntry(Map<String, Object> map) {
        OrderLogisticsInfo info = (OrderLogisticsInfo) map.get("logisticsInfo");
        List<OrderAddressInfo> address = (List<OrderAddressInfo>) map.get("addressInfo");
        List<OrderItemPrice> itemPrices = (List<OrderItemPrice>) map.get("orderItemPrice");
        iOrderLogisticsInfoService.save(info);
        iOrderAddressInfoService.saveBatch(address);
        iOrderItemPriceService.saveBatch(itemPrices);

    }

    @Override
    public Orders formalOrderAndItemEntry(Map<String, List> map, Orders orders) {
        List<OrderItem> orderItems = (List<OrderItem>) map.get("orderItems");
        List<OrderItemProductSku> skus = (List<OrderItemProductSku>) map.get("orderItemProductsSku");
        iOrdersService.save(orders);
        orderItems.forEach(item -> item.setOrderId(orders.getId()));
        iOrderItemService.saveBatch(orderItems);
        for (OrderItem item : orderItems) {
            for (OrderItemProductSku sku : skus) {
                if (item.getOrderItemNo().equals(sku.getOrderItemNo())) {
                    sku.setOrderItemId(item.getId());
                    sku.setOrderNo(item.getOrderNo());
                }
            }
        }
        iOrderItemProductSkuService.saveBatch(skus);
        return orders;
    }


    @Override
    public Map<String, Object> msgForLogistics(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders,
                                               Map<String, List> itemMap) {
        HashMap<String, Object> map = new HashMap<>();
        List<OrderItem> orderItems = (List<OrderItem>) itemMap.get("orderItems");
        OrderItem item = orderItems.get(0);
        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
        ArrayList<OrderAddressInfo> addressInfos = new ArrayList<>();
        SaleOrderDetailDTO details = orderReceiveFromThirdDTO.getSaleOrderDetails();

//        List<String> result = Arrays.asList(addressDTO.getPostalCode().split("-"));
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        SaleOrderItemDTO dto = saleOrderItemsList.get(0);


        TenantShippingAddressVo addressVo = TenantHelper.ignore(() -> tenantShippingAddressService.queryById(Long.valueOf(addressId)));

        for (SaleOrderItemDTO itemDTO : saleOrderItemsList) {
            // 拿默认地址模版

            WorldLocation country = iWorldLocationService.getOne(new LambdaQueryWrapper<WorldLocation>().eq(WorldLocation::getLocationCode, addressVo.getCountryCode()));

            OrderAddressInfo orderAddressInfo = new OrderAddressInfo();

            orderAddressInfo.setOrderId(orders.getId());
            orderAddressInfo.setOrderNo(orders.getOrderNo());
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
            // 拿默认模版里面
            orderAddressInfo.setRecipient(addressVo.getFullName());

            orderAddressInfo.setPhoneNumber(addressVo.getPhoneNumber());
            orderAddressInfo.setCountry(ObjectUtil.isNotNull(country) ? country.getLocationName() : addressVo.getCountryCode());
            orderAddressInfo.setCountryCode(addressVo.getCountryCode());
            orderAddressInfo.setState(addressVo.getState());
            orderAddressInfo.setStateCode(addressVo.getState());
            orderAddressInfo.setCity(addressVo.getCity());
            orderAddressInfo.setAddress1(addressVo.getShippingAddress());
            orderAddressInfo.setAddress2(addressVo.getAddress2());
            orderAddressInfo.setZipCode(addressVo.getZipCode());
            orderAddressInfo.setEmail(addressVo.getEmail());
            orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);

            addressInfos.add(orderAddressInfo);
        }

        // 物流信息
        orderLogisticsInfo.setOrderId(orders.getId());
        orderLogisticsInfo.setOrderNo(orders.getOrderNo());
        orderLogisticsInfo.setShippingLabelExist(true);
        // erp 统一为自提
//        orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orderReceiveFromErpDTO.getLogisticsType()));
        orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.PickUp);

        orderLogisticsInfo.setLogisticsCompanyName(details.getCarrier());
        orderLogisticsInfo.setLogisticsCountryCode("US");
        orderLogisticsInfo.setLogisticsServiceName(details.getCarrier());
        orderLogisticsInfo.setZipCode(addressVo.getZipCode());
//        orderLogisticsInfo.setLogisticsAccount(logisticsAccount);
//        orderLogisticsInfo.setLogisticsAccountZipCode(logisticsAccountZipCode);


//        orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(zipCode));
//        if (StrUtil.contains(zipCode, "-")) {
//            // 存在-的邮编，需要分割出前面5位的主邮编
//            String mainZipCode = StrUtil.trim(StrUtil.split(zipCode, "-").get(0));
//            orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(mainZipCode));
//        }
//        orderLogisticsInfo.setLogisticsCountryCode(countryCode);

        List<OrderItemTrackingRecord> trackingList = new ArrayList<>();
        String trimTracking = StrUtil.trim(details.getLogisticsTrackingNo());
        if (StrUtil.isNotBlank(trimTracking)) {
            OrderItemTrackingRecord trackingRecord = new OrderItemTrackingRecord();
            trackingRecord.setSku(dto.getErpSku());
            trackingRecord.setProductSkuCode(item.getProductSkuCode());
            trackingRecord.setLogisticsCarrier(details.getCarrier());
            trackingRecord.setLogisticsTrackingNo(trimTracking);
            trackingRecord.setOrderNo(orders.getOrderNo());
            trackingRecord.setOrderItemNo(item.getOrderItemNo());
            trackingRecord.setLogisticsProgress(LogisticsProgress.UnDispatched);
            // 用的henjian仓库ID
            trackingRecord.setWarehouseSystemCode(warehouseSystemCode);

            trackingRecord.setQuantity(item.getTotalQuantity());

            trackingList.add(trackingRecord);
        }


        iOrderItemTrackingRecordService.saveBatch(trackingList);


        List<OrderItemPrice> itemPrices = new ArrayList<>();
        OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
        // 物流跟踪单

        for (OrderItem orderItem : orderItems) {
            paramDTO.setOrderItem(orderItem);
            paramDTO.setLogisticsType(LogisticsTypeEnum.PickUp);
            paramDTO.setSiteId(orders.getSiteId());
            OrderPriceCalculateDTO calculateDTO = orderSupport.calculationOrderItemPriceForThird(paramDTO);
            OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
            itemPrice.setOrderItemId(orderItem.getId());
            itemPrices.add(itemPrice);
        }

        map.put("logisticsInfo", orderLogisticsInfo);
        map.put("addressInfo", addressInfos);
        map.put("orderItemPrice", itemPrices);
        return map;
    }


    @Override
    public Map<String, List> msgForItems(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        List<OrderItem> orderItems = new ArrayList();
        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
        HashMap<String, List> hashMap = new HashMap<>();


        for (SaleOrderItemDTO saleOrderItemDTO : saleOrderItemsList) {
            OrderItem orderItem = new OrderItem();
            orderItem.setChannelType(orders.getChannelType());
            orderItemService.setSiteField(orderItem, orderReceiveFromThirdDTO);
            orderItemService.setOrderBusinessField(orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            orderItemService.setChannelTag(orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);

            iOrderItemThirdService.setOrderTagSystem(orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            orderItems.add(orderItem);
            OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
            // 通过 自订单编号进行关联,
            orderItemProductSkuThirdService.setBusinessFieldForErp(orderItemProductSku, orderItem, orderReceiveFromThirdDTO, orders, saleOrderItemDTO);
            orderItemProductSkus.add(orderItemProductSku);
        }
        hashMap.put("orderItems", orderItems);
        hashMap.put("orderItemProductsSku", orderItemProductSkus);
        return hashMap;
    }

    @Override
    public void getShippingLabels(List<Map<String, List>> orderData) {
    }

    @Override
    public Boolean isNeedPay() {
        return Boolean.TRUE;
    }

    @Override
    public String attachmentsFlow(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {


        return orders.getOrderNo();
    }

    @Override
    public Boolean isNeedPay(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {

        return ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(orderReceiveFromThirdDTO.getTenantId(),orderReceiveFromThirdDTO.getCurrencyCode());
    }

    @Override
    public Boolean payOrder(OrderReceiveFromThirdDTO erpDTO) throws RuntimeException{
        if(ZSMallSystemEventUtils.checkAutoPaymentEvent(erpDTO.getTenantId())){
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, erpDTO.getOrderNo());
            List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                .collect(Collectors.toList());
            OrderPayBo bo = new OrderPayBo();
            bo.addOrderNoList(orderNos);
            bo.setPaymentPassword(null);
            if(CollUtil.isEmpty(orderNos)){
                return false;
            }
            // 这里的支付要为erp单独设计一个排除尾程的支付
            TenantHelper.ignore(() -> {
                try {
                    return ordersService.payOrderOnlyErp(bo, erpDTO.getTenantId(), false, false);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e.getMessage());
                }
            });
        }
        return true;

    }

    @Override
    public Boolean payOrderForAsync(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Boolean isAsync) throws Exception {
        return null;
    }

    @Override
    public Orders thirdToDistribution(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
//        Orders orders = new Orders();
        String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
        if(ObjectUtil.isNotNull(orders.getId())){
            ChannelTypeEnum channelType = orders.getChannelType();
            orders = new Orders();
            orders.setChannelType(channelType);
        }
        orders.setOrderNo(orderNo);
        orders.setOrderExtendId(orderNo);
        // 业务属性
        orders = ordersService.setSiteField(orderReceiveFromThirdDTO, orders);
        orders = ordersService.setOrderBusinessFieldOnlyErp(orderReceiveFromThirdDTO, orders);
        orders = ordersService.setOrderTagSystemOnlyErp(orderReceiveFromThirdDTO, orders);
        orders = ordersService.setChannelTagOnlyErp(orderReceiveFromThirdDTO, orders);

        return orders;
    }

    /**
     * 功能描述：订单拆解
     *
     * @param orderReceiveFromThirdDTO 从ERP DTO接收订单
     * @return {@link List }<{@link OrderReceiveFromThirdDTO }>
     * <AUTHOR>
     * @date 2024/01/11
     */
    @Override
    public List<OrderReceiveFromThirdDTO> ordersDisassemble(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        List<SaleOrderItemDTO> saleOrderItemsList = orderReceiveFromThirdDTO.getSaleOrderItemsList();
        // erp 不会给reigon,默认取中国地区
        String regionCode = "CN";

        Long siteId;

        SiteCountryCurrency siteByCountryCode = iSiteCountryCurrencyService.getSiteByCountryCode(regionCode);
        siteId = siteByCountryCode.getId();
        List<OrderReceiveFromThirdDTO> erpDTOS = new ArrayList<>();
        // 把订单项拆开
        StringBuilder sb = new StringBuilder();
        sb.append("商品信息不存在,商品sku:");
        StringBuilder error = new StringBuilder();
        for (SaleOrderItemDTO itemDTO : saleOrderItemsList) {
            List<SaleOrderItemDTO> saleOrderItemDTOS = new ArrayList<>();
            OrderReceiveFromThirdDTO erpDTO = new OrderReceiveFromThirdDTO();
            // redis 加锁
            RedissonClient client = RedisUtils.getClient();
            RLock lock = client.getLock("erp-channel-batch:" + erpDTO.getOrderNo());
            lock.lock(1L, TimeUnit.SECONDS);

            List<Orders> orders = iOrdersService.list(new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, orderReceiveFromThirdDTO.getOrderNo()));
//            AtomicStampedReference atomicOrder = new AtomicStampedReference(orders, 1);

            if(orders.size()>=saleOrderItemsList.size()){
                lock.unlock();
                throw new AppRuntimeException(String.valueOf(ErpExceptionEnums.ALREADY_EXIST.getCode()));
            }
            lock.unlock();

            saleOrderItemDTOS.add(itemDTO);
            BeanUtils.copyProperties(orderReceiveFromThirdDTO, erpDTO);
            erpDTO.pushSaleOrderItemsList(saleOrderItemDTOS);
            erpDTO.setTotalQuantity(itemDTO.getQuantity());
            TikTokRecipientAddress tikTokRecipientAddress = new TikTokRecipientAddress();
            SiteMsgBo siteMsgBo = new SiteMsgBo();
            BeanUtils.copyProperties(siteByCountryCode, siteMsgBo);
            siteMsgBo.setSiteId(siteByCountryCode.getId());
            tikTokRecipientAddress.setSiteBo(siteMsgBo);
            erpDTO.setAddress(tikTokRecipientAddress);
            // 商品信息不存在
            String erpSku = itemDTO.getErpSku();

            try{
                TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSku(erpSku, siteId));
            }catch (Exception e){
                sb.append("商品或商品站点信息不存在,商品sku:");
                error.append(erpSku).append(",");

            }

            erpDTOS.add(erpDTO);
        }
        if(StrUtil.isNotBlank(error)) {
            error.deleteCharAt(error.length() - 1);
            throw new AppRuntimeException(sb.append(error).toString());
        }
        // 加校验,对商品
        return erpDTOS;
    }

    /**
     * 功能描述：三方更新
     *
     * @param o o
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/01/12
     */
    @Override
    public R tripartiteUpdate(Object o) {

        return R.ok();
    }

    // tripartiteDeliverGoods   tripartiteReceiptGoods
    @Override
    public Boolean tripartiteReceiptGoods(Object obj) {
        ThirdReceiptDTO bo = (ThirdReceiptDTO) obj;


        LambdaQueryWrapper<Orders> eq = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, bo.getOrderNo());
        List<Orders> ordersList = iOrdersService.list(eq);
        if(CollUtil.isEmpty(ordersList)){
            throw new AppRuntimeException("渠道订单号不存在:"+bo.getOrderNo());
        }
        for (Orders orders : ordersList) {
            List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orders.getId());
            List<String> orderItemNoList = orderItems.stream().map(OrderItem::getOrderItemNo)
                                                     .collect(Collectors.toList());

            if (CollUtil.isEmpty(orderItemNoList)) {
                throw new AppRuntimeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY.getMessage());
            }

            Set<Long> orderIds = new HashSet<>();
            Set<Long> orderItemIds = new HashSet<>();
            List<OrderItem> saveOrderItems = new ArrayList<>();
            //获取已发货、已支付的子订单
            List<OrderItem> orderItemList = iOrderItemService.getOrderItemListByShipped(orderItemNoList);
            if (CollUtil.isNotEmpty(orderItemList)) {
                for (OrderItem orderItem : orderItemList) {
                    Long orderId = orderItem.getOrderId();
                    Long orderItemId = orderItem.getId();
                    orderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
                    orderItem.setFulfillmentTime(new Date());
                    orderIds.add(orderId);
                    orderItemIds.add(orderItemId);
                    saveOrderItems.add(orderItem);
                }
                log.info("手动确认收货的子订单id为 : {}", JSONUtil.toJsonStr(orderItemIds));
                // 更新子订单的物流状态
                iOrderItemService.saveOrUpdateBatch(saveOrderItems);
                // 批量更新主订单的物流状态
                setOrderFulfillment(orderIds);

                // 记账
                billSupport.generateBillDTOByOrderItem(saveOrderItems, null);
            } else {
                throw new AppRuntimeException(ZSMallStatusCodeEnum.ORDER_CANNOT_UPDATE_NOT_BELONG_TO_ME_ERROR.getMessage());
            }
        }
        return true;
    }

    /**
     * 批量更新主订单的物流状态
     *
     * @param orderIds
     */
    public void setOrderFulfillment(Set<Long> orderIds) {
        log.info("setOrderFulfillment orderIds = {}", orderIds);
        List<Orders> orders = iOrdersService.queryListByIds(orderIds);
        if (CollectionUtils.isNotEmpty(orders)) {
            for (Orders order : orders) {
                String orderNo = order.getOrderNo();
                // 处理复合状态
                Set<LogisticsProgress> logisticsProgresses = iOrderItemService.queryFulfillmentTypesByOrderId(order.getId());
                log.info("setOrderFulfillment - orderNo = {}, logisticsProgresses = {}", orderNo, logisticsProgresses);
                LogisticsProgress originFulfillment = order.getFulfillmentProgress();
                order.setFulfillmentProgress(LogisticsProgress.getComplexType(logisticsProgresses));

                // 现货订单若在此完成，需要计入账单（NotEqual是为了判断主订单是否是从其他状态变成Fulfilled的） TODO
                if (ObjectUtil.equals(order.getOrderType(), OrderType.Wholesale)
                    && ObjectUtil.equals(order.getFulfillmentProgress(), LogisticsProgress.Fulfilled)
                    && ObjectUtil.notEqual(order.getFulfillmentProgress(), originFulfillment)) {
                    // wholesaleSupport.wholesaleOrderAddToBill(order);  TODO 待完善
                }
            }
            iOrdersService.saveOrUpdateBatch(orders);
        }
    }

    @Override
    public Boolean tripartiteDeliverGoods(Object obj) {
        // 模拟租户

        ThirdReceiptDTO bo = (ThirdReceiptDTO) obj;
        String channelOrderNo = bo.getOrderNo();
        LambdaQueryWrapper<Orders> eq = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, channelOrderNo);
        List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.list(eq));
        if (CollUtil.isEmpty(ordersList)) {
            log.error("订单不存在:{}", channelOrderNo);
            throw new AppRuntimeException(ZSMallStatusCodeEnum.ORDER_NOT_EXIST.getMessage());

        }
        Orders o = ordersList.get(0);
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(o.getTenantId());
        TenantType tenantType = TenantType.Supplier;
        // channelOrderNo:order ---> one:more so the trackingNO & carrier for orders is the same
        OrderItemTrackingRecord orderItemTrackingRecord = iOrderItemTrackingRecordService.getOne(new LambdaQueryWrapper<OrderItemTrackingRecord>().eq(OrderItemTrackingRecord::getOrderNo, o.getOrderNo())
                                                                                                                                                  .last("limit 1"));
        String trackingNo = null;
        String carrier = null;
        if (ObjectUtil.isNotNull(orderItemTrackingRecord)) {
            trackingNo = orderItemTrackingRecord.getLogisticsTrackingNo();
            carrier = orderItemTrackingRecord.getLogisticsCarrier();
        }

        LocaleMessage localeMessage = new LocaleMessage();
        for (Orders order : ordersList) {
            OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderId(order.getId());
            OrderType orderType = order.getOrderType();
            List<OrderItem> orderItems = TenantHelper.ignore(() -> iOrderItemService.getListByOrderId(order.getId()));
            ChannelTypeEnum channelType = order.getChannelType();
            // 存在进行中的退款单，禁止发货
            Integer inProgress = iOrderRefundService.countByInProgress(order.getId());
            OrderItem orderItem = orderItems.get(0);
            OrderStateType orderState = orderItem.getOrderState();
            LogisticsTypeEnum logisticsType = orderItem.getLogisticsType();
            String serviceName = null;
            if (logisticsInfo != null) {
                serviceName = logisticsInfo.getLogisticsServiceName();
            }
            if (Objects.equals(orderState, OrderStateType.Verifying)) {
                log.error("订单退款中，无法履约.渠道订单号:{},订单号:{}", channelOrderNo, o.getOrderNo());
                throw new AppRuntimeException(ZSMallStatusCodeEnum.ORDER_REFUNDING_CANT_FULFILL.getMessage());

            }


            if (inProgress > 0) {
                log.error("订单退款中，无法履约.渠道订单号:{},订单号:{}", channelOrderNo, o.getOrderNo());

            }

            if (ObjectUtil.equals(orderState, OrderStateType.Paid) && ObjectUtil.equals(order.getOrderState(), OrderStateType.Paid)) {
                LogisticsProgress fulfillment = orderItem.getFulfillmentProgress();
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                String sku = orderItemProductSku.getSku();
                String productSkuCode = orderItemProductSku.getProductSkuCode();
                String warehouseSystemCode = orderItemProductSku.getWarehouseSystemCode();

                // 未发货，或管理员账号已发货，才能继续往下走，管理员账号可以操作已发货的子订单重复发货
                if ((ObjectUtil.equals(fulfillment, LogisticsProgress.UnDispatched)
                    && ObjectUtil.equals(tenantType, TenantType.Supplier))
                    || (ObjectUtil.equals(fulfillment, LogisticsProgress.Dispatched) && ObjectUtil.equals(tenantType, TenantType.Manager))) {

                    //创建新的跟踪单信息 遍历order
                    List<OrderItemTrackingRecord> trackingRecordList = new ArrayList<>();

                    // 防止误填多余空格
                    if (StrUtil.contains(trackingNo, " ")) {
                        trackingNo = StrUtil.replace(trackingNo, " ", "");
                    }
                    OrderItemTrackingRecord record = new OrderItemTrackingRecord();
                    record.setOrderNo(order.getOrderNo());
                    record.setOrderItemNo(orderItem.getOrderItemNo());
                    record.setSku(sku);
                    record.setProductSkuCode(productSkuCode);
                    record.setQuantity(orderItem.getTotalQuantity());
                    record.setDispatchedTime(new Date());
                    record.setLogisticsCarrier(carrier);
                    record.setLogisticsService(serviceName);
                    record.setLogisticsTrackingNo(trackingNo);
                    record.setWarehouseSystemCode(warehouseSystemCode);
                    record.setSystemManaged(true);
                    thirdPartyLogisticsSupport.queryLogistics(record);
                    trackingRecordList.add(record);


                    // 子订单强制变为已发货，只有管理员才能再次修改
                    orderItem.setFulfillmentProgress(LogisticsProgress.Dispatched);
                    // 批发、批量自提和代发的订单，发货后直接完结
                    if (ObjectUtil.equals(orderType, OrderType.Wholesale) && (
                        ObjectUtil.equals(logisticsType, LogisticsTypeEnum.PickUp) || ObjectUtil
                            .equals(logisticsType, LogisticsTypeEnum.DropShipping))) {
                        orderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
                        orderItem.setFulfillmentTime(new Date());
                    }
                    if (orderItem.getDispatchedTime() == null) {
                        orderItem.setDispatchedTime(new Date());
                        updateRefundExecutableAmount(orderItem);
                    }

                    // 删除所有已存在的跟踪单
                    iOrderItemTrackingRecordService.trackingListDisassociate(orderItem.getOrderItemNo());
                    // 保存新的跟踪单
                    iOrderItemTrackingRecordService.saveBatch(trackingRecordList);
                    // 更新子订单数据
                    iOrderItemService.updateNoTenant(orderItem);
                    // 更新发货单状态
                    iOrderItemShippingRecordService.updateShippingStateByOrderItem(orderItem.getOrderItemNo(), ShippingStateEnum.Shipped);
                    // 查询主订单所有子订单的物流状态，主订单需要设置履约复合状态
                    List<OrderItem> orderItemList = iOrderItemService.getListByOrderIdNotTenant(orderItem.getOrderId());
                    Set<LogisticsProgress> fulfillmentProgresses = orderItemList.stream()
                                                                                .filter(item -> OrderStateType.Paid.equals(item.getOrderState()))
                                                                                .map(OrderItem::getFulfillmentProgress)
                                                                                .collect(Collectors.toSet());
                    order.setFulfillmentProgress(LogisticsProgress.getComplexType(fulfillmentProgresses));
                    iOrdersService.updateNoTenant(order);

                    channelTrackingSync(channelType, orderItem, order);
                    iOrderItemService.updateNoTenant(orderItem);

                    //现货订单若在此完成，需要计入账单
                    if (ObjectUtil.equals(orderType, OrderType.Wholesale) &&
                        ObjectUtil.equals(order.getFulfillmentProgress(), LogisticsProgress.Fulfilled)) {
                        // 此接口下已屏蔽租户
                        wholesaleSupports.wholesaleOrderAddToBill(order);
                    }

                } else {
                    log.error("当前无法操作发货.渠道订单号:{},订单号:{}", channelOrderNo, o.getOrderNo());
                    //ZSMallStatusCodeEnum.ORDER_ALREADY_DISPATCHED.getMessage()
                    throw new AppRuntimeException(ZSMallStatusCodeEnum.ORDER_ALREADY_DISPATCHED.getMessage());

                }
            } else {
                log.error("订单状态不为[已支付]，无法确认发货.渠道订单号:{},订单号:{}", channelOrderNo, o.getOrderNo());
                throw new AppRuntimeException("当前渠道订单,在分销存在未支付的子订单:" + o.getOrderNo());

            }

        }
        return true;
    }

    @Override
    public R<Void> test() {


        return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
    }

    @Override
    public void orderOperationHandler(OrderReceiveFromThirdDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void priceOperation(ConcurrentHashMap<String, List<Object>> businessMap,
                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void orderOperationHandlerSave(ChannelTypeEnum channelTypeEnum,
                                          ConcurrentHashMap<String, List<Object>> businessMap) {

    }

    @Override
    public List<OrderReceiveFromThirdDTO> ordersDisassembleForList(List<OrderReceiveFromThirdDTO> list,
                                                                   BusinessTypeMappingEnum mappingEnum) {
        return null;
    }

    @Override
    public List<OrderReceiveFromThirdDTO> parseThirdDataForList(String s) {
        return null;
    }

    private void channelTrackingSync(ChannelTypeEnum channelType, OrderItem orderItem, Orders order) {
        // Others和OneLink不需要回传物流跟踪信息
        if (ObjectUtil.notEqual(channelType, ChannelTypeEnum.Others) || ObjectUtil
            .notEqual(channelType, ChannelTypeEnum.OneLink)) {
            // 同步物流信息至第三方渠道
            ThirdChannelFulfillmentRecord thirdChannelFulfillmentRecord = new ThirdChannelFulfillmentRecord();

            thirdChannelFulfillmentRecord.setChannelId(orderItem.getChannelId());
            thirdChannelFulfillmentRecord.setChannelType(channelType);
            thirdChannelFulfillmentRecord.setOrderNo(order.getOrderNo());
            thirdChannelFulfillmentRecord.setOrderItemNo(orderItem.getOrderItemNo());
            thirdChannelFulfillmentRecord.setChannelItemNo(orderItem.getChannelItemNo());
            thirdChannelFulfillmentRecord.setChannelOrderNo(order.getChannelOrderNo());
            thirdChannelFulfillmentRecord.setChannelOrderName(order.getChannelOrderName());
            thirdChannelFulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.WaitPush);
            iThirdChannelFulfillmentRecordService.save(thirdChannelFulfillmentRecord);
        }
    }


    /**
     * 刷新子订单的售后可执行金额
     *
     * @param orderItem
     */
    private void updateRefundExecutableAmount(OrderItem orderItem) {
        String orderItemNo = orderItem.getOrderItemNo();
        // 新版售后，活动订单发货后，需要把订金加回到可执行金额中
        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItem.getOrderItemNo());
        Integer orderItemNum = orderItemPrice.getTotalQuantity();
        BigDecimal depositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
        BigDecimal depositUnitPriceSup = orderItemPrice.getOriginalDepositUnitPrice();
        if (depositUnitPrice == null || depositUnitPriceSup == null) {
            return;
        }
        log.info("刷新可执行金额 orderItemNo = {} orderItemNum = {}, depositUnitPrice = {}, depositUnitPriceSup = {}", orderItemNo,
            orderItemNum, depositUnitPrice, depositUnitPriceSup);

        BigDecimal depositTotalPrice = NumberUtil.mul(depositUnitPrice, orderItemNum);
        BigDecimal depositTotalPriceSup = NumberUtil.mul(depositUnitPriceSup, orderItemNum);
        log.info("刷新可执行金额 orderItemNo = {} depositTotalPrice = {}, depositTotalPriceSup = {}", orderItemNo,
            depositTotalPrice, depositTotalPriceSup);

        BigDecimal refundExecutableAmount = orderItem.getPlatformRefundExecutableAmount();
        BigDecimal refundExecutableAmountSup = orderItem.getOriginalRefundExecutableAmount();

        log.info("刷新可执行金额 orderItemNo = {} refundExecutableAmount（原始值） = {}, refundExecutableAmountSup（原始值） = {}",
            orderItemNo, refundExecutableAmount, refundExecutableAmountSup);

        BigDecimal newRefundExecutableAmount = NumberUtil.add(depositTotalPrice, refundExecutableAmount);
        BigDecimal newRefundExecutableAmountSup = NumberUtil.add(depositTotalPriceSup, refundExecutableAmountSup);

        log.info("刷新可执行金额 orderItemNo = {} refundExecutableAmount（新值） = {}, refundExecutableAmountSup（新值） = {}", orderItemNo,
            newRefundExecutableAmount, newRefundExecutableAmountSup);
        orderItem.setPlatformRefundExecutableAmount(newRefundExecutableAmount);
        orderItem.setOriginalRefundExecutableAmount(newRefundExecutableAmountSup);
    }
//
//    @Test
//    public void test1() {
//        ignore(()->{
//            int i = 1/0;
//        });
//    }
//    public static void ignore(Runnable handle) {
//
//        try {
//            handle.run();
//        }catch (Exception e){
//            log.info("试试能不能跑出来啊");
//            throw new RuntimeException(e.getMessage());
//        } finally {
//            log.info("试试");
//        }
//    }



}
