package com.zsmall.system.entity.domain.vo.salesChannel;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年5月23日  16:19
 * @description: 订单Tracking信息中的package信息
 */
@Data
@Accessors(chain = true)
public class TrackingCallBackPackagesVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // 包裹重量
    private BigDecimal packageWeight;

    // 承运商
    private String carrier;

    // 跟踪号
    private String trackingNo;

    // 发货时间
    private String shipDate;

    // 订单行信息
    private List<TrackingCallBackPackagesItemsVo> items;
}
