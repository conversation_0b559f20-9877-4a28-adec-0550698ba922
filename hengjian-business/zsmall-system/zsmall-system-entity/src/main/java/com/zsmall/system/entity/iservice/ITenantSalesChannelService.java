package com.zsmall.system.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hengjian.common.core.constant.TenantConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.JacksonUtils;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.xxl.conf.core.XxlConfClient;
import com.zsmall.common.enums.SalesChannelInterfaceStatusEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.tiktok.TikTokStoreTypeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.system.entity.domain.ChannelWarehouseInfo;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.XxlConfKeyValue;
import com.zsmall.system.entity.domain.bo.salesChannel.TenantSalesChannelBo;
import com.zsmall.system.entity.domain.vo.salesChannel.SaleChannelCallBackVo;
import com.zsmall.system.entity.domain.vo.salesChannel.TenantSalesChannelVo;
import com.zsmall.system.entity.enums.XxlConfEnum;
import com.zsmall.system.entity.mapper.TenantSalesChannelMapper;
import com.zsmall.system.entity.util.AuzTiktokUtil;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户渠道Service接口实现
 *
 * <AUTHOR> Li
 * @date 2023-06-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ITenantSalesChannelService extends ServiceImpl<TenantSalesChannelMapper, TenantSalesChannel> {

//
//    @Value("${distribution.tenant.sales.channel.tiktok.appkey}")
//    private String appkey;
//
//    @Value("${distribution.tenant.sales.channel.tiktok.appSecret}")
//    private String appSecret;

    @Value("${distribution.boss.account.authorize.redirecturl}")
    private String redirectUrl;
    @Value("${distribution.tenant.sales.channel.amz.clientid}")
    private String clientId;

    @Value("${distribution.tenant.sales.channel.amz.clientsecret}")
    private String clientSecret;

    @Resource
    private AuzTiktokUtil auzTiktokUtil;

    @Resource
    private IChannelWarehouseInfoServiceImpl channelWarehouseInfoService;

    private  final RabbitTemplate rabbitTemplate;

    public TenantSalesChannel selectByIdNotTenant(Long id) {
        return TenantHelper.ignore(() -> baseMapper.selectById(id));
    }

    public List<TenantSalesChannel> selectByIdsNotTenant(List<Long> ids) {
        return TenantHelper.ignore(() -> baseMapper.selectBatchIds(ids));
    }

    /**
     * 查询租户渠道
     */
    public TenantSalesChannelVo queryById(Long id) {
        if (LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)) {
            return baseMapper.selectVoById(id);
        }
        return TenantHelper.ignore(() -> baseMapper.selectVoById(id));
    }
    public TenantSalesChannelVo queryByIdV2(Long id) {

        if (LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)) {
            TenantSalesChannelVo tenantSalesChannelVo = baseMapper.selectVoById(id);
            if(ObjectUtil.isNotEmpty(tenantSalesChannelVo.getSite())){
                tenantSalesChannelVo.setSiteList(Collections.singletonList(tenantSalesChannelVo.getSite()));
            }
            if(ObjectUtil.isNotEmpty(tenantSalesChannelVo)&&ChannelTypeEnum.Temu.name().equals(tenantSalesChannelVo.getChannelType())){
                String connectStr = tenantSalesChannelVo.getConnectStr();
                if(StrUtil.isNotBlank(connectStr)){
                    JSONObject jsonObject = JSONObject.parseObject(connectStr);
                    if(ObjectUtil.isNotEmpty(jsonObject)){
                        String usAccessToken = jsonObject.getString("usAccessToken");
                        String cnAccessToken = jsonObject.getString("cnAccessToken");
                        tenantSalesChannelVo.setUsAccessToken(usAccessToken);
                        tenantSalesChannelVo.setCnAccessToken(cnAccessToken);
                        tenantSalesChannelVo.setAppKey( jsonObject.getString("appKey"));
                        tenantSalesChannelVo.setAppSecret( jsonObject.getString("appSecret"));
                        tenantSalesChannelVo.setAccessToken( jsonObject.getString("accessToken"));
                    }
                }
            }
            if(ObjectUtil.isNotEmpty(tenantSalesChannelVo)&&ChannelTypeEnum.EC.name().equals(tenantSalesChannelVo.getChannelType())){
                String connectStr = tenantSalesChannelVo.getConnectStr();
                if(StrUtil.isNotBlank(connectStr)){
                    JSONObject jsonObject = JSONObject.parseObject(connectStr);
                    if(ObjectUtil.isNotEmpty(jsonObject)){
                        String userName = jsonObject.getString("userName");
                        String userPass = jsonObject.getString("userPass");
                        String serviceToken = jsonObject.getString("serviceToken");
                        String apiUri = jsonObject.getString("apiUri");
                        tenantSalesChannelVo.setUserName(userName);
                        tenantSalesChannelVo.setUserPass(userPass);
                        tenantSalesChannelVo.setServiceToken(serviceToken);
                        tenantSalesChannelVo.setApiUri(apiUri);
                        List<ChannelWarehouseInfo> channelWarehouseInfoList = channelWarehouseInfoService.queryListByTenantSaleChannelId(tenantSalesChannelVo.getId());
                        tenantSalesChannelVo.setChannelWarehouseInfoList(channelWarehouseInfoList);
                    }
                }
                return tenantSalesChannelVo;
            }
            return tenantSalesChannelVo;
        }
        TenantSalesChannelVo tenantSalesChannelVo = TenantHelper.ignore(() -> baseMapper.selectVoById(id));
        if(ObjectUtil.isNotEmpty(tenantSalesChannelVo.getSite())){
            tenantSalesChannelVo.setSiteList(Collections.singletonList(tenantSalesChannelVo.getSite()));
        }
        if(ObjectUtil.isNotEmpty(tenantSalesChannelVo)&&ChannelTypeEnum.Temu.name().equals(tenantSalesChannelVo.getChannelType())){
            String connectStr = tenantSalesChannelVo.getConnectStr();
            if(StrUtil.isNotBlank(connectStr)){
                JSONObject jsonObject = JSONObject.parseObject(connectStr);
                if(ObjectUtil.isNotEmpty(jsonObject)){
                    String usAccessToken = jsonObject.getString("usAccessToken");
                    String cnAccessToken = jsonObject.getString("cnAccessToken");
                    tenantSalesChannelVo.setUsAccessToken(usAccessToken);
                    tenantSalesChannelVo.setCnAccessToken(cnAccessToken);
                }
            }

        }
        return tenantSalesChannelVo;
    }
    /**
     * 查询租户渠道
     */
    public TenantSalesChannel queryByIdAndChannelType(Long id, ChannelTypeEnum channelTypeEnum) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getId, id);
        lqw.eq(TenantSalesChannel::getChannelType, channelTypeEnum);

        if (LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)) {
            return baseMapper.selectOne(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 查询租户渠道列表
     */
    public TableDataInfo<TenantSalesChannelVo> queryPageList(TenantSalesChannelBo bo, PageQuery pageQuery) {
        if (StrUtil.isBlank(bo.getChannelType())){
            throw new RuntimeException("渠道类型不能为空");
        }
        LambdaQueryWrapper<TenantSalesChannel> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(TenantSalesChannel::getCreateTime);
        Page<TenantSalesChannelVo> result;
        // 如果是管理员权限 不加tenantId
        if (StringUtils.equals(LoginHelper.getTenantId(), TenantConstants.DEFAULT_TENANT_ID)) {
            result = TenantHelper.ignore(() -> baseMapper.selectVoPage(pageQuery.build(), lqw));
        } else {
            result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询租户渠道列表
     */
    public List<TenantSalesChannelVo> queryList(TenantSalesChannelBo bo) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TenantSalesChannel> buildQueryWrapper(TenantSalesChannelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getChannelName()), TenantSalesChannel::getChannelName, bo.getChannelName());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), TenantSalesChannel::getChannelType, bo.getChannelType());
        lqw.like(StringUtils.isNotBlank(bo.getChannelAlias()), TenantSalesChannel::getChannelAlias, bo.getChannelAlias());
        lqw.eq(bo.getState() != null, TenantSalesChannel::getState, bo.getState());
        lqw.eq(bo.getLogisticsType() != null, TenantSalesChannel::getLogisticsType, bo.getLogisticsType());
        return lqw;
    }

    /**
     * 新增租户渠道
     */
    public Boolean insertByBo(TenantSalesChannelBo bo) throws RStatusCodeException {
        TenantSalesChannel add = MapstructUtils.convert(bo, TenantSalesChannel.class);
        validEntityBeforeSave(add);
        add.setTenantId(LoginHelper.getTenantId());
        add.setStatus(SalesChannelInterfaceStatusEnum.CREATED.getValue());
        boolean flag = baseMapper.insert(add) > 0;
        return flag;
    }

    // aop 加个根据类型的校验
    @Transactional(rollbackFor = Exception.class)
    public ArrayList<TenantSalesChannel> insertByBoV2(TenantSalesChannelBo tenantSalesChannelBo) throws Exception {
        boolean flag =false;
        String channelType = tenantSalesChannelBo.getChannelType();
        String tenantId = LoginHelper.getTenantId();
        List<String> siteList = tenantSalesChannelBo.getSiteList();
        ArrayList<TenantSalesChannel> list = new ArrayList<>();
        Map<String, List<ChannelWarehouseInfo>> amazonScMap=new HashMap<>();
        if(CollUtil.isNotEmpty(siteList) && !siteList.isEmpty()){
            if(StrUtil.equalsIgnoreCase(channelType, ChannelTypeEnum.TikTok.name())){
                for (String s : siteList) {
                    TenantSalesChannelBo tenantSalesChannelBo1 = new TenantSalesChannelBo();
                    BeanUtil.copyProperties(tenantSalesChannelBo,tenantSalesChannelBo1);
                    // 防止大小写问题
                    tenantSalesChannelBo1.setChannelType(ChannelTypeEnum.TikTok.name());
                    String storeName = tenantSalesChannelBo1.getChannelName();
                    tenantSalesChannelBo1.setAccountName(storeName);
                    // storeName + - + site    拿店铺名拼站点
                    storeName = storeName + "-" + s;
                    LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<TenantSalesChannel>()
                        .eq(TenantSalesChannel::getChannelType, tenantSalesChannelBo.getChannelType())
                        .eq(TenantSalesChannel::getChannelName, storeName)
                        .eq(TenantSalesChannel::getDelFlag,0);
                    List<TenantSalesChannel> tenantSalesChannels = TenantHelper.ignore(()->list(wrapper));
                    if(CollUtil.isNotEmpty(tenantSalesChannels)){
                        throw new Exception("站点已存在");
                    }
                    tenantSalesChannelBo1.setStoreName(storeName);
                    tenantSalesChannelBo1.setChannelName(storeName);

                    tenantSalesChannelBo1.setThirdChannelFlag(storeName);

                    tenantSalesChannelBo1.setChannelAlias(storeName);

                    tenantSalesChannelBo1.setSite(s);

                    TenantSalesChannel add = MapstructUtils.convert(tenantSalesChannelBo1, TenantSalesChannel.class);
                    validEntityBeforeSave(add);
                    add.setTenantId(LoginHelper.getTenantId());
                    add.setStatus(SalesChannelInterfaceStatusEnum.CREATED.getValue());
                    String tiktokStoreType = tenantSalesChannelBo.getTiktokStoreType();
                    // 如果是本土走new 跨境走newkj
                    if(TikTokStoreTypeEnum.MAINLAND.getStatus().equals(tiktokStoreType)){
                        add.setXxlConfAppKey("distribution.tiktok.new.appkey");
                        add.setXxlConfAppSecret("distribution.tiktok.new.appsecret");
                        add.setTiktokStoreType(TikTokStoreTypeEnum.MAINLAND.getStatus());
                    }
                    if(TikTokStoreTypeEnum.CROSS_BORDER.getStatus().equals(tiktokStoreType)){
                        add.setXxlConfAppKey("distribution.tiktok.newkj.appkey");
                        add.setXxlConfAppSecret("distribution.tiktok.newkj.appsecret");
                        add.setTiktokStoreType(TikTokStoreTypeEnum.CROSS_BORDER.getStatus());
                    }

                    list.add(add);
                }
            }

            if(StrUtil.equalsIgnoreCase(channelType, ChannelTypeEnum.Temu.name())){
//                String usAccessToken = tenantSalesChannelBo.getUsAccessToken();
//                if(ObjectUtil.isEmpty(usAccessToken)){
//                    throw new Exception("usAccessToken不能为空");
//                }
                // temu渠道为单站点
                for (String s : siteList) {
                    TenantSalesChannelBo tenantSalesChannelBo1 = new TenantSalesChannelBo();
                    BeanUtil.copyProperties(tenantSalesChannelBo,tenantSalesChannelBo1);
                    // 防止大小写问题
                    tenantSalesChannelBo1.setChannelType(ChannelTypeEnum.Temu.name());
                    String storeName = tenantSalesChannelBo1.getChannelName();
                    tenantSalesChannelBo1.setAccountName(storeName);
                    // storeName + - + site    拿店铺名拼站点
                    storeName = storeName + "-" + s;
                    tenantSalesChannelBo1.setStoreName(storeName);
                    tenantSalesChannelBo1.setChannelName(storeName);

                    LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<TenantSalesChannel>()
                        .eq(TenantSalesChannel::getChannelType, tenantSalesChannelBo.getChannelType())
                        .eq(TenantSalesChannel::getChannelName, storeName)
                        .eq(TenantSalesChannel::getDelFlag,0);
                    List<TenantSalesChannel> tenantSalesChannels = list(wrapper);
                    if(CollUtil.isNotEmpty(tenantSalesChannels)){
                        throw new Exception("站点已存在");
                    }
                    tenantSalesChannelBo1.setThirdChannelFlag(storeName);

                    tenantSalesChannelBo1.setChannelAlias(storeName);

                    tenantSalesChannelBo1.setSite(s);

                    TenantSalesChannel add = MapstructUtils.convert(tenantSalesChannelBo1, TenantSalesChannel.class);
//                     创建一个ObjectMapper实例
                    ObjectMapper objectMapper = new ObjectMapper();

                    // 创建一个空的ObjectNode
                    ObjectNode node = objectMapper.createObjectNode();
                    if (ObjectUtil.isNotNull(tenantSalesChannelBo.getTemuChannelType())){
                        if (tenantSalesChannelBo.getTemuChannelType()==1){
                            // 手动添加属性到ObjectNode
                            node.put("usAccessToken", tenantSalesChannelBo1.getUsAccessToken());
                            node.put("cnAccessToken", tenantSalesChannelBo1.getCnAccessToken());
                        }
                        if (tenantSalesChannelBo.getTemuChannelType()==2){
                            // 手动添加属性到ObjectNode
                            node.put("appKey", tenantSalesChannelBo1.getAppKey());
                            node.put("appSecret", tenantSalesChannelBo1.getAppSecret());
                            node.put("accessToken", tenantSalesChannelBo1.getAccessToken());
                        }

                    }
                    String json;
                    // 将ObjectNode转换为JSON字符串
                    try {
                        json = objectMapper.writeValueAsString(node);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }

                    // 对tenantSalesChannel的usAccessToken和cnAccessToken进行json化 保存到connectStr
                    if (add != null) {
                        add.setConnectStr(json);
                    }
                    validEntityBeforeSave(add);
                    add.setTenantId(tenantId);
                    add.setStatus(SalesChannelInterfaceStatusEnum.CREATED.getValue());
                    list.add(add);
                }
            }

            if(StrUtil.equalsIgnoreCase(channelType, ChannelTypeEnum.Amazon_SC.name())){
                amazonScMap = tenantSalesChannelBo.getAmazonScChannelWarehouseInfos()
                                                                                      .stream()
                                                                                      .collect(Collectors.toMap(
                                                                                          amazonScChannelWarehouseInfo -> tenantSalesChannelBo.getChannelName() + "-" + amazonScChannelWarehouseInfo.getSiteType(), // 使用流中元素的属性
                                                                                          TenantSalesChannelBo.AmazonScChannelWarehouseInfo::getWarehouseInfos, // value是warehouseInfos
                                                                                          (list1, list2) -> { list1.addAll(list2); return list1; } // 合并策略，如果键相同，则合并列表
                                                                                      ));
                for (String s : siteList) {
                    TenantSalesChannelBo tenantSalesChannelBo1 = new TenantSalesChannelBo();
                    BeanUtil.copyProperties(tenantSalesChannelBo,tenantSalesChannelBo1);
                    // 防止大小写问题
                    tenantSalesChannelBo1.setChannelType(ChannelTypeEnum.Amazon_SC.name());
                    String storeName = tenantSalesChannelBo1.getChannelName();
                    tenantSalesChannelBo1.setAccountName(storeName);
                    // storeName + - + site    拿店铺名拼站点
                    storeName = storeName + "-" + s;
                    tenantSalesChannelBo1.setStoreName(storeName);
                    tenantSalesChannelBo1.setChannelName(storeName);

                    LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<TenantSalesChannel>()
                        .eq(TenantSalesChannel::getChannelType, tenantSalesChannelBo.getChannelType())
                        .eq(TenantSalesChannel::getChannelName, storeName)
                        .eq(TenantSalesChannel::getDelFlag,0);
                    List<TenantSalesChannel> tenantSalesChannels = list(wrapper);
                    if(CollUtil.isNotEmpty(tenantSalesChannels)){
                        throw new Exception("站点已存在");
                    }
                    tenantSalesChannelBo1.setThirdChannelFlag(storeName);

                    tenantSalesChannelBo1.setChannelAlias(storeName);

                    tenantSalesChannelBo1.setSite(s);
                    TenantSalesChannel add = MapstructUtils.convert(tenantSalesChannelBo1, TenantSalesChannel.class);
                    validEntityBeforeSave(add);
                    add.setTenantId(tenantId);
                    add.setStatus(SalesChannelInterfaceStatusEnum.CREATED.getValue());
                    list.add(add);
                }
            }


            if(StrUtil.equalsIgnoreCase(channelType, ChannelTypeEnum.Amazon_VC.name())){
                for (String s : siteList) {
                    TenantSalesChannelBo tenantSalesChannelBo1 = new TenantSalesChannelBo();
                    BeanUtil.copyProperties(tenantSalesChannelBo,tenantSalesChannelBo1);
                    // 防止大小写问题
                    tenantSalesChannelBo1.setChannelType(ChannelTypeEnum.Amazon_VC.name());
                    String storeName = tenantSalesChannelBo1.getChannelName();
                    // storeName + - + site    拿店铺名拼站点
                    storeName = storeName + "-" + s;
                    tenantSalesChannelBo1.setStoreName(storeName);
                    tenantSalesChannelBo1.setChannelName(storeName);

                    LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<TenantSalesChannel>()
                        .eq(TenantSalesChannel::getChannelType, tenantSalesChannelBo.getChannelType())
                        .eq(TenantSalesChannel::getChannelName, storeName)
                        .eq(TenantSalesChannel::getDelFlag,0);
                    List<TenantSalesChannel> tenantSalesChannels = list(wrapper);
                    if(CollUtil.isNotEmpty(tenantSalesChannels)){
                        throw new Exception("站点已存在");
                    }
                    tenantSalesChannelBo1.setThirdChannelFlag(storeName);

                    tenantSalesChannelBo1.setChannelAlias(storeName);

                    tenantSalesChannelBo1.setSite(s);

                    TenantSalesChannel add = MapstructUtils.convert(tenantSalesChannelBo1, TenantSalesChannel.class);
                    validEntityBeforeSave(add);
                    add.setTenantId(tenantId);
                    add.setStatus(SalesChannelInterfaceStatusEnum.CREATED.getValue());
                    list.add(add);
                }
            }
        }

        if(StrUtil.equalsIgnoreCase(channelType, ChannelTypeEnum.EC.name())){
            TenantSalesChannelBo tenantSalesChannelBo1 = new TenantSalesChannelBo();
            BeanUtil.copyProperties(tenantSalesChannelBo,tenantSalesChannelBo1);
            // 防止大小写问题
            tenantSalesChannelBo1.setChannelType(ChannelTypeEnum.EC.name());
            String storeName = tenantSalesChannelBo1.getChannelName();
            tenantSalesChannelBo1.setStoreName(storeName);
            tenantSalesChannelBo1.setChannelName(storeName);
            LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<TenantSalesChannel>()
                .eq(TenantSalesChannel::getChannelType, tenantSalesChannelBo.getChannelType())
                .eq(TenantSalesChannel::getChannelName, storeName)
                .eq(TenantSalesChannel::getDelFlag,0);
            List<TenantSalesChannel> tenantSalesChannels = list(wrapper);
            if(CollUtil.isNotEmpty(tenantSalesChannels)){
                throw new Exception("店铺已存在");
            }
            tenantSalesChannelBo1.setThirdChannelFlag(storeName);
            tenantSalesChannelBo1.setChannelAlias(storeName);
            TenantSalesChannel add = MapstructUtils.convert(tenantSalesChannelBo1, TenantSalesChannel.class);
            validEntityBeforeSave(add);
            add.setTenantId(tenantId);
            add.setStatus(SalesChannelInterfaceStatusEnum.CREATED.getValue());
            Map<String, Object> map = new HashMap<>();
            map.put("userName", tenantSalesChannelBo.getUserName());
            map.put("userPass", tenantSalesChannelBo.getUserPass());
            map.put("serviceToken", tenantSalesChannelBo.getServiceToken());
            map.put("apiUri", tenantSalesChannelBo.getApiUri());
            // 店铺连接参数 转json
            ObjectMapper objectMapper = new ObjectMapper();
            String connectStr = objectMapper.writeValueAsString(map);
            add.setConnectStr(connectStr);
            list.add(add);
        }
        baseMapper.insertBatch(list);
        //处理店铺仓库信息
        //如果是自提处理
        if (tenantSalesChannelBo.getLogisticsType().equals(LogisticsTypeEnum.PickUp)){
            List<ChannelWarehouseInfo> channelWarehouseInfoList=new ArrayList<>();
            for (TenantSalesChannel s : list) {
                //如果是Amazon_SC
                if(StrUtil.equalsIgnoreCase(s.getChannelType(), ChannelTypeEnum.Amazon_SC.name())){
                    //分别获取每个站点的仓库信息
                    List<ChannelWarehouseInfo> channelWarehouseInfos = amazonScMap.get(s.getThirdChannelFlag());
                    if (CollUtil.isEmpty(channelWarehouseInfos)){
                        throw new RuntimeException(StrUtil.format("错误的数据,当前站点下未找到店铺仓库信息",s.getThirdChannelFlag()));
                    }
                    channelWarehouseInfos.forEach(ss->{
                        ChannelWarehouseInfo ca= new ChannelWarehouseInfo();
                        ca.setTenantId(s.getTenantId());
                        ca.setTenantSaleChannelId(s.getId());
                        ca.setWarehouseAdminId(ss.getWarehouseAdminId());
                        ca.setWarehouseCode(ss.getWarehouseCode());
                        ca.setChannelName(s.getChannelName());
                        ca.setChannelWarehouseName(ss.getChannelWarehouseName());
                        ca.setChannelWarehouseCode(ss.getChannelWarehouseCode());
                        channelWarehouseInfoList.add(ca);
                    });
                }else {
                    //直接取值
                    tenantSalesChannelBo.getChannelWarehouseInfoList().forEach(ss->{
                        ChannelWarehouseInfo ca= new ChannelWarehouseInfo();
                        ca.setTenantId(s.getTenantId());
                        ca.setTenantSaleChannelId(s.getId());
                        ca.setWarehouseAdminId(ss.getWarehouseAdminId());
                        ca.setWarehouseCode(ss.getWarehouseCode());
                        ca.setChannelName(s.getChannelName());
                        ca.setChannelWarehouseName(ss.getChannelWarehouseName());
                        ca.setChannelWarehouseCode(ss.getChannelWarehouseCode());
                        channelWarehouseInfoList.add(ca);

                    });
                }
            }
            channelWarehouseInfoService.saveBatch(channelWarehouseInfoList);
        }

        return list;
    }

    /**
     * 修改租户渠道
     */
    public TenantSalesChannel updateByBo(TenantSalesChannelBo bo) throws Exception {
        if(ObjectUtil.isNotEmpty(bo)){
            boolean notEmpty = ObjectUtil.isEmpty(bo.getLogisticsType());
            if(notEmpty){
                throw new Exception("发货方式不能为空");
            }
        }
        // 如果bo的区域/站点信息发生变更,需要变更商店名称
        TenantSalesChannel byId = getById(bo.getId());
        HashMap<String, String> map = null;
        if(!StrUtil.equalsIgnoreCase(bo.getChannelType(), ChannelTypeEnum.EC.name()) && !byId.getSite().equals(bo.getSite())){
            String channelName = bo.getChannelName();
            String storeName = channelName.substring(0, channelName.lastIndexOf("-"));
            String site = bo.getSite();
            storeName = storeName+ "-" + site;
            LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<TenantSalesChannel>()
                .eq(TenantSalesChannel::getChannelType, bo.getChannelType())
                .eq(TenantSalesChannel::getChannelName, storeName)
                .eq(TenantSalesChannel::getDelFlag,0);
            List<TenantSalesChannel> list = TenantHelper.ignore(()->list(wrapper));
            if(CollUtil.isNotEmpty(list)){
                throw new Exception("站点已存在");
            }
            bo.setStoreName(storeName);
            bo.setChannelName(storeName);
            bo.setChannelAlias(storeName);
            bo.setThirdChannelFlag(storeName);
        }
        //处理秘钥信息
        if (ObjectUtil.isNotNull(bo.getTemuChannelType())){
           map= new HashMap<>();
            if (bo.getTemuChannelType()==1){
                // 手动添加属性到ObjectNode
                map.put("usAccessToken", bo.getUsAccessToken());
                if (ObjectUtil.isEmpty(bo.getCnAccessToken())){
                    map.put("cnAccessToken", "");
                }else {
                    map.put("cnAccessToken", bo.getCnAccessToken());
                }
            }
            if (bo.getTemuChannelType()==2){
                // 手动添加属性到ObjectNode
                map.put("appKey", bo.getAppKey());
                map.put("appSecret", bo.getAppSecret());
                map.put("accessToken", bo.getAccessToken());
            }
        }
        TenantSalesChannel update = MapstructUtils.convert(bo, TenantSalesChannel.class);
        validEntityBeforeSave(update);
        if (ObjectUtil.isNotNull(map)){
            update.setConnectStr(JSON.toJSONString(map));
        }
        if(StrUtil.equalsIgnoreCase(bo.getChannelType(), ChannelTypeEnum.EC.name())){
            Map<String, Object> mapUpdate = new HashMap<>();
            mapUpdate.put("userName", bo.getUserName());
            mapUpdate.put("userPass", bo.getUserPass());
            mapUpdate.put("serviceToken", bo.getServiceToken());
            mapUpdate.put("apiUri", bo.getApiUri());
            // 店铺连接参数 转json
            ObjectMapper objectMapper = new ObjectMapper();
            String connectStr = objectMapper.writeValueAsString(mapUpdate);
            update.setConnectStr(connectStr);

        }
         baseMapper.updateById(update);
        //处理渠道仓库逻辑
        if(ObjectUtil.equals(bo.getLogisticsType(), LogisticsTypeEnum.PickUp)) {
            channelWarehouseInfoService.deleteByTenantSaleChannelId(bo.getId());
            //插入新的
            bo.getChannelWarehouseInfoList().forEach(s->{
                ChannelWarehouseInfo ca= new ChannelWarehouseInfo();
                ca.setTenantId(s.getTenantId());
                ca.setTenantSaleChannelId(bo.getId());
                ca.setWarehouseAdminId(s.getWarehouseAdminId());
                ca.setWarehouseCode(s.getWarehouseCode());
                ca.setChannelName(bo.getChannelName());
                ca.setChannelWarehouseName(s.getChannelWarehouseName());
                ca.setChannelWarehouseCode(s.getChannelWarehouseCode());
                channelWarehouseInfoService.save(ca);
            });
        }
        return  update;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TenantSalesChannel entity) throws RStatusCodeException {
        // 做一些数据校验,如唯一约束
        if (StrUtil.equals(entity.getChannelType(), ChannelTypeEnum.Wayfair.getValue())) {
            if (StrUtil.isBlank(entity.getChannelUrl())) {
                entity.setChannelUrl("https://partners.wayfair.com/");
            }
            if (StrUtil.isBlank(entity.getChannelAlias())) {
                entity.setChannelAlias(entity.getChannelName());
            }
        } else if (StrUtil.equals(entity.getChannelType(), ChannelTypeEnum.Rakuten.getValue())) {
            entity.setChannelUrl("https://glogin.gl.rakuten.co.jp/?sp_id=0");
            entity.setChannelAlias(entity.getChannelName());
        }
    }

    /**
     * 判断是否存在销售渠道
     *
     * @param channelType
     * @param clientSecret
     * @param privateKey
     * @return
     */
    public boolean existsSalesChannel(String channelType, String clientSecret, String privateKey, Long notChannelId) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelType, channelType);
        lqw.eq(TenantSalesChannel::getClientSecret, clientSecret);
        lqw.eq(TenantSalesChannel::getPrivateKey, privateKey);
        lqw.ne(notChannelId != null, TenantSalesChannel::getId, notChannelId);
        return baseMapper.exists(lqw);
    }

    /**
     * 批量删除租户渠道
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据渠道类型和店铺名称去查询未删除的渠道授权信息
     *
     * @param channelTypeEnum
     * @param channelName
     * @return
     */
    public TenantSalesChannel queryNotDeletedByChannelTypeAndName(ChannelTypeEnum channelTypeEnum, String channelName) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelName, channelName);
        lqw.eq(TenantSalesChannel::getChannelType, channelTypeEnum.getValue());
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 根据渠道类型和店铺名称去查询非当前租户的未删除的渠道授权信息
     *
     * @param tenantId
     * @param channelTypeEnum
     * @param channelName
     * @return
     */
    public TenantSalesChannel queryValidByChannelTypeAndNameAndNonTenantId(String tenantId,
                                                                           ChannelTypeEnum channelTypeEnum,
                                                                           String channelName) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelName, channelName);
        lqw.eq(TenantSalesChannel::getChannelType, channelTypeEnum.getValue());
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        lqw.ne(NoDeptTenantEntity::getTenantId, tenantId);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 根据渠道类型和店铺名称去查询当前租户的未删除的渠道授权信息
     *
     * @param tenantId
     * @param channelTypeEnum
     * @param channelName
     * @return
     */
    public TenantSalesChannel queryValidByChannelTypeAndNameAndTenantId(String tenantId,
                                                                        ChannelTypeEnum channelTypeEnum,
                                                                        String channelName) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelName, channelName);
        lqw.eq(TenantSalesChannel::getChannelType, channelTypeEnum.getValue());
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        lqw.eq(NoDeptTenantEntity::getTenantId, tenantId);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 查询有用的渠道授权信息
     *
     * @param tenantId        租户Id
     * @param channelTypeEnum 渠道类型
     * @param shopDomain      店铺
     * @return
     */
    public TenantSalesChannel queryEnableByChannelTypeAndName(String tenantId, ChannelTypeEnum channelTypeEnum,
                                                              String shopDomain) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelName, shopDomain);
        lqw.eq(TenantSalesChannel::getChannelType, channelTypeEnum.getValue());
        lqw.eq(TenantSalesChannel::getTenantId, tenantId);
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    @InMethodLog("根据渠道查询渠道店铺（未删除的）")
    public List<TenantSalesChannel> queryByChannelTypeNoDelete(ChannelTypeEnum channelTypeEnum) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelType, channelTypeEnum.getValue());
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据店铺查询销售渠道
     *
     * @param shopDomain
     * @return
     */
    public TenantSalesChannel queryByChannelNameWithIgnoreTenant(String shopDomain) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelName, shopDomain);
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    /**
     * 根据渠道类型和店铺别名查询销售渠道
     *
     * @param channelAlias
     * @return
     */
    public List<TenantSalesChannel> queryByChannelTypeAndAlias(ChannelTypeEnum channelType, String channelAlias) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelType, channelType.getValue());
        lqw.eq(TenantSalesChannel::getChannelAlias, channelAlias);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据渠道类型查询有效的渠道（无视租户）
     *
     * @param channelType
     * @return
     */
    public List<TenantSalesChannel> queryValidByChannelTypeNotTenant(ChannelTypeEnum channelType) {
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelType, channelType.getValue());
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        lqw.eq(TenantSalesChannel::getDelFlag, 0);
        lqw.isNotNull(TenantSalesChannel::getConnectStr);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 保存租户渠道信息
     *
     * @param salesChannel
     * @return
     */
    public Boolean saveWithIgnoreTenant(TenantSalesChannel salesChannel) {
        return TenantHelper.ignore(() -> baseMapper.insertOrUpdate(salesChannel));
    }


    /**
     * 功能描述：按频道标志获取
     *
     * @param thirdChannelFlag 商店标志
     * @param channelType      通道类型
     * @return {@link TenantSalesChannel }
     * <AUTHOR>
     * @date 2024/01/08
     */
    public TenantSalesChannel getByChannelFlag(String thirdChannelFlag, String channelType) {
        LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantSalesChannel::getChannelType, channelType)
               .eq(TenantSalesChannel::getThirdChannelFlag, thirdChannelFlag)
            .eq(TenantSalesChannel::getDelFlag, 0);

        return getOne(wrapper);
    }

    /**
     * 功能描述：获取授权重定向 URL
     *
     * @param tenantSalesChannel 租户销售渠道
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/01/11
     */
    private String getAuthorizeRedirectUrl(TenantSalesChannel tenantSalesChannel) {
        return redirectUrl + "?id=" + tenantSalesChannel.getId() + "&type=" + tenantSalesChannel.getChannelType();
    }

    /**
     * 功能描述：获取供应商刷新令牌
     *
     * @param sellerId    卖家 ID
     * @param acquireCode 获取代码
     * @param channelFlag 通道标志
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/01/11
     */
    public String acquireVendorRefreshToken(String sellerId, String acquireCode, String channelFlag) {
        log.info("amazonVendor 授权请求回调,渠道是:{} ,授权code是 {}", channelFlag, acquireCode);
        TenantSalesChannel tenantSalesChannel = getOne(new LambdaQueryWrapper<TenantSalesChannel>().eq(TenantSalesChannel::getThirdChannelFlag, channelFlag)
                                                                                                   .last("limit 1"));
        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            throw new AppRuntimeException("没有对应的渠道");
        }
        // 授权跳转 不重要
//        String redirectUrl = getAuthorizeRedirectUrl(tenantSalesChannel);

        //
        String amazonVendorToken = "";
        /**
         * POST /auth/o2/token HTTP/l.l
         * Host: api.amazon.com
         * Content-Type: application/x-www-form-urlencoded;charset=UTF-8
         * grant_type=authorization_code&code=SplxlOexamplebYS6WxSbIA&client_id=foodev&client_secret=Y76SDl2F
         *
         */


        String url = "https://api.amazon.com/auth/o2/token";
        String bodyStr = "grant_type=authorization_code&code=" + acquireCode + "&client_id=" + clientId + "&client_secret=" + clientSecret;

        HttpResponse httpResponse = HttpRequest.post(url)
                                               .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                                               .header(Header.ACCEPT_CHARSET, "UTF-8")
                                               .body(bodyStr)
                                               .timeout(20000)//超时，毫秒
                                               .execute();

        String body = httpResponse.body();
//        String body = "{\n" +
//            "        \"access_token\": \"Atza|FGGGGGGGGGGGGGGGGGGGGGGGlmbhfJxioYrD7wA9OEuYoJQo7PJp_UCYUM9SYwUt8zmXqRUVHNIvD2_WSFP7wAI2dtIX5AImeFAjAARz2ah8BVo7Ty2VeWnGcPA1ZeNY8NLLhnFiZilwzxRgWxwW_2Mgg7rpHBHTdKZ-RNabEJbbXMrhpv-uX6wt077qzR3QpLGFQ6dzXuu_KdAjF0wt0ZsIKL0AYwo-Z7PwqeqwewewewewedsWWWWWWWWWWWWWWWWWW52Nzj_NBUjKTm7RoTWZD2mTNrwCVDDDDD\",\n" +
//            "        \"refresh_token\": \"Atzr|ITTTTTTTTTTTTTTTTTTTTTTTTT54wLPXUlwifdiFBbXdYaoYRsm3_cSrsRZx6puVrI8-koQ0Yt4jKHdbJ2XRurRtXpFAkF_JSx7bbmp2UREadccTuP5_Oh13parJUp5a6k8xXOXO89zmn64ZeFZK6XyMPCm1emHp8Ma8b0WUzj44CSspcOJETdg0Q8pY3eJybpX_84AzyMEc-GZ0OZaTVvKomwUHzrXzCirxaGD3EgHT81z75T2UXpAduk1m0OUYYYYYYYYYYYYYYYYYYYYYYYYYYYYY0FYMlaKth24\",\n" +
//            "        \"token_type\": \"bearer\",\n" +
//            "        \"expires_in\": 3600\n" +
//            "}";

        //打出日志
        log.info("请求amazon vendor的返回值为:{}", httpResponse);
        log.info("请求amazon vendor的返回值body为:{}", body);


        //拿到refeshToken的值
        Map<String, Object> responseMap = JacksonUtils.jsonToMap(body);
        if (responseMap.containsKey("refresh_token")) {
            amazonVendorToken = (String) responseMap.get("refresh_token");
        } else {
            log.info("请求amazon vendor的返回值里面没有refresh_toke的值,授权失败");
            return null;
        }

        //找到渠道对应的参数，并更新
        if (ObjectUtil.isNotEmpty(tenantSalesChannel)) {
            String connectStr = tenantSalesChannel.getConnectStr();
            if (StrUtil.isBlank(connectStr)){
                connectStr="{}";
            }
            Map<String, Object> connectMap = JacksonUtils.jsonToMap(connectStr);
            connectMap.put("sellerId", sellerId);
            connectMap.put("refreshToken", amazonVendorToken);
            String newConnectStr = JacksonUtils.toJson(connectMap);
            // 修改授权正常状态
            tenantSalesChannel.setStatus(SalesChannelInterfaceStatusEnum.RUNING.getValue());
            tenantSalesChannel.setConnectStr(newConnectStr);
            tenantSalesChannel.setAuthorizationAt(LocalDate.now());
            saveOrUpdate(tenantSalesChannel);

            //如果是Amazon_VC 需要推送多渠道
            if (ObjectUtil.equals("Amazon_VC",tenantSalesChannel.getChannelType())){
                sendMqByTenantSalesChannel(tenantSalesChannel);
            }

            //处理子账号下的 授权都刷新一遍
//            updateChildRenAccountToken(tenantSalesChannel.getOrgId(), channelFlag, amazonVendorToken);
        } else {
            log.info("请求amazon vendor的渠道flag {} 有问题,没有此账号", channelFlag);
        }
        return null;
    }

    /**
     * 功能描述：获取 Amazon 刷新令牌
     *
     * @param sellerId    卖家 ID
     * @param acquireCode 获取代码
     * @param channelFlag 通道标志
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/01/11
     */
    public String acquireAmazonRefreshToken(String sellerId, String acquireCode, String channelFlag) {
        log.info("amazon 授权请求回调,渠道是:{} ,授权code是 {}", channelFlag, acquireCode);
        TenantSalesChannel tenantSalesChannel = getOne(new LambdaQueryWrapper<TenantSalesChannel>().eq(TenantSalesChannel::getThirdChannelFlag, channelFlag)
                                                                                                   .last("limit 1"));
        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            throw new AppRuntimeException("没有对应的渠道");
        }

        String redirectUrl = getAuthorizeRedirectUrl(tenantSalesChannel);

//
        String amazonToken = "";
        /**
         * POST /auth/o2/token HTTP/l.l
         * Host: api.amazon.com
         * Content-Type: application/x-www-form-urlencoded;charset=UTF-8
         * grant_type=authorization_code&code=SplxlOexamplebYS6WxSbIA&client_id=foodev&client_secret=Y76SDl2F
         *
         */


        String url = "https://api.amazon.com/auth/o2/token";
        String bodyStr = "grant_type=authorization_code&code=" + acquireCode + "&client_id=" + clientId + "&client_secret=" + clientSecret;

        HttpResponse httpResponse = HttpRequest.post(url)
                                               .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                                               .header(Header.ACCEPT_CHARSET, "UTF-8")
                                               .body(bodyStr)
                                               .timeout(20000)//超时，毫秒
                                               .execute();

        String body = httpResponse.body();


        //打出日志
        log.info("请求amazon的返回值为:{}", httpResponse);
        log.info("请求amazon的返回值body为:{}", body);


        //拿到refeshToken的值
        Map<String, Object> responseMap = JacksonUtils.jsonToMap(body);
        if (responseMap.containsKey("refresh_token")) {
            amazonToken = (String) responseMap.get("refresh_token");
        } else {
            log.info("请求amazon的返回值里面没有refresh_toke的值,授权失败");
            if (ObjectUtil.isNotNull(tenantSalesChannel) &&
                !tenantSalesChannel.getStatus().equals(SalesChannelInterfaceStatusEnum.RUNING.getValue()) &&
                !tenantSalesChannel.getStatus().equals(SalesChannelInterfaceStatusEnum.INACTIVE.getValue())
            ) {
                log.error("更新授权异常状态信息！");
                tenantSalesChannel.setStatus(SalesChannelInterfaceStatusEnum.EXCEPTION.getValue());
                save(tenantSalesChannel);
            }

        }

        //找到渠道对应的参数，并更新
        if (ObjectUtil.isNotEmpty(tenantSalesChannel)) {
            String status = tenantSalesChannel.getStatus();
            tenantSalesChannel.setAuthorizationAt(LocalDate.now());
            String connectStr = tenantSalesChannel.getConnectStr();
            Map<String, Object> connectMap = JacksonUtils.jsonToMap(connectStr);
            connectMap.put("refreshToken", amazonToken);
            connectMap.put("sellerId", sellerId);
            String newConnectStr = JacksonUtils.toJson(connectMap);
            tenantSalesChannel.setConnectStr(newConnectStr);
            if (!status.equals(SalesChannelInterfaceStatusEnum.RUNING.getValue()) && !status.equals(SalesChannelInterfaceStatusEnum.INACTIVE.getValue())) {
                tenantSalesChannel.setStatus(SalesChannelInterfaceStatusEnum.RUNING.getValue());
                tenantSalesChannel.setAuthorizationAt(LocalDate.now());
            }
            // 应该是update逻辑 稍后修改
            save(tenantSalesChannel);
            //处理子账号下的 授权都刷新一遍
//            updateChildRenAccountToken(accountEntity.getOrgId(), channelFlag, amazonToken);
        } else {
            log.info("请求amazon的渠道flag {} 有问题,没有此账号", channelFlag);
        }

        return redirectUrl;
    }
    public XxlConfKeyValue getXxlConfKey(TenantSalesChannel tenantSalesChannel) {
        String xxlConfAppKey = tenantSalesChannel.getXxlConfAppKey();
        String xxlConfAppSecret = tenantSalesChannel.getXxlConfAppSecret();
        HashMap<String,String> hashMap = new HashMap();
        XxlConfKeyValue xxlConfKeyValue = new XxlConfKeyValue();




        String key = XxlConfClient.get(xxlConfAppKey);
        String secrect = XxlConfClient.get(xxlConfAppSecret);
        if(StrUtil.isNotEmpty(key)&&StrUtil.isNotEmpty(secrect)){
            hashMap.put(XxlConfEnum.TikTokAppKey.name(),key);
            hashMap.put(XxlConfEnum.TikTokAppSecret.name(),secrect);
        }else {
            throw new RuntimeException("Failed to obtain the tiktok key. Check the xxlConf configuration");
        }

        xxlConfKeyValue.setXxlValueMap(hashMap);

        return xxlConfKeyValue;
    }
    /**
     * 功能描述：抖音授权
     *
     * @param channelFlag   通道标志
     * @param authorizeCode 授权代码
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/01/11
     */
    public String tiktokAuthorize(String channelFlag, String authorizeCode,SaleChannelCallBackVo saleChannelCallBackVo) {
        log.info("tiktok授权请求回调,渠道是:{} ,授权code是 {}", channelFlag, authorizeCode);
        TenantSalesChannel tenantSalesChannel = getOne(new LambdaQueryWrapper<TenantSalesChannel>().eq(TenantSalesChannel::getThirdChannelFlag, channelFlag)
                                                                                                   .last("limit 1"));

        XxlConfKeyValue xxlConfKey = getXxlConfKey(tenantSalesChannel);
        String tiktokAppKey = xxlConfKey.xxlValueMap.get(XxlConfEnum.TikTokAppKey.name());
        String tiktokAppSecret = xxlConfKey.xxlValueMap.get(XxlConfEnum.TikTokAppSecret.name());
        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            throw new AppRuntimeException("没有对应的渠道");
        }
        String redirectUrl = getAuthorizeRedirectUrl(tenantSalesChannel);

        String url = "https://auth.tiktok-shops.com/api/v2/token/get?";
        String param = "grant_type=authorized_code&app_key=" + tiktokAppKey + "&app_secret=" + tiktokAppSecret + "&auth_code=" + authorizeCode;

        HttpResponse httpResponse = HttpRequest.get(url + param)
                                               .header(Header.ACCEPT_CHARSET, "UTF-8")
                                               .body("")
                                               .timeout(20000)//超时，毫秒
                                               .execute();

        String body = httpResponse.body();

        //打出日志
        log.info("请求tiktok的返回值为:{}", httpResponse);
        log.info("请求tiktok的返回值body为:{}", body);

        String refreshToken = "";
        //拿到refreshToken的值
        Map<String, Object> responseMap = JacksonUtils.jsonToMap(body);
        Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("data");
        if (dataMap.containsKey("refresh_token")) {
            refreshToken = (String) dataMap.get("refresh_token");
        } else {
            log.info("channel#{}请求tiktok的返回值里面没有refresh_toke的值,授权失败", channelFlag);
            return redirectUrl;
        }

        String accessToken = "";
        if (dataMap.containsKey("access_token")) {
            accessToken = (String) dataMap.get("access_token");
        }

        String openId = "";
        if (dataMap.containsKey("open_id")) {
            openId = (String) dataMap.get("open_id");
        }


        //找到渠道对应的参数，并更新
        if (ObjectUtil.isNotEmpty(tenantSalesChannel)) {
            String connectStr = tenantSalesChannel.getConnectStr();
            Map<String, Object> connectMap = null;
            if (StrUtil.isEmpty(connectStr)) {
                connectMap = new HashMap<>() {};

            } else {
                connectMap = JacksonUtils.jsonToMap(connectStr);
            }
            connectMap.put("refreshToken", refreshToken);
            connectMap.put("accessToken", accessToken);
            connectMap.put("openId", openId);
            String newConnectStr = JacksonUtils.toJson(connectMap);
            tenantSalesChannel.setConnectStr(newConnectStr);
            // tag lty 授权
            if (!SalesChannelInterfaceStatusEnum.RUNING.getValue().equals(tenantSalesChannel.getStatus())
                &&
                !SalesChannelInterfaceStatusEnum.INACTIVE.getValue().equals(tenantSalesChannel.getStatus())) {
                tenantSalesChannel.setStatus(SalesChannelInterfaceStatusEnum.RUNING.getValue());
                tenantSalesChannel.setAuthorizationAt(LocalDate.now());
            }

            baseMapper.insertOrUpdate(tenantSalesChannel);

            if (!connectMap.containsKey("shopId") || (connectMap.containsKey("shopId") && connectMap.get("shopId") == null)) {
                auzTiktokUtil.getTikTokShopInfo(tiktokAppKey, tiktokAppSecret, accessToken, tenantSalesChannel);
            }
            if(null != tenantSalesChannel.getState() && tenantSalesChannel.getState().equals(1)) {
                // 回传到多渠道
                saleChannelCallBackVo.setChannelFlag(tenantSalesChannel.getChannelName())
                                     .setType(tenantSalesChannel.getChannelType()).setSource_system("distribution")
                                     .setConnectStr(tenantSalesChannel.getConnectStr());
            }
        } else {
            log.info("请求tiktok的渠道flag:{} 有问题,没有此账号", channelFlag);
        }
        log.info("店铺注册回调信息:{}", redirectUrl);
        return redirectUrl;
    }

    /**
     * 获取商店 ID
     *
     * @param shopId 店铺编号
     * @return {@link TenantSalesChannel }
     * <AUTHOR>
     */
    public TenantSalesChannel getShopId(String shopId) {
        if (StringUtil.isEmpty(shopId)) {
            return null;
        }
        TenantSalesChannel tenantSalesChannel = new TenantSalesChannel();
        tenantSalesChannel.setChannelType(ChannelTypeEnum.TikTok.name());

        List<TenantSalesChannel> accounts = baseMapper.selectAccountListRefactor(tenantSalesChannel);
        for (TenantSalesChannel accountItem : accounts) {
            JSONObject accountItemJson;
            try{
                accountItemJson = JSONObject.parseObject(accountItem.getConnectStr());
                if (ObjectUtil.isNotEmpty(accountItemJson)) {
                    if (shopId.equals(String.valueOf(accountItem.getId()))) {
                        return accountItem;
                    }
                }
            }catch (Exception e){
                log.error("获取商店ID异常",e);

            }
        }
        return null;
    }

    /**
     * 功能描述：通过真实店铺ID获取店铺ID
     *
     * @param shopId 店铺编号
     * @return {@link TenantSalesChannel }
     * <AUTHOR>
     * @date 2024/01/30
     */
    public TenantSalesChannel getShopMsgByRealShopId(String shopId) {
        if (StringUtil.isEmpty(shopId)) {
            return null;
        }
        TenantSalesChannel tenantSalesChannel = new TenantSalesChannel();
        tenantSalesChannel.setChannelType(ChannelTypeEnum.TikTok.name());

        List<TenantSalesChannel> accounts = baseMapper.selectAccountListRefactor(tenantSalesChannel);
        for (TenantSalesChannel accountItem : accounts) {
            log.info("accountItem:{}",accountItem.getId());
            JSONObject accountItemJson = JSONObject.parseObject(accountItem.getConnectStr());
            if (null != accountItemJson) {
                if (shopId.equals(accountItemJson.getString("shopId"))) {
                    return accountItem;
                }
            }
        }
        return null;
    }

    /**
     * 根据channelName获取数据
     *
     * @param channelName
     * @return
     */
    public TenantSalesChannel getTenantSalesChannelByChannelName(String channelName,String channelType){
        LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantSalesChannel::getChannelName, channelName).eq(TenantSalesChannel::getChannelType,channelType).eq(TenantSalesChannel::getDelFlag,0).eq(TenantSalesChannel::getState,1).last("limit 1");
        return getOne(wrapper);
    }

    public TenantSalesChannel getTenantSalesChannelByChannelName(String channelName){
        LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantSalesChannel::getChannelName, channelName).eq(TenantSalesChannel::getDelFlag,0).eq(TenantSalesChannel::getState,1).last("limit 1");
        return getOne(wrapper);
    }

    public R getTenantSalesChannelHierarchy(String tenantType) {
        //查询出符合状态的TenantSalesChannel
        LambdaQueryWrapper<TenantSalesChannel> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ObjectUtil.isNotEmpty(tenantType),TenantSalesChannel::getChannelType,tenantType);
        lambdaQueryWrapper.orderByDesc(TenantSalesChannel::getCreateTime);
        List<TenantSalesChannel> tenantSalesChannels = baseMapper.selectList(lambdaQueryWrapper);
        // 使用Stream API进行转换
        Map<String, List<TenantSalesChannel>> groupedByChannelType = tenantSalesChannels.stream()
                                                                                        .collect(Collectors.groupingBy(TenantSalesChannel::getChannelType));

        // 将Map转换为List<Map<channelType, List<TenantSalesChannel>>>
        List<Map<String, List<TenantSalesChannel>>> transformedList = groupedByChannelType.entrySet().stream()
                                                                                          .map(entry -> {
                                                                                              Map<String, List<TenantSalesChannel>> map = new HashMap<>();
                                                                                              map.put(entry.getKey(), entry.getValue());
                                                                                              return map;
                                                                                          })
                                                                                          .collect(Collectors.toList());
    return R.ok(transformedList);
    }

    public String acquireAmazonVcRefreshToken(String sellerId, String acquireCode, String channelFlag) {
        log.info("amazon-vc授权请求回调,渠道是:{} ,授权code是 {}", channelFlag, acquireCode);
        TenantSalesChannel tenantSalesChannel = getOne(new LambdaQueryWrapper<TenantSalesChannel>()
            .eq(TenantSalesChannel::getThirdChannelFlag, channelFlag)
            .eq(TenantSalesChannel::getChannelType, ChannelTypeEnum.Amazon_VC.getValue())
            .last("limit 1"));
        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            throw new AppRuntimeException("没有对应的渠道");
        }

//        String redirectUrl = getAuthorizeRedirectUrl(tenantSalesChannel);

//
        String amazonToken = "";
        /**
         * POST /auth/o2/token HTTP/l.l
         * Host: api.amazon.com
         * Content-Type: application/x-www-form-urlencoded;charset=UTF-8
         * grant_type=authorization_code&code=SplxlOexamplebYS6WxSbIA&client_id=foodev&client_secret=Y76SDl2F
         *
         */


        String url = "https://api.amazon.com/auth/o2/token";
        String bodyStr = "grant_type=authorization_code&code=" + acquireCode + "&client_id=" + clientId + "&client_secret=" + clientSecret;

        HttpResponse httpResponse = HttpRequest.post(url)
                                               .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                                               .header(Header.ACCEPT_CHARSET, "UTF-8")
                                               .body(bodyStr)
                                               .timeout(20000)//超时，毫秒
                                               .execute();

        String body = httpResponse.body();


        //打出日志
        log.info("请求amazon-vc的返回值为:{}", httpResponse);
        log.info("请求amazon-vc的返回值body为:{}", body);


        //拿到refeshToken的值
        Map<String, Object> responseMap = JacksonUtils.jsonToMap(body);
        if (responseMap.containsKey("refresh_token")) {
            amazonToken = (String) responseMap.get("refresh_token");
        } else {
            log.info("请求amazon-vc的返回值里面没有refresh_toke的值,授权失败");

            return redirectUrl;
        }

        //找到渠道对应的参数，并更新
        if (ObjectUtil.isNotEmpty(tenantSalesChannel)) {
            tenantSalesChannel.setAuthorizationAt(LocalDate.now());
            String connectStr = tenantSalesChannel.getConnectStr();
            Map<String, Object> connectMap = JacksonUtils.jsonToMap(connectStr);
            connectMap.put("refreshToken", amazonToken);
            connectMap.put("sellerId", sellerId);
            String newConnectStr = JacksonUtils.toJson(connectMap);
            tenantSalesChannel.setConnectStr(newConnectStr);
            tenantSalesChannel.setStatus(SalesChannelInterfaceStatusEnum.RUNING.getValue());
            tenantSalesChannel.setAuthorizationAt(LocalDate.now());
            // 应该是update逻辑 稍后修改
            baseMapper.insertOrUpdate(tenantSalesChannel);
            //处理子账号下的 授权都刷新一遍
//            updateChildRenAccountToken(accountEntity.getOrgId(), channelFlag, amazonToken);
        } else {
            log.info("请求amazon-vc的渠道flag {} 有问题,没有此账号", channelFlag);
        }
        return null;
    }

    public String acquireAmazonScRefreshToken(String sellerId, String acquireCode, String channelFlag) {
        log.info("amazon-sc 授权请求回调,渠道是:{} ,授权code是 {}", channelFlag, acquireCode);
        TenantSalesChannel tenantSalesChannel = getOne(new LambdaQueryWrapper<TenantSalesChannel>()
            .eq(TenantSalesChannel::getThirdChannelFlag, channelFlag)
            .eq(TenantSalesChannel::getChannelType, ChannelTypeEnum.Amazon_SC.getValue())
            .last("limit 1"));
        if (ObjectUtil.isEmpty(tenantSalesChannel)) {
            throw new AppRuntimeException("没有对应的渠道");
        }

//        String redirectUrl = getAuthorizeRedirectUrl(tenantSalesChannel);

//
        String amazonToken = "";
        /**
         * POST /auth/o2/token HTTP/l.l
         * Host: api.amazon.com
         * Content-Type: application/x-www-form-urlencoded;charset=UTF-8
         * grant_type=authorization_code&code=SplxlOexamplebYS6WxSbIA&client_id=foodev&client_secret=Y76SDl2F
         *
         */


        String url = "https://api.amazon.com/auth/o2/token";
        String bodyStr = "grant_type=authorization_code&code=" + acquireCode + "&client_id=" + clientId + "&client_secret=" + clientSecret;

        HttpResponse httpResponse = HttpRequest.post(url)
                                               .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                                               .header(Header.ACCEPT_CHARSET, "UTF-8")
                                               .body(bodyStr)
                                               .timeout(20000)//超时，毫秒
                                               .execute();

        String body = httpResponse.body();


        //打出日志
        log.info("请求amazon-sc的返回值为:{}", httpResponse);
        log.info("请求amazon-sc的返回值body为:{}", body);


        //拿到refeshToken的值
        Map<String, Object> responseMap = JacksonUtils.jsonToMap(body);
        if (responseMap.containsKey("refresh_token")) {
            amazonToken = (String) responseMap.get("refresh_token");
        } else {
            log.info("请求amazon-sc的返回值里面没有refresh_toke的值,授权失败;店铺:{}", channelFlag);
            return null;
        }

        //找到渠道对应的参数，并更新
        if (ObjectUtil.isNotEmpty(tenantSalesChannel)) {

            tenantSalesChannel.setAuthorizationAt(LocalDate.now());
            String connectStr = tenantSalesChannel.getConnectStr();
            Map<String, Object> connectMap = JacksonUtils.jsonToMap(connectStr);
            connectMap.put("refreshToken", amazonToken);
            // sellerId 是商家id
            connectMap.put("sellerId", sellerId);
            String newConnectStr = JacksonUtils.toJson(connectMap);
            tenantSalesChannel.setConnectStr(newConnectStr);
            tenantSalesChannel.setStatus(SalesChannelInterfaceStatusEnum.RUNING.getValue());
            tenantSalesChannel.setAuthorizationAt(LocalDate.now());
            // 应该是update逻辑 稍后修改
            baseMapper.insertOrUpdate(tenantSalesChannel);
            //处理子账号下的 授权都刷新一遍
//            updateChildRenAccountToken(accountEntity.getOrgId(), channelFlag, amazonToken);
        } else {
            log.info("请求amazon-sc的渠道flag {} 有问题,没有此账号", channelFlag);
        }
        return null;
    }

    /**
     * 店铺渠道发送MQ
     * @param channel
     */
    public void sendMqByTenantSalesChannel(TenantSalesChannel channel){
        try {
            if (ObjectUtil.isNull(channel)){
                return;
            }
            String cacheMapValue = RedisUtils.getCacheMapValue("sys_config", "Disable_Channel_Send_Mq");
             List<String> productMappingChannelList= JSONUtil.toList(cacheMapValue, String.class);
            if (!productMappingChannelList.contains(channel.getChannelType())){
                return;
            }
            log.info("[分销店铺新增/变更MQ通知多渠道]，店铺信息：{}",channel);
            HashMap<String, String> map = new HashMap<>();
            map.put("channelFlag",channel.getChannelName());
            map.put("connectStr",channel.getConnectStr());
            map.put("source_system","distribution");
            if (ObjectUtil.equals("Amazon_VC",channel.getChannelType())){
                map.put("type","amazonvendords");
            }else {
                map.put("type",channel.getChannelType());
            }
            map.put("temuChannelType", String.valueOf(channel.getTemuChannelType()));
            if (channel.getState()==1){
                map.put("active", "Y");
            }
            if (channel.getState()==0){
                map.put("active", "N");
            }
            String str = JSON.toJSONString(map);
            String messageId = IdUtil.fastSimpleUUID();
            Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
            rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE, RabbitMqConstant.TIKTOK_SHOP_ROUTING_KEY, message);
            log.info("[分销店铺新增/变更MQ通知多渠道]成功，发送参数：{}",JSON.toJSON(map));
        }catch (Exception e){
            log.error("[分销店铺新增/变更MQ通知多渠道]失败，店铺信息：{},失败原因：{}",channel,e.toString());
        }

    }

    public TenantSalesChannel getByChannelId(Long channelId) {
        LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantSalesChannel::getId,channelId);

        return TenantHelper.ignore(()->baseMapper.selectOne(wrapper));
    }

    public boolean isChannelFlag(TenantSalesChannel tenantSalesChannel) {
        List<String> channelNames = Arrays.asList("FineJoys Home", "MyDepot Outlet", "Mydepot3-US","Tik Tok-MyDepot4店-US");
        boolean channelNameFlag = channelNames.contains(tenantSalesChannel.getChannelName());

        boolean channelFlag = org.apache.commons.lang3.StringUtils.isNotEmpty(tenantSalesChannel.getConnectStr())
            && null != tenantSalesChannel.getState()
            && tenantSalesChannel.getState().equals(1)
            || channelNameFlag;
        return channelFlag;
    }
}
