package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.activity.entity.domain.ProductActivityItem;
import com.zsmall.activity.entity.domain.ProductActivityPriceItem;
import com.zsmall.activity.entity.domain.ProductActivityStockItem;
import com.zsmall.activity.entity.iservice.IProductActivityItemService;
import com.zsmall.activity.entity.iservice.IProductActivityPriceItemService;
import com.zsmall.activity.entity.iservice.IProductActivityStockItemService;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.order.OrderType;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.ReceiptReviewStateEnum;
import com.zsmall.common.enums.transaction.TransactionMethodEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.vo.OrderRefundVo;
import com.zsmall.order.entity.iservice.IOrderItemPriceService;
import com.zsmall.order.entity.iservice.IOrderItemProductSkuService;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrderRefundItemService;
import com.zsmall.order.entity.mapper.*;
import com.zsmall.system.biz.service.PaymentHandleService;
import com.zsmall.system.biz.service.PaymentMethodService;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.bo.transaction.TransactionOrderBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionOrderRecordBo;
import com.zsmall.system.entity.domain.bo.transaction.TransactionReceiptBo;
import com.zsmall.system.entity.domain.vo.payment.PaymentMethodListVo;
import com.zsmall.system.entity.domain.vo.payment.RechargeRecordVo;
import com.zsmall.system.entity.domain.vo.payment.TransactionOrderRecordVo;
import com.zsmall.system.entity.domain.vo.transaction.*;
import com.zsmall.system.entity.iservice.ITenantPayoneerService;
import com.zsmall.system.entity.iservice.ITransactionsProductActivityItemService;
import com.zsmall.system.entity.mapper.TenantWalletMapper;
import com.zsmall.system.entity.mapper.TransactionReceiptAttachmentMapper;
import com.zsmall.system.entity.mapper.TransactionReceiptMapper;
import com.zsmall.system.entity.mapper.TransactionRecordMapper;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支付相关-实现类
 *
 * <AUTHOR> @date 2023年6月13日
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentMethodServiceImpl implements PaymentMethodService {

    private static final DateTimeFormatter dateTimeFormatter =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    private final OrdersMapper ordersMapper;
    private final OrderItemMapper orderItemMapper;
    private final OrderRefundItemMapper orderRefundItemMapper;
    private final OrderItemProductSkuMapper orderItemProductSkuMapper;
    private final OrderItemPriceMapper orderItemPriceMapper;
    private final TransactionRecordMapper transactionRecordMapper;
    private final TransactionReceiptMapper transactionReceiptMapper;
    private final TransactionReceiptAttachmentMapper transactionReceiptAttachmentMapper;
    private final TenantWalletMapper tenantWalletMapper;
    private final OrderRefundMapper orderRefundMapper;
    private final ITenantPayoneerService iTenantPayoneerService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IOrderRefundItemService iOrderRefundItemService;
    private final IOrderItemService iOrderItemService;
    private final IProductActivityPriceItemService iProductActivityPriceItemService;
    private final IProductActivityItemService iProductActivityItemService;
    private final IProductActivityStockItemService iProductActivityStockItemService;
    private final ITransactionsProductActivityItemService iTransactionsProductActivityItemService;
    private final ISysTenantService sysTenantService;
    @Autowired
    private List<PaymentHandleService> paymentHandleServices;

    /**
     * 获取分销商钱包首页信息
     *
     * @param obj
     * @return
     */
    @Override
    public R<PaymentMethodListVo> getPaymentMethodHome(Object obj) {
        log.info("调用【获取支付方法首页信息（分销商）】接口 = {}", JSONUtil.toJsonStr(obj));
        String tenantId = LoginHelper.getTenantId();

        //分销商可用余额
        BigDecimal balance = new BigDecimal("0.00");
        //冻结金额
        Double frozenBalance = 0.00;
        //冻结详情
        List<String> frozenDetails = new ArrayList<>();
        //待支付订单
        List<PaymentMethodListVo.PendingOrders> pendingOrders = new ArrayList<>();
        //充值记录
        List<PaymentMethodListVo.RechargeRecords> rechargeRecords = new ArrayList<>();
        //交易记录
        List<PaymentMethodListVo.OrderTransactions> orderTransactions = new ArrayList<>();

        if (StrUtil.isNotBlank(tenantId)) {
            LambdaQueryWrapper<TenantWallet> twLqw = new LambdaQueryWrapper<>();
            twLqw.eq(TenantWallet::getTenantId, tenantId);
            if(null != obj){
                if(null != ((Map<?, ?>) obj).get("currency")){
                    twLqw.eq(TenantWallet::getCurrency, ((Map<?, ?>) obj).get("currency"));
                }else {
                    twLqw.eq(TenantWallet::getCurrency, "USD");
                }
            }else {
                twLqw.eq(TenantWallet::getCurrency, "USD");
            }
            TenantWallet tenantWallet = tenantWalletMapper.selectOne(twLqw);
            if (ObjectUtil.isNotNull(tenantWallet)) {
                balance = tenantWallet.getWalletBalance();
            }

            //查询活动订金
            LambdaQueryWrapper<ProductActivityItem> paiLqw = new LambdaQueryWrapper<>();
            paiLqw.eq(ProductActivityItem::getTenantId, tenantId);
            List<ProductActivityItem> itemEntityList = iProductActivityItemService.list(paiLqw);
            //计算冻结金额并记录冻结详情
            if (CollUtil.isNotEmpty(itemEntityList)) {
                for (ProductActivityItem itemEntity : itemEntityList) {
                    //获取活动价格
                    ProductActivityPriceItem priceEntity =
                        iProductActivityPriceItemService.getByActivityCodeNoTenant(itemEntity.getActivityCode());
                    //获取活动商品库存信息列表
                    List<ProductActivityStockItem> inventoryEntityList =
                        iProductActivityStockItemService.queryByActivityItemId(itemEntity.getId());
                    int num = 0;
                    if (CollUtil.isNotEmpty(inventoryEntityList)) {
                        //计算活动商品剩余库存
                        for (ProductActivityStockItem productActivityItemInventory : inventoryEntityList) {
                            num += productActivityItemInventory.getQuantitySurplus();
                        }
                    }
                    if (ObjectUtil.isNotNull(priceEntity)) {
                        //计算剩余活动商品总订金
                        BigDecimal depositTotalPrice = NumberUtil.mul(priceEntity.getPlatformDepositUnitPrice(), num);
                        if (ObjectUtil.isNull(depositTotalPrice) || depositTotalPrice.compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                        frozenBalance = NumberUtil.add(depositTotalPrice, frozenBalance).doubleValue();
                        //记录冻结详情
                        String frozenDetail =
                            itemEntity.getActivityType() + "：活动编码" + itemEntity.getActivityCode() + " 金额$" + NumberUtil.toStr(
                                depositTotalPrice);
                        frozenDetails.add(frozenDetail);
                    }
                }
            }

            //查询最新的两条未支付订单
            Page<Orders> page = new Page<>();
            page.setCurrent(0).setSize(2);
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<>();
            lqw.eq(Orders::getTenantId, tenantId).eq(Orders::getOrderState, OrderStateType.UnPaid)
                .orderByDesc(Orders::getCreateTime);
            Page<Orders> ordersPage = ordersMapper.selectPage(page, lqw);
            List<Orders> orders = ordersPage.getRecords();
            if (CollUtil.isNotEmpty(orders)) {
                for (Orders order : orders) {
                    PaymentMethodListVo.PendingOrders pendingOrder = new PaymentMethodListVo.PendingOrders();
                    pendingOrder.setOrderNo(order.getOrderNo());
                    String createDateTime = DateUtil.format(order.getCreateTime(), dateTimeFormatter);
                    pendingOrder.setCreateTime(createDateTime);
                    if(null == order.getPlatformActualTotalAmount()){
                        pendingOrder.setPendingAmount("0");
                    }
                    if(null != order.getPlatformActualTotalAmount()){
                        pendingOrder.setPendingAmount(NumberUtil.toStr(order.getPlatformActualTotalAmount()));
                    }
//                    pendingOrder.setTotalNumber(order.getOriginalActualTotalAmount().toPlainString());
                    pendingOrders.add(pendingOrder);
                }
            }

            //查询最新的7条充值记录
            Page<TransactionReceipt> page1 = new Page<>();
            page1.setCurrent(0).setSize(7);
            LambdaQueryWrapper<TransactionReceipt> lqw1 = new LambdaQueryWrapper<>();
            lqw1.eq(TransactionReceipt::getTenantId, tenantId).in(TransactionReceipt::getTransactionType, TransactionTypeEnum.Recharge)
                .orderByDesc(TransactionReceipt::getCreateTime);
            Page<TransactionReceipt> transactionReceiptPage = transactionReceiptMapper.selectPage(page1, lqw1);

            List<TransactionReceipt> transactionReceipts = transactionReceiptPage.getRecords();
            if (CollUtil.isNotEmpty(transactionReceipts)) {
                for (TransactionReceipt receipt : transactionReceipts) {
                    PaymentMethodListVo.RechargeRecords rechargeRecord = new PaymentMethodListVo.RechargeRecords();
                    rechargeRecord.setCreateTime(DateUtil.format(receipt.getCreateTime(), dateTimeFormatter));
                    String transactionTime = LocalDateTimeUtil.format(receipt.getTransactionTime(), dateTimeFormatter);
                    rechargeRecord.setRechargeTime(transactionTime);
                    ReceiptReviewStateEnum reviewState = receipt.getReviewState();
                    rechargeRecord.setRechargeStatus(reviewState.getValue());
                    rechargeRecord.setArrivalAmount("0.00");
                    if (reviewState == ReceiptReviewStateEnum.Accepted) {
                        String receiptTime = LocalDateTimeUtil.format(receipt.getReceiptTime(), dateTimeFormatter);
                        rechargeRecord.setArrivalTime(receiptTime);
                        rechargeRecord.setArrivalAmount(receipt.getTransactionAmount().toPlainString());
                    }

                    rechargeRecords.add(rechargeRecord);
                }
            }

            //查询最新的7条订单交易记录
            Page<TransactionRecord> page2 = new Page<>();
            page2.setCurrent(0).setSize(7);
            List<TransactionSubTypeEnum> subTypeEnums = TransactionSubTypeEnum.getPaymentSubType();
            LambdaQueryWrapper<TransactionRecord> lqw2 = new LambdaQueryWrapper<>();
            lqw2.eq(TransactionRecord::getTenantId, tenantId)
                .in(TransactionRecord::getTransactionSubType, subTypeEnums)
                .orderByDesc(TransactionRecord::getCreateTime);
            Page<TransactionRecord> transactionRecordPage = transactionRecordMapper.selectPage(page2, lqw2);

            List<TransactionRecord> recordPage1Records = transactionRecordPage.getRecords();
            if (CollUtil.isNotEmpty(recordPage1Records)) {
                for (TransactionRecord transactions : recordPage1Records) {
                    PaymentMethodListVo.OrderTransactions orderTransaction = new PaymentMethodListVo.OrderTransactions();
                    orderTransaction.setTransactionId(transactions.getTransactionNo());
                    String updateDateTime = DateUtil.format(transactions.getUpdateTime(), dateTimeFormatter);
                    orderTransaction.setTransactionTime(updateDateTime);
                    orderTransaction.setAmount(NumberUtil.toStr(transactions.getTransactionAmount()));
                    if (ObjectUtil.isNotNull(transactions.getTransactionSubType())) {
                        orderTransaction.setType(transactions.getTransactionSubType().name());
                    } else {
                        orderTransaction.setType(transactions.getTransactionType().name());
                    }
                    orderTransactions.add(orderTransaction);
                }
            }
        }

        PaymentMethodListVo respBody = new PaymentMethodListVo();
        respBody.setBalance(balance.toString());
        respBody.setFrozenBalance(DecimalUtil.doubleToString(frozenBalance));
        respBody.setCashAmount(balance.toString());
        respBody.setCashBackAmount("0.00");
        respBody.setFrozenDetails(frozenDetails);
        respBody.setPendingOrders(pendingOrders);
        respBody.setRechargeRecords(rechargeRecords);
        respBody.setOrderTransactions(orderTransactions);

        return R.ok(respBody);
    }

    @Override
    public R<RechargeRecordVo> getWalletDetailsPage(RechargeRecordVo.RechargeRecordDetails bo, PageQuery pageQuery) {
        log.info("进入【翻页查询充值记录列表】方法，{}", JSONUtil.toJsonStr(bo));
        RechargeRecordVo vo = new RechargeRecordVo();

        // 余额
        String tenantId = LoginHelper.getTenantId();
        LambdaQueryWrapper<TenantWallet> twLqw = new LambdaQueryWrapper<>();
        twLqw.eq(TenantWallet::getTenantId, tenantId);
        twLqw.eq(TenantWallet::getCurrency, StringUtils.isNotBlank(bo.getCurrency()) ? bo.getCurrency() : "USD");
        TenantWallet tenantWallet = tenantWalletMapper.selectOne(twLqw);
        BigDecimal balance = new BigDecimal("0.00");
        if (ObjectUtil.isNotNull(tenantWallet)) {
            balance = tenantWallet.getWalletBalance();
            vo.setCurrency(tenantWallet.getCurrency());
        }
        vo.setBalance(balance);


        // 充值记录
        LambdaQueryWrapper<TransactionReceipt> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionReceipt::getTransactionType, TransactionTypeEnum.Recharge)
            .eq(TransactionReceipt::getDelFlag, "0");

        String transactionMethod = bo.getTransactionMethod();
        if (StrUtil.isNotBlank(transactionMethod)) {
            lqw.eq(TransactionReceipt::getTransactionMethod, TransactionMethodEnum.valueOf(transactionMethod));
        }

        String transactionState = bo.getTransactionState();
        if (StrUtil.isNotBlank(transactionState)) {
            lqw.eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.valueOf(transactionState));
        }

        String transactionTime = bo.getTransactionTime();
        if (StrUtil.isNotBlank(transactionTime)) {
            Date date = DateUtil.parseDate(transactionTime);
            lqw.ge(TransactionReceipt::getTransactionTime, DateUtil.beginOfDay(date))
                .le(TransactionReceipt::getTransactionTime, DateUtil.endOfDay(date));
        }

        String createTime1 = bo.getCreateTime();
        if (StrUtil.isNotBlank(createTime1)) {
            Date date = DateUtil.parseDate(createTime1);
            lqw.ge(TransactionReceipt::getCreateTime, DateUtil.beginOfDay(date))
                .le(TransactionReceipt::getCreateTime, DateUtil.endOfDay(date));
        }

        String receiptTime = bo.getReceiptTime();
        if (StrUtil.isNotBlank(receiptTime)) {
            Date date = DateUtil.parseDate(receiptTime);
            lqw.ge(TransactionReceipt::getReceiptTime, DateUtil.beginOfDay(date))
                .le(TransactionReceipt::getReceiptTime, DateUtil.endOfDay(date))
                .eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.Accepted);
        }
        String currency = bo.getCurrency();
        if (StrUtil.isNotBlank(currency)) {
            lqw.eq(TransactionReceipt::getCurrency, currency);
        }
        lqw.orderByDesc(TransactionReceipt::getCreateTime);
        Page<TransactionReceipt> transactionRecordPage = transactionReceiptMapper.selectPage(pageQuery.build(), lqw);
        List<RechargeRecordVo.RechargeRecordDetails> rows = new ArrayList<>();

        List<TransactionReceipt> records = transactionRecordPage.getRecords();
        for (TransactionReceipt record : records) {
            RechargeRecordVo.RechargeRecordDetails row = new RechargeRecordVo.RechargeRecordDetails();
            Date createTime = record.getCreateTime();
            row.setCreateTime(DateUtil.format(createTime, dateTimeFormatter));
            row.setTransactionTime(LocalDateTimeUtil.format(record.getTransactionTime(), dateTimeFormatter));
            row.setCurrency(record.getCurrency());
            row.setCurrencySymbol(record.getCurrencySymbol());
            // 充值金额
            BigDecimal receiptAmount = record.getTransactionAmount();
            receiptAmount = ObjectUtil.isNull(receiptAmount) ? new BigDecimal("0.00") : receiptAmount;
            // 手续费
            BigDecimal transactionFee = record.getTransactionFee();
            transactionFee = ObjectUtil.isNull(transactionFee) ? new BigDecimal("0.00") : transactionFee;
            // 交易金额
            BigDecimal transactionAmount = receiptAmount.add(transactionFee);
            row.setRechargeAmount(receiptAmount);
            row.setTransactionFee(transactionFee);
            row.setTransactionAmount(transactionAmount);
            row.setTransactionNo(record.getTransactionReceiptNo());
            TransactionMethodEnum transactionMethod1 = record.getTransactionMethod();
            row.setTransactionMethod(transactionMethod1.name());
            row.setRemark(record.getNote());
            ReceiptReviewStateEnum reviewState = record.getReviewState();
            row.setTransactionState(reviewState.getValue());
            row.setAccountName(record.getAccountName());
            // 线上p卡取账号名称
            if (transactionMethod1 == TransactionMethodEnum.OnlinePayoneer) {
                TenantPayoneer tenantPayoneer = iTenantPayoneerService.selectByAccountIdAndTenantId(record.getTenantId(), record.getAccountId());
                if (ObjectUtil.isNotNull(tenantPayoneer)) {
                    row.setAccountName(tenantPayoneer.getAccountName());
                }
            }

            row.setAccountId(record.getAccountId());

            if (reviewState == ReceiptReviewStateEnum.Pending) {
                DateTime offsetDay = DateUtil.offsetDay(createTime, 4);
                String forecastReceiptTime = DateUtil.format(createTime, dateFormatter) + "-" + DateUtil.format(offsetDay, dateFormatter);
                row.setForecastReceiptTime(forecastReceiptTime);
            } else if (reviewState == ReceiptReviewStateEnum.Rejected) {
                row.setFailureReason(record.getNoteManager());
            } else if (reviewState == ReceiptReviewStateEnum.Accepted) {
                row.setReceiptAmount(receiptAmount);
                row.setReceiptTime(LocalDateTimeUtil.format(record.getReceiptTime(), dateTimeFormatter));
            }

            // 附件
            LambdaQueryWrapper<TransactionReceiptAttachment> attachmentLqw = new LambdaQueryWrapper<>();
            attachmentLqw.eq(TransactionReceiptAttachment::getTransactionReceiptNo, record.getTransactionReceiptNo())
                .eq(TransactionReceiptAttachment::getDelFlag, "0");
            List<TransactionReceiptAttachment> attachments = transactionReceiptAttachmentMapper.selectList(attachmentLqw);
            if (CollUtil.isNotEmpty(attachments)) {
                row.setAttachmentShowUrl(attachments.get(0).getAttachmentShowUrl());
                row.setAttachmentFileType(attachments.get(0).getAttachmentType());
                row.setAttachmentName(attachments.get(0).getAttachmentName());
            }

            rows.add(row);
        }

        TableDataInfo<RechargeRecordVo.RechargeRecordDetails> rechargeRecordPage = new TableDataInfo<>();

        rechargeRecordPage.setRows(rows);
        rechargeRecordPage.setTotal(transactionRecordPage.getTotal());
        vo.setRechargeRecordPage(rechargeRecordPage);

        return R.ok(vo);
    }

    /**
     * 查询交易回执单列表
     */
    @Override
    public List<TransactionReceiptVo> queryList(RechargeRecordVo.RechargeRecordDetails bo) {
//        LambdaQueryWrapper<TransactionReceipt> lqw = transactionReceiptBuildQueryWrapper(bo);
        LambdaQueryWrapper<TransactionReceipt> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionReceipt::getTransactionType, TransactionTypeEnum.Recharge)
            .eq(TransactionReceipt::getDelFlag, "0");

        String transactionMethod = bo.getTransactionMethod();
        if (StrUtil.isNotBlank(transactionMethod)) {
            lqw.eq(TransactionReceipt::getTransactionMethod, TransactionMethodEnum.valueOf(transactionMethod));
        }

        String transactionState = bo.getTransactionState();
        if (StrUtil.isNotBlank(transactionState)) {
            lqw.eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.valueOf(transactionState));
        }

        String createTime1 = bo.getCreateTime();
        if (StrUtil.isNotBlank(createTime1)) {
            Date date = DateUtil.parseDate(createTime1);
            lqw.ge(TransactionReceipt::getCreateTime, DateUtil.beginOfDay(date))
                .le(TransactionReceipt::getCreateTime, DateUtil.endOfDay(date));
        }

        String transactionTime = bo.getTransactionTime();
        if (StrUtil.isNotBlank(transactionTime)) {
            Date date = DateUtil.parseDate(transactionTime);
            lqw.ge(TransactionReceipt::getTransactionTime, DateUtil.beginOfDay(date))
                .le(TransactionReceipt::getTransactionTime, DateUtil.endOfDay(date));
        }

        String receiptTime = bo.getReceiptTime();
        if (StrUtil.isNotBlank(receiptTime)) {
            Date date = DateUtil.parseDate(receiptTime);
            lqw.ge(TransactionReceipt::getReceiptTime, DateUtil.beginOfDay(date))
                .le(TransactionReceipt::getReceiptTime, DateUtil.endOfDay(date))
                .eq(TransactionReceipt::getReviewState, ReceiptReviewStateEnum.Accepted);
        }

        String currency = bo.getCurrency();
        if (StrUtil.isNotBlank(currency)) {
            lqw.eq(TransactionReceipt::getCurrency, currency);
        }
        String tenantId = bo.getTenantId();
        if (StrUtil.isNotBlank(tenantId)) {
            lqw.eq(TransactionReceipt::getTenantId, tenantId);
        }
        lqw.orderByDesc(TransactionReceipt::getCreateTime);
        List<TransactionReceiptVo> transactionReceiptVos = transactionReceiptMapper.selectVoList(lqw);
        for (TransactionReceiptVo vo : transactionReceiptVos) {
            String transactionMethod1 = vo.getTransactionMethod();
            // 线上p卡取账号名称
            if (TransactionMethodEnum.OnlinePayoneer.getValue().equals(transactionMethod1)) {
                TenantPayoneer tenantPayoneer = iTenantPayoneerService.selectByAccountIdAndTenantId(vo.getTenantId(), vo.getAccountId());
                if (ObjectUtil.isNotNull(tenantPayoneer)) {
                    vo.setAccountName(tenantPayoneer.getAccountName());
                }
            }
        }
        return transactionReceiptVos;
    }

    @Override
    public R<TransactionOrderRecordVo> getDistrTransactionsPage(TransactionOrderRecordBo bo, PageQuery pageQuery) {
        log.info("进入【翻页查询分销商交易记录列表】方法，{} {}", JSONUtil.toJsonStr(bo), JSONUtil.toJsonStr(pageQuery));

        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        // 交易时间
        List<String> tradingDates = bo.getTradingDates();
        if (CollUtil.isNotEmpty(tradingDates)) {
            Date sDate = DateUtil.parseDate(tradingDates.get(0));
            Date eDate = DateUtil.parseDate(tradingDates.get(1));
            lqw.ge(TransactionRecord::getTransactionTime, DateUtil.beginOfDay(sDate))
                .le(TransactionRecord::getTransactionTime, DateUtil.endOfDay(eDate));
        }
        if (StrUtil.isNotEmpty(bo.getTenantId())){
            lqw.eq(TransactionRecord::getTenantId,bo.getTenantId());
        }
//        // 支付方式
//        String paymentMethod = bo.getPaymentMethod();

        // 交易类型
        String transactionType = bo.getTransactionType();
        if (StrUtil.isNotBlank(transactionType)) {
            lqw.eq(TransactionRecord::getTransactionType, TransactionTypeEnum.valueOf(transactionType));
        } else {
            List<TransactionTypeEnum> trs = new ArrayList<>();
            trs.add(TransactionTypeEnum.Income);
            trs.add(TransactionTypeEnum.Expenditure);
            lqw.in(TransactionRecord::getTransactionType, trs);
        }
        if (StrUtil.isNotEmpty(bo.getTransactionNo())){
            lqw.eq(TransactionRecord::getTransactionNo,bo.getTransactionNo());
        }
        if (StrUtil.isNotEmpty(bo.getCurrencyCode())){
            lqw.eq(TransactionRecord::getCurrency,bo.getCurrencyCode());
        }
        lqw.orderByDesc(TransactionRecord::getTransactionTime);
        Page<TransactionRecord> transactionRecordPage =new Page<>();
        List<TransactionRecord> recordList = transactionRecordPage.getRecords();
        if (StrUtil.isNotEmpty(bo.getOrderNo())){
            String sql="SELECT 1\n" +
                "              FROM (SELECT too.transactions_id\n" +
                "                    FROM transactions_orders too\n" +
                "                             INNER JOIN orders o ON too.order_id = o.id\n" +
                "                    WHERE too.transactions_id = transaction_record.id\n" +
                "                      AND o.order_no = {0}\n" +
                "                    UNION ALL\n" +
                "                    SELECT tor.transactions_id\n" +
                "                    FROM transactions_order_refund tor\n" +
                "                             INNER JOIN order_refund orr ON tor.order_refund_id = orr.id\n" +
                "                             INNER JOIN orders o ON orr.order_id = o.id\n" +
                "                    WHERE tor.transactions_id = transaction_record.id\n" +
                "                      AND o.order_no = {0}) AS subquery\n" +
                "              WHERE subquery.transactions_id = transaction_record.id";
            lqw.exists(sql, bo.getOrderNo());
        }
        transactionRecordPage =TenantHelper.ignore(()->transactionRecordMapper.selectPage(pageQuery.build(), lqw)) ;
        //总充值
        TransactionOrderRecordVo totalDeposit = transactionRecordMapper.getAmountByTransactionType(TransactionTypeEnum.Recharge);
        //总支付订单
        TransactionOrderRecordVo totalTransferOut = transactionRecordMapper.getAmountByTransactionType(TransactionTypeEnum.Expenditure);
        //总退款
        OrderRefundVo totalRefund = orderRefundMapper.sumPlatformRefundAmountForTenant();

        TransactionOrderRecordVo vo = new TransactionOrderRecordVo();
        BigDecimal deposit = ObjectUtil.isNotNull(totalDeposit) ? totalDeposit.getAmount() : new BigDecimal("0.00");
        vo.setDeposit(deposit);
        BigDecimal transferOut = ObjectUtil.isNotNull(totalTransferOut) ? totalTransferOut.getAmount() : new BigDecimal("0.00");
        vo.setTransferOut(transferOut);
        BigDecimal refundAmount = ObjectUtil.isNotNull(totalRefund) ? totalRefund.getPlatformRefundAmount() : new BigDecimal("0.00");
        vo.setRefund(refundAmount);
        TableDataInfo<TransactionRecordVo> transactionBodyList = new TableDataInfo<>();
        List<TransactionRecord> records = transactionRecordPage.getRecords();
        List<TransactionRecordVo> recordVos = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            for (TransactionRecord record : records) {
                TransactionRecordVo trVo = BeanUtil.toBean(record, TransactionRecordVo.class);
                trVo.setCurrencyCode(record.getCurrency());
                trVo.setCurrencySymbol(record.getCurrencySymbol());
                recordVos.add(trVo);
            }
        }

        transactionBodyList.setRows(recordVos);
        transactionBodyList.setTotal(transactionRecordPage.getTotal());
        vo.setTransactionsBodyList(transactionBodyList);
        return R.ok(vo);
    }

    @Override
    public List<TransactionRecordVo> queryTransactionRecordList(TransactionOrderRecordBo bo) {
        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        // 交易时间
        List<String> tradingDates = bo.getTradingDates();
        if (CollUtil.isNotEmpty(tradingDates)) {
            Date sDate = DateUtil.parseDate(tradingDates.get(0));
            Date eDate = DateUtil.parseDate(tradingDates.get(1));
            lqw.ge(TransactionRecord::getTransactionTime, DateUtil.beginOfDay(sDate))
                .le(TransactionRecord::getTransactionTime, DateUtil.endOfDay(eDate));
        }
        // 支付方式
//        String paymentMethod = bo.getPaymentMethod();

        // 交易类型
        String transactionType = bo.getTransactionType();
        if (StrUtil.isNotBlank(transactionType)) {
            lqw.eq(TransactionRecord::getTransactionType, TransactionTypeEnum.valueOf(transactionType));
        } else {
            List<TransactionTypeEnum> trs = new ArrayList<>();
            trs.add(TransactionTypeEnum.Income);
            trs.add(TransactionTypeEnum.Expenditure);
            lqw.in(TransactionRecord::getTransactionType, trs);
        }

        lqw.orderByDesc(TransactionRecord::getTransactionTime);
        //查询交易记录
        return transactionRecordMapper.selectVoList(lqw);
    }

    @Override
    public R<TableDataInfo<TransactionOrderDetailVo>> getTransactionOrder(TransactionOrderBo bo, PageQuery pageQuery) {
        log.info("进入【查询分销商交易记录订单详情】方法，{} {}", JSONUtil.toJsonStr(bo), JSONUtil.toJsonStr(pageQuery));
        String tenantId = LoginHelper.getTenantId();
        String transactionNo = bo.getTransactionNo();
        String orderNo = bo.getOrderNo();

        LambdaQueryWrapper<TransactionRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TransactionRecord::getTransactionNo, transactionNo);
        TransactionRecord transactionRecord = transactionRecordMapper.selectOne(lqw);

        if (ObjectUtil.isNull(transactionRecord)) {
            return R.fail(ZSMallStatusCodeEnum.STORE_TRANSACTION_IS_EMPTY);
        }
        log.info("transactionsId : {}", transactionRecord.getId());

        List<TransactionsProductVo> bodyList = new ArrayList<>();
        TransactionOrderDetailVo respTransactionDetailBody = new TransactionOrderDetailVo();
        long totalEle = 0;

        if (ObjectUtil.isNotNull(transactionRecord)) {
            TransactionSubTypeEnum subType = transactionRecord.getTransactionSubType();

            // 后续的交易记录处理，改用策略模式获取不同实现类以达到解耦目的，仅支持国外现货相关的交易记录详情处理
            Optional<PaymentHandleService> first =
                paymentHandleServices.stream().filter(service -> service.executeThisImpl(transactionRecord)).findFirst();
            if (first.isPresent()) {
                return first.get().handleTransactionDetail(transactionRecord, pageQuery);
            }

            //获取流水号
            respTransactionDetailBody.setTransactionId(transactionRecord.getTransactionNo());
            //获取交易编号
            respTransactionDetailBody.setTransactionNo(transactionRecord.getTransactionNo());
            //获取交易时间
            String updateDateTime = DateUtil.format(transactionRecord.getTransactionTime(), dateTimeFormatter);
            respTransactionDetailBody.setDate(updateDateTime);
            //获取交易类型
            if (ObjectUtil.isNotNull(subType)) {
                respTransactionDetailBody.setType(subType.getValue());
            } else if (ObjectUtil.isNotNull(transactionRecord.getTransactionType())) {
                respTransactionDetailBody.setType(transactionRecord.getTransactionType().getValue());
            }
            //获取交易金额
            String amount = '$' + NumberUtil.toStr(transactionRecord.getTransactionAmount());

            //获取总产品金额
            Double productAmount = 0.0;
            //获取总操作费
            BigDecimal operationFee = new BigDecimal("0.0");
            //获取总尾程派送费
            BigDecimal finalDeliveryFee = new BigDecimal("0.0");
            //获取其他费用
            Double otherCosts = 0.0;
            //获取订单数量
            Integer orderNum = 0;

            // 订单
            LambdaQueryWrapper<Orders> lqw2 = new LambdaQueryWrapper<>();
            lqw2.exists("SELECT 1 FROM transaction_record JOIN transactions_orders " +
                "ON transactions_orders.order_id = orders.id AND transactions_orders.transactions_id = transaction_record.id " +
                "WHERE transaction_record.transaction_no = '" + transactionNo + "'");
            if (StrUtil.isNotBlank(orderNo)) {
                lqw2.like(Orders::getOrderNo, orderNo);
            }
            List<Orders> ordersList =TenantHelper.ignore(()->ordersMapper.selectList(lqw2));
            if (CollUtil.isNotEmpty(ordersList)) {
                log.info("getTransactionOrders - transactionNo = {} ordersPage Not empty", transactionNo);
                for (Orders order : ordersList) {
                    LambdaQueryWrapper<OrderItem> lqw3 = new LambdaQueryWrapper<>();
                    if (StrUtil.isNotBlank(orderNo)) {
                        lqw3.exists("SELECT 1 FROM orders " +
                            "JOIN transactions_orders ON transactions_orders.order_id = orders.id " +
                            "JOIN transaction_record ON transactions_orders.transactions_id = transaction_record.id " +
                            "WHERE order_item.order_id = orders.id AND transaction_record.transaction_no = '" + transactionNo + "' AND orders.order_no LIKE '%" + orderNo + "%'");
                    } else {
                        lqw3.exists("SELECT 1 FROM orders " +
                            "JOIN transactions_orders ON transactions_orders.order_id = orders.id " +
                            "JOIN transaction_record ON transactions_orders.transactions_id = transaction_record.id " +
                            "WHERE order_item.order_id = orders.id AND transaction_record.transaction_no = '" + transactionNo + "'");
                    }
                    Page<OrderItem> orderItemsPage = TenantHelper.ignore(() -> orderItemMapper.selectPage(pageQuery.build(), lqw3));

                    List<OrderItem> orderItems = orderItemsPage.getRecords();
                    totalEle = orderItemsPage.getTotal();
                    LogisticsTypeEnum logisticsType = order.getLogisticsType();

                    //处理子订单数据
                    for (OrderItem orderItem : orderItems) {
                        TransactionsProductVo transactionsProductBody = new TransactionsProductVo();
                        OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemIdNoTenant(orderItem.getId());
                        //商品金额单价
                        BigDecimal productUnitPriceMd = BigDecimal.ZERO;
                        //操作费单价
                        BigDecimal operationFeeMd = BigDecimal.ZERO;
                        //尾程费单价
                        BigDecimal finalDeliveryFeeMd = BigDecimal.ZERO;
                        //获取商品价格信息
                        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemIdNoTenant(orderItem.getId());
                        //获取商品信息
                        getProductSkuInfo(orderProductSku, transactionsProductBody);

                        if (StrUtil.isNotBlank(orderProductSku.getActivityCode())) {
                            // 获取活动商品价格信息
                            ProductActivityPriceItem itemPriceEntity =
                                iProductActivityPriceItemService.getByActivityCodeNoTenant(orderProductSku.getActivityCode());
                            if (ObjectUtil.isNotNull(itemPriceEntity)) {
                                finalDeliveryFeeMd = itemPriceEntity.getPlatformFinalDeliveryFee();
                                //获取平台尾款单价
                                BigDecimal platformBalanceUnitPrice = itemPriceEntity.getPlatformBalanceUnitPrice();
                                //获取平台单价
                                BigDecimal platformUnitPrice = itemPriceEntity.getPlatformUnitPrice();
                                //获取平台自提价
                                BigDecimal platformPickUpPrice = itemPriceEntity.getPlatformPickUpPrice();
                                //计算活动商品尾款占自提价的比例
                                BigDecimal div = BigDecimal.ZERO;
                                if (!NumberUtil.equals(platformPickUpPrice, BigDecimal.ZERO) && platformPickUpPrice != null
                                    && platformBalanceUnitPrice != null) {
                                    div = NumberUtil.div(platformBalanceUnitPrice, platformPickUpPrice);
                                }
                                //获取剩余商品金额单价
                                productUnitPriceMd = NumberUtil.mul(platformUnitPrice, div);

                                operationFeeMd = NumberUtil.sub(platformBalanceUnitPrice, productUnitPriceMd);

                                transactionsProductBody.setUnitPrice(NumberUtil.toStr(platformUnitPrice));
                            }
                        } else if (ObjectUtil.isNotNull(orderItemPrice)) {
                            productUnitPriceMd = orderItemPrice.getPlatformUnitPrice();
                            operationFeeMd = orderItemPrice.getPlatformOperationFee();
                            finalDeliveryFeeMd = orderItemPrice.getPlatformFinalDeliveryFee();
                            transactionsProductBody.setUnitPrice(NumberUtil.toStr(productUnitPriceMd));
                        }

                        transactionsProductBody.setShippingMethod(logisticsType.getValue());
                        //获取商品数量
                        Integer num = orderItem.getTotalQuantity();
                        transactionsProductBody.setNum(NumberUtil.toStr(num));

                        //计算商品金额
                        BigDecimal productPrice = NumberUtil.mul(productUnitPriceMd, num);
                        Double productPrice1 = NumberUtil.toDouble(productPrice);
                        transactionsProductBody.setProductPrice(NumberUtil.toStr(productPrice));
                        //计算操作费
                        BigDecimal itemOperationFee =
                            operationFeeMd != null ? NumberUtil.mul(operationFeeMd, num) : BigDecimal.ZERO;
                        transactionsProductBody.setOperationFee(NumberUtil.toStr(itemOperationFee));
                        //计算尾程费用
                        BigDecimal deliveryFee =
                            finalDeliveryFeeMd != null ? NumberUtil.mul(finalDeliveryFeeMd, num) : BigDecimal.ZERO;
                        //自提的尾程费为0
                        if (logisticsType == LogisticsTypeEnum.PickUp) {
                            deliveryFee = BigDecimal.ZERO;
                        }
                        transactionsProductBody.setFinalDeliveryFee(NumberUtil.toStr(deliveryFee));

                        transactionsProductBody.setOtherCost("0.00");
                        //计算订单合计
                        String totalPrice = NumberUtil.toStr(NumberUtil.add(productPrice, itemOperationFee, deliveryFee));
                        transactionsProductBody.setTotalPrice(totalPrice);
                        bodyList.add(transactionsProductBody);
                    }


                    LambdaQueryWrapper<OrderItem> lqw8 = new LambdaQueryWrapper<>();
                    lqw8.eq(OrderItem::getOrderId, order.getId());
                    List<OrderItem> orderItemList = orderItemMapper.selectList(lqw8);
                    orderNum = orderNum + orderItemList.size();
                    for (OrderItem orderItem : orderItemList) {
                        OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemIdNoTenant(orderItem.getId());
                        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemIdNoTenant(orderItem.getId());

                        BigDecimal productUnitPriceMd = BigDecimal.ZERO;
                        BigDecimal operationFeeMd = BigDecimal.ZERO;
                        BigDecimal finalDeliveryFeeMd = BigDecimal.ZERO;
                        if (StrUtil.isNotBlank(orderProductSku.getActivityCode())) {
                            ProductActivityPriceItem itemPriceEntity =
                                iProductActivityPriceItemService.getByActivityCodeNoTenant(orderProductSku.getActivityCode());
                            if (ObjectUtil.isNotNull(itemPriceEntity)) {
                                finalDeliveryFeeMd = itemPriceEntity.getPlatformFinalDeliveryFee();
                                BigDecimal platformBalanceUnitPrice = itemPriceEntity.getPlatformBalanceUnitPrice();
                                BigDecimal platformUnitPrice = itemPriceEntity.getPlatformUnitPrice();
                                BigDecimal platformPickUpPrice = itemPriceEntity.getPlatformPickUpPrice();
                                BigDecimal div = BigDecimal.ZERO;
                                if (!NumberUtil.equals(platformPickUpPrice, BigDecimal.ZERO) && platformBalanceUnitPrice != null
                                    && platformPickUpPrice != null) {
                                    div = NumberUtil.div(platformBalanceUnitPrice, platformPickUpPrice);
                                }
                                productUnitPriceMd = NumberUtil.mul(platformUnitPrice, div);
                                operationFeeMd = NumberUtil.sub(platformBalanceUnitPrice, productUnitPriceMd);
                            }
                        } else if (ObjectUtil.isNotNull(orderItemPrice)) {
                            productUnitPriceMd = orderItemPrice.getPlatformUnitPrice();
                            operationFeeMd = orderItemPrice.getPlatformOperationFee();
                            finalDeliveryFeeMd = orderItemPrice.getPlatformFinalDeliveryFee();
                        }
                        Integer num = orderItem.getTotalQuantity();
                        BigDecimal productPrice = NumberUtil.mul(productUnitPriceMd, num);
                        Double productPrice1 = NumberUtil.toDouble(productPrice);
                        BigDecimal itemOperationFee =
                            operationFeeMd != null ? NumberUtil.mul(operationFeeMd, num) : BigDecimal.ZERO;
                        BigDecimal deliveryFee =
                            finalDeliveryFeeMd != null ? NumberUtil.mul(finalDeliveryFeeMd, num) : BigDecimal.ZERO;
                        if (logisticsType == LogisticsTypeEnum.PickUp) {
                            deliveryFee = BigDecimal.ZERO;
                        }
                        productAmount = NumberUtil.add(productAmount, productPrice1);
                        operationFee = NumberUtil.add(operationFee, itemOperationFee);
                        finalDeliveryFee = NumberUtil.add(finalDeliveryFee, deliveryFee);
                    }

                }
                respTransactionDetailBody.setOrderNos(ordersList.stream()
                                                                .map(Orders::getOrderNo)
                                                                .collect(Collectors.toList()));
                respTransactionDetailBody.setTransactionAmount('-' + amount);
                respTransactionDetailBody.setProductAmount(NumberUtil.toStr(productAmount));
                respTransactionDetailBody.setOperationFee(NumberUtil.toStr(operationFee));
                respTransactionDetailBody.setFinalDeliveryFee(NumberUtil.toStr(finalDeliveryFee));
                respTransactionDetailBody.setOrderNum(NumberUtil.toStr(orderNum));
                respTransactionDetailBody.setOtherCosts(NumberUtil.toStr(otherCosts));
                respTransactionDetailBody.setTransactionsProductBodyList(bodyList);
            }

            // 退款单
            LambdaQueryWrapper<OrderRefund> lqw4 = new LambdaQueryWrapper<>();
            lqw4.exists("SELECT 1 FROM transaction_record JOIN transactions_order_refund " +
                "ON transactions_order_refund.order_refund_id = order_refund.id AND transactions_order_refund.transactions_id = transaction_record.id " +
                "WHERE transaction_record.transaction_no = '" + transactionNo + "'");
            if (StrUtil.isNotBlank(orderNo)) {
                lqw4.like(OrderRefund::getOrderNo, orderNo);
            }
            List<OrderRefund> orderRefundList =TenantHelper.ignore(()->orderRefundMapper.selectList(lqw4)) ;
            if (CollUtil.isNotEmpty(orderRefundList) && CollUtil.isEmpty(bodyList)) {
                log.info("getTransactionOrders - transactionNo = {} orderRefundPage Not empty", transactionNo);
                for (OrderRefund orderRefund : orderRefundList) {
                    LambdaQueryWrapper<OrderRefundItem> lqw5 = new LambdaQueryWrapper<>();
                    if (StrUtil.isNotBlank(orderNo)) {
                        lqw5.exists("SELECT 1 FROM order_refund " +
                            "JOIN transactions_order_refund ON transactions_order_refund.order_refund_id = order_refund.id " +
                            "JOIN transaction_record ON transactions_order_refund.transactions_id = transaction_record.id " +
                            "WHERE order_refund_item.order_refund_id = order_refund.id AND transaction_record.transaction_no = '" + transactionNo + "' AND order_refund.order_no LIKE '%" + orderNo + "%'");
                    } else {
                        lqw5.exists("SELECT 1 FROM order_refund " +
                            "JOIN transactions_order_refund ON transactions_order_refund.order_refund_id = order_refund.id " +
                            "JOIN transaction_record ON transactions_order_refund.transactions_id = transaction_record.id " +
                            "WHERE order_refund_item.order_refund_id = order_refund.id AND transaction_record.transaction_no = '" + transactionNo + "'");
                    }
                    Page<OrderRefundItem> orderRefundItemPage = TenantHelper.ignore(() -> orderRefundItemMapper.selectPage(pageQuery.build(), lqw5));

                    totalEle = orderRefundItemPage.getTotal();
                    //获取退款子单
                    List<OrderRefundItem> orderRefundItems = iOrderRefundItemService.getByOrderRefundId(orderRefund.getId());
                    //判断发货方式
                    LogisticsTypeEnum logisticsType = null;
                    Orders order = ordersMapper.selectById(orderRefund.getOrderId());
                    OrderType orderType = null;
                    if (ObjectUtil.isNotNull(order)) {
                        orderType = order.getOrderType();
                        logisticsType = order.getLogisticsType();
                    }

                    for (OrderRefundItem orderRefundItem : orderRefundItems) {
                        TransactionsProductVo transactionsProductBody = new TransactionsProductVo();
                        //产品金额单价
                        BigDecimal productUnitPriceMd = BigDecimal.ZERO;
                        //操作费单价
                        BigDecimal operationFeeMd = BigDecimal.ZERO;
                        //尾程费单价
                        BigDecimal finalDeliveryFeeMd = BigDecimal.ZERO;
                        //获得退款子单关联的子订单
                        OrderItem orderItem = iOrderItemService.queryByIdNotTenant(orderRefundItem.getOrderItemId());
                        //获得价格列表
                        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemId(orderItem.getId());
                        OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemIdNoTenant(orderItem.getId());
                        if (StrUtil.isNotBlank(orderProductSku.getActivityCode())) {
                            // 获得商品活动价格
                            ProductActivityPriceItem itemPriceEntity =
                                iProductActivityPriceItemService.getByActivityCodeNoTenant(orderProductSku.getActivityCode());
                            if (ObjectUtil.isNotNull(itemPriceEntity)) {
                                finalDeliveryFeeMd = itemPriceEntity.getPlatformFinalDeliveryFee();
                                if (ObjectUtil.isNotNull(orderItemPrice.getPlatformDepositUnitPrice())) {
                                    //如果已收货则退全款
                                    productUnitPriceMd = itemPriceEntity.getPlatformUnitPrice();

                                    operationFeeMd = itemPriceEntity.getPlatformOperationFee();
                                } else {
                                    //获得尾款单价
                                    BigDecimal platformBalanceUnitPrice = itemPriceEntity.getPlatformBalanceUnitPrice();
                                    //获得活动商品单价
                                    BigDecimal platformUnitPrice = itemPriceEntity.getPlatformUnitPrice();
                                    //获得活动商品自提价
                                    BigDecimal platformPickUpPrice = itemPriceEntity.getPlatformPickUpPrice();
                                    //计算活动商品尾款占自提价的比例
                                    BigDecimal div = BigDecimal.ZERO;
                                    if (!NumberUtil.equals(platformPickUpPrice, BigDecimal.ZERO) && platformBalanceUnitPrice != null
                                        && platformPickUpPrice != null) {
                                        div = NumberUtil.div(platformBalanceUnitPrice, platformPickUpPrice);
                                    }
                                    //获得剩余商品金额单价
                                    productUnitPriceMd = NumberUtil.mul(platformUnitPrice, div);
                                    //获得剩余操作费单价
                                    operationFeeMd = NumberUtil.sub(platformBalanceUnitPrice, productUnitPriceMd);
                                }
                                transactionsProductBody.setUnitPrice(DecimalUtil.bigDecimalToString(itemPriceEntity.getPlatformUnitPrice()));
                            }
                        } else if (ObjectUtil.isNotNull(orderItemPrice)) {
                            productUnitPriceMd = orderItemPrice.getPlatformUnitPrice();
                            operationFeeMd = orderItemPrice.getPlatformOperationFee();
                            finalDeliveryFeeMd = orderItemPrice.getPlatformFinalDeliveryFee();
                            transactionsProductBody.setUnitPrice(DecimalUtil.bigDecimalToString(productUnitPriceMd));
                        }
                        //获得商品数量
                        Integer num = orderRefundItem.getRefundQuantity();
                        transactionsProductBody.setShippingMethod(ObjectUtil.isNotNull(logisticsType) ? logisticsType.getValue() : "");
                        //计算商品金额
                        BigDecimal productPrice = NumberUtil.mul(productUnitPriceMd, num);
                        //计算操作费
                        BigDecimal itemOperationFee = operationFeeMd != null ? NumberUtil.mul(operationFeeMd, num) : BigDecimal.ZERO;
                        //如果活动商品已收货，则商品金额需要算上订金
                        Double productPrice1 = NumberUtil.toDouble(productPrice);
                        //计算尾程费
                        BigDecimal deliveryFee = finalDeliveryFeeMd != null ? NumberUtil.mul(finalDeliveryFeeMd, num) : BigDecimal.ZERO;
                        //自提的尾程费为0
                        if (logisticsType == LogisticsTypeEnum.PickUp) {
                            deliveryFee = BigDecimal.ZERO;
                        }

                        getProductSkuInfo(orderProductSku, transactionsProductBody);
                        transactionsProductBody.setNum(NumberUtil.toStr(num));
                        transactionsProductBody.setProductPrice(NumberUtil.toStr(productPrice1));
                        transactionsProductBody.setOperationFee(NumberUtil.toStr(itemOperationFee));
                        transactionsProductBody.setFinalDeliveryFee(NumberUtil.toStr(deliveryFee));
                        // orderRefundItem表没有退款金额字段了，以下信息不用了
//                        if (OrderType.Wholesale.equals(orderType)) {
//                            transactionsProductBody.setRefund("-");
//                        } else {
//                            transactionsProductBody.setRefund(NumberUtil.toStr(orderRefundItem.getActualRefundPrice()));
//                        }
                        transactionsProductBody.setTotalPrice(NumberUtil.toStr(orderRefund.getPlatformRefundAmount()));
                        bodyList.add(transactionsProductBody);
                    }
                    orderNum = orderNum + orderRefundItems.size();
                    // 泰麟说以下信息用不上了
//                    for (OrderRefundItem orderRefundItem : orderRefund.getOrderRefundItems()) {
//                        BigDecimal productUnitPriceMd = BigDecimal.ZERO;
//                        BigDecimal operationFeeMd = BigDecimal.ZERO;
//                        BigDecimal finalDeliveryFeeMd = BigDecimal.ZERO;
//                        OrderItem orderItem = orderRefundItem.getOrderItem();
//                        List<OrderItemPrice> orderItemPriceList = orderItem.getOrderItemPriceList();
//                        OrderProductSku orderProductSku = orderRefundItem.getOrderProductSku();
//                        if (StrUtil.isNotBlank(orderProductSku.getActivityCode())) {
//                            ProductActivityPriceItem itemPriceEntity =
//                                iProductActivityPriceItemService.getByActivityCodeNoTenant(orderProductSku.getActivityCode());
//                            if (ObjectUtil.isNotNull(itemPriceEntity)) {
//                                finalDeliveryFeeMd = itemPriceEntity.getPlatformFinalDeliveryFee();
//                                if (ObjectUtil.isNotNull(orderRefundItem.getDepositUnitPrice())) {
//                                    //如果已收货则退全款
//                                    productUnitPriceMd = itemPriceEntity.getPlatformUnitPrice();
//
//                                    operationFeeMd = itemPriceEntity.getPlatformOperationFee();
//                                } else {
//                                    //获得尾款单价
//                                    BigDecimal platformBalanceUnitPrice = itemPriceEntity.getPlatformBalanceUnitPrice();
//                                    //获得活动商品单价
//                                    BigDecimal platformUnitPrice = itemPriceEntity.getPlatformUnitPrice();
//                                    //获得活动商品自提价
//                                    BigDecimal platformPickUpPrice = itemPriceEntity.getPlatformPickUpPrice();
//                                    //计算活动商品尾款占自提价的比例
//                                    BigDecimal div = BigDecimal.ZERO;
//                                    if (!NumberUtil.equals(platformPickUpPrice, BigDecimal.ZERO) && platformBalanceUnitPrice != null
//                                        && platformPickUpPrice != null) {
//                                        div = NumberUtil.div(platformBalanceUnitPrice, platformPickUpPrice);
//                                    }
//                                    //获得剩余商品金额单价
//                                    productUnitPriceMd = NumberUtil.mul(platformUnitPrice, div);
//                                    //获得剩余操作费单价
//                                    operationFeeMd = NumberUtil.sub(platformBalanceUnitPrice, productUnitPriceMd);
//                                }
//                            }
//                        } else if (CollUtil.isNotEmpty(orderItemPriceList)) {
//                            for (OrderItemPrice orderItemPrice : orderItemPriceList) {
//                                OrderItemPriceTypeEnum priceType = orderItemPrice.getPriceType();
//                                switch (priceType) {
//                                    case UnitPriceMd:
//                                        productUnitPriceMd = orderItemPrice.getPrice();
//                                        break;
//                                    case OperationFeeMd:
//                                        operationFeeMd = orderItemPrice.getPrice();
//                                        break;
//                                    case FinalDeliveryFeeMd:
//                                        finalDeliveryFeeMd = orderItemPrice.getPrice();
//                                        break;
//                                    default:
//                                        break;
//                                }
//                            }
//                        }
//                        Integer num = orderItem.getNum();
//                        BigDecimal productPrice = NumberUtil.mul(productUnitPriceMd, num);
//
//                        BigDecimal itemOperationFee =
//                            operationFeeMd != null ? NumberUtil.mul(operationFeeMd, num) : BigDecimal.ZERO;
//                        Double productPrice1 = NumberUtil.toDouble(productPrice);
//
//                        BigDecimal deliveryFee =
//                            finalDeliveryFeeMd != null ? NumberUtil.mul(finalDeliveryFeeMd, num) : BigDecimal.ZERO;
//                        if (StrUtil.equals(shippingMethod, LogisticsType.PickUp.name())) {
//                            deliveryFee = BigDecimal.ZERO;
//                        }
//                        productAmount = NumberUtil.add(productAmount, productPrice1);
//                        operationFee = NumberUtil.add(operationFee, itemOperationFee);
//                        finalDeliveryFee = NumberUtil.add(finalDeliveryFee, deliveryFee);
//                    }
                }
                respTransactionDetailBody.setOrderNos(orderRefundList.stream()
                                                                .map(OrderRefund::getOrderNo)
                                                                .collect(Collectors.toList()));
                respTransactionDetailBody.setTransactionAmount('+' + amount);
                respTransactionDetailBody.setProductAmount(NumberUtil.toStr(productAmount));
                respTransactionDetailBody.setOperationFee(NumberUtil.toStr(operationFee));
                respTransactionDetailBody.setFinalDeliveryFee(NumberUtil.toStr(finalDeliveryFee));
                respTransactionDetailBody.setOrderNum(NumberUtil.toStr(orderNum));
                respTransactionDetailBody.setOtherCosts(NumberUtil.toStr(otherCosts));
                respTransactionDetailBody.setTransactionsProductBodyList(bodyList);
            }

            if (CollUtil.isEmpty(bodyList)) {
                //处理活动订金，仓储费和买断费
                Long id = transactionRecord.getId();
                // 获得交易记录关联的活动
                LambdaQueryWrapper<TransactionsProductActivityItem> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TransactionsProductActivityItem::getTransactionsId, id);
                TransactionsProductActivityItem activityItemEntity = iTransactionsProductActivityItemService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(activityItemEntity)) {
                    //获得分销商活动id
                    Long productActivityItemId = activityItemEntity.getProductActivityItemId();
                    //获得活动价格
                    ProductActivityPriceItem activityItemPriceEntity = iProductActivityPriceItemService.queryByActivityItemId(productActivityItemId);
                    List<ProductActivityStockItem> inventoryEntities = iProductActivityStockItemService.queryByActivityItemId(productActivityItemId);
                    //获得活动商品信息
                    LambdaQueryWrapper<ProductActivityItem> queryWrapper1 = new LambdaQueryWrapper<>();
                    queryWrapper1.eq(ProductActivityItem::getId, productActivityItemId);
                    ProductActivityItem productActivityItemEntity = iProductActivityItemService.getOne(queryWrapper1);

                    TransactionsProductVo transactionsProductBody = new TransactionsProductVo();
                    transactionsProductBody.setProductSkuName(productActivityItemEntity.getProductName());
                    transactionsProductBody.setActivityType(productActivityItemEntity.getActivityType().getValue());
                    transactionsProductBody.setItemNo(productActivityItemEntity.getProductSkuCode());
                    transactionsProductBody.setSku(productActivityItemEntity.getProductSku());
                    transactionsProductBody.setImageShowUrl(productActivityItemEntity.getProductImg());
                    transactionsProductBody.setActivityCode(productActivityItemEntity.getActivityCode());
                    //仓储费
                    transactionsProductBody.setStorageCharge(NumberUtil.toStr(transactionRecord.getTransactionAmount()));
                    //商品单价
                    transactionsProductBody.setUnitPrice(NumberUtil.toStr(activityItemPriceEntity.getPlatformUnitPrice()));
                    //活动订金总价
                    BigDecimal depositTotalPrice = activityItemPriceEntity.getPlatformDepositTotalPrice();
                    //活动订金单价
                    BigDecimal depositUnitPrice = activityItemPriceEntity.getPlatformDepositUnitPrice();
                    //计算商品数量
                    BigDecimal num = BigDecimal.ZERO;
                    for (ProductActivityStockItem itemInventoryEntity : inventoryEntities) {
                        Integer total = itemInventoryEntity.getQuantityTotal();
                        num = NumberUtil.add(num, total);
                    }
                    //计算订金比例
                    BigDecimal depositPercentage = BigDecimal.ZERO;
                    if (!NumberUtil.equals(activityItemPriceEntity.getPlatformTotalPrice(), BigDecimal.ZERO)
                        && depositTotalPrice != null && activityItemPriceEntity.getPlatformTotalPrice() != null) {
                        depositPercentage = NumberUtil.div(depositTotalPrice, activityItemPriceEntity.getPlatformTotalPrice());
                    }
                    depositPercentage = NumberUtil.mul(depositPercentage, 100);

                    transactionsProductBody.setDeposit(NumberUtil.toStr(depositTotalPrice));
                    transactionsProductBody.setDepositPercentage(NumberUtil.toStr(depositPercentage) + '%');
                    transactionsProductBody.setNum(NumberUtil.toStr(num));
                    //商品金额
                    transactionsProductBody.setProductPrice(NumberUtil.toStr(activityItemPriceEntity.getPlatformTotalPrice()));
                    //订单合计
                    transactionsProductBody.setTotalPrice(NumberUtil.toStr(depositTotalPrice));
                    bodyList.add(transactionsProductBody);

                    if (TransactionSubTypeEnum.ProductActivityRefund.equals(subType)) {
                        respTransactionDetailBody.setTransactionAmount('+' + amount);
                    } else {
                        respTransactionDetailBody.setTransactionAmount('-' + amount);
                    }

                    respTransactionDetailBody.setProductAmount(NumberUtil.toStr(depositTotalPrice));
                    respTransactionDetailBody.setOperationFee(NumberUtil.toStr(operationFee));
                    respTransactionDetailBody.setFinalDeliveryFee(NumberUtil.toStr(finalDeliveryFee));
                    respTransactionDetailBody.setOrderNum(NumberUtil.toStr(1));
                    respTransactionDetailBody.setOtherCosts(NumberUtil.toStr(otherCosts));
                    respTransactionDetailBody.setTransactionsProductBodyList(bodyList);
                }
            }
        }

        List<TransactionOrderDetailVo> respTransactionDetailBodyList = new ArrayList<>();
        respTransactionDetailBodyList.add(respTransactionDetailBody);

        TableDataInfo<TransactionOrderDetailVo> tableDataInfo = new TableDataInfo<>();
        tableDataInfo.setTotal(totalEle);
        tableDataInfo.setRows(respTransactionDetailBodyList);
        return R.ok(tableDataInfo);
    }

    private LambdaQueryWrapper<TransactionReceipt> transactionReceiptBuildQueryWrapper(TransactionReceiptBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TransactionReceipt> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOperatorTenantId()), TransactionReceipt::getOperatorTenantId, bo.getOperatorTenantId());
        lqw.eq(bo.getTransactionsId() != null, TransactionReceipt::getTransactionsId, bo.getTransactionsId());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionReceiptNo()), TransactionReceipt::getTransactionReceiptNo, bo.getTransactionReceiptNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionType()), TransactionReceipt::getTransactionType, bo.getTransactionType());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionMethod()), TransactionReceipt::getTransactionMethod, bo.getTransactionMethod());
        lqw.eq(bo.getTransactionAmount() != null, TransactionReceipt::getTransactionAmount, bo.getTransactionAmount());
        lqw.eq(bo.getTransactionTime() != null, TransactionReceipt::getTransactionTime, bo.getTransactionTime());
        lqw.eq(bo.getReceiptTime() != null, TransactionReceipt::getReceiptTime, bo.getReceiptTime());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountId()), TransactionReceipt::getAccountId, bo.getAccountId());
        lqw.like(StringUtils.isNotBlank(bo.getAccountName()), TransactionReceipt::getAccountName, bo.getAccountName());
        lqw.eq(bo.getReceiptAccountId() != null, TransactionReceipt::getReceiptAccountId, bo.getReceiptAccountId());
        lqw.like(StringUtils.isNotBlank(bo.getBankName()), TransactionReceipt::getBankName, bo.getBankName());
        lqw.eq(StringUtils.isNotBlank(bo.getSwiftCode()), TransactionReceipt::getSwiftCode, bo.getSwiftCode());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TransactionReceipt::getNote, bo.getNote());
        lqw.eq(StringUtils.isNotBlank(bo.getNoteManager()), TransactionReceipt::getNoteManager, bo.getNoteManager());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdChannelNo()), TransactionReceipt::getThirdChannelNo, bo.getThirdChannelNo());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdChannelAccount()), TransactionReceipt::getThirdChannelAccount, bo.getThirdChannelAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getReviewState()), TransactionReceipt::getReviewState, bo.getReviewState());
        return lqw;
    }

    private void getProductSkuInfo(OrderItemProductSku orderProductSku, TransactionsProductVo transactionsProductBody) {
        String productSkuCode = orderProductSku.getProductSkuCode();
        String productSkuName = orderProductSku.getProductName();
        String activityType = orderProductSku.getActivityType() != null ? orderProductSku.getActivityType().name() : "";
        String sku = orderProductSku.getSku();
        String imageShowUrl = orderProductSku.getImageShowUrl();

        transactionsProductBody.setItemNo(productSkuCode);
        transactionsProductBody.setProductSkuName(productSkuName);
        transactionsProductBody.setActivityType(activityType);
        transactionsProductBody.setSku(sku);
        transactionsProductBody.setImageShowUrl(imageShowUrl);
    }

    @Override
    public void export(RechargeRecordVo.RechargeRecordDetails bo){
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.TRANSACTION_RECEIPT_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        bo.setTenantId(LoginHelper.getTenantId());
        DownloadRecordUtil.generate(fileName,DownloadTypePlusEnum.TransactionsReceiptExport, tempFileSavePath -> {
            List<TransactionReceiptVo> transactionReceiptVoList = queryList(bo);
            List<String> tenantIds = transactionReceiptVoList.stream().map(TransactionReceiptVo::getTenantId).distinct().collect(Collectors.toList());
            Map<String, SysTenant> tenantMapByTenantIds = sysTenantService.getTenantMapByTenantIds(tenantIds);
            transactionReceiptVoList.parallelStream().forEach(refundApplyVo -> {
                SysTenant sysTenant = tenantMapByTenantIds.get(refundApplyVo.getTenantId());
                if (ObjectUtil.isNotNull(sysTenant)){
                    refundApplyVo.setThirdChannelFlag(sysTenant.getThirdChannelFlag());
                }
            });
            List<TransactionReceiptExcelVo> transactionReceiptExcelVoList = new ArrayList<>();
            for (TransactionReceiptVo transactionReceiptVo : transactionReceiptVoList) {
                TransactionReceiptExcelVo transactionReceiptExcelVo = BeanUtil.copyProperties(transactionReceiptVo, TransactionReceiptExcelVo.class);
                if(null != transactionReceiptVo.getTransactionAmount()){
                    transactionReceiptExcelVo.setTransactionAmountString(transactionReceiptVo.getCurrencySymbol()+NumberUtil.toStr(transactionReceiptVo.getTransactionAmount()));
                }else {
                    transactionReceiptExcelVo.setTransactionAmountString(transactionReceiptVo.getCurrencySymbol()+"0.00");
                }
                if(null != transactionReceiptVo.getTransactionFee()){
                    transactionReceiptExcelVo.setTransactionFeeString(transactionReceiptVo.getCurrencySymbol()+NumberUtil.toStr(transactionReceiptVo.getTransactionFee()));
                }else {
                    transactionReceiptExcelVo.setTransactionFeeString(transactionReceiptVo.getCurrencySymbol()+"0.00");
                }
                transactionReceiptExcelVoList.add(transactionReceiptExcelVo);
            }
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(transactionReceiptExcelVoList, "充值记录明细", TransactionReceiptExcelVo.class, false, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });
    }

}
