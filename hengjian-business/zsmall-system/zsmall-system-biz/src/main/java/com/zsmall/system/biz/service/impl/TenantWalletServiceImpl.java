package com.zsmall.system.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.extend.event.SysTenantPaymentEvent;
import com.zsmall.common.constant.MallConstants;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.payment.WalletStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.WalletException;
import com.zsmall.extend.event.system.WalletInitEvent;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.entity.domain.TenantExtraSetting;
import com.zsmall.system.entity.domain.TenantWallet;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.bo.tenantWallet.TestWalletBo;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.domain.vo.tenantWallet.WalletPayReadyVo;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantExtraSettingService;
import com.zsmall.system.entity.iservice.ITenantWalletService;
import com.zsmall.system.entity.iservice.ITransactionRecordService;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 租户钱包-业务层实现类
 *
 * <AUTHOR>
 * @date 2023/6/14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TenantWalletServiceImpl implements TenantWalletService {

    /* 数据库层 */
    private final ITenantWalletService iTenantWalletService;
    private final ITransactionRecordService iTransactionRecordService;
    private final ITenantExtraSettingService iTenantExtraSettingService;
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;

    /* 编号生成器 */
    private final MallSystemCodeGenerator mallSystemCodeGenerator;

    /**
     * 事件 - 初始化租户钱包
     * @param walletInitEvent
     */
    @Async
    @EventListener
    public void initWalletEventListener(WalletInitEvent walletInitEvent) {
        String inTenantId = walletInitEvent.getInTenantId();

        List<TenantWallet> tenantWalletList = iTenantWalletService.queryByTenantIdWithNotTenant(inTenantId);
        boolean notExist = CollUtil.isEmpty(tenantWalletList);
        log.info("initWalletEventListener {}, {}", inTenantId, notExist);
        try {
            if(notExist) {
                List<SiteCountryCurrencyVo> currencyList = iSiteCountryCurrencyService.getCurrencyList();
                List<TenantWallet> tenantWalletAddList = new ArrayList<>();
                if(CollUtil.isNotEmpty(currencyList)){
                    for(SiteCountryCurrencyVo siteCountryCurrencyVo : currencyList){
                        String walletNo = mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.Wallet);
                        TenantWallet tenantWallet = new TenantWallet();
                        tenantWallet.setTenantId(inTenantId);
                        tenantWallet.setWalletNo(walletNo);
                        tenantWallet.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
                        tenantWallet.setCurrencySymbol(siteCountryCurrencyVo.getCurrencySymbol());
                        tenantWallet.setWalletBalance(BigDecimal.ZERO);
                        tenantWallet.setWalletFreezeAmount(BigDecimal.ZERO);
                        tenantWallet.setWalletState(WalletStateEnum.Enable);
                        tenantWalletAddList.add(tenantWallet);
                    }
                }
                if(CollUtil.isNotEmpty(tenantWalletAddList)){
                    iTenantWalletService.saveBatch(tenantWalletAddList);
                }
            }
        } catch (RStatusCodeException e) {
            log.error("initWalletEventListener error. {}, {}, {}", e.getStatusCode(), e.getCustomMsg(), e.getMessage(), e);
        } catch (Exception e) {
            log.error("initWalletEventListener error. {}", e.getMessage(), e);
        }

    }

    @Override
    public R<Void> testBalance(TestWalletBo bo) {
        String transactionType = bo.getTransactionType();
        BigDecimal amount = bo.getAmount();

        TransactionRecord transactionRecord = new TransactionRecord();
        transactionRecord.setTransactionType(TransactionTypeEnum.valueOf(transactionType));
        transactionRecord.setTransactionAmount(amount);
        try {
            this.walletChanges(transactionRecord);
            return R.ok("交易成功");
        } catch (WalletException e) {
            log.error("Exception {}", e.getMessage(), e);
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Exception {}", e.getMessage(), e);
            return R.fail("交易失败");
        }
    }

    /**
     * 钱包支付准备
     *
     * @return
     */
    @Override
    public R<WalletPayReadyVo> walletPayReady(String currency) {
        TenantWallet tenantWallet = iTenantWalletService.queryByTenantId(LoginHelper.getTenantId(),currency);
        BigDecimal walletBalance = tenantWallet.getWalletBalance();

        Boolean paymentVerifyValue = false;
        TenantExtraSetting tenantExtraSetting = iTenantExtraSettingService.queryBySettingCode(MallConstants.DistributorSettings.EnablePaymentPassword);
        if (tenantExtraSetting != null) {
            paymentVerifyValue = Boolean.valueOf(tenantExtraSetting.getSettingValue());
        }

        WalletPayReadyVo vo = new WalletPayReadyVo();
        vo.setBalance(walletBalance);
        vo.setPaymentVerifyValue(paymentVerifyValue);
        return R.ok(vo);
    }

    @Override
    public List<WalletPayReadyVo> walletPayReady() {
        List<TenantWallet> tenantWalletList = iTenantWalletService.queryByTenantIdWithNotTenant(LoginHelper.getTenantId());
        List<WalletPayReadyVo> walletPayReadyVoList = new ArrayList<>();
        for (TenantWallet tenantWallet : tenantWalletList) {
            BigDecimal walletBalance = tenantWallet.getWalletBalance();
            Boolean paymentVerifyValue = false;
            TenantExtraSetting tenantExtraSetting = iTenantExtraSettingService.queryBySettingCode(MallConstants.DistributorSettings.EnablePaymentPassword);
            if (tenantExtraSetting != null) {
                paymentVerifyValue = Boolean.valueOf(tenantExtraSetting.getSettingValue());
            }
            WalletPayReadyVo vo = new WalletPayReadyVo();
            vo.setBalance(walletBalance);
            vo.setPaymentVerifyValue(paymentVerifyValue);
            vo.setCurrency(tenantWallet.getCurrency());
            vo.setCurrencySymbol(tenantWallet.getCurrencySymbol());
            walletPayReadyVoList.add(vo);
        }
        return walletPayReadyVoList;
    }

    /**
     * 钱包变动（从当前登录状态获取租户编号，适用于本人操作的付款/扣款）
     * @param transactionRecord 流程结束后，会在此方法内保存一次交易记录，防止在外部未保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void walletChanges(TransactionRecord transactionRecord) throws Exception {
        log.info("准备钱包变动 入参 = {}", JSONUtil.toJsonStr(transactionRecord));
        String nowTenantId = LoginHelper.getTenantId();
        if(StringUtils.isEmpty(nowTenantId)){
            nowTenantId = transactionRecord.getTenantId();
        }
        log.info("准备钱包变动（本人操作） 用户 = {}", nowTenantId);
        lockWalletChanges(nowTenantId, transactionRecord, true);
    }

    /**
     * 钱包变动（指定租户，不需要从当前登录状态获取，适用于非本人操作的付款/扣款，如定时器操作、供货商同意退款给分销商）
     *
     * @param targetTenantId    目标租户编号，注意，如果是管理员操作某个用户的钱包时，此编号应该是目标用户的租户编号
     * @param transactionRecord 流程结束后，会在此方法内保存一次交易记录，防止在外部未保存
     * @param isNeedWalletPay
     * @throws Exception
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED,rollbackFor = Exception.class)
    public void walletChanges(String targetTenantId, TransactionRecord transactionRecord, boolean isNeedWalletPay) throws Exception {
        log.info("准备钱包变动 入参: targetTenantId = {}, transactionRecord = {}", targetTenantId, JSONUtil.toJsonStr(transactionRecord));
        String nowTenantId = LoginHelper.getTenantId();
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        log.info("准备钱包变动（非本人操作） 目标用户 = {}，当前操作人用户ID和类型 = {}/{}", targetTenantId, nowTenantId, tenantTypeEnum);
        lockWalletChanges(targetTenantId, transactionRecord, isNeedWalletPay);
    }

    private void lockWalletChanges(String targetTenantId, TransactionRecord transactionRecord, boolean isNeedWalletPay) throws Exception {
        transactionRecord.setTenantId(targetTenantId);
        transactionRecord.setOperatorTenantId(LoginHelper.getTenantId());

        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_WALLET_LOCK + targetTenantId;
        RLock lock = client.getLock(key);
        try {
            lock.lock(10L, TimeUnit.SECONDS);
            log.info("Redis = {} 加锁成功", key);
            // 钱包扣款
            if(isNeedWalletPay){
                dealWalletChanges(transactionRecord);
            }else{
                transactionRecord.setTransactionNote("直接支付订单,无需钱包扣款");
                iTransactionRecordService.insertOrUpdateByEntity(transactionRecord);
            }
        } catch (WalletException e) {
            log.error("租户{}钱包变动出现异常(WalletException) {}", targetTenantId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("租户{}钱包变动出现异常 {}", targetTenantId, e.getMessage(), e);
            throw new WalletException(ZSMallStatusCodeEnum.WALLET_UPDATE_FAILURE);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.info("Redis = {} 解锁成功", key);
        }
    }

    @Transactional(isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRES_NEW)
    public void dealWalletChanges(TransactionRecord transactionRecord) throws Exception {
        String tenantId = transactionRecord.getTenantId();
        TenantWallet tenantWallet = iTenantWalletService.queryByTenantId(tenantId,transactionRecord.getCurrency());
        log.info("租户 = {}，钱包 = {}", tenantId, JSONUtil.toJsonStr(tenantWallet));

        WalletStateEnum walletState = tenantWallet.getWalletState();
        if (WalletStateEnum.Freeze.equals(walletState)) {
            throw new WalletException(ZSMallStatusCodeEnum.WALLET_FREEZE);
        }

        TransactionTypeEnum transactionType = transactionRecord.getTransactionType();
        TransactionSubTypeEnum transactionSubType = transactionRecord.getTransactionSubType();
        BigDecimal beforeBalance = tenantWallet.getWalletBalance();
        BigDecimal afterBalance = tenantWallet.getWalletBalance();
        BigDecimal transactionAmount = transactionRecord.getTransactionAmount();
        transactionRecord.setBeforeBalance(beforeBalance);

        switch (transactionType) {
            case Recharge:
                afterBalance = NumberUtil.add(beforeBalance, transactionAmount);
                break;
            case Withdrawal:
                if (NumberUtil.isGreaterOrEqual(beforeBalance, transactionAmount)) {
                    afterBalance = NumberUtil.sub(beforeBalance, transactionAmount);
                } else {
                    throw new WalletException(ZSMallStatusCodeEnum.BULK_WALLET_BALANCE_INSUFFICIENT);
                }
                break;
            case Income:
                afterBalance = NumberUtil.add(beforeBalance, transactionAmount);
                break;
            case Expenditure:
                if (NumberUtil.isGreaterOrEqual(beforeBalance, transactionAmount)) {
                    afterBalance = NumberUtil.sub(beforeBalance, transactionAmount);
                } else if (TransactionSubTypeEnum.ProductActivityStorageFee.equals(transactionSubType)) {  // 扣除仓储费时，允许把分销商钱包扣成负数
                    afterBalance = NumberUtil.sub(beforeBalance, transactionAmount);
                } else {
                    throw new WalletException(ZSMallStatusCodeEnum.BULK_WALLET_BALANCE_INSUFFICIENT);
                }
                break;
            default:
                break;
        }

        tenantWallet.setWalletBalance(afterBalance);
        Boolean update = iTenantWalletService.updateByEntity(tenantWallet);
        if (update) {
            transactionRecord.setAfterBalance(afterBalance);
            transactionRecord.setTransactionTime(new Date());
            transactionRecord.setTransactionState(TransactionStateEnum.Success);
            iTransactionRecordService.insertOrUpdateByEntity(transactionRecord);
        } else {
            throw new WalletException(ZSMallStatusCodeEnum.WALLET_UPDATE_FAILURE);
        }
    }


    /**
     * 事件监听 - 查询租户交易相关信息
     *
     * @param sysTenantPaymentEvent 租户交易相关信息事件
     */
    @EventListener
    public void sysTenantPaymentEventListener(SysTenantPaymentEvent sysTenantPaymentEvent) {
        String tenantId = sysTenantPaymentEvent.getInTenantId();
        TenantWallet tenantWallet = iTenantWalletService.queryByTenantId(tenantId,sysTenantPaymentEvent.getCurrency());

        if (tenantWallet != null) {
            sysTenantPaymentEvent.setTenantPayment(new SysTenantPaymentEvent.TenantPayment(tenantWallet.getWalletBalance()));
        }
    }
}
