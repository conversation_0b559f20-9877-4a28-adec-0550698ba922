package com.zsmall.system.biz.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.mapper.SysTenantMapper;
import com.zsmall.common.enums.transaction.TransactionMethodEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.system.entity.domain.TransactionReceipt;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.dto.TransactionReceiptDTO;
import com.zsmall.system.entity.iservice.ITransactionReceiptService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/14 11:37
 */
@Component
@RequiredArgsConstructor
public class TransactionReceiptFacade {
    private final ITransactionReceiptService iTransactionReceiptService;
    private final SysTenantMapper sysTenantMapper;
    /**
     * 功能描述：获取交易收据以进行发送
     *
     * @param transactionReceipt transactionReceipt
     * @param transactionRecord
     * @return {@link TransactionReceiptDTO }
     * <AUTHOR>
     * @date 2024/09/14
     */
    public TransactionReceiptDTO getTransactionReceiptForSend(TransactionReceipt transactionReceipt,
                                                              TransactionRecord transactionRecord) {
        if(ObjectUtil.isEmpty(transactionReceipt)){
            return null;
        }
        BigDecimal afterBalance = transactionRecord.getAfterBalance();
        BigDecimal transactionAmount = transactionReceipt.getTransactionAmount();
        String note = transactionReceipt.getNote();
        TransactionTypeEnum transactionType = transactionReceipt.getTransactionType();
        TransactionMethodEnum transactionMethod = transactionReceipt.getTransactionMethod();
        if(!(TransactionMethodEnum.OnlinePayoneer.equals(transactionMethod))||!(TransactionTypeEnum.Recharge.equals(transactionRecord.getTransactionType()))){
            // do something
            return null;
        }
        String tenantId = transactionReceipt.getTenantId();
        LambdaQueryWrapper<SysTenant> eq = new LambdaQueryWrapper<SysTenant>().eq(SysTenant::getTenantId, tenantId);
        SysTenant sysTenant = TenantHelper.ignore(() -> sysTenantMapper.selectOne(eq));
        TransactionReceiptDTO transactionReceiptDTO = new TransactionReceiptDTO();
        BeanUtil.copyProperties(transactionReceipt, transactionReceiptDTO);
        transactionReceiptDTO.setBankAcc("Liangyan");
        transactionReceiptDTO.setAccountName("Liangyan");
        // 暂时写死USD
        transactionReceiptDTO.setCurrency("USD");
        transactionReceiptDTO.setOppAccName(sysTenant.getThirdChannelFlag());
        transactionReceiptDTO.setOppAccNo(sysTenant.getThirdChannelFlag());
        transactionReceiptDTO.setSerialId(transactionRecord.getTransactionNo());
        transactionReceiptDTO.setTradeDate(transactionRecord.getTransactionTime());
        transactionReceiptDTO.setTenantType("distributor");

        // 收入
        if(TransactionTypeEnum.Income.equals(transactionType)||TransactionTypeEnum.Recharge.equals(transactionType)){
            transactionReceiptDTO.setCdSign((byte) 0);
            transactionReceiptDTO.setIncomeAmount(transactionAmount);
        }
        if(TransactionTypeEnum.Withdrawal.equals(transactionType)||TransactionTypeEnum.Expenditure.equals(transactionType)){
            transactionReceiptDTO.setCdSign((byte) 1);
            transactionReceiptDTO.setOutlayAmount(transactionAmount);
        }
        transactionReceiptDTO.setAbs(note);
        transactionReceiptDTO.setAccountBalance(afterBalance);
        return transactionReceiptDTO;
    }
}
