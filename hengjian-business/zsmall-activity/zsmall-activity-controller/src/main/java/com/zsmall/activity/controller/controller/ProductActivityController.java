package com.zsmall.activity.controller.controller;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.activity.biz.service.ProductActivityService;
import com.zsmall.activity.entity.domain.bo.productActivity.*;
import com.zsmall.activity.entity.domain.vo.productActivity.*;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAndStockVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 商品活动相关
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/business/productActivity")
public class ProductActivityController {

    private final ProductActivityService productActivityService;

    /**
     * 分页查询活动可用商品列表
     */
    @GetMapping("/getProductList")
    public TableDataInfo<ProductSkuAndStockVo> getProductList(ProductByActivityBo bo, PageQuery pageQuery) {
        return productActivityService.getProductList(bo, pageQuery);
    }

    /**
     * 分页查询商品活动列表（自动分发至各角色不同处理逻辑）
     */
    @GetMapping("/getProductActivityPage")
    public TableDataInfo getProductActivityPage(ProductActivityQueryBo bo, PageQuery pageQuery) {
        return productActivityService.getProductActivityPage(bo, pageQuery);
    }

    /**
     * 创建商品活动
     */
    @PostMapping("/createActivity")
    public R<Void> createActivity(@RequestBody ProductActivityBaseBo bo) {
        return productActivityService.createActivity(bo);
    }

    /**
     * 查询商品活动草稿
     */
    @GetMapping("/getActivityDraft")
    public R<ProductActivityDraftVo> getActivityDraft(String activityID) {
        return productActivityService.getActivityDraft(activityID);
    }

    /**
     * 查询商品活动详情
     */
    @GetMapping("/getActivityDetail")
    public R<ProductActivityDetailBaseVo> getActivityDetail(String activityID) {
        return productActivityService.getActivityDetail(activityID);
    }

    /**
     * 锁货转圈货（分销商）
     */
    @PostMapping("/stockLockToBuyout")
    public R<Void> stockLockToBuyout(@RequestBody StockLockToBuyoutBo bo) {
        return productActivityService.stockLockToBuyout(bo);
    }

    /**
     * 更新活动状态（供货商、分销商）
     */
    @PostMapping("/updateState")
    public R<Void> updateState(@RequestBody ActivityUpdateBo bo) {
        return productActivityService.updateState(bo);
    }

    /**
     * 分页查询临期商品活动列表
     */
    @GetMapping("/queryExpiringActivityPage")
    public TableDataInfo<ExpiringActivityItemQueryVo> queryExpiringActivityPage(ExpiringActivityQueryBo bo, PageQuery pageQuery) {
        return productActivityService.queryExpiringActivityPage(bo, pageQuery);
    }

    /**
     * 查询分销商活动库存信息
     */
    @GetMapping("/queryActivityStockItem")
    public R<ActivityStockDVo> queryActivityStockItem(String activityID) {
        return productActivityService.queryActivityStockItem(activityID);
    }

    /**
     * 分页查询活动资金信息（管理员）
     */
    @GetMapping("/queryActivityPricePage")
    public ProductActivityPriceTableVo<ProductActivityPriceVo> queryActivityPricePage(ProductActivityPriceBo bo, PageQuery pageQuery) {
        return productActivityService.queryActivityPricePage(bo, pageQuery);
    }

    /**
     * 导出活动资金信息（管理员）
     */
    @GetMapping("/exportActivityPrice")
    public R<Void> exportActivityPrice(ProductActivityPriceBo bo) {
        return productActivityService.exportActivityPrice(bo);
    }

    /**
     * 取消分销商活动（管理员）
     */
    @PostMapping("/cancelActivityByManager")
    public R<Void> cancelActivityByManager(@Validated @RequestBody CancelBulkActivityBo bo) throws Exception {
        return productActivityService.cancelActivityByManager(bo);
    }

    /**
     * 调整分销商活动（管理员）
     */
    @PostMapping("/adjustActivityByManager")
    public R<Void> adjustActivityByManager(@Validated @RequestBody AdjustActivityDBo bo, HttpServletRequest request) throws Exception {
        return productActivityService.adjustActivityByManager(bo, request);
    }
}
