package com.zsmall.activity.entity.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.constant.AbstractCodeTypeBase;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.activity.entity.iservice.IChinaSpotProductService;
import com.zsmall.activity.entity.iservice.IChinaSpotProductSkuService;
import com.zsmall.activity.entity.iservice.IProductActivityItemService;
import com.zsmall.activity.entity.iservice.IProductActivityService;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.service.RedisCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 活动相关编码生成器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActivityCodeGenerator extends RedisCodeGenerator {

    private final IProductActivityService iProductActivityService;
    private final IProductActivityItemService iProductActivityItemService;
    private final IChinaSpotProductService iChinaSpotProductService;
    private final IChinaSpotProductSkuService iChinaSpotProductSkuService;

    /**
     * 编号生成器
     *
     * @param type 主类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type) throws RStatusCodeException {
        log.info("编号生成器 type = {}, subType = {}", type);
        var value = type.getValue();
        String code = null;
        var repeat = true;
        while (repeat) {
            if (BusinessCodeEnum.ProductActivityStockLock.equals(type) || BusinessCodeEnum.ProductActivityBuyout.equals(type)) {
                code = StrUtil.builder(value, RandomUtil.randomNumbers(1), super.getMillisecond(), RandomUtil.randomNumbers(2)).toString();
                repeat = iProductActivityService.existActivityCode(code);
            } else if (BusinessCodeEnum.ChinaSpotProductCode.equals(type)) {
                code = value + RandomUtil.randomStringUpper(6);
                repeat = iChinaSpotProductService.existProductCode(code);
            } else if (BusinessCodeEnum.ChinaSpotProductSkuCode.equals(type)) {
                code = value + RandomUtil.randomStringUpper(6);
                repeat = iChinaSpotProductSkuService.existProductCode(code);
            } else {
                repeat = false;
            }
        }
        return code;
    }

    /**
     * 编号生成器
     *
     * @param type    主类型
     * @param subType 子类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type, String subType) {
        String code = null;
        var repeat = true;
        while (repeat) {
            code = new StringBuilder(subType).append("-").append(super.getMillisecond()).append(RandomUtil.randomNumbers(1)).toString();
            repeat = iProductActivityItemService.existActivityCode(code);
        }
        return code;
    }
}
