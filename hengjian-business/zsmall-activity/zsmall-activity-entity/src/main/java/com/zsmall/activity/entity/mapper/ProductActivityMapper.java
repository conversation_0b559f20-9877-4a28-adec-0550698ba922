package com.zsmall.activity.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.zsmall.activity.entity.domain.ProductActivity;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityPriceBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityQueryBo;
import com.zsmall.activity.entity.domain.vo.productActivity.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface ProductActivityMapper extends TenantMapperPlus<ProductActivity, ProductActivity> {

    List<ProductActivity> queryByAll(ProductActivity productActivity);

    IPage<ProductActivityQueryVo> queryPage(@Param("bo") ProductActivityQueryBo bo, Page page);

    ProductActivityDraftVo queryDraftVo(String activityCode);

    @InterceptorIgnore(tenantLine = "true")
    ProductActivityDetailVo queryDetailVoSupplier(@Param("activityCode") String activityCode, @Param("tenantId") String tenantId);

    @InterceptorIgnore(tenantLine = "true")
    ProductActivityDetailVo queryDetailVoManager(@Param("activityCode") String activityCode);

    @InterceptorIgnore(tenantLine = "true")
    List<MpProductActivitySelectVo> queryMpProductActivitySelect(@Param("productCode") String productCode, @Param("productSkuCode") String productSkuCode, @Param("activityType") String activityType);

    @InterceptorIgnore(tenantLine = "true")
    IPage<ProductActivityPriceVo> queryActivityPricePage(@Param("bo") ProductActivityPriceBo bo, Page page);

    @InterceptorIgnore(tenantLine = "true")
    BigDecimal sumPlatformTotalAmount();

    ProductActivity queryActivityByActivityItemNo(@Param("activityItemCode") String activityItemCode);

}
