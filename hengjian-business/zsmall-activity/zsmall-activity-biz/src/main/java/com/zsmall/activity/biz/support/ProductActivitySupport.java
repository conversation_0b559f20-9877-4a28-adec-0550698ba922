package com.zsmall.activity.biz.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.domain.RStatusCodeBase;
import com.hengjian.common.core.enums.MessageTitleEnum;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.extend.verifycode.utils.EmailSmsSendUtils;
import com.hengjian.stream.mqProducer.domain.MessageDto;
import com.hengjian.stream.mqProducer.domain.MessageDtoBuilder;
import com.hengjian.stream.mqProducer.producer.RedisProducer;
import com.hengjian.system.domain.vo.SysUserVo;
import com.zsmall.activity.biz.factory.ProductActivityFactory;
import com.zsmall.activity.entity.domain.*;
import com.zsmall.activity.entity.domain.bo.productActivity.ActivityStockBo;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityBaseBo;
import com.zsmall.activity.entity.domain.dto.ActivityLockDTO;
import com.zsmall.activity.entity.domain.dto.ExpiringActivityItemDTO;
import com.zsmall.activity.entity.domain.vo.productActivity.ExpiringActivityItemQueryVo;
import com.zsmall.activity.entity.iservice.*;
import com.zsmall.activity.entity.util.ActivityCodeGenerator;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.productActivity.*;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.StockException;
import com.zsmall.common.exception.WalletException;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.extend.utils.ZSMallProductEventUtils;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceBo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.iservice.IProductSkuAttachmentService;
import com.zsmall.product.entity.iservice.IProductSkuPriceRuleService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.iservice.ITransactionsProductActivityItemService;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商品活动相关支持
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductActivitySupport {

    private final IProductActivityService iProductActivityService;
    private final IProductActivityItemService iProductActivityItemService;
    private final IProductActivityPriceService iProductActivityPriceService;
    private final IProductActivityPriceItemService iProductActivityPriceItemService;
    private final IProductActivityStockService iProductActivityStockService;
    private final IProductActivityStockItemService iProductActivityStockItemService;
    private final IProductActivityCheckoutService iProductActivityCheckoutService;
    private final IProductActivityBuyoutService iProductActivityBuyoutService;
    private final IProductActivityBuyoutItemService iProductActivityBuyoutItemService;
    private final IProductActivityStockLockService iProductActivityStockLockService;
    private final IProductActivityStockLockItemService iProductActivityStockLockItemService;
    private final IProductActivityReviewRecordService iProductActivityReviewRecordService;

    private final IProductSkuService iProductSkuService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IProductSkuPriceRuleService iProductSkuPriceRuleService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IWarehouseService iWarehouseService;
    private final ITransactionsProductActivityItemService iTransactionsProductActivityItemService;

    private final BusinessParameterService businessParameterService;

    private final ProductActivityFactory productActivityFactory;
    private final ActivityCodeGenerator activityCodeGenerator;
    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final BillSupport billSupport;
    private final RedisProducer redisProducer;

    private final TenantWalletService tenantWalletService;


    /**
     * 创建活动草稿-通用参数
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public ProductActivity createActivityDraft(ProductActivityBaseBo bo) throws RStatusCodeException {
        log.info("创建活动草稿-通用参数 ProductActivityBaseBo = {}", JSONUtil.toJsonStr(bo));

        String activityName = bo.getActivityName();
        String productSkuCode = bo.getProductSkuCode();
        Integer quantityMinimum = bo.getQuantityMinimum();
        BigDecimal activityUnitPrice = bo.getActivityUnitPrice();
        BigDecimal activityOperationFee = bo.getActivityOperationFee();
        BigDecimal activityFinalDeliveryFee = bo.getActivityFinalDeliveryFee();
        BigDecimal activityStorageFee = bo.getActivityStorageFee();

        ProductActivityStateEnum activityStatusEnum = ProductActivityStateEnum.valueOf(bo.getActivityState());
        ProductActivityTypeEnum activityTypeEnum = ProductActivityTypeEnum.valueOf(bo.getActivityType());
        List<ActivityStockBo> stockList = bo.getStockList();

        String activityCode = bo.getActivityID();
        Integer quantityTotal = bo.getQuantityTotal();

        // 根据productSkuCode查询商品信息
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        if (productSku == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
        }
        Long productSkuId = productSku.getId();
        ProductSkuAttachmentVo firstImage = iProductSkuAttachmentService.queryFirstImageByProductSkuId(productSkuId);

        // 是否是编辑
        boolean isUpdate = false;
        // 查询是否存在活动，存在则为编辑模式
        ProductActivity productActivity;
        ProductActivityPrice productActivityPrice;
        if (StrUtil.isNotBlank(activityCode)) {
            isUpdate = true;
            // 传activityID，更新
            productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder().activityCode(activityCode).activityType(activityTypeEnum).build());
            if (productActivity == null) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.ACTIVITY_NOT_EXIST);
            } else {
                productActivityPrice = iProductActivityPriceService.queryOneByEntity(ProductActivityPrice.builder().productActivityId(productActivity.getId()).build());
            }
        } else {
            productActivity = ProductActivity.builder().build();
            productActivityPrice = ProductActivityPrice.builder().build();

            activityCode = activityCodeGenerator.codeGenerate(BusinessCodeEnum.valueOf("ProductActivity" + activityTypeEnum.name()));
            productActivity.setActivityCode(activityCode);
            productActivityPrice.setActivityCode(activityCode);
        }

        //保存活动信息
        productActivity.setActivityName(activityName);
        productActivity.setActivityType(activityTypeEnum);
        productActivity.setProductImg(firstImage.getAttachmentShowUrl());
        productActivity.setProductSku(productSku.getSku());
        productActivity.setProductSkuCode(productSkuCode);
        productActivity.setProductCode(productSku.getProductCode());
        productActivity.setProductName(productSku.getName());
        productActivity.setQuantityTotal(quantityTotal);
        productActivity.setQuantitySurplus(quantityTotal);
        productActivity.setQuantityMinimum(quantityMinimum);
        productActivity.setActivityState(activityStatusEnum);
        iProductActivityService.saveOrUpdate(productActivity);
        Long activityId = productActivity.getId();

        // 活动订金比例
        JSONObject ACTIVITY_DEPOSIT_PRICE_PERCENT = businessParameterService.getValueFromJSONObject(BusinessParameterType.ACTIVITY_DEPOSIT_PRICE_PERCENT);
        BigDecimal depositPricePercent = new BigDecimal(ACTIVITY_DEPOSIT_PRICE_PERCENT.getStr(activityTypeEnum.name()));

        productActivityPrice.setActivityCode(activityCode);
        // 商品活动定价表数据处理和保存
        productActivityPrice.setProductActivityId(activityId);
        // 供货商设置
        productActivityPrice.setActivityUnitPrice(activityUnitPrice);
        productActivityPrice.setActivityOperationFee(activityOperationFee);
        productActivityPrice.setActivityFinalDeliveryFee(activityFinalDeliveryFee);
        productActivityPrice.setActivityStorageFee(activityStorageFee);
        // 活动自提价（活动单价+活动操作费）
        BigDecimal activityPuPrice = NumberUtil.add(activityUnitPrice, activityOperationFee);
        // 活动代发价（活动单价+活动操作费+活动尾程派送费）
        BigDecimal activityDsPrice = NumberUtil.add(activityUnitPrice, activityOperationFee, activityFinalDeliveryFee);
        productActivityPrice.setActivityPickUpPrice(activityPuPrice);
        productActivityPrice.setActivityDropShippingPrice(activityDsPrice);

        // 活动订金单价（活动自提价*一定比例后得出）
        BigDecimal activityDepositUnitPrice = NumberUtil.mul(activityPuPrice, depositPricePercent).setScale(2, RoundingMode.HALF_UP);;
        // 活动尾款单价（活动自提价-活动订金单价后得出）
        BigDecimal activityBalanceUnitPrice = NumberUtil.sub(activityPuPrice, activityDepositUnitPrice);
        // 活动订金总价（活动订金单价*数量得出）
        BigDecimal activityDepositTotalPrice = NumberUtil.mul(activityDepositUnitPrice, quantityTotal);
        productActivityPrice.setActivityDepositUnitPrice(activityDepositUnitPrice);
        productActivityPrice.setActivityBalanceUnitPrice(activityBalanceUnitPrice);
        productActivityPrice.setActivityDepositTotalPrice(activityDepositTotalPrice);

        ProductSkuPriceBo priceBo = new ProductSkuPriceBo();
        priceBo.setProductSkuId(productSkuId);
        priceBo.setOriginalUnitPrice(activityUnitPrice);
        priceBo.setOriginalOperationFee(activityOperationFee);
        priceBo.setOriginalFinalDeliveryFee(activityFinalDeliveryFee);

        // 查询平台定价公式，计算平台价
        ProductSkuPrice productSkuPrice = iProductSkuPriceRuleService.queryByPriceRange(priceBo);
        if (productSkuPrice == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SYSTEM_ERROR_E10028);
        }

        // 平台三个价格
        BigDecimal platformUnitPriceMd = productSkuPrice.getPlatformUnitPrice();
        BigDecimal platformOperationFeeMd = productSkuPrice.getPlatformOperationFee();
        BigDecimal platformFinalDeliveryFeeMd = productSkuPrice.getPlatformFinalDeliveryFee();
        productActivityPrice.setPlatformUnitPrice(platformUnitPriceMd);
        productActivityPrice.setPlatformOperationFee(platformOperationFeeMd);
        productActivityPrice.setPlatformFinalDeliveryFee(platformFinalDeliveryFeeMd);

        // 平台自提价（平台自提单价+平台自提操作费）
        BigDecimal platformPickUpPrice = NumberUtil.add(platformUnitPriceMd, platformOperationFeeMd);
        productActivityPrice.setPlatformPickUpPrice(platformPickUpPrice);
        // 平台代发价（平台代发单价+平台代发操作费+平台代发尾程派送费）
        BigDecimal platformDropShippingPrice = NumberUtil.add(platformPickUpPrice, platformFinalDeliveryFeeMd);
        productActivityPrice.setPlatformDropShippingPrice(platformDropShippingPrice);

        // 平台订金单价（根据平台设置的平台自提价*一定比例后得出）
        BigDecimal platformDepositUnitPrice = NumberUtil.mul(platformPickUpPrice, depositPricePercent).setScale(2, RoundingMode.HALF_UP);;
        // 平台尾款单价（根据平台设置的平台自提价-平台订金单价后得出）
        BigDecimal platformBalanceUnitPrice = NumberUtil.sub(platformPickUpPrice, platformDepositUnitPrice);
        // 活动订金总价（活动订金单价*数量得出）
        BigDecimal platformDepositTotalPrice = NumberUtil.mul(platformDepositUnitPrice, quantityTotal);
        productActivityPrice.setPlatformDepositUnitPrice(platformDepositUnitPrice);
        productActivityPrice.setPlatformBalanceUnitPrice(platformBalanceUnitPrice);
        productActivityPrice.setPlatformDepositTotalPrice(platformDepositTotalPrice);
        // 商品价格规则Id
        productActivityPrice.setProductSkuPriceRuleId(productSkuPrice.getProductSkuPriceRuleId());

        // 每种活动都有不同的处理方式
        switch (activityTypeEnum) {
            case Buyout:
                ProductActivityBuyout buyout;
                if (isUpdate) {
                    //查询要更新的圈货信息
                    buyout = iProductActivityBuyoutService.queryByProductActivityId(activityId);
                } else {
                    buyout = new ProductActivityBuyout();
                }

                buyout.setActivityCode(activityCode);
                buyout.setProductActivityId(activityId);
                buyout.setBuyoutQuantityTotal(quantityTotal);
                buyout.setBuyoutQuantitySurplus(quantityTotal);
                buyout.setBuyoutQuantityMinimum(quantityMinimum);
                iProductActivityBuyoutService.saveOrUpdate(buyout);
                break;
            case StockLock:
                ProductActivityStockLock stockLock;
                if (isUpdate) {
                    //查询要更新的锁货信息
                    stockLock = iProductActivityStockLockService.queryByProductActivityId(activityId);
                } else {
                    stockLock = new ProductActivityStockLock();
                }

                stockLock.setActivityCode(activityCode);
                stockLock.setProductActivityId(activityId);
                stockLock.setStockLockDays(bo.getActivityTime());
                stockLock.setFreeStoragePeriod(bo.getFreeStoragePeriod());
                stockLock.setStockLockQuantityMinimum(quantityMinimum);
                stockLock.setStockLockQuantityTotal(quantityTotal);
                stockLock.setStockLockQuantitySurplus(quantityTotal);
                iProductActivityStockLockService.saveOrUpdate(stockLock);
                break;
            default:
                break;
        }

        iProductActivityPriceService.saveOrUpdate(productActivityPrice);

        // 当前为UPDATE，需要删除原有的库存信息
        if (isUpdate) {
            iProductActivityStockService.deleteByProductActivityId(activityId);
        }

        //保存库存信息
        List<ProductActivityStock> activityStockList = new ArrayList<>();
        for (ActivityStockBo stockBo : stockList) {
            Integer quantity = stockBo.getQuantity();
            String warehouseSystemCode = stockBo.getWarehouseSystemCode();

            ProductActivityStock newStock = new ProductActivityStock();
            newStock.setActivityCode(activityCode);
            newStock.setProductActivityId(activityId);
            newStock.setWarehouseSystemCode(warehouseSystemCode);
            newStock.setQuantityTotal(quantity);
            newStock.setQuantitySurplus(quantity);
            activityStockList.add(newStock);
        }
        iProductActivityStockService.saveBatch(activityStockList);
        return productActivity;
    }

    /**
     * 加锁创建活动
     *
     * @param bo
     * @throws RStatusCodeException
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public void lockCreateActivity(ProductActivityBaseBo bo) throws RStatusCodeException {
        String productSkuCode = bo.getProductSkuCode();
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);

        if (productSku == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXIST);
        }

        // 活动创建成功，开锁扣减库存
        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_PRODUCT_SKU_STOCK_LOCK + productSkuCode;
        RLock lock = client.getLock(key);

        try {
            lock.lock(10L, TimeUnit.SECONDS);
            log.info("创建活动[{}]加锁成功！", key);
            createActivity(bo, productSku);
        } catch (RStatusCodeException e) {
            log.error("加锁创建活动，出现业务错误，原因 {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("加锁创建活动，出现未知错误，原因 {}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SYSTEM_BUSY);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.info("创建活动[{}]解锁成功！", key);
        }

    }

    /**
     * 创建活动-通用参数
     */
    public void createActivity(ProductActivityBaseBo bo, ProductSku productSku) {
        log.info("创建活动-通用参数 bo = {}", JSONUtil.toJsonStr(bo));
        try {
            String activityState = bo.getActivityState();
            String productSkuCode = bo.getProductSkuCode();

            ProductActivityStateEnum activityStateEnum = ProductActivityStateEnum.valueOf(activityState);
            List<ActivityStockBo> stockList = bo.getStockList();

            // 再次校验库存是否充足
            for (ActivityStockBo stockBo : stockList) {
                String warehouseSystemCode = stockBo.getWarehouseSystemCode();
                Integer stockQuantity = stockBo.getQuantity();

                ProductSkuStock productSkuStock = iProductSkuStockService.queryByProductSkuCode(productSkuCode, warehouseSystemCode);
                Integer stockAvailable = productSkuStock.getStockAvailable();
                //库存不足
                if (stockQuantity > stockAvailable) {
                    Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCode(warehouseSystemCode);
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.STOCK_LOCK_QUANTITY_NOT_ENOUGH.args(warehouse != null ? warehouse.getWarehouseName() : warehouseSystemCode));
                }
                // 变为负数，方便下面扣减
                stockBo.setQuantity(stockQuantity);
            }

            //创建活动
            ProductActivity productActivity = createActivityDraft(bo);
            //如果供应商直接提交了审核，则保存审核记录
            if (ProductActivityStateEnum.UnderReview.equals(activityStateEnum)) {
                ProductActivityReviewRecord reviewRecordEntity = new ProductActivityReviewRecord();
                reviewRecordEntity.setActivityOriginState(activityStateEnum);
                reviewRecordEntity.setProductActivityId(productActivity.getId());
                reviewRecordEntity.setReviewState(ProductActivityReviewState.Pending);
                iProductActivityReviewRecordService.save(reviewRecordEntity);
            }

            stockList.forEach(stock -> stock.setQuantity(stock.getQuantity() * -1));
            handleActivityProductStock(productSkuCode, stockList);
            ZSMallProductEventUtils.setProductSkuStock(productSku);
            iProductSkuService.updateById(productSku);
        } catch (RStatusCodeException e) {
            throw e;
        } catch (DataIntegrityViolationException e) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.TOTAL_AMOUNT_IS_TOO_LARGE);
        } catch (Exception e) {
            log.error("创建活动出现未知异常，原因 {}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.ACTIVITY_CREATE_ERROR);
        }
    }

    /**
     * 加锁参加活动
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public void lockParticipateActivity(ActivityLockDTO activityLockDTO) {
        ProductActivity activity = activityLockDTO.getActivity();
        List<ActivityStockBo> reqStockList = activityLockDTO.getReqStockList();

        Long activityId = activity.getId();
        String activityCode = activity.getActivityCode();

        Integer quantityMinimum = activityLockDTO.getQuantityMinimum();
        Integer quantityRequired = activityLockDTO.getQuantityRequired();
        // 需求量小于最低起订量
        if (quantityMinimum > quantityRequired) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.QUANTITY_LESS_MINIMUM_QUANTITY);
        }

        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_PRODUCT_ACTIVITY_LOCK + activityCode;
        RLock lock = client.getLock(key);

        try {
            lock.lock(10L, TimeUnit.SECONDS);
            log.info("参加活动[{}]加锁成功！", key);

            //判断每一个仓库的库存是否足够扣除 持锁后，第二次判断  保存分销商和供应商的锁货库存信息
            List<ProductActivityStockItem> stockItemList = new ArrayList<>();
            List<ProductActivityStock> stockList = new ArrayList<>();
            for (ActivityStockBo stockBo : reqStockList) {
                Integer quantity = stockBo.getQuantity();
                String warehouseSystemCode = stockBo.getWarehouseSystemCode();

                ProductActivityStock productActivityStock = iProductActivityStockService.queryByActivityIdAndWarehouseSysCode(activityId, warehouseSystemCode);

                Integer quantitySurplus = productActivityStock.getQuantitySurplus();
                //如果剩余库存小于锁货库存，则抛出异常
                if (quantitySurplus < quantity) {
                    log.info("活动库存不足");
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.STOCK_LOCK_QUANTITY_NOT_ENOUGH.args(warehouseSystemCode));
                }

                // 分销商库存信息
                ProductActivityStockItem stockItem = new ProductActivityStockItem();
                stockItem.setWarehouseSystemCode(warehouseSystemCode);
                stockItem.setQuantityTotal(quantity);
                stockItem.setQuantitySurplus(quantity);
                stockItemList.add(stockItem);

                //供货商信息
                quantitySurplus -= quantity;
                productActivityStock.setQuantitySurplus(quantitySurplus);
                stockList.add(productActivityStock.resetSold());
            }

            // 分销商所需数量
            activityLockDTO.setStockList(stockList);
            activityLockDTO.setStockItemList(stockItemList);

            log.info("活动库存充足，开始生成分销商活动数据");
            createActivityItem(activityLockDTO);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.info("参加活动[{}]解锁成功！", key);
        }
    }

    /**
     * 创建子活动（分销商）
     */
    public void createActivityItem(ActivityLockDTO activityLockDTO) throws RStatusCodeException {
        String distributorId = activityLockDTO.getDistributorId();

        // 需求总量
        Integer quantityRequired = activityLockDTO.getQuantityRequired();
        List<ProductActivityStock> stockList = activityLockDTO.getStockList();
        List<ProductActivityStockItem> stockItemList = activityLockDTO.getStockItemList();

        ProductActivity activity = activityLockDTO.getActivity();
        log.info("创建子活动（分销商） distributorId = {} , quantityRequired = {} ", distributorId, quantityRequired);
        log.info("创建子活动（分销商） stockList = {} , stockItemList = {} ", JSONUtil.toJsonStr(stockList), JSONUtil.toJsonStr(stockItemList));

        Long productActivityId = activity.getId();
        String activityCodeParent = activity.getActivityCode();
        String productName = activity.getProductName();
        String productSku = activity.getProductSku();
        String productImg = activity.getProductImg();
        String productSkuCode = activity.getProductSkuCode();
        String productCode = activity.getProductCode();
        ProductActivityTypeEnum activityType = activity.getActivityType();
        ProductActivityStateEnum activityState = activity.getActivityState();

        // 查询活动定价信息表
        ProductActivityPrice activityPrice = iProductActivityPriceService.queryOneByEntity(ProductActivityPrice.builder().productActivityId(productActivityId).build());

        // 新增分销商活动信息
        ProductActivityItem.ProductActivityItemBuilder productActivityItemBuilder = ProductActivityItem.builder();

        String activityCode = activityCodeGenerator.codeGenerate(BusinessCodeEnum.valueOf("ProductActivity" + activityType.name()), activityCodeParent);
        productActivityItemBuilder.activityState(ProductActivityItemStateEnum.InProgress);
        productActivityItemBuilder.activityType(activityType);
        productActivityItemBuilder.productName(productName);
        productActivityItemBuilder.productSku(productSku);
        productActivityItemBuilder.productImg(productImg);
        productActivityItemBuilder.productSkuCode(productSkuCode);
        productActivityItemBuilder.productCode(productCode);
        productActivityItemBuilder.activityCode(activityCode);
        productActivityItemBuilder.activityCodeParent(activityCodeParent);
        productActivityItemBuilder.quantityTotal(quantityRequired);
        productActivityItemBuilder.quantitySurplus(quantityRequired);

        ProductActivityItem productActivityItem = productActivityItemBuilder.build();
        productActivityItem.setTenantId(distributorId);

        iProductActivityItemService.save(productActivityItem);
        Long activityItemId = productActivityItem.getId();

        // set进DTO，准备下面生成日志
        activityLockDTO.setActivityItem(productActivityItem);

        // 活动订金单价（活动自提价*一定比例后得出）
        BigDecimal activityDepositUnitPrice = activityPrice.getActivityDepositUnitPrice();
        // 活动订金总价（活动订金单价*数量得出）
        BigDecimal activityDepositTotalPrice = NumberUtil.mul(activityDepositUnitPrice, quantityRequired);
        // 活动自提价（活动单价+活动操作费）
        BigDecimal activityPickUpPrice = activityPrice.getActivityPickUpPrice();
        // 活动总价（活动自提价*数量得出）
        BigDecimal activityTotalPrice = NumberUtil.mul(activityPickUpPrice, quantityRequired);

        // 需要支付的金额为订金 * 需求数量
        BigDecimal platformDepositUnitPrice = activityPrice.getPlatformDepositUnitPrice();
        // 平台订金总价（平台订金单价*数量后得出，需要用于分销商参加活动时支付）
        BigDecimal platformDepositTotalPrice = NumberUtil.mul(platformDepositUnitPrice, quantityRequired).setScale(2, RoundingMode.HALF_UP);
        // 平台总价（平台自提价*数量后得出，表示分销商参与此次活动的总价）
        BigDecimal platformPickUpPrice = activityPrice.getPlatformPickUpPrice();
        BigDecimal platformTotalPrice = NumberUtil.mul(platformPickUpPrice, quantityRequired).setScale(2, RoundingMode.HALF_UP);

        // 新增分销商活动定价信息
        ProductActivityPriceItem productActivityPriceItem = BeanUtil.toBean(activityPrice, ProductActivityPriceItem.class);
        productActivityPriceItem.setId(null);
        productActivityPriceItem.setActivityCode(activityCode);
        productActivityPriceItem.setActivityCodeParent(activityCodeParent);
        productActivityPriceItem.setProductActivityItemId(activityItemId);
        productActivityPriceItem.setActivityDepositTotalPrice(activityDepositTotalPrice);
        productActivityPriceItem.setActivityDepositSurplusPrice(activityDepositTotalPrice);
        productActivityPriceItem.setActivityTotalPrice(activityTotalPrice);
        productActivityPriceItem.setPlatformDepositTotalPrice(platformDepositTotalPrice);
        productActivityPriceItem.setPlatformDepositSurplusPrice(platformDepositTotalPrice);
        productActivityPriceItem.setPlatformTotalPrice(platformTotalPrice);
        iProductActivityPriceItemService.save(productActivityPriceItem);

        // 各个活动的单独处理
        if (ProductActivityTypeEnum.StockLock.equals(activityType)) {  // 锁货单独处理
            ProductActivityStockLock activityStockLock = activityLockDTO.getStockLock();
            Integer stockLockDays = activityStockLock.getStockLockDays();
            Integer freeStoragePeriod = activityStockLock.getFreeStoragePeriod();
            Integer stockLockQuantityAlready = activityStockLock.getStockLockQuantityAlready();
            Integer stockLockQuantitySurplus = activityStockLock.getStockLockQuantitySurplus();

            //到期时间 锁货当天+锁货期限+1的23:59:59
            Date date = new Date();
            DateTime endDate = DateUtil.offsetDay(date, stockLockDays + 1);
            DateTime expiryDateTime = DateUtil.beginOfDay(endDate);
            //免仓期到期时间 锁货当天+免仓期+1
            DateTime freeStorageEndDate = DateUtil.offsetDay(date, freeStoragePeriod + 1);
            freeStorageEndDate = DateUtil.beginOfDay(freeStorageEndDate);

            ProductActivityStockLockItem stockLockItem = new ProductActivityStockLockItem();
            stockLockItem.setTenantId(distributorId);
            stockLockItem.setActivityCodeParent(activityCodeParent);
            stockLockItem.setActivityCode(activityCode);
            stockLockItem.setProductActivityItemId(activityItemId);
            stockLockItem.setStockLockQuantity(quantityRequired);
            stockLockItem.setStockLockSurplus(quantityRequired);
            stockLockItem.setExpiryDateTime(expiryDateTime);
            stockLockItem.setStockLockDays(stockLockDays);
            stockLockItem.setFreeStoragePeriod(freeStoragePeriod);
            stockLockItem.setFreeStorageExpiryDate(freeStorageEndDate);
            iProductActivityStockLockItemService.save(stockLockItem);

            // 新增分销商锁货库存信息 扣除供应商锁货库存
            stockItemList.forEach(stockItem -> stockItem.setProductActivityItemId(activityItemId).setActivityCode(activityCode));
            iProductActivityStockItemService.saveBatch(stockItemList);
            TenantHelper.ignore(() -> iProductActivityStockService.updateBatchById(stockList));


            // 更新已锁货的库存
            Integer newAlready = stockLockQuantityAlready + quantityRequired;
            Integer newSurplus = stockLockQuantitySurplus - quantityRequired;
            activityStockLock.setStockLockQuantityAlready(newAlready);
            activityStockLock.setStockLockQuantitySurplus(newSurplus);
            TenantHelper.ignore(() -> iProductActivityStockLockService.updateById(activityStockLock));

            // set进DTO，准备下面生成日志
            activityLockDTO.setStockLockItem(stockLockItem);
        } else if (ProductActivityTypeEnum.Buyout.equals(activityType)) {  // 圈货单独处理
            ProductActivityBuyout activityBuyout = activityLockDTO.getBuyout();
            Integer buyoutQuantityAlready = activityBuyout.getBuyoutQuantityAlready();
            Integer buyoutQuantitySurplus = activityBuyout.getBuyoutQuantitySurplus();

            // 新增分销商圈货信息
            ProductActivityBuyoutItem buyoutItem = new ProductActivityBuyoutItem();
            buyoutItem.setTenantId(distributorId);
            buyoutItem.setActivityCodeParent(activityCodeParent);
            buyoutItem.setActivityCode(activityCode);
            buyoutItem.setProductActivityItemId(activityItemId);
            buyoutItem.setBuyoutQuantity(quantityRequired);
            buyoutItem.setBuyoutSurplus(quantityRequired);
            iProductActivityBuyoutItemService.save(buyoutItem);

            // 新增分销商圈货库存信息 扣除供应商圈货库存
            stockItemList.forEach(stockItem -> stockItem.setProductActivityItemId(activityItemId).setActivityCode(activityCode));
            iProductActivityStockItemService.saveBatch(stockItemList);
            TenantHelper.ignore(() -> iProductActivityStockService.updateBatchById(stockList));

            // 更新已锁货的库存
            Integer newAlready = buyoutQuantityAlready + quantityRequired;
            Integer newSurplus = buyoutQuantitySurplus - quantityRequired;
            activityBuyout.setBuyoutQuantityAlready(newAlready);
            activityBuyout.setBuyoutQuantitySurplus(newSurplus);
            TenantHelper.ignore(() -> iProductActivityBuyoutService.updateById(activityBuyout));

            // set进DTO，准备下面生成日志
            activityLockDTO.setBuyoutItem(buyoutItem);
        }

        // 更新主活动的已消耗总数和剩余总数
        Integer quantitySurplus = activity.getQuantitySurplus();
        Integer quantityAlready = activity.getQuantityAlready();
        activity.setQuantitySurplus(quantitySurplus - quantityRequired);
        activity.setQuantityAlready(quantityAlready + quantityRequired);

        // 更新供应商活动状态
        if (ProductActivityStateEnum.Published.equals(activityState)) {
            activity.setActivityState(ProductActivityStateEnum.InProgress);
        }
        TenantHelper.ignore(() -> iProductActivityService.updateById(activity));

        try {
            TransactionSubTypeEnum transactionSubTypeEnum = null;
            if (ProductActivityTypeEnum.StockLock.equals(activityType)) {
                transactionSubTypeEnum = TransactionSubTypeEnum.StockLockDeposit;
            } else if (ProductActivityTypeEnum.Buyout.equals(activityType)) {
                transactionSubTypeEnum = TransactionSubTypeEnum.BuyoutDeposit;
            }

            TransactionRecord transactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
            transactionRecord.setTransactionType(TransactionTypeEnum.Expenditure);
            transactionRecord.setTransactionSubType(transactionSubTypeEnum);
            transactionRecord.setTransactionState(TransactionStateEnum.Processing);
            transactionRecord.setTransactionAmount(platformDepositTotalPrice);
            // 测试用，不上线
            transactionRecord.setCurrency("USD");
            transactionRecord.setCurrencySymbol("$");
            tenantWalletService.walletChanges(transactionRecord);

            iTransactionsProductActivityItemService.saveRelation(transactionRecord.getId(), activityItemId);
        } catch (WalletException e) {
            log.info("【参加活动】钱包支付时出现异常（WalletException） {}", e.getMessage(), e);
            LocaleMessage localeMessage = e.getLocaleMessage();
            throw new RStatusCodeException(localeMessage.toMessage());
        } catch (Exception e) {
            log.info("【参加活动】钱包支付时出现未知异常 {}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PAYMENT_FAILED);
        }

        // 生成日志
        // this.generateActivityLog(activityLockDTO);
    }

    /**
     * 加锁调整库存
     *
     * @param dto
     * @return
     * @throws StockException
     */
    @InMethodLog("商品活动加锁调整库存")
    public String lockAdjustStock(AdjustStockDTO dto) throws StockException {
        String destCountry = dto.getDestCountry();
        String productSkuCode = dto.getProductSkuCode();
        String activityCode = dto.getActivityCode();
        Integer adjustQuantity = dto.getAdjustQuantity();
        String specifyWarehouse = dto.getSpecifyWarehouse();
        Boolean logisticsAccount = dto.getLogisticsAccount();

        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_PRODUCT_ACTIVITY_ITEM_LOCK + activityCode;
        RLock lock = client.getLock(key);

        String finalWarehouseSystemCode = dto.getSpecifyWarehouse();
        try {
            lock.lock(10L, TimeUnit.SECONDS);
            // 持锁后，取消该分销商的活动
            log.info("子活动{} 加锁成功", activityCode);

            ProductActivityItem productActivityItem = iProductActivityItemService.queryOneByEntity(
                ProductActivityItem.builder().activityCode(activityCode).build()
            );

            Long activityItemId = productActivityItem.getId();
            ProductActivityTypeEnum activityType = productActivityItem.getActivityType();
            Integer quantitySurplus = productActivityItem.getQuantitySurplus();

            ProductActivityStockLockItem activityStockLockItem = iProductActivityStockLockItemService.queryByActivityItemId(activityItemId);
            ProductActivityBuyoutItem activityBuyoutItem = iProductActivityBuyoutItemService.queryByActivityItemId(activityItemId);

            // 调整库存数值为负数时（说明是扣减）
            if (adjustQuantity < 0) {
                if (!ProductActivityItemStateEnum.InProgress.equals(productActivityItem.getActivityState())) {
                    throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_NOT_AVAILABLE.args(activityCode));
                }

                // 总剩余库存对比
                if (quantitySurplus < Math.abs(adjustQuantity)) {
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                }

                // 锁货活动还需要判断是否过期
                if (ProductActivityTypeEnum.StockLock.equals(activityType)) {
                    Date expiryDateTime = activityStockLockItem.getExpiryDateTime();
                    //活动过期
                    if (DateUtil.compare(expiryDateTime, new Date()) <= 0) {
                        throw new StockException(ZSMallStatusCodeEnum.ACTIVITY_NOT_AVAILABLE.args(activityCode));
                    }
                }
            } else {  // else说明adjustQuantity>=0，是归还库存
                if (!ProductActivityItemStateEnum.InProgress.equals(productActivityItem.getActivityState())) {

                    // 若子活动不为进行中，则需要将定金退到供货商的账单中，然后直接结束本方法，不再往下归还库存
                    if (productActivityItem != null) {
                        ProductActivity productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder()
                            .activityCode(productActivityItem.getActivityCodeParent()).build());
                        // 开始计算剩余订金及剩余库存
                        ProductActivityPriceItem activityPriceItem =
                            iProductActivityPriceItemService.queryByActivityItemId(activityItemId);
                        BigDecimal activityDepositUnitPrice = activityPriceItem.getActivityDepositUnitPrice();
                        BigDecimal platformDepositUnitPrice = activityPriceItem.getPlatformDepositUnitPrice();

                        //记录扣除的订金到结算表
                        ProductActivityCheckout checkout = new ProductActivityCheckout();
                        checkout.setSupplierTenantId(productActivity.getTenantId());
                        checkout.setDistributorTenantId(productActivityItem.getTenantId());
                        checkout.setProductActivityId(productActivity.getId());
                        checkout.setProductActivityItemId(activityItemId);
                        checkout.setCheckoutType(ActivityCheckoutTypeEnum.PenaltyFee);
                        checkout.setCheckoutUnitPrice(activityDepositUnitPrice);
                        checkout.setCheckoutAmount(NumberUtil.mul(activityDepositUnitPrice, adjustQuantity));
                        checkout.setPlatformCheckoutUnitPrice(platformDepositUnitPrice);
                        checkout.setPlatformCheckoutAmount(NumberUtil.mul(platformDepositUnitPrice, adjustQuantity));
                        checkout.setCheckoutQuantity(adjustQuantity);
                        checkout.setDistributorPay(CheckoutPayEnum.Paid);
                        iProductActivityCheckoutService.save(checkout);
                        iProductActivityPriceItemService.updateById(activityPriceItem);
                        billSupport.generateBillDTOByProductActivityCheckout(checkout, null);
                    }

                    return finalWarehouseSystemCode;
                }

                if (StrUtil.isBlank(specifyWarehouse)) {
                    throw new StockException(ZSMallStatusCodeEnum.BACK_INVENTORY_NO_SPECIAL_WAREHOUSE);
                }
            }

            // 调整指定仓库的库存
            ProductActivityStockItem finalStockItem;
            if (StrUtil.isNotBlank(specifyWarehouse)) {
                // 匹配仓库进行库存调整
                finalStockItem = iProductActivityStockItemService.queryByActivityItemIdAndWarehouse(activityItemId, specifyWarehouse);
            } else if (adjustQuantity < 0) {
                LogisticsTypeEnum logisticsType = dto.getLogisticsType();

                // 不指定仓库时，调整库存（只有扣除才能不指定仓库）
                // 1.查询库存充足的仓库的zipCode集合，去重
                List<ProductActivityStockItem> stockItemList = iProductActivityStockItemService.queryAdequateStockByParams(destCountry, activityItemId, adjustQuantity, logisticsType, logisticsAccount);

                // 自提的因为是全世界范围内选择库存，所以需要判断有没有当前国家的库存，若有则优先取当前国家的库存，没有则取其他国家的随机
                if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                    List<ProductActivityStockItem> currentCountry = stockItemList.stream().filter(stock -> stock.getCountry().equals(destCountry)).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(currentCountry)) {
                        stockItemList = currentCountry;
                    }
                }

                if (CollUtil.size(stockItemList) == 1) {
                    finalStockItem = stockItemList.get(0);
                } else if (CollUtil.size(stockItemList) > 1) {
                    finalStockItem = stockItemList.get(RandomUtil.randomInt(0, CollUtil.size(stockItemList)));
                } else {
                    finalStockItem = null;
                }
            } else {
                finalStockItem = null;
            }

            if (finalStockItem != null) {
                Integer quantityTotal = finalStockItem.getQuantityTotal();
                Integer quantitySold = finalStockItem.getQuantitySold();
                Integer newQuantitySold = NumberUtil.sub(quantitySold, adjustQuantity).intValue();
                Integer newQuantitySurplus = NumberUtil.sub(quantityTotal, newQuantitySold).intValue();

                if (newQuantitySurplus >= 0) {
                    finalStockItem.setQuantitySold(newQuantitySold);
                    finalStockItem.setQuantitySurplus(newQuantitySurplus);

                    finalWarehouseSystemCode = finalStockItem.getWarehouseSystemCode();
                } else if (adjustQuantity < 0) {  // 只有扣除库存时需要报错库存不足，归还库存时不需要报错
                    throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_OUT_OF_STOCK.args(productSkuCode));
                }

                // 重新计算子活动的数量
                Integer quantityTotal_pai = productActivityItem.getQuantityTotal();
                Integer quantitySold_pai = productActivityItem.getQuantitySold();

                Integer newQuantitySold_pai = NumberUtil.sub(quantitySold_pai, adjustQuantity).intValue();
                Integer newQuantitySurplus_pai = NumberUtil.sub(quantityTotal_pai, newQuantitySold_pai).intValue();

                productActivityItem.setQuantitySold(newQuantitySold_pai);
                productActivityItem.setQuantitySurplus(newQuantitySurplus_pai);

                // 开始计算剩余订金及剩余库存
                ProductActivityPriceItem activityPriceItem =
                    iProductActivityPriceItemService.queryByActivityItemId(activityItemId);

                // 调整活动定金剩余（供货商）
                // 获取活动定金单价（供货商）
                BigDecimal activityDepositUnitPrice = activityPriceItem.getActivityDepositUnitPrice();
                // 调整的定金总金额
                BigDecimal adjustActivityDepositAmount = NumberUtil.mul(activityDepositUnitPrice, adjustQuantity);
                BigDecimal activityDepositSurplusPrice = activityPriceItem.getActivityDepositSurplusPrice();
                activityDepositSurplusPrice = NumberUtil.add(activityDepositSurplusPrice, adjustActivityDepositAmount);
                activityPriceItem.setActivityDepositSurplusPrice(activityDepositSurplusPrice);

                // 调整平台定金剩余（平台）
                // 获取平台定金单价（平台）
                BigDecimal platformDepositUnitPrice = activityPriceItem.getPlatformDepositUnitPrice();
                // 调整的定金总金额
                BigDecimal adjustPlatformDepositAmount = NumberUtil.mul(platformDepositUnitPrice, adjustQuantity);
                BigDecimal platformDepositSurplusPrice = activityPriceItem.getPlatformDepositSurplusPrice();
                platformDepositSurplusPrice = NumberUtil.add(platformDepositSurplusPrice, adjustPlatformDepositAmount);
                activityPriceItem.setPlatformDepositSurplusPrice(platformDepositSurplusPrice);

                TenantHelper.ignore(() -> {
                    iProductActivityItemService.updateById(productActivityItem);
                    iProductActivityStockItemService.updateById(finalStockItem);
                    iProductActivityPriceItemService.updateById(activityPriceItem);
                });

                if (activityStockLockItem != null) {
                    activityStockLockItem.setStockLockSold(newQuantitySold_pai);
                    activityStockLockItem.setStockLockSurplus(newQuantitySurplus_pai);

                    TenantHelper.ignore(() -> {
                        iProductActivityStockLockItemService.updateById(activityStockLockItem);
                    });
                }

                if (activityBuyoutItem != null) {
                    activityBuyoutItem.setBuyoutSold(newQuantitySold_pai);
                    activityBuyoutItem.setBuyoutSurplus(newQuantitySurplus_pai);

                    TenantHelper.ignore(() -> {
                        iProductActivityBuyoutItemService.updateById(activityBuyoutItem);
                    });
                }

                // 调整成功，创建库存同步任务
                ZSMallProductEventUtils.createTaskStockSync(productSkuCode);
            } else {
                throw new StockException(ZSMallStatusCodeEnum.NO_AVAILABLE_STOCK_FOUND.args(productSkuCode));
            }
        } catch (RStatusCodeException e) {
            log.error("活动商品[{}]库存调整出现状态码错误 = {}", productSkuCode, e.getMessage(), e);
            RStatusCodeBase statusCode = e.getStatusCode();
            throw new StockException(statusCode);
        } catch (StockException e) {
            throw e;
        } catch (Exception e) {
            log.error("活动商品[{}]库存调整出现未知错误 = {}", productSkuCode, e.getMessage(), e);
            throw new StockException(ZSMallStatusCodeEnum.PRODUCT_SKU_ADJUST_STOCK_ERROR.args(productSkuCode));
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.info("子活动{} 解锁成功", activityCode);
        }
        return finalWarehouseSystemCode;
    }

    /**
     * 取消活动或管理员拒绝商品活动审核（）
     * @param productActivity
     */
    public void cancelActivity(ProductActivity productActivity) {
        String productSkuCode = productActivity.getProductSkuCode();

        List<ProductActivityStock> activityStockList = iProductActivityStockService.queryByProductActivityId(productActivity.getId());
        List<ActivityStockBo> stockList = new ArrayList<>();
        activityStockList.forEach(stock -> stockList.add(new ActivityStockBo(stock.getWarehouseSystemCode(), stock.getQuantitySurplus())));
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        if (productSku != null) {
            // 活动创建成功，开锁归还库存
            RedissonClient client = RedisUtils.getClient();
            String key = RedisConstants.ZSMALL_PRODUCT_SKU_STOCK_LOCK + productSkuCode;
            RLock lock = client.getLock(key);

            try {
                lock.lock(10L, TimeUnit.SECONDS);
                log.info("活动归还库存[{}]加锁成功！", key);
                handleActivityProductStock(productSkuCode, stockList);
                ZSMallProductEventUtils.setProductSkuStock(productSku);
                TenantHelper.ignore(() -> iProductSkuService.updateById(productSku), TenantType.Manager);
            } catch (RStatusCodeException e) {
                log.error("活动归还库存，出现业务错误，原因 {}", e.getMessage(), e);
                throw e;
            } catch (Exception e) {
                log.error("活动归还库存，出现未知错误，原因 {}", e.getMessage(), e);
                throw new RStatusCodeException(ZSMallStatusCodeEnum.SYSTEM_BUSY);
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
                log.info("活动归还库存[{}]解锁成功！", key);
            }
        }
    }

    /**
     * 取消子活动（分销商）
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public void cancelActivityItem(ProductActivityItem productActivityItem, ProductActivityItemStateEnum newItemState) throws RStatusCodeException {
        Long productActivityItemId = productActivityItem.getId();
        ProductActivityStockLockItem stockLockItem = iProductActivityStockLockItemService.queryByActivityItemId(productActivityItemId);

        String activityCode = productActivityItem.getActivityCode();
        String activityCodeParent = productActivityItem.getActivityCodeParent();

        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_PRODUCT_ACTIVITY_LOCK + activityCodeParent;
        RLock lock = client.getLock(key);

        String keyItem = RedisConstants.ZSMALL_PRODUCT_ACTIVITY_ITEM_LOCK + activityCode;
        RLock lockItem = client.getLock(keyItem);

        try {
            lock.lock(10L, TimeUnit.SECONDS);
            log.info("更新活动状态，主活动[{}]加锁成功！", key);
            lockItem.lock(10L, TimeUnit.SECONDS);
            log.info("更新活动状态，子活动[{}]加锁成功！", keyItem);

            stockLockItem = iProductActivityStockLockItemService.queryByActivityItemId(productActivityItemId);
            lockCancelActivityItem(productActivityItem, stockLockItem, newItemState);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.info("更新活动状态，主活动[{}]解锁成功！", key);
            if (lockItem.isLocked() && lockItem.isHeldByCurrentThread()) {
                lockItem.unlock();
            }
            log.info("更新活动状态，子活动[{}]解锁成功！", keyItem);
        }
    }

    @InMethodLog("构建临期商品活动列表查询DTO")
    public ExpiringActivityItemDTO buildExpiringActivityItemDTO(String tenantId, String queryType, String queryValue, Integer endDay) {
        ExpiringActivityItemDTO dto = new ExpiringActivityItemDTO();
        String expiryOffsetDay = businessParameterService.getValueFromString(BusinessParameterType.EXPIRY_PRODUCT_ACTIVITY_OFFSET_DAY);
        dto.formatDTO(tenantId, queryType, queryValue, endDay, Integer.valueOf(expiryOffsetDay));
        return dto;
    }

    /**
     * 发送活动到期提醒
     */
    @InMethodLog("发送活动到期提醒")
    public List<MessageDto> sendActivityExpirationNotice(String tenantId, String token) {
        log.info("发送活动到期提醒给用户[{}]", tenantId);
        String nowDate = DateUtil.formatDate(new Date());

        // 发送次数限制，按天计
        Integer sendOfTimesLimit = businessParameterService.getValueFromInteger(BusinessParameterType.ACTIVITY_EXPIRATION_NOTICE_SEND_OF_TIMES);
        String key = StrUtil.builder(RedisConstants.ZSMALL_NOTICE_SEND_OF_TIMES, "_", tenantId, "_", nowDate).toString();
        Boolean hasKey = RedisUtils.hasKey(key);
        String hKey = MessageTitleEnum.ActivityExpiration.name();
        log.info("hKey[{}]", hKey);

        Integer sendOfTimes = 0;
        // 校验发送次数是否已达到上限
        if (hasKey) {
            Integer redisSendOfTimes = RedisUtils.getCacheMapValue(key, hKey);
            log.info("用户[{}]活动到期提醒今日已发送次数 = {}", tenantId, redisSendOfTimes);
            if (redisSendOfTimes != null && redisSendOfTimes >= sendOfTimesLimit) {  // Redis发送次数已大于限制次数，结束方法
                log.info("用户[{}]活动到期提醒今日发送次数已大于限制次数，结束方法", tenantId);
                return null;
            } else {
                sendOfTimes = redisSendOfTimes == null ? 0 : redisSendOfTimes;
            }
        }

        // 校验该Token是否已发送过此通知
        String tokenKey = StrUtil.builder(MessageTitleEnum.ActivityExpiration.name(), "_tokens").toString();
        List<String> tokens = RedisUtils.getCacheMapValue(key, tokenKey);
        if (tokens == null) {
            tokens = new ArrayList<>();
        } else if (CollUtil.contains(tokens, token)) {
            log.info("用户[{}]当前登录token已发送过通知，结束方法，等待下次登录再发送", tenantId);
            return null;
        }
        tokens.add(token);

        ExpiringActivityItemDTO dto = buildExpiringActivityItemDTO(tenantId, null, null, null);
        IPage<ExpiringActivityItemQueryVo> resultPage = iProductActivityItemService.getExpiringActivityItem(dto, new PageQuery().build());
        List<ExpiringActivityItemQueryVo> resultList = iProductActivityItemService.getExpiringActivityItemOneThird(tenantId);

        List<ExpiringActivityItemQueryVo> allActivityList = new ArrayList<>();
        List<ExpiringActivityItemQueryVo> records = resultPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            allActivityList.addAll(records);
        }
        if (CollUtil.isNotEmpty(resultList)) {
            allActivityList.addAll(resultList);
        }

        // 没有即将到期的活动，跳过
        if (CollUtil.isEmpty(allActivityList)) {
            return null;
        }

        List<MessageDto> messageDtoList = new ArrayList<>();
        for (ExpiringActivityItemQueryVo itemQueryVo : allActivityList) {
            String dTenantId = itemQueryVo.getDTenantId();
            String activityID = itemQueryVo.getActivityID();
            String productSkuCode = itemQueryVo.getProductSkuCode();
            String expiryDateTime = itemQueryVo.getExpiryDateTime();

            MessageDtoBuilder builder = redisProducer.builder(MessageTitleEnum.ActivityExpiration);
            builder.addArgs(activityID, productSkuCode, expiryDateTime);
            builder.addTenantId(dTenantId);
            messageDtoList.add(builder.build());
        }
        log.info("活动到期提醒数据准备完成，开始发送 = {}", JSONUtil.toJsonStr(messageDtoList));
        redisProducer.noticePublishProducer(messageDtoList);

        RedisUtils.setCacheMapValue(key, hKey, ++sendOfTimes);
        RedisUtils.setCacheMapValue(key, tokenKey, tokens);
        // 之前不存在key，第一次创建时需要设置超时时间
        if (!hasKey) {
            RedisUtils.expire(key, Duration.ofDays(1));
        }
        return messageDtoList;
    }

    @InMethodLog("发送活动到期邮件或短信")
    public void sendActivityExpirationEmailOrSms() {
        ExpiringActivityItemDTO dto = buildExpiringActivityItemDTO(null, null, null, null);
        IPage<ExpiringActivityItemQueryVo> resultPage = iProductActivityItemService.getExpiringActivityItem(dto, new PageQuery().build());
        List<ExpiringActivityItemQueryVo> resultList = iProductActivityItemService.getExpiringActivityItemOneThird(null);

        List<ExpiringActivityItemQueryVo> allActivityList = new ArrayList<>();
        List<ExpiringActivityItemQueryVo> records = resultPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            allActivityList.addAll(records);
        }
        if (CollUtil.isNotEmpty(resultList)) {
            allActivityList.addAll(resultList);
        }

        // 没有即将到期的活动，跳过
        if (CollUtil.isEmpty(allActivityList)) {
            return;
        }

        log.info("【发送活动到期邮件或短信】所有需要通知的活动数量 => {}", CollUtil.size(allActivityList));
        Map<String, List<ExpiringActivityItemQueryVo>> activityItemGroupMap = allActivityList.stream().collect(Collectors.groupingBy(ExpiringActivityItemQueryVo::getDTenantId));
        Set<String> keySet = activityItemGroupMap.keySet();

        // String rowTemplate = "<p><b>活动ID：</b>{}，<b>剩余数量：</b>{}，<b>到期时间：</b><span style='color: red'>{}</span></p>";
        String rowTemplate = "<p style='color: #409EFF; font-weight: bold; text-decoration: underline;'>{}</p>";
        for (String key : keySet) {
            try {
                SysUserVo sysUserVo = SystemEventUtils.getSysTenantAdministrator(key);
                List<ExpiringActivityItemQueryVo> expiringActivityItemQueryVoList = activityItemGroupMap.get(key);
                List<String> activityID = expiringActivityItemQueryVoList.stream().map(ExpiringActivityItemQueryVo::getActivityID).collect(Collectors.toList());
                StringBuilder content = new StringBuilder();

                for (ExpiringActivityItemQueryVo vo : expiringActivityItemQueryVoList) {
                    String row = StrUtil.format(rowTemplate, vo.getActivityID(), vo.getQuantitySurplus(), vo.getExpiryDateTime());
                    content.append(row);
                }

                Map<String, String> params = new HashMap<>();
                params.put("content", content.toString());

                String email = sysUserVo.getEmail();
                String phonenumber = sysUserVo.getPhonenumber();
                if (StrUtil.isNotBlank(email)) {
                    EmailSmsSendUtils.sendActivityExpirationWithEmail(sysUserVo.getLanguage(), email, params);
                } else if (StrUtil.isNotBlank(phonenumber)) {
                    String areaCode = sysUserVo.getAreaCode();
                    EmailSmsSendUtils.sendActivityExpirationWithSms(phonenumber, areaCode, CollUtil.join(activityID, ", "));
                }
            } catch (Exception e) {
                log.error("【发送活动到期邮件或短信】发生未知错误，分销商ID => {}，错误原因：{}", key, e.getMessage(), e);
            }
        }
    }

    /**
     * 加锁取消子活动
     */
    private void lockCancelActivityItem(ProductActivityItem productActivityItem, ProductActivityStockLockItem stockLockItem, ProductActivityItemStateEnum newItemState) {
        String activityCodeParent = productActivityItem.getActivityCodeParent();
        String tenantId_DIS = productActivityItem.getTenantId();

        // 查询供应商的活动状态
        ProductActivity productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder().activityCode(activityCodeParent).build());
        String tenantId_SUP = productActivity.getTenantId();
        ProductActivityStateEnum activityState = productActivity.getActivityState();
        Long activityId = productActivity.getId();
        String productSkuCode = productActivity.getProductSkuCode();

        Long activityItemId = productActivityItem.getId();
        // 再次查询分销商锁货信息  如果已经退款，则不处理
        ProductActivityItem newItem = iProductActivityItemService.getById(activityItemId);
        Long newItemId = newItem.getId();
        ProductActivityItemStateEnum itemActivityState = newItem.getActivityState();
        Integer stockLockSurplus = stockLockItem.getStockLockSurplus();

        //查询价格信息
        ProductActivityPriceItem itemPrice = iProductActivityPriceItemService.queryByActivityItemId(newItemId);
        //如果状态是已退款或者已过期则不更新状态
        if (ProductActivityItemStateEnum.Canceled.equals(itemActivityState) || ProductActivityItemStateEnum.Expired.equals(itemActivityState)) {
            log.info("分销商已经退款或者活动已经过期 分销商活动ID = {} ", newItemId);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.STOCK_LOCK_CLOSED);
        }

        // 定金单价
        BigDecimal activityDepositUnitPrice = itemPrice.getActivityDepositUnitPrice();
        // 剩余定金全部扣除
        BigDecimal activityDepositSurplusPrice = itemPrice.getActivityDepositSurplusPrice();

        // 定金单价
        BigDecimal platformDepositUnitPrice = itemPrice.getPlatformDepositUnitPrice();
        // 剩余定金全部扣除
        BigDecimal platformDepositSurplusPrice = itemPrice.getPlatformDepositSurplusPrice();

        //更新分销商锁货信息  状态修改为已取消或已过期，剩余订金清零0，剩余库存清0
        newItem.setActivityState(newItemState);
        newItem.setQuantitySurplus(0);
        iProductActivityItemService.updateById(newItem);

        ProductActivityStockLockItem newStockLockItem = iProductActivityStockLockItemService.queryByActivityItemId(activityItemId);
        Integer oldStockLockSurplus = newStockLockItem.getStockLockSurplus();
        newStockLockItem.setStockLockSurplus(0);
        iProductActivityStockLockItemService.updateById(newStockLockItem);
        itemPrice.setActivityDepositSurplusPrice(BigDecimal.ZERO);
        itemPrice.setPlatformDepositSurplusPrice(BigDecimal.ZERO);
        iProductActivityPriceItemService.updateById(itemPrice);

        //更新分销商锁货库存信息， 清空库存
        List<ActivityStockBo> stockBoList = new ArrayList<>();
        List<ProductActivityStockItem> stockItemList = iProductActivityStockItemService.queryByActivityItemId(activityItemId);

        log.info("分销商库存集合 stockItemList = {} ", JSONUtil.toJsonStr(stockItemList));
        stockItemList.forEach(stockItem -> {
            Integer stockLockSurplusInventory = stockItem.getQuantitySurplus();
            String warehouseSystemCode = stockItem.getWarehouseSystemCode();
            stockItem.setQuantitySurplus(0);

            ActivityStockBo activityStockBo = new ActivityStockBo();
            activityStockBo.setWarehouseSystemCode(warehouseSystemCode);
            activityStockBo.setQuantity(stockLockSurplusInventory);
            stockBoList.add(activityStockBo);
        });
        iProductActivityStockItemService.updateBatchById(stockItemList);
        log.info("待处理库存集合 stockBoList = {} ", JSONUtil.toJsonStr(stockBoList));

        //记录扣除的订金到结算表
        ProductActivityCheckout checkout = new ProductActivityCheckout();
        checkout.setSupplierTenantId(tenantId_SUP);
        checkout.setDistributorTenantId(tenantId_DIS);
        checkout.setProductActivityId(activityId);
        checkout.setProductActivityItemId(activityItemId);
        checkout.setCheckoutType(ActivityCheckoutTypeEnum.PenaltyFee);
        checkout.setCheckoutUnitPrice(activityDepositUnitPrice);
        checkout.setCheckoutAmount(activityDepositSurplusPrice);
        checkout.setPlatformCheckoutUnitPrice(platformDepositUnitPrice);
        checkout.setPlatformCheckoutAmount(platformDepositSurplusPrice);
        checkout.setCheckoutQuantity(stockLockSurplus);
        checkout.setDistributorPay(CheckoutPayEnum.Paid);
        iProductActivityCheckoutService.save(checkout);

        // 记账
        billSupport.generateBillDTOByProductActivityCheckout(checkout, null);

        //更新供应商锁货库存
        ProductActivityStockLock stockLock = iProductActivityStockLockService.queryByProductActivityId(activityId);
        Long stockLockId = stockLock.getId();
        log.info("stockLockId = {} ", stockLockId);

        //如果供应商已经取消则需要返回到原库存
        if (ProductActivityStateEnum.Canceled.equals(activityState)) {
            // TODO 改动，供货商已取消的活动，库存直接遗弃
            // backStockFromBulk(productSkuCode, stockBoList);
        } else { //如果供应商未取消则需要返回到锁货库存

            Integer quantityAlready = productActivity.getQuantityAlready();
            quantityAlready -= stockLockSurplus;
            productActivity.setQuantityAlready(quantityAlready);
            productActivity.resetSurplus();
            TenantHelper.ignore(() -> iProductActivityService.updateById(productActivity));

            //调整主库存
            Integer stockLockQuantityAlready = stockLock.getStockLockQuantityAlready();
            stockLockQuantityAlready -= stockLockSurplus;
            stockLock.setStockLockQuantityAlready(stockLockQuantityAlready);
            stockLock.resetSurplus();
            TenantHelper.ignore(() -> iProductActivityStockLockService.updateById(stockLock));

            //调整所有仓库的库存
            for (ActivityStockBo stockBo : stockBoList) {
                Integer quantity = stockBo.getQuantity();
                String warehouseSystemCode = stockBo.getWarehouseSystemCode();

                ProductActivityStock stock = iProductActivityStockService.queryByActivityIdAndWarehouseSysCode(activityId, warehouseSystemCode);
                Long stockId = stock.getId();
                log.info("供应商库存ID stockId = {}", stockId);
                Integer quantitySurplus = stock.getQuantitySurplus();
                //归还库存
                quantitySurplus += quantity;
                stock.setQuantitySurplus(quantitySurplus);
                stock.resetSold();
                iProductActivityStockService.updateById(stock);
            }
        }

        //记录结算日志
        // ThreadUtil.execute(() -> {
        //     ProductActivitySettledLogEntity settledLog = iProductActivitySettledLogService.generateBySettled(settledEntity);
        //     iProductActivitySettledLogService.save(settledLog);
        // });
    }

    @InMethodLog("分销商锁货活动到期、取消扣除定金并生成违约金结账记录")
    public void deductDeposit(List<ProductActivityStockLockItem> stockLockItemList) {
        for (ProductActivityStockLockItem stockLockItem : stockLockItemList) {
            String activityCode = stockLockItem.getActivityCode();

            try {
                Long activityItemId = stockLockItem.getProductActivityItemId();
                String activityCodeParent = stockLockItem.getActivityCodeParent();

                Integer stockLockSurplus = stockLockItem.getStockLockSurplus();
                ProductActivityPriceItem activityPriceItem = iProductActivityPriceItemService.queryByActivityItemId(activityItemId);
                BigDecimal activityDepositUnitPrice = activityPriceItem.getActivityDepositUnitPrice();
                BigDecimal activityDepositSurplusPrice = activityPriceItem.getActivityDepositSurplusPrice();
                BigDecimal platformDepositUnitPrice = activityPriceItem.getPlatformDepositUnitPrice();
                BigDecimal platformDepositSurplusPrice = activityPriceItem.getPlatformDepositSurplusPrice();

                activityPriceItem.setActivityDepositSurplusPrice(BigDecimal.ZERO);

                ProductActivity productActivity = iProductActivityService.queryOneByEntity(ProductActivity.builder().activityCode(activityCodeParent).build());
                ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().id(activityItemId).build());
                Long activityId = productActivity.getId();

                String tenantId_SUP = productActivity.getTenantId();
                String tenantId_DIS = activityItem.getTenantId();

                //记录扣除的订金到结算表
                ProductActivityCheckout checkout = new ProductActivityCheckout();
                checkout.setSupplierTenantId(tenantId_SUP);
                checkout.setDistributorTenantId(tenantId_DIS);
                checkout.setProductActivityId(activityId);
                checkout.setProductActivityItemId(activityItemId);
                checkout.setCheckoutType(ActivityCheckoutTypeEnum.PenaltyFee);
                checkout.setCheckoutUnitPrice(activityDepositUnitPrice);
                checkout.setCheckoutAmount(activityDepositSurplusPrice);
                checkout.setPlatformCheckoutUnitPrice(platformDepositUnitPrice);
                checkout.setPlatformCheckoutAmount(platformDepositSurplusPrice);
                checkout.setCheckoutQuantity(stockLockSurplus);
                checkout.setDistributorPay(CheckoutPayEnum.Paid);
                iProductActivityCheckoutService.save(checkout);
                iProductActivityPriceItemService.updateById(activityPriceItem);
                billSupport.generateBillDTOByProductActivityCheckout(checkout, null);
            } catch (Exception e) {
                log.error("分销商活动{}扣除定金并生成结账记录发生异常，原因 {}", activityCode, e.getMessage(), e);
            }
        }
    }

    @InMethodLog("操作活动商品库存（正数是加，负数是减）")
    private void handleActivityProductStock(String productSkuCode, List<ActivityStockBo> stockList) {
        List<ProductSkuStock> productSkuStockList = new ArrayList<>();
        // 开始操作库存
        for (ActivityStockBo stockBo : stockList) {
            String warehouseSystemCode = stockBo.getWarehouseSystemCode();
            Integer stockQuantity = stockBo.getQuantity();
            Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCode(warehouseSystemCode);

            ProductSkuStock productSkuStock = iProductSkuStockService.queryByProductSkuCode(productSkuCode, warehouseSystemCode);
            if (productSkuStock == null) {
                if (stockQuantity > 0) {
                    // 若是归还库存时库存已不存在，则直接返回，不报错
                    return;
                } else {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_IS_NOT_STORED_IN_THE_WAREHOUSE.args(warehouse != null ? warehouse.getWarehouseName() : warehouseSystemCode));
                }
            }

            Integer stockAvailable = productSkuStock.getStockAvailable();
            if (stockQuantity < 0) {
                // 扣除时需要判断库存是否充足
                if (Math.abs(stockQuantity) > stockAvailable) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.STOCK_LOCK_QUANTITY_NOT_ENOUGH.args(warehouse != null ? warehouse.getWarehouseName() : warehouseSystemCode));
                }
            }

            stockAvailable += stockQuantity;
            productSkuStock.setStockTotal(stockAvailable);
            productSkuStock.setStockAvailable(stockAvailable);
            productSkuStockList.add(productSkuStock);
        }
        TenantHelper.ignore(() -> iProductSkuStockService.updateBatchById(productSkuStockList), TenantType.Manager);
    }

}
