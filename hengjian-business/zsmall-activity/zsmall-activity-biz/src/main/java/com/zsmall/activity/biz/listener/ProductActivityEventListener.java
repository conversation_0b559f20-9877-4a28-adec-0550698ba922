package com.zsmall.activity.biz.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mqProducer.domain.MessageDto;
import com.zsmall.activity.biz.support.ProductActivitySupport;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.exception.StockException;
import com.zsmall.extend.event.activity.ActivityExpirationNoticeEvent;
import com.zsmall.extend.event.activity.ActivityStockAdjustEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品活动事件监听
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductActivityEventListener {

    private final ProductActivitySupport productActivitySupport;

    /**
     * 活动库存调整事件
     * @param event
     */
    @EventListener
    public void listenInActivityStockAdjust(ActivityStockAdjustEvent event) throws StockException {
        AdjustStockDTO dto = event.getDto();
        TimeInterval timer = DateUtil.timer();
        String warehouseSystemCode = productActivitySupport.lockAdjustStock(dto);
        log.info("活动库存调整事件 >>> 耗时{}ms", timer.interval());
        event.setWarehouseSystemCode(warehouseSystemCode);
    }

    /**
     * 活动到期提醒事件
     * @param event
     */
    // @Async
    @EventListener
    public void listenInActivityExpirationNotice(ActivityExpirationNoticeEvent event) {
        String dTenantId = event.getDTenantId();
        String token = event.getToken();
        List<MessageDto> messageDtoList = productActivitySupport.sendActivityExpirationNotice(dTenantId, token);
        event.setMessageArray(JSONUtil.parseArray(messageDtoList));
    }

}
