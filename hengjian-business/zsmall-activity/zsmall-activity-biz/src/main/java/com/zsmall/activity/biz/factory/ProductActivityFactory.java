package com.zsmall.activity.biz.factory;

import cn.hutool.core.util.ObjectUtil;
import com.hengjian.common.core.enums.TenantType;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 商品活动工厂
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
@Component
public class ProductActivityFactory {

    public ProductActivityFactoryService getService(TenantType tenantType) {
        return map.get(tenantType);
    }

    private static Map<TenantType, ProductActivityFactoryService> map = new HashMap<>();

    public static void register(TenantType key, ProductActivityFactoryService value) throws Exception {
        if (ObjectUtil.hasNull(key, value)) {
            throw new Exception("商品活动工厂 - 未找到对应实例注册");
        }
        map.put(key, value);
    }

}
