package com.zsmall.xxl.job.jobHandler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.xxl.job.service.ProductActivityJobService;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 商品活动相关定时任务-执行器
 *
 * <AUTHOR>
 * @date 2023/8/4
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductActivityJob {

    private final ProductActivityJobService productActivityJobService;

    /**
     * 仓储费扣除定时任务
     */
    @XxlJob("storageFeeDeductionJob")
    public void storageFeeDeductionJob() {
        productActivityJobService.storageFeeDeductionJob();
    }

    /**
     * 活动定时过期任务
     */
    @XxlJob("activityExpiredJob")
    public void activityExpiredJob() {
        productActivityJobService.activityExpiredJob();
    }

    /**
     * 定时完成活动（分销商）任务
     */
    @XxlJob("activityCompleteDisJob")
    public void activityCompleteDisJob() {
        productActivityJobService.activityCompleteDisJob();
    }

    /**
     * 定时完成活动（供应商）任务
     */
    @XxlJob("activityCompleteSupJob")
    public void activityCompleteSupJob() {
        productActivityJobService.activityCompleteSupJob();
    }

    /**
     * 定时回收订金任务（分销商已过期活已取消的活动）
     */
    @XxlJob("activityRecoveryDepositJob")
    public void activityRecoveryDepositJob() {
        productActivityJobService.activityRecoveryDepositJob();
    }

    /**
     * 定时发送临期活动通知（邮件或者短信）
     */
    @XxlJob("activityAdventNoticeJob")
    public void activityAdventNoticeJob() {
        productActivityJobService.activityAdventNoticeJob();
    }

}
