package com.zsmall.xxl.job.factory.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrJoiner;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.extend.event.OSSObtainEvent;
import com.xxl.job.core.context.XxlJobHelper;
import com.zsmall.activity.entity.domain.ProductActivityItem;
import com.zsmall.activity.entity.iservice.IProductActivityItemService;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.FulfillmentPushStateEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderAddressType;
import com.zsmall.common.enums.order.OrderType;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import com.zsmall.common.enums.productActivity.ProductActivityItemStateEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.extend.shop.rakuten.api.CabinetApi;
import com.zsmall.extend.shop.rakuten.api.NavigationApi;
import com.zsmall.extend.shop.rakuten.constant.OrderConstant;
import com.zsmall.extend.shop.rakuten.constant.ProductConstant;
import com.zsmall.extend.shop.rakuten.enums.product.ImageTypeEnum;
import com.zsmall.extend.shop.rakuten.enums.product.ItemTypeEmun;
import com.zsmall.extend.shop.rakuten.exception.RakutenClientException;
import com.zsmall.extend.shop.rakuten.kit.RakutenDelegate;
import com.zsmall.extend.shop.rakuten.kit.RakutenKit;
import com.zsmall.extend.shop.rakuten.model.RakutenResult;
import com.zsmall.extend.shop.rakuten.model.cabinet.OutCabinetFileInsert;
import com.zsmall.extend.shop.rakuten.model.genres.OutGenre;
import com.zsmall.extend.shop.rakuten.model.genres.OutGenresGet;
import com.zsmall.extend.shop.rakuten.model.order.*;
import com.zsmall.extend.shop.rakuten.model.product.InProductUpsert;
import com.zsmall.extend.shop.shopify.kit.ShopifyDelegate;
import com.zsmall.extend.shop.shopify.kit.ShopifyKit;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.dto.OrderItemDTO;
import com.zsmall.order.entity.domain.event.GenerateOrderItemEvent;
import com.zsmall.order.entity.domain.event.RecalculateOrderAmountEvent;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.ChannelExtendRakutenGenre;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.IChannelExtendRakutenGenreService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.xxl.job.factory.ThirdChannelFactory;
import com.zsmall.xxl.job.factory.ThirdChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RIdGenerator;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Rakuten相关业务实现
 *
 * <AUTHOR>
 * @date 2023/10/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RakutenImpl implements ThirdChannelService {

    private final static DateTimeFormatter dtf = DateTimeFormatter.ofPattern("YYYY-MM-dd'T'HH:mm:ssZ");
    private final static String MANAGE_NUMBER_PREFIX_INCREASE = GlobalConstants.GLOBAL_REDIS_KEY + "RAKUTEN:PRODUCT_ID";

    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductCategoryService iProductCategoryService;
    private final IProductMappingService iProductMappingService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final IProductActivityItemService iProductActivityItemService;

    private final OrderCodeGenerator orderCodeGenerator;
    private final IOrdersService iOrdersService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;
    private final IChannelExtendRakutenGenreService iChannelExtendRakutenGenreService;
    private final IProductMappingExtendRakutenInfoService iProductMappingExtendRakutenInfoService;

    private final OrderSupport orderSupport;
    private final BusinessParameterService businessParameterService;

    @Override
    public void afterPropertiesSet() throws Exception {
        ThirdChannelFactory.register(ChannelTypeEnum.Rakuten, this);
    }

    /**
     * 渠道通用定时事件
     */
    @Override
    public void timedEvent() {
        log.info("Rakuten定时任务【获取乐天市场品类】 - 开始执行");
        JSONObject RAKUTEN_COMMON_KEY = businessParameterService.getValueFromJSONObject(BusinessParameterType.RAKUTEN_COMMON_KEY);
        String serviceSecret = RAKUTEN_COMMON_KEY.getStr("serviceSecret");
        String licenseKey = RAKUTEN_COMMON_KEY.getStr("licenseKey");
        RakutenDelegate rakutenDelegate = RakutenKit.create(serviceSecret, licenseKey);

        List<String> residueGenreIds = iChannelExtendRakutenGenreService.queryAllGenreId();

        NavigationApi navigationApi = rakutenDelegate.navigationApi();
        // getItemRegisterFlg(navigationApi);

        OutGenresGet genres = navigationApi.getGenres("0", true);
        OutGenre genre = genres.getGenre();

        // 先处理根节点
        List<OutGenre> rootGenres = genre.getChildren();
        List<ChannelExtendRakutenGenre> rootGenreList = new ArrayList<>();
        for (OutGenre outGenre : rootGenres) {
            OutGenre.Properties properties = outGenre.getProperties();
            ChannelExtendRakutenGenre rakutenGenre = BeanUtil.toBean(outGenre, ChannelExtendRakutenGenre.class);
            String genreId = rakutenGenre.getGenreId();
            rakutenGenre.setParentGenreId("0");

            if (properties != null) {
                rakutenGenre.setItemRegisterFlg(properties.getItemRegisterFlg());
            }

            ChannelExtendRakutenGenre oldRakutenGenre = iChannelExtendRakutenGenreService.queryByGenreId(genreId);
            if (oldRakutenGenre != null) {
                rakutenGenre.setId(oldRakutenGenre.getId());
            }

            residueGenreIds.remove(genreId);
            rootGenreList.add(rakutenGenre);
        }
        iChannelExtendRakutenGenreService.saveOrUpdateBatch(rootGenreList);

        for (ChannelExtendRakutenGenre channelExtendRakutenGenre : rootGenreList) {
            String genreId = channelExtendRakutenGenre.getGenreId();
            generateGenreEntity(navigationApi, residueGenreIds, genreId);
        }

        if (CollUtil.isNotEmpty(residueGenreIds)) {
            iChannelExtendRakutenGenreService.removeByGenreId(residueGenreIds);
        }
    }

    private void getItemRegisterFlg(NavigationApi navigationApi) {
        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_RAKUTEN_TIMED_EVENT_LOCK_KEY;

        Page<ChannelExtendRakutenGenre> pageQuery = Page.of(1, 500);
        Page<ChannelExtendRakutenGenre> rakutenGenreList = iChannelExtendRakutenGenreService.queryAll(pageQuery);

        while (rakutenGenreList.hasNext()) {
            List<ChannelExtendRakutenGenre> records = rakutenGenreList.getRecords();
            for (ChannelExtendRakutenGenre channelExtendRakutenGenre : records) {
                RLock lock = client.getLock(key);
                lock.lock(1500, TimeUnit.MILLISECONDS);

                String genreId = channelExtendRakutenGenre.getGenreId();
                OutGenresGet genres = navigationApi.getGenres(genreId, false);
                OutGenre genre = genres.getGenre();
                Boolean itemRegisterFlg = genre.getProperties().getItemRegisterFlg();
                channelExtendRakutenGenre.setItemRegisterFlg(itemRegisterFlg);
            }
            iChannelExtendRakutenGenreService.updateBatchById(records);

            rakutenGenreList.setCurrent(rakutenGenreList.getCurrent() + 1);
            rakutenGenreList = iChannelExtendRakutenGenreService.queryAll(rakutenGenreList);
        }
    }

    private void generateGenreEntity(NavigationApi navigationApi, List<String> allGenreIds, String parentGenreId) {
        RedissonClient client = RedisUtils.getClient();
        String key = RedisConstants.ZSMALL_RAKUTEN_TIMED_EVENT_LOCK_KEY;
        RLock lock = client.getLock(key);
        lock.lock(1500, TimeUnit.MILLISECONDS);

        List<String> validGenreId = new ArrayList<>();

        OutGenresGet parentGenres = navigationApi.getGenres(parentGenreId, true);
        OutGenre parentGenre = parentGenres.getGenre();

        List<OutGenre> childrenGenres = parentGenre.getChildren();
        List<ChannelExtendRakutenGenre> childrenGenreList = new ArrayList<>();
        for (OutGenre childrenGenre : childrenGenres) {
            OutGenre.Properties properties = childrenGenre.getProperties();
            ChannelExtendRakutenGenre rakutenGenre = BeanUtil.toBean(childrenGenre, ChannelExtendRakutenGenre.class);
            String genreId = rakutenGenre.getGenreId();
            rakutenGenre.setParentGenreId(parentGenreId);

            if (properties != null) {
                rakutenGenre.setItemRegisterFlg(properties.getItemRegisterFlg());
            }

            ChannelExtendRakutenGenre oldRakutenGenre = iChannelExtendRakutenGenreService.queryByGenreId(genreId);
            if (oldRakutenGenre != null) {
                rakutenGenre.setId(oldRakutenGenre.getId());
            }

            allGenreIds.remove(genreId);
            childrenGenreList.add(rakutenGenre);
        }
        iChannelExtendRakutenGenreService.saveOrUpdateBatch(childrenGenreList);

        for (ChannelExtendRakutenGenre channelExtendRakutenGenre : childrenGenreList) {
            String genreId = channelExtendRakutenGenre.getGenreId();
            Boolean lowest = channelExtendRakutenGenre.getLowest();
            if (lowest) {
                continue;
            } else {
                generateGenreEntity(navigationApi, allGenreIds, genreId);
            }
        }
    }

    /**
     * 推送商品至渠道店铺
     *
     * @param mappingList
     */
    @Override
    public void pushProduct(List<ProductMapping> mappingList) {
        log.info("Rakuten定时任务 - 【推送商品至渠道店铺】 商品数量 = {}", CollUtil.size(mappingList));
        if (CollUtil.isNotEmpty(mappingList)) {
            for (ProductMapping productMapping : mappingList) {
                Long productMappingId = productMapping.getId();
                String tenantId = productMapping.getTenantId();
                String productCode = productMapping.getProductCode();
                String productSkuCode = productMapping.getProductSkuCode();
                String channelProductId = productMapping.getChannelProductId();

                try {
                    Long channelId = productMapping.getChannelId();
                    TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
                    if (salesChannel == null) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.SALES_CHANNEL_CANNOT_SYNC);
                    }

                    RakutenDelegate rakutenDelegate = RakutenKit.create(salesChannel.getClientSecret(), salesChannel.getPrivateKey());

                    CabinetApi cabinetApi = rakutenDelegate.cabinetApi();
                    Product product = iProductService.queryByProductCodeNotTenant(productCode);
                    String description = product.getDescription();
                    if (StrUtil.isNotBlank(description)) {
                        description = HtmlUtil.cleanHtmlTag(description);
                    }

                    String manageNumber;
                    if (StrUtil.isNotBlank(channelProductId)) {
                        manageNumber = channelProductId;
                    } else {
                        manageNumber = generateManageNumber(productCode);
                    }

                    InProductUpsert inProductUpsert = new InProductUpsert();
                    InProductUpsert.ProductDescription productDescription = new InProductUpsert.ProductDescription();
                    productDescription.setPc(description);
                    productDescription.setSp(description);

                    inProductUpsert.setHideItem(true);
                    inProductUpsert.setItemNumber(manageNumber);
                    inProductUpsert.setTitle(productMapping.getProductName());
                    inProductUpsert.setItemType(ItemTypeEmun.NORMAL);
                    inProductUpsert.setSalesDescription(description);
                    inProductUpsert.setProductDescription(productDescription);

                    ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductId(product.getId());
                    ProductMappingExtendRakutenInfo rakutenInfo = iProductMappingExtendRakutenInfoService.queryByProductMappingId(productMappingId);
                    if (rakutenInfo != null) {
                        inProductUpsert.setGenreId(rakutenInfo.getGenreId());
                    }

                    List<ProductAttribute> optionalSpecList = iProductAttributeService.queryByProductIdAndAttributeType(product.getId(), AttributeTypeEnum.OptionalSpec);
                    for (ProductAttribute productAttribute : optionalSpecList) {
                        String attributeName = productAttribute.getAttributeName();
                        List<String> attributeValues = productAttribute.getAttributeValues().toList(String.class);
                        inProductUpsert.addVariantSelector(attributeName, attributeName, attributeValues);
                    }

                    Map<String, InProductUpsert.Variant> variants = new HashMap<>();
                    ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                    Long productSkuId = productSku.getId();
                    InProductUpsert.Variant variant = new InProductUpsert.Variant();

                    Map<String, String> selectorValues = new HashMap<>();
                    List<ProductSkuAttribute> skuAttributes = iProductSkuAttributeService.queryByProductSkuId(productSkuId);
                    for (ProductSkuAttribute skuAttribute : skuAttributes) {
                        String attributeName = skuAttribute.getAttributeName();
                        String attributeValue = skuAttribute.getAttributeValue();
                        selectorValues.put(attributeName, attributeValue);
                    }
                    variant.setSelectorValues(selectorValues);

                    String firstImage = null;
                    List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Image);
                    for (int i = 0; i < skuAttachmentList.size(); i++) {
                        // 乐天图片不能超过三张
                        if (i == 3) {
                            break;
                        }

                        ProductSkuAttachment productSkuAttachment = skuAttachmentList.get(i);
                        String attachmentSuffix = productSkuAttachment.getAttachmentSuffix();
                        if (StrUtil.equals(attachmentSuffix, "jpeg")) {
                            attachmentSuffix = "jpg";
                        }
                        String fullFileName = StrUtil.builder(productSkuCode, ".", attachmentSuffix).toString().toLowerCase();
                        if (StrUtil.isBlank(firstImage)) {
                            firstImage = fullFileName;
                        }

                        byte[] bytes = OSSObtainEvent.obtainFileBytes(productSkuAttachment.getOssId());
                        OutCabinetFileInsert outCabinetFileInsert = cabinetApi.fileInsert(bytes, productSkuCode, fullFileName);

                        variant.addImage(ImageTypeEnum.CABINET, "/" + fullFileName);
                    }

                    if (StrUtil.isNotBlank(firstImage)) {
                        // 设置一张主图
                        inProductUpsert.addImage(ImageTypeEnum.CABINET, "/" + firstImage);
                    }
                    // 功能影响,加之暂无乐天渠道,暂时注释
//                    ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuId(productSkuId);

                    InProductUpsert.Variant.ReferencePrice referencePrice = new InProductUpsert.Variant.ReferencePrice();
                    referencePrice.setDisplayType(ProductConstant.ReferencePrice.DisplayType.REFERENCE_PRICE);
                    referencePrice.setType(ProductConstant.ReferencePrice.Type.NORMAL_PRICE);
                    referencePrice.setValue(DecimalUtil.bigDecimalToString0(productMapping.getFinalPrice()));

                    variant.setReferencePrice(referencePrice);
                    variant.setStandardPrice(DecimalUtil.bigDecimalToString0(productMapping.getFinalPrice()));

                    InProductUpsert.Variant.ArticleNumber articleNumber = new InProductUpsert.Variant.ArticleNumber();
                    // articleNumber.setValue(productSkuId.toString());
                    articleNumber.setExemptionReason(5);
                    variant.setArticleNumber(articleNumber);
                    variants.put(productSkuCode, variant);
                    inProductUpsert.setVariants(variants);

                    log.info("Rakuten商品准备推送 = {}", JSONUtil.toJsonStr(inProductUpsert));
                    RakutenResult upsert = rakutenDelegate.productApi().upsert(manageNumber, inProductUpsert);
                    log.info("Rakuten商品准备推送 upsert = {}", JSONUtil.toJsonStr(upsert));
                    if (upsert != null && CollUtil.isNotEmpty(upsert.getErrors())) {
                        List<RakutenResult.Error> errors = upsert.getErrors();
                        StrJoiner errorJoiner = new StrJoiner(";");
                        if (CollUtil.isNotEmpty(errors)) {
                            for (RakutenResult.Error error : errors) {
                                errorJoiner.append(error.getMessage());
                            }
                        }

                        productMapping.setSyncState(SyncStateEnum.SyncFailed);
                        productMapping.setSyncMessage(new LocaleMessage(errorJoiner.toString(), errorJoiner.toString()).toJSON());
                    } else {
                        String resp = rakutenDelegate.inventoryApi().inventoryUpsert(manageNumber, productSkuCode, productSku.getStockTotal());
                        log.info("库存同步结果 = {}", resp);

                        productMapping.setChannelProductId(manageNumber.toLowerCase());
                        productMapping.setChannelSkuId(productSkuCode);
                        productMapping.setSyncState(SyncStateEnum.Synced);
                        productMapping.setSyncMessage(null);
                    }
                } catch (RakutenClientException e) {
                    String message = e.getMessage();
                    log.error("Rakuten商品推送失败（客户端错误），租户 = {}，SKU编号 = {}，原因 = {}", tenantId, productSkuCode, e.getMessage(), e);
                    productMapping.setSyncState(SyncStateEnum.SyncFailed);

                    if (StrUtil.equals(message, "RAKUTEN_IMAGE_UPLOAD_FAILED")) {
                        productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.RAKUTEN_IMAGE_UPLOAD_FAILED));
                    } else {
                        productMapping.setSyncMessage(LocaleMessage.toJSON(message));
                    }
                } catch (RStatusCodeException e) {
                    log.error("Rakuten商品推送失败（业务错误），租户 = {}，SKU编号 = {}，原因 = {}", tenantId, productSkuCode, e.getMessage(), e);
                    productMapping.setSyncState(SyncStateEnum.SyncFailed);
                    productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(e.getStatusCode()));
                } catch (Exception e) {
                    log.error("Rakuten商品推送失败（未知错误），租户 = {}，SKU编号 = {}，原因 = {}", tenantId, productSkuCode, e.getMessage(), e);
                    productMapping.setSyncState(SyncStateEnum.SyncFailed);
                    productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.SYNC_PRODUCT_UNKNOWN_ERROR));
                }
                iProductMappingService.updateById(productMapping);
            }
        }
    }

    /**
     * 更新商品
     *
     * @param mappingList
     */
    @Override
    public void updateProduct(List<ProductMapping> mappingList) {
        log.info("Rakuten定时任务 - 【更新商品】 商品数量 = {}", CollUtil.size(mappingList));
        if (CollUtil.isNotEmpty(mappingList)) {
            for (ProductMapping productMapping : mappingList) {
                Long productMappingId = productMapping.getId();
                String tenantId = productMapping.getTenantId();
                String productCode = productMapping.getProductCode();
                String productSkuCode = productMapping.getProductSkuCode();
                String channelProductId = productMapping.getChannelProductId();

                try {
                    Long channelId = productMapping.getChannelId();
                    TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
                    if (salesChannel == null) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.SALES_CHANNEL_CANNOT_SYNC);
                    }

                    RakutenDelegate rakutenDelegate = RakutenKit.create(salesChannel.getClientSecret(), salesChannel.getPrivateKey());

                    CabinetApi cabinetApi = rakutenDelegate.cabinetApi();
                    Product product = iProductService.queryByProductCodeNotTenant(productCode);
                    String description = product.getDescription();
                    if (StrUtil.isNotBlank(description)) {
                        description = HtmlUtil.cleanHtmlTag(description);
                    }

                    InProductUpsert inProductUpsert = new InProductUpsert();
                    InProductUpsert.ProductDescription productDescription = new InProductUpsert.ProductDescription();
                    productDescription.setPc(description);
                    productDescription.setSp(description);

                    inProductUpsert.setHideItem(true);
                    inProductUpsert.setItemNumber(channelProductId);
                    inProductUpsert.setTitle(product.getName());
                    inProductUpsert.setItemType(ItemTypeEmun.NORMAL);
                    inProductUpsert.setSalesDescription(description);
                    inProductUpsert.setProductDescription(productDescription);

                    ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductId(product.getId());
                    ProductMappingExtendRakutenInfo rakutenInfo = iProductMappingExtendRakutenInfoService.queryByProductMappingId(productMappingId);
                    if (rakutenInfo != null) {
                        inProductUpsert.setGenreId(rakutenInfo.getGenreId());
                    }

                    List<ProductAttribute> optionalSpecList = iProductAttributeService.queryByProductIdAndAttributeType(product.getId(), AttributeTypeEnum.OptionalSpec);
                    for (ProductAttribute productAttribute : optionalSpecList) {
                        String attributeName = productAttribute.getAttributeName();
                        List<String> attributeValues = productAttribute.getAttributeValues().toList(String.class);
                        inProductUpsert.addVariantSelector(attributeName, attributeName, attributeValues);
                    }

                    Map<String, InProductUpsert.Variant> variants = new HashMap<>();
                    ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                    Long productSkuId = productSku.getId();
                    InProductUpsert.Variant variant = new InProductUpsert.Variant();

                    Map<String, String> selectorValues = new HashMap<>();
                    List<ProductSkuAttribute> skuAttributes = iProductSkuAttributeService.queryByProductSkuId(productSkuId);
                    for (ProductSkuAttribute skuAttribute : skuAttributes) {
                        String attributeName = skuAttribute.getAttributeName();
                        String attributeValue = skuAttribute.getAttributeValue();
                        selectorValues.put(attributeName, attributeValue);
                    }
                    variant.setSelectorValues(selectorValues);

                    String firstImage = null;
                    List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Image);
                    for (int i = 0; i < skuAttachmentList.size(); i++) {
                        // 乐天图片不能超过三张
                        if (i == 3) {
                            break;
                        }

                        ProductSkuAttachment productSkuAttachment = skuAttachmentList.get(i);
                        String attachmentSuffix = productSkuAttachment.getAttachmentSuffix();
                        if (StrUtil.equals(attachmentSuffix, "jpeg")) {
                            attachmentSuffix = "jpg";
                        }
                        String fullFileName = StrUtil.builder(productSkuCode, ".", attachmentSuffix).toString().toLowerCase();
                        if (StrUtil.isBlank(firstImage)) {
                            firstImage = fullFileName;
                        }

                        byte[] bytes = OSSObtainEvent.obtainFileBytes(productSkuAttachment.getOssId());
                        OutCabinetFileInsert outCabinetFileInsert = cabinetApi.fileInsert(bytes, productSkuCode, fullFileName);

                        variant.addImage(ImageTypeEnum.CABINET, "/" + fullFileName);
                    }

                    if (StrUtil.isNotBlank(firstImage)) {
                        // 设置一张主图
                        inProductUpsert.addImage(ImageTypeEnum.CABINET, "/" + firstImage);
                    }
                    // 功能影响,加之暂无乐天渠道,暂时注释
//                    ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuId(productSkuId);

                    InProductUpsert.Variant.ReferencePrice referencePrice = new InProductUpsert.Variant.ReferencePrice();
                    referencePrice.setDisplayType(ProductConstant.ReferencePrice.DisplayType.REFERENCE_PRICE);
                    referencePrice.setType(ProductConstant.ReferencePrice.Type.NORMAL_PRICE);
                    referencePrice.setValue(DecimalUtil.bigDecimalToString0(productMapping.getFinalPrice()));

                    variant.setReferencePrice(referencePrice);
                    variant.setStandardPrice(DecimalUtil.bigDecimalToString0(productMapping.getFinalPrice()));

                    InProductUpsert.Variant.ArticleNumber articleNumber = new InProductUpsert.Variant.ArticleNumber();
                    articleNumber.setExemptionReason(5);
                    variant.setArticleNumber(articleNumber);
                    variants.put(productSkuCode, variant);
                    inProductUpsert.setVariants(variants);

                    log.info("Rakuten商品准备推送 = {}", JSONUtil.toJsonStr(inProductUpsert));
                    RakutenResult upsert = rakutenDelegate.productApi().upsert(channelProductId, inProductUpsert);
                    log.info("Rakuten商品准备推送 upsert = {}", JSONUtil.toJsonStr(upsert));
                    if (upsert != null && CollUtil.isNotEmpty(upsert.getErrors())) {
                        List<RakutenResult.Error> errors = upsert.getErrors();
                        StrJoiner errorJoiner = new StrJoiner(";");
                        if (CollUtil.isNotEmpty(errors)) {
                            for (RakutenResult.Error error : errors) {
                                errorJoiner.append(error.getMessage());
                            }
                        }

                        productMapping.setSyncState(SyncStateEnum.SyncFailed);
                        productMapping.setSyncMessage(new LocaleMessage(errorJoiner.toString(), errorJoiner.toString()).toJSON());
                    } else {
                        String resp = rakutenDelegate.inventoryApi().inventoryUpsert(channelProductId, productSkuCode, productSku.getStockTotal());
                        log.info("库存同步结果 = {}", resp);

                        productMapping.setChannelProductId(channelProductId);
                        productMapping.setChannelSkuId(productSkuCode);
                        productMapping.setSyncState(SyncStateEnum.Synced);
                        productMapping.setSyncMessage(null);
                    }
                } catch (RStatusCodeException e) {
                    log.error("Rakuten商品推送失败（业务错误），租户 = {}，SKU编号 = {}，原因 = {}", tenantId, productSkuCode, e.getMessage(), e);
                    productMapping.setSyncState(SyncStateEnum.SyncFailed);
                    productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(e.getStatusCode()));
                } catch (Exception e) {
                    log.error("Rakuten商品推送失败（未知错误），租户 = {}，SKU编号 = {}，原因 = {}", tenantId, productSkuCode, e.getMessage(), e);
                    productMapping.setSyncState(SyncStateEnum.SyncFailed);
                    productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.SYNC_PRODUCT_UNKNOWN_ERROR));
                }
                iProductMappingService.updateById(productMapping);
            }
        }
    }

    /**
     * 取消同步商品
     *
     * @param mappingList
     */
    @Override
    public void cancelProduct(List<ProductMapping> mappingList) {
        log.info("Rakuten定时任务 - 【取消同步商品】 商品数量 = {}", CollUtil.size(mappingList));
        for (ProductMapping productMapping : mappingList) {
            Long channelId = productMapping.getChannelId();
            TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
            if (salesChannel != null) {
                RakutenDelegate rakutenDelegate = RakutenKit.create(salesChannel.getClientSecret(), salesChannel.getPrivateKey());
                deleteProductById(rakutenDelegate, productMapping.getChannelProductId());
            }

            productMapping.setSyncState(SyncStateEnum.NotSynced);
            productMapping.setChannelProductId(null);
            productMapping.setChannelSkuId(null);
            iProductMappingService.updateById(productMapping);
        }
    }

    /**
     * 删除商品
     *
     * @param mappingList
     */
    @Override
    public void deleteProduct(List<ProductMapping> mappingList) {
        log.info("Rakuten定时任务 - 【删除商品】 商品数量 = {}", CollUtil.size(mappingList));
        for (ProductMapping productMapping : mappingList) {
            Long channelId = productMapping.getChannelId();
            TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
            if (salesChannel != null) {
                RakutenDelegate rakutenDelegate = RakutenKit.create(salesChannel.getClientSecret(), salesChannel.getPrivateKey());
                deleteProductById(rakutenDelegate, productMapping.getChannelProductId());
            }
            iProductMappingService.removeById(productMapping);
        }
    }

    /**
     * 从渠道店铺拉取订单
     *
     * @param startDate
     * @param endDate
     */
    @Override
    public void pullOrder(String startDate, String endDate) {
        log.info("Rakuten定时任务 - 【拉取订单】 起始时间 = {} 截止时间 = {}", startDate, endDate);
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.Rakuten);
        log.info("Rakuten定时任务 - 【拉取订单】 有效店铺数量 = {}", CollUtil.size(channelList));
        for (TenantSalesChannel salesChannel : channelList) {
            String tenantId = salesChannel.getTenantId();
            String channelName = salesChannel.getChannelName();
            try {
                RakutenDelegate rakutenDelegate = RakutenKit.create(salesChannel.getClientSecret(), salesChannel.getPrivateKey());

                InSearchOrder inSearchOrder = new InSearchOrder();
                inSearchOrder.setOrderProgressList(OrderConstant.OrderProgress.WaitForShipment);
                inSearchOrder.setDateType(OrderConstant.DateType.OrderDate);
                inSearchOrder.pageDesc(1, 100);

                if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                    inSearchOrder.setDateBetween(DateUtil.parse(startDate), DateUtil.parse(endDate));
                } else {  // 未设置起始结束时间，默认为查询三天内的订单
                    inSearchOrder.setDateBetweenByOffset(3);
                }
                log.info("Rakuten定时任务 - 【拉取订单】 店铺 = {} 订单拉取请求参数 = {}", channelName, JSONUtil.toJsonStr(inSearchOrder));

                List<OutGetOrder.OrderModel> rakutenOrderList = new ArrayList<>();
                OutSearchOrder outSearchOrder = rakutenDelegate.orderApi().searchOrder(inSearchOrder);
                OutSearchOrder.PaginationResponseModel paginationResponseModel = outSearchOrder.getPaginationResponseModel();
                List<String> orderNumberList = outSearchOrder.getOrderNumberList();

                if (CollUtil.isNotEmpty(orderNumberList)) {
                    InGetOrder inGetOrder = new InGetOrder(orderNumberList);
                    OutGetOrder outGetOrder = rakutenDelegate.orderApi().getOrder(inGetOrder);
                    rakutenOrderList.addAll(outGetOrder.getOrderModelList());

                    while (paginationResponseModel != null && paginationResponseModel.getRequestPage() < inSearchOrder.getRequestPage()) {
                        inSearchOrder.pageDesc(inSearchOrder.getRequestPage() + 1, 100);
                        outSearchOrder = rakutenDelegate.orderApi().searchOrder(inSearchOrder);
                        paginationResponseModel = outSearchOrder.getPaginationResponseModel();

                        List<String> newOrderNumberList = outSearchOrder.getOrderNumberList();
                        if (CollUtil.isNotEmpty(newOrderNumberList)) {
                            InGetOrder inGetOrder1 = new InGetOrder(newOrderNumberList);
                            OutGetOrder outGetOrder1 = rakutenDelegate.orderApi().getOrder(inGetOrder1);
                            rakutenOrderList.addAll(outGetOrder1.getOrderModelList());
                        }
                    }
                }

                List<Orders> ordersList = ordersBodyToEntity(rakutenOrderList, salesChannel);
                if (CollUtil.isNotEmpty(ordersList)) {
                    Boolean autoPayment = ZSMallSystemEventUtils.checkAutoPaymentEvent(tenantId);
                    if (autoPayment) {
                        try {
                            orderSupport.orderPayChain(tenantId, ordersList, true, true);
                        } catch (RStatusCodeException e) {
                            log.error("Rakuten店铺[{}]，订单自动支付失败，原因 {}", channelName, e.getMessage(), e);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Rakuten店铺[{}]，拉取订单失败，原因 {}", channelName, e.getMessage(), e);
            }
        }
    }

    /**
     * 推送履约信息至渠道店铺
     *
     * @param fulfillmentRecordList
     */
    @Override
    public void pushFulfillment(List<ThirdChannelFulfillmentRecord> fulfillmentRecordList) {
        log.info("Rakuten定时任务 - 【推送履约信息至渠道店铺】 需要推送的数量 = {}", CollUtil.size(fulfillmentRecordList));

        // 乐天专用承运商转换
        JSONObject RAKUTEN_CARRIER_CODE = businessParameterService.getValueFromJSONObject(BusinessParameterType.RAKUTEN_CARRIER_CODE);

        for (ThirdChannelFulfillmentRecord fulfillmentRecord : fulfillmentRecordList) {
            String orderNo = fulfillmentRecord.getOrderNo();

            try {
                String orderItemNo = fulfillmentRecord.getOrderItemNo();
                String channelItemNo = fulfillmentRecord.getChannelItemNo();
                String channelOrderNo = fulfillmentRecord.getChannelOrderNo();
                Long channelId = fulfillmentRecord.getChannelId();

                TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
                RakutenDelegate rakutenDelegate = RakutenKit.create(salesChannel.getClientSecret(), salesChannel.getPrivateKey());

                OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
                List<OrderItemTrackingRecord> trackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
                log.info("Rakuten订单[{}] - 【推送履约信息至渠道店铺】 - orderItemNo = {}, trackingList.size = {}", orderNo, orderItemNo,
                    CollectionUtils.size(trackingList));

                InUpdateOrderShipping inUpdateOrderShipping = new InUpdateOrderShipping(channelOrderNo);
                List<InUpdateOrderShipping.BasketidModel> BasketidModelList = new ArrayList<>();

                InUpdateOrderShipping.BasketidModel basketidModel = new InUpdateOrderShipping.BasketidModel();
                basketidModel.setBasketId(Integer.parseInt(channelItemNo));
                List<InUpdateOrderShipping.ShippingModel> ShippingModelList = new ArrayList<>();

                for (OrderItemTrackingRecord trackingRecord : trackingList) {
                    String logisticsCarrier = trackingRecord.getLogisticsCarrier();
                    String logisticsTrackingNo = trackingRecord.getLogisticsTrackingNo();
                    String carrierCode = RAKUTEN_CARRIER_CODE.getStr(logisticsCarrier);
                    if (StrUtil.isBlank(carrierCode)) {
                        carrierCode = "1000";
                    }

                    InUpdateOrderShipping.ShippingModel shippingModel = new InUpdateOrderShipping.ShippingModel();
                    shippingModel.setShippingDate(DateUtil.formatDate(orderItem.getDispatchedTime()));
                    shippingModel.setDeliveryCompany(carrierCode);
                    shippingModel.setShippingNumber(logisticsTrackingNo);
                    ShippingModelList.add(shippingModel);
                }
                basketidModel.setShippingModelList(ShippingModelList);

                BasketidModelList.add(basketidModel);
                inUpdateOrderShipping.setBasketidModelList(BasketidModelList);

                log.info("Rakuten订单[{}] - 【推送履约信息至渠道店铺】 - 请求参数 = {}", orderNo, JSONUtil.toJsonStr(inUpdateOrderShipping));
                rakutenDelegate.orderApi().updateOrderShipping(inUpdateOrderShipping);

                fulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.Pushed);
                fulfillmentRecord.setChannelFulfillmentMessage(null);
            } catch (Exception e) {
                log.error("Rakuten订单[{}]，推送履约信息至渠道店铺出现未知异常，原因 {}", orderNo, e.getMessage(), e);
                fulfillmentRecord.setChannelFulfillmentMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.PUSH_FULFILLMENT_TO_SALES_CHANNEL_ERROR));
                fulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.PushFailed);
            }
            iThirdChannelFulfillmentRecordService.updateById(fulfillmentRecord);
        }
    }

    /**
     * 更新库存
     *
     * @param mappingList
     * @param stockTotal
     */
    @Override
    public void updateStock(List<ProductMapping> mappingList, Integer stockTotal) {
        log.info("Rakuten定时任务 - 【更新库存】 需要推送的数量 = {}", CollUtil.size(mappingList));
        for (ProductMapping productMapping : mappingList) {
            Long channelId = productMapping.getChannelId();
            Long productMappingId = productMapping.getId();

            try {
                TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
                if (salesChannel != null) {
                    ShopifyDelegate shopifyDelegate = ShopifyKit.create();
                    String activityCode = productMapping.getActivityCode();
                    String channelProductId = productMapping.getChannelProductId();
                    String channelSkuId = productMapping.getChannelSkuId();

                    // 如果是参加活动的，则不能取定时器传过来的通用库存，需要单独取活动库存
                    if (StrUtil.isNotBlank(activityCode)) {
                        ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityCode).activityState(ProductActivityItemStateEnum.InProgress).build());
                        if (activityItem != null) {
                            stockTotal = activityItem.getQuantitySurplus();
                        } else {
                            stockTotal = 0;
                        }
                    }

                    RakutenDelegate rakutenDelegate = RakutenKit.create(salesChannel.getClientSecret(), salesChannel.getPrivateKey());
                    String resp = rakutenDelegate.inventoryApi().inventoryUpsert(channelProductId, channelSkuId, stockTotal);
                    if (StrUtil.isNotBlank(resp)) {
                        log.error("Rakuten定时任务 - 【更新库存】 productMappingId = {} 更新库存出现异常，原因：{}", productMappingId, resp);
                        XxlJobHelper.log("Rakuten定时任务 - 【更新库存】 productMappingId = {} 更新库存出现异常，原因：{}", productMappingId, resp);
                    }
                }
            } catch (Exception e) {
                log.error("Rakuten定时任务 - 【更新库存】 productMappingId = {} 更新库存出现异常，原因：{}", productMappingId, e.getMessage());
                XxlJobHelper.log("Rakuten定时任务 - 【更新库存】 productMappingId = {} 更新库存出现异常，原因：{}", productMappingId, e.getMessage());
            }
        }
    }

    @Override
    public void pullOrderByJson(String voJson, String startDate, String endDate) {

    }

    @Override
    public void pullProduct(String json) {

    }

    /**
     * 将Orders JSON响应信息转化为实体类
     *
     * @param rakutenOrderList
     * @param channel
     * @return
     */
    private List<Orders> ordersBodyToEntity(List<OutGetOrder.OrderModel> rakutenOrderList, TenantSalesChannel channel) throws RStatusCodeException {
        List<Orders> orderList = new ArrayList<>();
        Long channelId = channel.getId();
        String tenantId = channel.getTenantId();

        for (OutGetOrder.OrderModel rakutenOrder : rakutenOrderList) {
            String channelOrderName = rakutenOrder.getOrderNumber();

            Orders order = iOrdersService.queryValidOrder(channelId, tenantId, channelOrderName);

            if (order == null) {  // 数据库中没有存过该订单，准备创建实体类
                order = new Orders();
                String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
                order.setOrderNo(orderNo);
                order.setTenantId(tenantId);
                order.setOrderType(OrderType.Normal);
                order.setChannelType(ChannelTypeEnum.Rakuten);
                order.setLogisticsType(LogisticsTypeEnum.DropShipping);
                order.setChannelId(channelId);
                order.setOrderNote(rakutenOrder.getRemarks());
                order.setChannelOrderNo(channelOrderName);
                order.setChannelOrderName(channelOrderName);
                order.setChannelOrderTime(rakutenOrder.getOrderDatetime());

                // 物流信息
                OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
                orderLogisticsInfo.setOrderNo(orderNo);
                orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.DropShipping);

                OutGetOrder.OrdererModel ordererModel = rakutenOrder.getOrdererModel();
                // 收货地址
                OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
                if (ordererModel != null) {
                    StrJoiner phoneJoiner = StrJoiner.of("-");
                    phoneJoiner.setNullMode(StrJoiner.NullMode.IGNORE);
                    phoneJoiner.append(ordererModel.getPhoneNumber1());
                    phoneJoiner.append(ordererModel.getPhoneNumber2());
                    phoneJoiner.append(ordererModel.getPhoneNumber3());

                    StrJoiner zipCodeJoiner = StrJoiner.of("-").append(ordererModel.getZipCode1()).append(ordererModel.getZipCode2());
                    String zipCode = zipCodeJoiner.toString();

                    orderAddressInfo.setOrderNo(orderNo);
                    orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
                    orderAddressInfo.setRecipient(ordererModel.getFamilyName() + " " + ordererModel.getFirstName());
                    orderAddressInfo.setPhoneNumber(phoneJoiner.toString());

                    // 当前乐天市场订单收货地址默认都是日本
                    orderAddressInfo.setCountry("JP");
                    orderAddressInfo.setCountryCode("JP");
                    orderAddressInfo.setState(ordererModel.getPrefecture());
                    orderAddressInfo.setStateCode(ordererModel.getPrefecture());
                    orderAddressInfo.setCity(ordererModel.getCity());
                    orderAddressInfo.setAddress1(ordererModel.getSubAddress());
                    orderAddressInfo.setZipCode(zipCode);
                    orderLogisticsInfo.setZipCode(zipCode);
                    orderLogisticsInfo.setLogisticsZipCode(zipCode);
                    orderLogisticsInfo.setLogisticsCountryCode("JP");
                }

                List<OutGetOrder.PackageModel> packageModelList = rakutenOrder.getPackageModelList();

                List<OrderItem> orderItemList = new ArrayList<>();
                List<OrderItemPrice> orderItemPriceList = new ArrayList<>();

                LocaleMessage localeMessage = new LocaleMessage();
                Integer totalQuantity = 0;
                for (OutGetOrder.PackageModel packageModel : packageModelList) {
                    Integer basketId = packageModel.getBasketId();
                    List<OutGetOrder.ItemModel> itemModelList = packageModel.getItemModelList();
                    for (OutGetOrder.ItemModel itemModel : itemModelList) {
                        List<OutGetOrder.SkuModel> skuModelList = itemModel.getSkuModelList();
                        if (CollUtil.isEmpty(skuModelList)) {
                            continue;
                        }

                        OutGetOrder.SkuModel skuModel = skuModelList.get(0);
                        String mappingSku = skuModel.getVariantId();
                        Integer quantity = itemModel.getUnits();
                        totalQuantity += quantity;

                        ProductMapping productMapping = iProductMappingService.queryByTenantAndMappingSku(tenantId, channelId, mappingSku);
                        if (productMapping != null) {
                            GenerateOrderItemEvent generateOrderItemEvent = new GenerateOrderItemEvent();
                            generateOrderItemEvent.setDTenantId(tenantId);
                            generateOrderItemEvent.setOrder(order);
                            generateOrderItemEvent.setChannelTypeEnum(ChannelTypeEnum.Rakuten);
                            generateOrderItemEvent.setLogisticsType(LogisticsTypeEnum.DropShipping);
                            generateOrderItemEvent.setCountry(orderAddressInfo.getCountryCode());
                            generateOrderItemEvent.setActivityCode(productMapping.getActivityCode());
                            generateOrderItemEvent.setProductSkuCode(productMapping.getProductSkuCode());
                            generateOrderItemEvent.setTotalQuantity(quantity);
                            SpringUtils.context().publishEvent(generateOrderItemEvent);

                            OrderItemDTO outDTO = generateOrderItemEvent.getOutDTO();
                            LocaleMessage message = outDTO.getLocaleMessage();
                            if (message.hasData()) {
                                localeMessage.append(message);
                            }

                            OrderItem orderItem = outDTO.getOrderItem();
                            OrderItemPrice orderItemPrice = outDTO.getOrderItemPrice();
                            OrderItemProductSku orderItemProductSku = outDTO.getOrderItemProductSku();

                            orderItem.setTenantId(tenantId);
                            orderItem.setChannelItemNo(StrUtil.toStringOrNull(basketId));
                            orderItemProductSku.setTenantId(tenantId);

                            orderItem.setOrderItemPrice(orderItemPrice);
                            orderItem.setOrderItemProductSku(orderItemProductSku);

                            orderItemList.add(orderItem);
                            orderItemPriceList.add(orderItemPrice);
                        } else {
                            log.info("Rakuten订单 {} 不存在映射SKU {}", channelOrderName, mappingSku);
                        }
                    }
                }


                log.info("Rakuten订单 {} 有效的子订单数量 {}", channelOrderName, CollUtil.size(orderItemList));
                if (CollUtil.isNotEmpty(orderItemList)) {
                    order.setTotalQuantity(totalQuantity);

                    RecalculateOrderAmountEvent recalculateOrderAmountEvent = new RecalculateOrderAmountEvent();
                    recalculateOrderAmountEvent.setOrder(order);
                    recalculateOrderAmountEvent.setOrderItemPriceList(orderItemPriceList);
                    SpringUtils.publishEvent(recalculateOrderAmountEvent);

                    iOrdersService.save(order);
                    Long orderId = order.getId();

                    iOrderLogisticsInfoService.save(orderLogisticsInfo.setOrderId(orderId));

                    if (orderAddressInfo.getOrderNo() != null) {
                        iOrderAddressInfoService.save(orderAddressInfo.setOrderId(orderId));
                    }

                    for (OrderItem orderItem : orderItemList) {
                        iOrderItemService.save(orderItem.setOrderId(orderId));
                        Long orderItemId = orderItem.getId();

                        OrderItemPrice orderItemPrice = orderItem.getOrderItemPrice();
                        OrderItemProductSku orderItemProductSku = orderItem.getOrderItemProductSku();

                        iOrderItemPriceService.save(orderItemPrice.setOrderItemId(orderItemId));
                        iOrderItemProductSkuService.save(orderItemProductSku.setOrderItemId(orderItemId));
                    }
                    orderList.add(order);
                }
            }
        }
        return orderList;
    }

    /**
     * 根据id删除第三方渠道商品
     *
     * @param channelProductId
     */
    private void deleteProductById(RakutenDelegate rakutenDelegate, String channelProductId) {
        log.info("Rakuten 根据id删除第三方渠道商品 channelProductId = {}", channelProductId);
        if (rakutenDelegate == null || channelProductId == null) {
            return;
        }

        try {
            RakutenResult rakutenResult = rakutenDelegate.productApi().delete(channelProductId);
            if (rakutenResult != null && CollUtil.isNotEmpty(rakutenResult.getErrors())) {
                List<RakutenResult.Error> errors = rakutenResult.getErrors();
                StrJoiner errorJoiner = new StrJoiner(";");
                if (CollUtil.isNotEmpty(errors)) {
                    for (RakutenResult.Error error : errors) {
                        errorJoiner.append(error.getMessage());
                    }
                }
                log.error("Rakuten定时任务 - 【删除商品】 channelProductId = {} 出现异常，原因：{}", channelProductId, errorJoiner.toString());
                XxlJobHelper.log("Rakuten定时任务 - 【删除商品】 channelProductId = {} 出现异常，原因：{}", channelProductId, errorJoiner.toString());
            }
        } catch (Exception e) {
            log.info("Rakuten 根据id删除第三方渠道商品失败，原因 {}", e.getMessage(), e);
        }
    }

    /**
     * 生成商品管理编号
     * @param productCode
     * @return
     */
    private String generateManageNumber(String productCode) {
        StrJoiner manageNoJoiner = new StrJoiner("-");
        DateTime now = DateTime.now();
        manageNoJoiner.append(productCode);

        RedissonClient client = RedisUtils.getClient();
        RIdGenerator idGenerator = client.getIdGenerator(MANAGE_NUMBER_PREFIX_INCREASE);
        boolean b = idGenerator.tryInit(1, 1);
        // 初始化，第一次创建，则设置过期时间
        if (b) {
            // 15秒超时，稍微延后
            idGenerator.expire(Duration.ofSeconds(90));
        }

        long id = idGenerator.nextId();
        if (id == 9L) {
            // 删除自增，下次再重新初始化
            idGenerator.delete();
        }

        int msid = Integer.parseInt(DateUtil.format(now, "S") + id);
        String strNum = String.format("%04d", msid);
        // manageNoJoiner.append(strNum);
        manageNoJoiner.append(DateUtil.format(now, "mmss") + strNum);
        return manageNoJoiner.toString();
    }

}
