package com.zsmall.xxl.job.factory.impl;

import com.zsmall.activity.entity.iservice.IProductActivityItemService;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.ThirdChannelFulfillmentRecord;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.iservice.IShopifyExtraPropertiesService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.xxl.job.factory.ThirdChannelFactory;
import com.zsmall.xxl.job.factory.ThirdChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/26 15:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ErpImpl implements ThirdChannelService {
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductCategoryService iProductCategoryService;
    private final IProductMappingService iProductMappingService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final IShopifyExtraPropertiesService iShopifyExtraPropertiesService;
    private final IProductActivityItemService iProductActivityItemService;

    private final OrderCodeGenerator orderCodeGenerator;
    private final IOrdersService iOrdersService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;

    private final OrderSupport orderSupport;

    @Override
    public void afterPropertiesSet() throws Exception {
        ThirdChannelFactory.register(ChannelTypeEnum.Erp, this);
    }

    @Override
    public void pushProduct(List<ProductMapping> mappingList) {

    }

    @Override
    public void updateProduct(List<ProductMapping> mappingList) {

    }

    @Override
    public void cancelProduct(List<ProductMapping> mappingList) {

    }

    @Override
    public void deleteProduct(List<ProductMapping> mappingList) {

    }

    /**
     * 功能描述：拉取渠道订单
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * <AUTHOR>
     * @date 2023/12/26
     */
    @Override
    public void pullOrder(String startDate, String endDate) {
        // 从erp订单队列消费,不做定时任务拉取

    }

    @Override
    public void pushFulfillment(List<ThirdChannelFulfillmentRecord> fulfillmentRecordList) {

    }

    @Override
    public void updateStock(List<ProductMapping> mappingList, Integer stockTotal) {

    }

    @Override
    public void pullOrderByJson(String voJson, String startDate, String endDate) {

    }

    @Override
    public void pullProduct(String json) {

    }


}
