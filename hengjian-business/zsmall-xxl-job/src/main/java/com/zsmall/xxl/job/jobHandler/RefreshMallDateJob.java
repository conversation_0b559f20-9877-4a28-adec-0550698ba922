package com.zsmall.xxl.job.jobHandler;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.bo.SysTenantBo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysTenantService;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.common.enums.marketplaceConfig.ModuleTypeEnum;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshMallDateJob {
    @Resource
    private ISysTenantService sysTenantService;
    @Resource
    private IProductSkuService iProductSkuService;

    @XxlJob("refreshMallDate")
    private void refreshMallDate() {
        try {
            log.info("进入销量排行榜,查询19个商品");
            List<String> productSkus= TenantHelper.ignore(()-> iProductSkuService.getBaseMapper().getSalesRankingProduct());
            RedisUtils.deleteKeys(GlobalConstants.GLOBAL_REDIS_KEY+ ModuleTypeEnum.SalesRanking);
            RedisUtils.setCacheList(GlobalConstants.GLOBAL_REDIS_KEY+ModuleTypeEnum.SalesRanking,productSkus);

            List<String> productSkuCodeList=new ArrayList<>();
            SysTenantBo sysTenantBo=new SysTenantBo();
            List<SysTenantVo> sysTenantVos = sysTenantService.queryList(sysTenantBo);
            for (SysTenantVo s : sysTenantVos) {
                if ("S0BJHUA".equals(s.getTenantId()) || "D4ZQGHV".equals(s.getTenantId())){
                    continue;
                }
                log.info("进入猜你喜欢排行榜,查询10个商品");
                List<String> belongCategoryIds = new ArrayList<>();
                if (TenantType.Distributor.name().equals(s.getTenantType())) {
                    belongCategoryIds = TenantHelper.ignore(() -> iProductSkuService.getBaseMapper().getGuessYouLikeCategoryId(LoginHelper.getTenantId()));
                    //查询品类
                    Set<String> uniqueProductSkus = new HashSet<>();
                    for (String belongCategoryId : belongCategoryIds) {
                        List<ProductSku>     productSku1 = TenantHelper.ignore(() -> iProductSkuService.getBaseMapper().getGuessYouLikeProduct(belongCategoryId));
                        Set<String> productSkuCodes = productSku1.stream()
                                                                 .map(ProductSku::getProductSkuCode)
                                                                 .collect(Collectors.toSet());
                        uniqueProductSkus.addAll(productSkuCodes);
                        if (uniqueProductSkus.size() >= 10) {
                            break;
                        }
                    }
                    //品类不够查询下面的商品
                    if (uniqueProductSkus.size() < 10) {
                        int remainingSize = 10 - uniqueProductSkus.size();
                        LambdaQueryWrapper<ProductSku> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.orderByDesc(ProductSku::getId);
                        queryWrapper.notLikeRight(ProductSku::getSku,"ZJHJ");
                        queryWrapper.last("limit " + remainingSize);
                        List<ProductSku> additionalProductSkus = TenantHelper.ignore(() -> iProductSkuService.getBaseMapper().selectList(queryWrapper));
                        Set<String> productSkuCodes = additionalProductSkus.stream()
                                                                           .map(ProductSku::getProductSkuCode)
                                                                           .collect(Collectors.toSet());
                        uniqueProductSkus.addAll(productSkuCodes);
                    }

                    productSkuCodeList   = new ArrayList<>(uniqueProductSkus);
                    // 只保留前10个商品
                    if (productSkuCodeList.size() > 10) {
                        productSkuCodeList = productSkuCodeList.subList(0, 10);
                    }
                } else {
                    //商品表查询10个商品
                    LambdaQueryWrapper<ProductSku> w = new LambdaQueryWrapper<>();
                    w.eq(ProductSku::getShelfState,"OnShelf");
                    w.orderByDesc(ProductSku::getId);
                    w.notLikeRight(ProductSku::getSku,"ZJHJ");
                    w.last("limit 10");
                    List<ProductSku> ignore = TenantHelper.ignore(() -> iProductSkuService.getBaseMapper()
                                                                                          .selectList(w));
                    productSkuCodeList = ignore.stream()
                                               .map(ProductSku::getProductSkuCode)
                                               .collect(Collectors.toList());
                }
                //将Set转List
                RedisUtils.deleteKeys(GlobalConstants.GLOBAL_REDIS_KEY+ModuleTypeEnum.GuessYouLike+s.getTenantId());
                RedisUtils.setCacheList(GlobalConstants.GLOBAL_REDIS_KEY+ModuleTypeEnum.GuessYouLike+s.getTenantId(),productSkuCodeList);

                log.info("商城首页数据清洗"+ s.getTenantId());
            }


        }catch (Exception e){
            log.error("清洗商城首页数据失败"+ JSONUtil.toJsonStr(e));
        }
    }
}
