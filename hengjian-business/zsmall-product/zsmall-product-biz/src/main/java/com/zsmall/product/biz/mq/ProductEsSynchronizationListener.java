package com.zsmall.product.biz.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.stream.mqProducer.constants.QueueConstants;
import com.hengjian.stream.mqProducer.domain.MessageDto;
import com.rabbitmq.client.Channel;
import com.zsmall.product.biz.support.EsProductSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProductEsSynchronizationListener {
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private  EsProductSupport esProductSupport;


    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE)
    public void productEsSynchronizationListener(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        String messageContext = new String(message.getBody());
        log.info("【消费者 - ElasticSearch商品数据推送】通道: {}, 收到数据: {}", QueueConstants.QueueName.ES_PRODUCT_PUSH, messageContext);
        try {
            MessageDto messageDto = JSONUtil.toBean(messageContext, MessageDto.class);
            String msgId = messageDto.getMsgId();
            Map<String, Object> data = (Map<String, Object>) messageDto.getData();
            List<String> productCodes = (List<String>) data.get("productCodes");
            List<String> productSkuCodes = (List<String>) data.get("productSkuCodes");

            // 使用重试机制处理可能的数据不一致问题
            processWithRetry(() -> {
                if (CollUtil.isNotEmpty(productCodes)) {
                    esProductSupport.productUploadForConsumer(productCodes);
                }
                if (CollUtil.isNotEmpty(productSkuCodes)) {
                    esProductSupport.productSkuUploadForConsumer(productSkuCodes);
                }
            }, 3, 100); // 最多重试3次，每次间隔100ms

        } catch (Exception e) {
            log.error("【消费者 - ElasticSearch商品数据推送】发生异常：{}", e.getMessage(), e);
            throw e; // 重新抛出异常，让消息重新入队
        }finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }
    }

    /**
     * 重试机制处理
     */
    private void processWithRetry(Runnable task, int maxRetries, long delayMs) {
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                task.run();
                return; // 成功执行，退出重试
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    log.error("重试{}次后仍然失败", maxRetries, e);
                    throw e;
                }
                log.warn("第{}次执行失败，{}ms后重试", retryCount, delayMs, e);
                try {
                    Thread.sleep(delayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
            }
        }
    }
}

