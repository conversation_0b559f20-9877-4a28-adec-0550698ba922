package com.zsmall.product.biz.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.stream.mqProducer.domain.MessageDto;
import com.hengjian.stream.mqProducer.producer.RedisProducer;
import com.zsmall.activity.entity.domain.ProductActivity;
import com.zsmall.activity.entity.iservice.IProductActivityService;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import com.zsmall.common.enums.product.ProductTypeEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.extend.es.entity.EsKeyValuePair;
import com.zsmall.extend.es.entity.EsProduct;
import com.zsmall.extend.es.esmapper.EsProductMapper;
import com.zsmall.product.biz.service.IProductLabelRelationService;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.warehouse.entity.domain.LogisticsTemplateRateRule;
import com.zsmall.warehouse.entity.iservice.ILogisticsTemplateRateRuleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ES商品支持类
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = { @Lazy })
public class EsProductSupport {

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private final EsProductMapper esProductMapper;
    private final IProductService iProductService;
    private final IProductActivityService iProductActivityService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductCategoryService iProductCategoryService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductLabelRelationService iProductLabelRelationService;
    private final ILogisticsTemplateRateRuleService iLogisticsTemplateRateRuleService;
    private final IProductChannelControlService iProductChannelControlService;
    private final IViewProductSkuSalesService iViewProductSkuSalesService;

    private final ITaskEsProductSyncService iTaskEsProductSyncService;

    private final RedisProducer redisProducer;
    private final RabbitTemplate rabbitTemplate;

    /**
     * ES商品上传(SKU维度)
     *
     * @param productSku
     */
    public void productSkuUpload(ProductSku productSku) {
        if (productSku != null) {
            productSkuUpload(productSku.getProductSkuCode());
        }
    }

    /**
     * ES商品上传(SKU维度)
     *
     * @param
     */
    public void productSkuUpload(Long... productSkuId) {
        if (ArrayUtil.isNotEmpty(productSkuId)) {
            productSkuUpload(iProductSkuService.queryByIdIncludeDel(productSkuId));
        }
    }

    /**
     * ES商品上传(SKU维度)
     *
     * @param
     */
    public void productSkuUpload(List<ProductSku> productSkuList) {
        if (CollUtil.isNotEmpty(productSkuList)) {
            String[] productSkuCodes = productSkuList.stream().map(ProductSku::getProductSkuCode).toArray(String[]::new);
            productSkuUpload(productSkuCodes);
        }
    }

    /**
     * ES商品上传(SKU维度)
     *
     * @param
     */
    public void productSkuUpload(String... productSkuCodes) {
        if (ArrayUtil.isNotEmpty(productSkuCodes)) {
            Date date = new Date();
            MessageDto messageDto = new MessageDto(DateUtil.format(date, "yyyy-MM-dd_HH:mm:ss.SSSS"));

            Map<String, Object> data = new HashMap<>();
            data.put("productSkuCodes", List.of(productSkuCodes));

            messageDto.setMsgSource("Es Product Push");
            messageDto.setData(data);
          //  redisProducer.esProductPushProducer(messageDto);
            log.info("[ES商品上传],发送消息,内容:{}", JSON.toJSONString(messageDto));
            rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE, RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE, JSON.toJSONString(messageDto));

        }
    }

    /**
     * ES商品上传(SPU维度)
     *
     * @param product
     */
    public void productUpload(Product product) {
        if (product != null) {
            productUpload(CollUtil.newArrayList(product));
        }
    }

    /**
     * ES商品上传(SPU维度)
     *
     * @param
     */
    public void productUpload(Long... productId) {
        if (ArrayUtil.isNotEmpty(productId)) {
            productUpload(iProductService.queryByIdIncludeDel(productId));
        }
    }

    /**
     * ES商品上传(SPU维度)
     *
     * @param
     */
    public void productUpload(List<Product> productList) {
        if (CollUtil.isNotEmpty(productList)) {
            String[] productCodes = productList.stream().map(Product::getProductCode).toArray(String[]::new);
            productUpload(productCodes);
        }
    }

    /**
     * ES商品上传(SPU维度)
     *
     * @param
     */
    public void productUpload(String... productCodes) {
        if (ArrayUtil.isNotEmpty(productCodes)) {
            Date date = new Date();
            MessageDto messageDto = new MessageDto(DateUtil.format(date, "yyyy-MM-dd_HH:mm:ss.SSSS"));

            Map<String, Object> data = new HashMap<>();
            data.put("productCodes", List.of(productCodes));

            messageDto.setMsgSource("Es Product Push");
            messageDto.setData(data);
           // redisProducer.esProductPushProducer(messageDto);
            log.info("[ES商品上传],发送消息,内容:{}", JSON.toJSONString(messageDto));
            rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE, RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE, JSON.toJSONString(messageDto));
        }
    }

    /**
     * ES商品上传（SKU维度）提供给队列消费者使用，正常业务请勿使用
     * @param productSkuCode
     */
    public void productSkuUploadForConsumer(List<String> productSkuCode) {
        this.insertByProductSku(iProductSkuService.queryByProductSkuCodesIncludeDel(productSkuCode));
    }

    /**
     * ES商品上传（SPU维度）提供给队列消费者使用，正常业务请勿使用
     * @param
     */
    public void productUploadForConsumer(List<String> productCodes) {
        this.insertByProduct(iProductService.queryByProductCodesIncludeDel(productCodes));
    }

    //
    // /**
    //  * ES商品上传（SKU维度）
    //  * @param productSku
    //  */
    // public void productSkuUpload(Long... productSkuId) {
    //     threadPoolTaskExecutor.execute(() -> this.insertByProductSku(iProductSkuService.queryByIdIncludeDel(productSkuId)));
    // }
    //
    // /**
    //  * ES商品上传（SKU维度）
    //  * @param productSku
    //  */
    // public void productSkuUpload(ProductSku productSku) {
    //     threadPoolTaskExecutor.execute(() -> this.insertByProductSku(CollUtil.newArrayList(productSku)));
    // }
    //
    // /**
    //  * ES商品上传（SKU维度）
    //  * @param productSku
    //  */
    // public void productSkuUpload(List<ProductSku> productSkuList) {
    //     threadPoolTaskExecutor.execute(() -> this.insertByProductSku(productSkuList));
    // }
    //
    // /**
    //  * ES商品上传（SPU维度）
    //  * @param product
    //  */
    // public void productUpload(String... productSkuCodes) {
    //     threadPoolTaskExecutor.execute(() -> this.insertByProduct(iProductService.queryByProductCodesIncludeDel(ListUtil.of(productSkuCodes))));
    // }
    //
    //
    // /**
    //  * ES商品上传（SPU维度）
    //  * @param product
    //  */
    // public void productUpload(Long... productId) {
    //     threadPoolTaskExecutor.execute(() -> this.insertByProduct(iProductService.queryByIdIncludeDel(productId)));
    // }
    //
    // /**
    //  * ES商品上传（SPU维度）
    //  * @param product
    //  */
    // public void productUpload(Product product) {
    //     threadPoolTaskExecutor.execute(() -> this.insertByProduct(CollUtil.newArrayList(product)));
    // }
    //
    // /**
    //  * ES商品上传（SPU维度）
    //  * @param product
    //  */
    // public void productUpload(List<Product> productList) {
    //     threadPoolTaskExecutor.execute(() -> this.insertByProduct(productList));
    // }

    /**
     * ES商品批量上传（SPU维度）
     *
     * @param products
     */
    private void insertByProduct(List<Product> products) {
        List<EsProduct> esProductList = new ArrayList<>();
        for (Product product : products) {
            esProductList.addAll(productPack(product));
        }
        if (CollUtil.isNotEmpty(esProductList)) {
            esProductMapper.insertBatch(esProductList);
        }
    }

    /**
     * ES商品批量上传（SKU维度）
     *
     * @param productSkuList
     */
    private void insertByProductSku(List<ProductSku> productSkuList) {
        List<EsProduct> esProductList = productSkuPack(null, productSkuList);
        if (CollUtil.isNotEmpty(esProductList)) {
            esProductMapper.insertBatch(esProductList);
            log.info("上传信息至ES完成");
        }
    }

    /**
     * SPU维度数据打包
     */
    private List<EsProduct> productPack(Product product) {
        List<ProductSku> productSkuList = iProductSkuService.queryByProductIdIncludeDel(product.getId());
        return this.productSkuPack(product, productSkuList);
    }

    /**
     * SKU维度数据打包前置方法
     */
    private List<EsProduct> productSkuPack(Product product, List<ProductSku> productSkuList) {
        List<EsProduct> esProductList = new ArrayList<>();
        for (ProductSku productSku : productSkuList) {
            EsProduct esProduct = this.generateEsProduct(product, productSku);
            if (esProduct != null) {
                esProductList.add(esProduct);
            }
        }
        return esProductList;
    }

    /**
     * 生成ES商品实体
     */
    private EsProduct generateEsProduct(Product product, ProductSku productSku) {
        Long productId = productSku.getProductId();
        Long productSkuId = productSku.getId();
        String productSkuCode = productSku.getProductSkuCode();
        String skuDelFlag = productSku.getDelFlag();

        EsProduct esProduct = new EsProduct();
        try {
            if (product == null) {
                product = iProductService.queryByIdNotTenant(productId);
            }
            String spuDelFlag = product.getDelFlag();

            String supplierId = product.getTenantId();
            String productName = product.getName();
            String productCode = product.getProductCode();
            ProductTypeEnum productType = product.getProductType();
            if (ObjUtil.equals(productType, ProductTypeEnum.WholesaleProduct)) {
                return null;
            }

            esProduct.setId(productSkuCode);
            esProduct.setSpuCode(productCode);
            esProduct.setSkuCode(productSkuCode);
            esProduct.setProductName(productName);
            esProduct.setProductType(EnumUtil.toString(productType));
            esProduct.setCreateTime(product.getCreateTime().getTime());
            esProduct.setSupplierId(supplierId);
            esProduct.setSpuState(Integer.parseInt(spuDelFlag));
            esProduct.setSkuState(Integer.parseInt(skuDelFlag));
            esProduct.setShelfState(product.getShelfState().name());
            esProduct.setSkuShelfState(productSku.getShelfState().name());

            // SKU或SPU已被删除，下面的数据封装已没必要运行
            if (StrUtil.equals(skuDelFlag, "2") || StrUtil.equals(spuDelFlag, "2")) {
                return esProduct;
            }

            ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductSkuId(productSkuId);
            String originImageShowUrl = "";
            String compressImageShowUrl = "";
            if (productSkuAttachmentVo != null) {
                // TODO 是否要压缩 compressImageShowUrl = fileProperties.getImageCompressPrefix() + productSkuAttachment.getSavePath() + fileProperties.getImageCompressSuffix();
                compressImageShowUrl = productSkuAttachmentVo.getAttachmentShowUrl();
                originImageShowUrl = productSkuAttachmentVo.getAttachmentShowUrl();
            }
            esProduct.setOriginImageShowUrl(originImageShowUrl);
            esProduct.setCompressImageShowUrl(compressImageShowUrl);

            // 取三个分类
            Long belongCategoryId = product.getBelongCategoryId();
            List<ProductCategory> allCategoryByChildren = iProductCategoryService.queryCategoryChainById(belongCategoryId);
            allCategoryByChildren = CollUtil.reverse(allCategoryByChildren);
            for (int i = 0; i < 3; i++) {
                String nameKey = "category" + String.valueOf(Math.abs(i + 1)) + "Name";
                String idKey = "category" + String.valueOf(Math.abs(i + 1)) + "Id";

                String name = null;
                Long cid = null;
                if (i < CollUtil.size(allCategoryByChildren)) {
                    ProductCategory productCategory = allCategoryByChildren.get(i);
                    name = productCategory.getCategoryName();
                    cid = productCategory.getId();
                }

                Class beanClass = esProduct.getClass();
                Field nameField = beanClass.getDeclaredField(nameKey);
                nameField.setAccessible(true);
                Field idField = beanClass.getDeclaredField(idKey);
                idField.setAccessible(true);
                nameField.set(esProduct, name);
                idField.set(esProduct, cid);
            }

            ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuId(productSkuId);
            // es 内的价格放的是第一个,es价格放哪个不影响,实际价格会重现落mysql查询,此处默认放us
            ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuId(productSkuId);
            List<ProductSkuAttribute> skuAttributes = iProductSkuAttributeService.queryByProductSkuId(productSkuId);

            Integer soldQuantity = TenantHelper.ignore(()->iProductSkuPriceService.getSoldOutByProductSkuId(productSkuId)) ;
            // 2024.8.12 注释,改查询影响性能,后续考虑定时任务去跑
//            ViewProductSkuSales viewProductSkuSales = iViewProductSkuSalesService.queryByProductSkuId(productSkuId);

            List<String> specList = new ArrayList<>();
            if (CollUtil.isNotEmpty(skuAttributes)) {
                for (ProductSkuAttribute skuAttribute : skuAttributes) {
                    specList.add(skuAttribute.getAttributeName());
                    specList.add(skuAttribute.getAttributeValue());
                }
            }
            esProduct.setSpecification(CollUtil.join(specList, " "));

            List<ProductAttribute> productAttributes = iProductAttributeService.queryByProductId(productId);

            Set<EsKeyValuePair> attributeList = new HashSet<>();
            Set<EsKeyValuePair> featuresList = new HashSet<>();
            if (CollUtil.isNotEmpty(productAttributes)) {
                for (ProductAttribute productAttribute : productAttributes) {
                    AttributeTypeEnum attributeType = productAttribute.getAttributeType();
                    String attributeName = productAttribute.getAttributeName();
                    String attributeValue = productAttribute.getAttributeValue();
                    if (StrUtil.isBlank(attributeValue)) {
                        continue;
                    }

                    switch (attributeType) {
                        case Feature:
                            featuresList.add(new EsKeyValuePair(attributeName, attributeValue));
                            break;
                        case GenericSpec:
                            attributeList.add(new EsKeyValuePair(attributeName, attributeValue));
                            break;
                        default:
                            break;
                    }
                }
            }

            esProduct.setSku(productSku.getSku());
            esProduct.setStockTotal(productSku.getStockTotal());
            esProduct.setAttributes(attributeList);
            esProduct.setFeatures(featuresList);
            if (ObjUtil.isNotNull(productSkuPrice)){
                esProduct.setDropShippingPrice(productSkuPrice.getPlatformDropShippingPrice());
                esProduct.setPickUpPrice(productSkuPrice.getPlatformPickUpPrice());
                esProduct.setMsrp(productSkuPrice.getMsrp());
            }
            esProduct.setTransportMethod(StrUtil.emptyToNull(productSkuDetail.getTransportMethod()));

            // 商品标签
            List<ProductLabel> labelList = iProductLabelRelationService.getLabelByProductId(productId);
            List<Long> labelGroup = new ArrayList<>();
            List<String> labelNameGroup = new ArrayList<>();
            if (CollUtil.isNotEmpty(labelList)) {
                labelList.forEach(labelEntity -> {
                    labelGroup.add(labelEntity.getId());
                    labelNameGroup.add(labelEntity.getLabelName());
                });
            }
            esProduct.setLabelGroup(labelGroup);
            esProduct.setLabelNameGroup(labelNameGroup);

            // 是否支持自提
            SupportedLogisticsEnum supportedLogisticsEnum = product.getSupportedLogistics();
            Integer supportedLogistics;
            if (SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogisticsEnum)) {
                supportedLogistics = 0;
            } else if (SupportedLogisticsEnum.PickUpOnly.equals(supportedLogisticsEnum)) {
                supportedLogistics = 1;
            } else {
                supportedLogistics = 2;
            }
            esProduct.setSupportedLogistics(supportedLogistics);

            if (SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogisticsEnum)) {
                if (ObjUtil.isNotNull(productSkuPrice)){
                    esProduct.setMainPrice(productSkuPrice.getPlatformDropShippingPrice());
                }
            } else {
                if (ObjUtil.isNotNull(productSkuPrice)){
                    esProduct.setMainPrice(productSkuPrice.getPlatformPickUpPrice());
                }
            }

            // 禁售渠道
            List<Integer> forbiddenChannelIdList = new ArrayList<>();
            JSONArray forbiddenChannel = product.getForbiddenChannel();
            if (CollUtil.isNotEmpty(forbiddenChannel)) {
                forbiddenChannelIdList = ChannelTypeEnum.convertChannelId(forbiddenChannel.toList(String.class));
            }
            esProduct.setForbiddenChannel(forbiddenChannelIdList);

            // 物流到货天数等相关信息
            List<LogisticsTemplateRateRule> logisticsTemplateRateRules = iLogisticsTemplateRateRuleService.queryByProductSkuCode(productSkuCode);
            Set<Integer> mixDays = new HashSet<>();
            Set<Integer> maxDays = new HashSet<>();
            for (LogisticsTemplateRateRule logisticsTemplateRateRule : logisticsTemplateRateRules) {
                mixDays.add(logisticsTemplateRateRule.getFastestDeliveryTime());
                maxDays.add(logisticsTemplateRateRule.getSlowestDeliveryTime());
            }

            List<Integer> shippingCountry = new ArrayList<>();
            shippingCountry.add(1);
            esProduct.setShippingCountry(shippingCountry);
            esProduct.setFreeShipping(1);
            esProduct.setShippingDayMin(CollUtil.isEmpty(mixDays) ? -1 : CollUtil.min(mixDays));
            esProduct.setShippingDayMax(CollUtil.isEmpty(maxDays) ? -1 : CollUtil.max(maxDays));
            // 销量默认为零，由另一个定时器同步 8.12
            esProduct.setSalesVolume(soldQuantity);

            // 渠道管控
            ProductChannelControl allChannelControl =
                iProductChannelControlService.queryAllChannelControl(productSkuCode);
            List<String> allowTenantId = new ArrayList<>();
            if (allChannelControl != null) {
                JSONArray allowTenantIdArray = allChannelControl.getAllowTenantId();
                if (CollUtil.isNotEmpty(allowTenantIdArray)) {
                    allowTenantId = allowTenantIdArray.toList(String.class);
                }
            }
            if (CollUtil.isEmpty(allowTenantId)) {
                allowTenantId.add("ALL");
            }
            esProduct.setAllowTenantId(allowTenantId);
            esProduct.setProcessingTime(0);
            // 商品活动
            List<ProductActivity> activityList =
                iProductActivityService.queryByProductCodeAndInProgress(productCode);
            List<String> activityTypes = new ArrayList<>();
            if (CollUtil.isNotEmpty(activityList)) {
                activityTypes = activityList.stream().map(activity -> activity.getActivityType().name()).distinct().collect(Collectors.toList());
            }
            esProduct.setActivityTypes(activityTypes);
            log.info("商品SKU{}封装ES信息完成 = {}", productSkuCode, JSONUtil.toJsonStr(esProduct));
        } catch (Exception e) {
            log.error("商品SKU{}封装ES信息出现错误，原因 {}", productSkuCode, e.getMessage(), e);
        }
        return esProduct;
    }

}
