package com.zsmall.product.biz.event;

import cn.hutool.core.collection.CollUtil;
import com.zsmall.extend.event.product.EsProductUploadEvent;
import com.zsmall.product.biz.support.EsProductSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * ES商品同步事件处理器
 * 使用事务事件监听器确保在事务提交后处理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EsProductSyncEventHandler {

    private final EsProductSupport esProductSupport;

    /**
     * 处理ES商品上传事件 - 在事务提交后执行
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleEsProductUpload(EsProductUploadEvent event) {
        try {
            EsProductUploadEvent.Type type = event.getType();
            switch (type) {
                case ProductCode:
                    log.info("事务提交后处理商品ES同步: {}", event.getProductCode());
                    esProductSupport.sendProductMessage(event.getProductCode());
                    break;
                case ProductSkuCode:
                    log.info("事务提交后处理SKU ES同步: {}", event.getProductSkuCodes());
                    esProductSupport.sendProductSkuMessage(event.getProductSkuCodes());
                    break;
                default:
                    log.warn("未知的ES同步事件类型: {}", type);
                    break;
            }
        } catch (Exception e) {
            log.error("处理ES商品同步事件失败", e);
            // 这里可以考虑重试机制或者记录到失败队列
        }
    }

    /**
     * 处理ES商品上传事件 - 在事务回滚后执行（可选）
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_ROLLBACK)
    public void handleEsProductUploadRollback(EsProductUploadEvent event) {
        log.info("事务回滚，取消ES同步操作: {}", event);
        // 可以在这里处理回滚后的清理工作
    }
}
