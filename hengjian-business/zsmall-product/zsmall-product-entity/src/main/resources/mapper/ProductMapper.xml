<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductMapper">
    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.Product">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="productType" column="product_type" jdbcType="VARCHAR"/>
        <result property="shelfState" column="shelf_state" jdbcType="VARCHAR"/>
        <result property="verifyTime" column="verify_time" jdbcType="TIMESTAMP"/>
        <result property="verifyState" column="verify_state" jdbcType="VARCHAR"/>
        <result property="supportedLogistics" column="supported_logistics" jdbcType="VARCHAR"/>
        <result property="forbiddenChannel" column="forbidden_channel" jdbcType="OTHER"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="belongCategoryId" column="belong_category_id" jdbcType="BIGINT"/>
        <result property="downloadCount" column="download_count" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="inventoryPushTime" column="inventory_push_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        tenant_id,
        name,
        description,
        product_code,
        product_type,
        shelf_state,
        verify_time,
        verify_state,
        supported_logistics,
        forbidden_channel,
        belong_category_id,
        download_count,
        del_flag,
        create_by,
        create_time,
        update_by,
        update_time,
        inventoryPushTime
    </sql>

    <select id="existProductCode" resultType="java.lang.Boolean">
        SELECT COUNT(p.id)
        FROM product p
        WHERE p.product_code = #{productCode}
    </select>

    <select id="getProductPage2Label" resultType="com.zsmall.product.entity.domain.Product">
        SELECT
        p.id, p.product_code, p.name,
        p.supported_logistics, psp.platform_pick_up_price, p.create_time,
        0 AS stock_total,
        0 AS sold_quantity
        FROM product p
        INNER JOIN product_sku ps
        ON p.id = ps.product_id AND ps.del_flag = '0'
        INNER JOIN product_sku_price psp
        ON ps.id = psp.product_sku_id
        LEFT JOIN product_label_relation plr
        ON p.id = plr.product_id AND plr.del_flag = '0'
        -- 替换 EXISTS 为 JOIN
        <if test="queryValue != null">
            INNER JOIN product_sku ps2
            ON p.id = ps2.product_id
            AND (ps2.sku LIKE CONCAT('%', #{queryValue}, '%')
            OR ps2.product_sku_code LIKE CONCAT('%', #{queryValue}, '%'))
        </if>
        WHERE p.del_flag = '0'
        AND (p.product_type IS NULL OR p.product_type = 'NormalProduct')
        <if test="labelIdList != null and labelIdList.size > 0">
            AND plr.label_id IN
            <foreach item="item" collection="labelIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY p.id
    </select>

    <select id="queryPageList" resultType="com.zsmall.product.entity.domain.Product">
        SELECT p.id,
               tenant_id,
               belong_category_id,
               create_by,
               create_time,
               description,
               del_flag,
               name,
               product_code,
               product_type,
               shelf_state,
               first_on_shelf_time,
               last_on_shelf_time,
               verify_state,
               verify_time,
               supported_logistics,
               forbidden_channel,
               delivery_time,
               deliver_goods_time,
               download_count,
               update_by,
               update_time,
        inventory_push_time
        FROM product p
        WHERE p.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductName')">
                AND p.name LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'Sku')">
                AND EXISTS(SELECT 1
                           FROM product_sku ps
                           WHERE ps.product_id = p.id
                             AND ps.sku LIKE CONCAT('%',
                                                    #{queryBo.queryValue}, '%')
                             AND ps.del_flag = '0')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductSkuCode')">
                AND EXISTS(SELECT 1
                           FROM product_sku ps
                           WHERE ps.product_id = p.id
                             AND ps.product_sku_code LIKE CONCAT('%',
                                                                 #{queryBo.queryValue}, '%')
                             AND ps.del_flag = '0')
            </if>
        </if>
        <if test="null != queryBo.productSkuCodeList and queryBo.productSkuCodeList.size() > 0">
            AND EXISTS(SELECT 1
            FROM product_sku ps
            WHERE ps.product_id = p.id
            AND ps.product_sku_code in (
            <foreach item="item" collection="queryBo.productSkuCodeList" separator=",">
                #{item}
            </foreach>
            )
            AND ps.del_flag = '0')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.skuShelfState)">
            AND EXISTS(SELECT 1
                       FROM product_sku ps
                       WHERE ps.product_id = p.id
                         AND ps.shelf_state = #{queryBo.skuShelfState}
                         AND ps.del_flag = '0')
        </if>
        <if test="queryBo.siteId!=null ">
            AND EXISTS(SELECT 1
                        FROM product_sku_price psp
                        WHERE psp.product_id = p.id
                            AND psp.site_id = #{queryBo.siteId}
                            AND psp.del_flag = '0')
        </if>

        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.productType)">
            AND p.product_type = #{queryBo.productType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND p.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND p.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OffShelf')">
            AND p.shelf_state IN ('OffShelf', 'ForcedOffShelf')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.auditStatus)">
            AND p.verify_state = #{queryBo.auditStatus}
        </if>
        ORDER BY p.create_time DESC, p.id DESC
    </select>

    <select id="queryByWrapperNoTenant" resultType="com.zsmall.product.entity.domain.Product">
        select p.*
        FROM product p ${ew.getCustomSqlSegment}
    </select>

    <select id="queryByProductSkuCodeNoDelete" resultType="com.zsmall.product.entity.domain.Product">
        SELECT p.*
        FROM product p
        WHERE p.del_flag = '0'
          AND EXISTS(SELECT 1
                     FROM product_sku ps
                     WHERE ps.product_id = p.id
                       AND ps.del_flag = '0'
                       AND ps.product_sku_code = #{productSkuCode})
    </select>

    <select id="queryByProductCodesIncludeDel" resultMap="BaseResultMap">
        SELECT p.*
        FROM product p
        WHERE p.product_code IN
        <foreach collection="productCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByProductSkuCode" resultType="com.zsmall.product.entity.domain.Product">
        SELECT p.*
        FROM product p
        WHERE p.product_code = #{productCode}
    </select>

    <select id="queryByIdIncludeDelete" resultType="com.zsmall.product.entity.domain.Product">
        SELECT p.*
        FROM product p
        WHERE p.id = #{productId}
    </select>

    <select id="querySupportedLogisticsByProductSkuCode"
            resultType="com.zsmall.common.enums.product.SupportedLogisticsEnum">
        SELECT p.supported_logistics
        FROM product p
        WHERE EXISTS(SELECT 1
                     FROM product_sku ps
                     WHERE ps.product_id = p.id
                       AND ps.product_sku_code = #{productSkuCode})
    </select>

    <select id="queryByIdsIncludeDel" resultMap="BaseResultMap">
        SELECT p.*
        FROM product p
        WHERE p.id IN
        <foreach collection="productIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 铺货到渠道一体SQL -->
    <resultMap id="ImportReadyVoMap" type="com.zsmall.product.entity.domain.vo.productMapping.ImportReadyVo">
        <result column="name" property="productName"/>
        <result column="product_code" property="productCode"/>
        <result column="disId"/>
        <result column="channel_type" property="channelType"/>
        <collection property="skuList" select="queryOptionalSkuList" column="product_code"/>
        <collection property="optionalChannelList" select="queryOptionalChannelList"
                    column="{ tenantId = disId, channelType = channel_type }"/>
    </resultMap>
    <select id="queryImportReadyInfo" resultMap="ImportReadyVoMap">
        SELECT p.name, p.product_code, #{disId} AS 'disId', #{channelType} AS 'channel_type'
        FROM product p
        WHERE p.del_flag = '0'
          AND p.shelf_state = 'OnShelf'
          AND p.product_code = #{productCode}
    </select>
    <select id="queryOptionalChannelList"
            resultType="com.zsmall.product.entity.domain.vo.productMapping.OptionalChannelVo">
        SELECT tsc.id, tsc.channel_alias
        FROM tenant_sales_channel tsc
        WHERE tsc.del_flag = '0'
          AND tsc.tenant_id = #{tenantId}
          AND tsc.channel_type = #{channelType}
    </select>
    <resultMap id="OptionalSkuVoMap" type="com.zsmall.product.entity.domain.vo.productMapping.OptionalSkuVo">
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="spec_val_name" property="specValName"/>
        <result column="platform_pick_up_price" property="platformPickUpPrice"/>
        <result column="platform_drop_shipping_price" property="platformDropShippingPrice"/>
        <association property="imageShowUrl" column="product_sku_code"
                     select="com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper.queryFirstImageUrlByProductSkuCode"/>
    </resultMap>
    <select id="queryOptionalSkuList" resultMap="OptionalSkuVoMap">
        SELECT ps.product_sku_code, ps.spec_val_name, psp.platform_pick_up_price, psp.platform_drop_shipping_price
        FROM product_sku ps
                 JOIN product_sku_price psp on ps.id = psp.product_sku_id
        WHERE ps.del_flag = '0'
          AND ps.product_code = #{productCode}
          AND ps.shelf_state = 'OnShelf'
    </select>
    <!-- 铺货到渠道一体SQL -->

    <resultMap id="ProductNewestVoMap" type="com.zsmall.product.entity.domain.vo.product.ProductNewestVo">
        <result column="id"/>
        <result column="name" property="productName"/>
        <result column="product_code" property="productCode"/>
        <result column="product_type" property="productType"/>
        <result column="min_price" property="minPrice"/>
        <result column="stock_total" property="stockTotal"/>
        <association property="productImage" column="id" select="com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper.queryFirstImageUrlByProductId"/>
    </resultMap>
    <select id="queryNewestProduct" resultMap="ProductNewestVoMap">
        SELECT
        p.id,
        p.name,
        p.product_code,
        p.product_type,
        tmp_price.min_price  -- 仅保留价格计算逻辑
        FROM product p
        -- 价格计算派生表（子查询改写为JOIN）
        LEFT JOIN (
        SELECT
        ps.product_id,
        MIN(
        CASE
        WHEN p_inner.supported_logistics = 'DropShippingOnly'
        THEN psp.platform_drop_shipping_price
        ELSE psp.platform_pick_up_price
        END
        ) AS min_price
        FROM product_sku ps
        JOIN product_sku_price psp ON ps.id = psp.product_sku_id
        JOIN product p_inner ON ps.product_id = p_inner.id
        WHERE ps.del_flag = '0'
        AND ps.shelf_state = 'OnShelf'
        <if test="isZjHj == true"> AND ps.sku NOT LIKE 'ZJHJ%' </if>
        GROUP BY ps.product_id
        ) tmp_price ON p.id = tmp_price.product_id
        WHERE p.del_flag = '0'
        AND p.shelf_state = 'OnShelf'
        -- 排除特定SKU产品
        AND NOT EXISTS (
        SELECT 1
        FROM product_sku pss
        WHERE pss.product_id = p.id
        AND pss.del_flag = '0'
        AND pss.shelf_state = 'OnShelf'
        <if test="isZjHj == true"> AND pss.sku LIKE 'ZJHJ%' </if>
        )
        ORDER BY p.create_time DESC, p.id DESC
    </select>

    <select id="getProductOnShelfDTOByProductSkuCode"
            resultType="com.zsmall.product.entity.domain.dto.product.ProductOnShelfDTO">
        SELECT p.id                        AS productId,
               p.name                      AS productName,
               p.product_code              AS productCode,
               p.pick_up                   AS pickUpType,
               ps.id                       AS productSkuId,
               ps.stock_total              AS stock,
               ps.sku                      AS productSku,
               psd.transport_method        AS transportMethod,
               psp.platform_pick_up_price  AS price,
               psp.platform_unit_price     AS dropshippingPrice
        FROM product p
                 INNER JOIN product_sku ps ON ps.product_id = p.id
                 INNER JOIN product_sku_detail psd on ps.id = psd.product_sku_id
                 INNER JOIN product_sku_price psp on ps.id = psp.product_sku_id
        WHERE p.shelf_state = 'OnShelf'
          AND p.del_flag = '0'
          AND ps.del_flag = '0'
          AND ps.product_sku_code = #{productSkuCode}
    </select>

    <select id="queryNormalProductForES" resultMap="BaseResultMap">
        SELECT p.*
        FROM product p
        WHERE p.del_flag = '0'
          AND p.verify_state = 'Accepted'
          AND p.product_type = 'NormalProduct'
          AND EXISTS(SELECT 1 FROM product_sku ps WHERE ps.del_flag = '0')
    </select>
    <select id="getAllBizArkProduct" resultType="java.lang.String">
        select DISTINCT product_sku_code
        from product_sku_stock
        where warehouse_system_code in (select warehouse_system_code
                                        from warehouse
                                        where warehouse_type = 'BizArk' and del_flag = 0 and warehouse_state = 1)and del_flag = 0;

    </select>
    <select id="queryPageListForMemberLevel" resultType="com.zsmall.product.entity.domain.Product">
        SELECT p.id,
        tenant_id,
        belong_category_id,
        create_by,
        create_time,
        description,
        name,
        product_code,
        product_type,
        shelf_state,
        first_on_shelf_time,
        last_on_shelf_time,
        verify_state,
        verify_time,
        supported_logistics
        FROM product p
        WHERE p.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductName')">
                AND p.name LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'Sku')">
                AND EXISTS(SELECT 1
                FROM product_sku ps
                WHERE ps.product_id = p.id
                AND ps.sku LIKE CONCAT('%',
                #{queryBo.queryValue}, '%')
                AND ps.del_flag = '0')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductSkuCode')">
                AND EXISTS(SELECT 1
                FROM product_sku ps
                WHERE ps.product_id = p.id
                AND ps.product_sku_code LIKE CONCAT('%',
                #{queryBo.queryValue}, '%')
                AND ps.del_flag = '0')
            </if>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.skuShelfState)">
            AND EXISTS(SELECT 1
                FROM product_sku ps
            WHERE ps.product_id = p.id
            AND ps.shelf_state = #{queryBo.skuShelfState}
            and ps.del_flag = '0')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.skuAuditStatus)">
            AND EXISTS(SELECT 1
            FROM product_sku ps
            WHERE ps.product_id = p.id
            AND ps.verify_state = #{queryBo.skuAuditStatus}
            AND ps.del_flag = '0')
        </if>
        <if test="queryBo.isMemberPrice!=null">
            <if test="queryBo.isMemberPrice==0">
                and !exists(select * from rule_level_product_price where product_id = p.id and del_flag = '0')
            </if>
            <if test="queryBo.isMemberPrice==1">
                and exists(select * from rule_level_product_price where product_id = p.id and
                (original_drop_shipping_price is not null or original_pick_up_price is not null and del_flag = '0'))
            </if>
        </if>
        <if test="queryBo.siteId!=null">
            and exists(select * from product_sku_price where product_id = p.id and
            site_id = #{queryBo.siteId} and del_flag = '0')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.productType)">
            AND p.product_type = #{queryBo.productType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND p.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND p.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OffShelf')">
            AND p.shelf_state IN ('OffShelf', 'ForcedOffShelf')
        </if>
        ORDER BY p.create_time DESC, p.id DESC
    </select>

    <update id="updateProductInventoryPushTime">
        update product as p
            inner join product_sku as ps
            on p.id = ps.product_id
        set p.inventory_push_time= now()
        where ps.product_sku_code = #{productSkuCode,jdbcType=VARCHAR}

    </update>

    <select id="queryExportDate" resultType="com.zsmall.product.entity.domain.vo.product.ProductExportDto">
        SELECT
        p.id as productId,
        p.product_code as productCode,
        p.supported_logistics as supportedLogistics,
        ps.id as productSkuId,
        ps.name as productName,
        ps.product_sku_code as productSkuCode,
        ps.sku as sku,
        ps.shelf_state as skuShelfState,
        ps.spec_val_name as specValName,
        ps.stock_total as stockTotal,
        ps.verify_state as verifyState,
        p.create_time as productCreateTime,
        (vpss.sold_quantity - vpss.refund_quantity) AS stockSold
        FROM product p
        inner join product_sku ps on p.id=ps.product_id
        JOIN view_product_sku_sales vpss on ps.id = vpss.sku_id
        WHERE p.del_flag = '0'
        AND ps.del_flag = '0'
        and p.tenant_id=#{tenantId}
        and ps.tenant_id=#{tenantId}
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductName')">
                AND p.name LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'Sku')">
                AND ps.sku LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductSkuCode')">
                AND ps.product_sku_code LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.productType)">
            AND p.product_type = #{queryBo.productType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND p.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND p.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OffShelf')">
            AND p.shelf_state IN ('OffShelf', 'ForcedOffShelf')
        </if>
        ORDER BY p.create_time DESC
        limit  #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <select id="getProductExportCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM product p
        inner join product_sku ps on p.id=ps.product_id
        WHERE p.del_flag = '0'
        AND ps.del_flag = '0'
        and p.tenant_id=#{tenantId}
        and ps.tenant_id=#{tenantId}
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductName')">
                AND p.name LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'Sku')">
                AND ps.sku LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductSkuCode')">
                AND ps.product_sku_code LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.productType)">
            AND p.product_type = #{queryBo.productType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND p.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND p.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OffShelf')">
            AND p.shelf_state IN ('OffShelf', 'ForcedOffShelf')
        </if>
    </select>
    <select id="queryExportInventoryDimension"
            resultType="com.zsmall.product.entity.domain.vo.product.ProductExportDto">
        SELECT
        pss.id,
        t1.productName,
        t1.productCode,
        t1.verifyState,
        t1.productSkuCode,
        t1.sku,
        t1.skuShelfState,
        t1.specValName,
        pss.warehouse_system_code,
        pss.stock_available,
        pss.tenant_id,
        pss.create_time,
        t1.stockSold
        FROM
        product_sku_stock pss
        INNER JOIN
        (SELECT
        p.id AS productId,
        p.product_code AS productCode,
        p.supported_logistics AS supportedLogistics,
        ps.id AS productSkuId,
        ps.name AS productName,
        ps.product_sku_code AS productSkuCode,
        ps.sku AS sku,
        ps.shelf_state AS skuShelfState,
        ps.spec_val_name AS specValName,
        ps.stock_total AS stockTotal,
        ps.verify_state AS verifyState,
        p.create_time AS productCreateTime,
        (vpss.sold_quantity - vpss.refund_quantity) AS stockSold
        FROM
        product p
        INNER JOIN
        product_sku ps ON p.id = ps.product_id
        INNER JOIN
        view_product_sku_sales vpss ON ps.id = vpss.sku_id
        WHERE
        p.del_flag = '0'
        AND ps.del_flag = '0'
        AND p.tenant_id = #{tenantId}
        AND ps.tenant_id = #{tenantId}
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductName')">
                AND p.name LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'Sku')">
                AND ps.sku LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductSkuCode')">
                AND ps.product_sku_code LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductCodes')">
                <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(dto.queryValues)">
                    AND p.product_code IN
                    <foreach collection="dto.queryValues" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.productType)">
            AND p.product_type = #{queryBo.productType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND ps.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OffShelf')">
            AND ps.shelf_state ='OffShelf'
        </if>
        ) t1
        ON
        pss.product_sku_code = t1.productSkuCode
        WHERE
        pss.tenant_id = #{tenantId}
        AND pss.del_flag = 0
        ORDER BY pss.create_time DESC,pss.id asc
        limit  #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>
    <sql id="commonProductSkuStockQuery">
        SELECT
        psp.currency,
        psp.country_code,
        psp.site_id,
        pss.id,
        ps.name AS productName,
        p.product_code AS productCode,
        ps.verify_state AS verifyState,
        ps.product_sku_code AS productSkuCode,
        ps.sku,
        ps.shelf_state AS skuShelfState,
        ps.spec_val_name AS specValName,
        pss.warehouse_system_code,
        pss.stock_available,
        pss.tenant_id,
        pss.create_time,
        ps.id AS productSkuId,
        pss.drop_shipping_stock_available,
        pss.pickup_lock_used + pss.pickup_lock_reserved as pickupLockUsed,
        pss.drop_shipping_lock_used + pss.drop_shipping_lock_reserved as dropShippingLockUsed
<!--        (vpss.sold_quantity - vpss.refund_quantity) AS stockSold-->
        FROM
        product_sku_price psp
        INNER JOIN product_sku_stock pss ON psp . product_sku_code = pss . product_sku_code COLLATE utf8mb4_0900_ai_ci
        INNER JOIN product_sku ps ON pss . product_sku_code = ps . product_sku_code COLLATE utf8mb4_0900_ai_ci
        INNER JOIN product p ON ps.product_id = p.id
<!--        INNER JOIN view_product_sku_sales vpss ON ps . id = vpss . sku_id COLLATE utf8mb4_0900_ai_ci-->
        WHERE
        psp.del_flag = 0
        AND pss.tenant_id = #{tenantId}
        AND pss.del_flag = 0
        AND p.tenant_id = #{tenantId}
        AND p.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductName')">
                AND p.name LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'Sku')">
                AND ps.sku LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductSkuCode')">
                AND psp.product_sku_code LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(queryBo.queryValues)">
            AND p.product_code IN
            <foreach collection="queryBo.queryValues" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.productType)">
            AND p.product_type = #{queryBo.productType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OnShelf')">
            AND p.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.skuShelfState, 'OnShelf')">
            AND ps.shelf_state = 'OnShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.shelfState, 'OffShelf')">
            AND p.shelf_state ='OffShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.skuShelfState, 'OffShelf')">
            AND ps.shelf_state ='OffShelf'
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.auditStatus)">
            AND p.verify_state =#{queryBo.auditStatus}
        </if>
        <if test="null != queryBo.lockExceptionCode">
            and pss.lock_exception_code = #{queryBo.lockExceptionCode}
        </if>
    </sql>
    <select id="getExportInventoryDimensionCount" resultType="java.lang.Integer">
        select count(t2.id) from (
        <include refid="com.zsmall.product.entity.mapper.ProductMapper.commonProductSkuStockQuery"/>
        ) t2
    </select>
    <select id="queryExportInventoryDimensionV2"
            resultType="com.zsmall.product.entity.domain.vo.product.ProductExportDto">

        <include refid="com.zsmall.product.entity.mapper.ProductMapper.commonProductSkuStockQuery"/>
        ORDER BY
        pss.create_time DESC,
        pss.id ASC

    </select>
    <resultMap id="getByProductSkuCodesMap" extends="BaseResultMap" type="com.zsmall.product.entity.domain.Product">
        <result column="productCode" property="productCode"/>
    </resultMap>
    <select id="getByProductSkuCodes" resultMap="getByProductSkuCodesMap" >
        select p.*, ps.product_sku_code as productCode
        from product p
                 inner join product_sku ps on p.id = ps.product_id
        where 1 = 1
          AND ps.product_sku_code IN
        <foreach collection="productSkuCodes" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="isAllSkuOffShelfBySpu" resultType="boolean">
        SELECT
            NOT EXISTS (
                SELECT 1
                FROM product p
                         JOIN product_sku ps ON p.id = ps.product_id
                WHERE p.product_code = #{productCode}
                  AND ps.shelf_state = 'OnShelf'
                  AND ps.del_flag = '0'
                  AND p.del_flag = '0'
            ) AS all_off_shelf;
    </select>
    <select id="getUnrelatedProducts" resultType="com.zsmall.product.entity.domain.Product">
        SELECT p.*
        FROM product p
                 LEFT JOIN product_sku ps ON p.id = ps.product_id AND ps.del_flag = 0
        WHERE p.del_flag = 0 and p.tenant_id = #{targetTenantId}
          AND ps.id IS NULL;

    </select>


</mapper>
