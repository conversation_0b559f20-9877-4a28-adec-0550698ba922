<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.WarningMessageMapper">

    <resultMap type="com.zsmall.product.entity.domain.dto.warningMessage.WarningMessage" id="WarningMessageResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="tenantType"    column="tenant_type"    />
        <result property="warningMessageType"    column="warning_message_type"    />
        <result property="warningMessageSubType"    column="warning_message_sub_type"    />
        <result property="content"    column="content"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>



    <!-- 根据租户类型和业务类型查询未读消息数量 -->
    <select id="countUnreadMessages" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM warning_message wm
        inner join  warning_message_tenant wmt
        on wm.id = wmt.warning_message_id
        WHERE wm.del_flag = 0
        AND wmt.del_flag = 0
        AND wmt.is_read = 0
    </select>

    <!-- 批量标记消息为已读 -->
    <update id="batchMarkAsRead">
        UPDATE warning_message_tenant
        SET is_read = #{isRead}, update_time = NOW()
        WHERE warning_message_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </update>


    <select id="getList" resultType="com.zsmall.product.entity.domain.vo.warningMessage.WarningMessageVo">
        select wm.*,wmt.is_read
        from warning_message wm
        inner join warning_message_tenant wmt
        on wm.id = wmt.warning_message_id
        where wm.del_flag=0
        and wmt.del_flag=0
        <if test="bo.title != null and bo.title != ''">
            and wm.title like concat('%',#{bo.title},'%')
        </if>
        <if test="bo.warningMessageType != null">
            and wm.warning_message_type = #{bo.warningMessageType}
        </if>
        <if test="bo.warningMessageSubType != null">
            and wm.warning_message_sub_type = #{bo.warningMessageSubType}
        </if>
        <if test="bo.isRead != null">
            and wmt.is_read = #{bo.isRead}
        </if>
        <if test="bo.createTimeStart != null">
            and wm.create_time &lt;= #{bo.createTimeStart}
        </if>
        <if test="bo.createTimeEnd != null">
            and wm.create_time &gt;= #{bo.createTimeEnd}
        </if>

    </select>

    <update id="deleteWarningMessage">
        update warning_message_tenant set del_flag=1
        where warning_message_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getWarningMessageList"
            resultType="com.zsmall.product.entity.domain.vo.warningMessage.WarningMessageVo">
        select *
        from warning_message wm
        inner join warning_message_tenant wmt
        on wm.id = wmt.warning_message_id
        where wm.del_flag=0
        and wmt.del_flag=0
        <if test="bo.title != null and bo.title != ''">
            and wm.title like concat('%',#{bo.title},'%')
        </if>
        <if test="bo.createTimeStart != null">
            and wm.create_time &gt;= #{bo.createTimeStart}
        </if>
        <if test="bo.createTimeEnd != null">
            and wm.create_time &lt;= #{bo.createTimeEnd}
        </if>
        <if test="bo.warningMessageType != null">
            and wm.warning_message_type = #{bo.warningMessageType}
        </if>
        <if test="bo.warningMessageSubType != null">
            and wm.warning_message_sub_type = #{bo.warningMessageSubType}
        </if>
        <if test="bo.isRead != null">
            and wmt.is_read = #{bo.isRead}
        </if>
        order by wm.id desc
    </select>

</mapper>
