<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuMapper">
    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSku">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="productId" column="product_id" jdbcType="BIGINT"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="sku" column="sku" jdbcType="VARCHAR"/>
        <result property="upc" column="upc" jdbcType="VARCHAR"/>
        <result property="stockTotal" column="stock_total" jdbcType="INTEGER"/>
        <result property="stockManager" column="stock_manager" jdbcType="VARCHAR"/>
        <result property="shelfState" column="shelf_state" jdbcType="VARCHAR"/>
        <result property="verifyState" column="verify_state" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <update id="updateStockTotalBatch">
        UPDATE product_sku
        SET stock_total = CASE
        <foreach collection="stockTotalMap" item="entry" index="key" separator=" ">
            WHEN id = #{key} THEN #{entry.value}
        </foreach>
        ELSE stock_total
        END
        WHERE id IN
        <foreach collection="stockTotalMap.keySet()" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="queryByIdsIncludeDel" resultMap="BaseResultMap">
        SELECT ps.*
        FROM product_sku ps
        WHERE ps.id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getBoundApplicableProductSkuNum" resultType="java.lang.Integer">
        SELECT count(DISTINCT rela.id)
        FROM product_sku sku
                 JOIN product p ON sku.product_id = p.id
                 JOIN product_sku_price psp ON psp.product_sku_id = sku.id AND psp.del_flag = '0'
                 JOIN product_sku_price_rule_relation rela ON rela.product_sku_id = sku.id AND rela.del_flag = '0'
                 JOIN product_sku_price_rule r ON r.id = rela.product_sku_price_rule_id AND r.del_flag = '0'
        WHERE p.shelf_state = 'OnShelf'
          AND sku.del_flag = '0'
        <if test="dto.ruleCode != null and dto.ruleCode != ''">
            AND r.rule_code != #{dto.ruleCode}
        </if>
        <if test="dto.startPrice != null and dto.endPrice != null">
            AND psp.original_unit_price &gt;= #{dto.startPrice}
            AND psp.original_unit_price &lt;= #{dto.endPrice}
        </if>
        <if test="dto.itemNos != null and dto.itemNos.size > 0">
            AND sku.product_sku_code IN
            <foreach collection="dto.itemNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.deleteItemNoList != null and dto.deleteItemNoList.size > 0">
            AND sku.product_sku_code NOT IN
            <foreach collection="dto.deleteItemNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getApplicableProductSku"
            resultType="com.zsmall.product.entity.domain.dto.productSku.ApplicableProductSkuDTO">
        SELECT sku.id                                     as productSkuId,
               rela.product_sku_price_rule_id             as skuPriceRuleId,
               sku.product_sku_code                       as itemNo,
               psp.id                                     as productSkuPriceId,
               IFNULL(psp.original_unit_price, 0)         as unitPrice,
               IFNULL(psp.original_operation_fee, 0)      as operationFee,
               IFNULL(psp.original_final_delivery_fee, 0) as finalDeliveryFee
        FROM product_sku sku
                 JOIN product p ON sku.product_id = p.id
                 JOIN product_sku_price psp ON psp.product_sku_id = sku.id AND psp.del_flag = '0'
                 LEFT JOIN product_sku_price_rule_relation rela ON rela.product_sku_id = sku.id AND rela.del_flag = '0'
                 LEFT JOIN product_sku_price_rule r ON r.id = rela.product_sku_price_rule_id AND r.del_flag = '0'
        WHERE sku.del_flag = '0'
          AND p.shelf_state = 'OnShelf'
          AND sku.shelf_state = 'OnShelf'
        <if test="dto.type != null and dto.type != ''">
            <if test="dto.type == 'sku'">
                AND sku.sku like CONCAT('%', #{dto.queryValue}, '%')
            </if>
            <if test="dto.type == 'itemNo'">
                AND sku.product_sku_code like CONCAT('%', #{dto.queryValue}, '%')
            </if>
            <if test="dto.type == 'productName'">
                AND p.name like CONCAT('%', #{dto.queryValue}, '%')
            </if>
        </if>
        <if test="dto.ruleCode != null and dto.ruleCode != ''">
            AND (r.rule_code IS NULL OR r.rule_code != #{dto.ruleCode})
        </if>
        <if test="dto.startPrice != null and dto.endPrice != null">
            AND psp.original_unit_price &gt;= #{dto.startPrice}
            AND psp.original_unit_price &lt;= #{dto.endPrice}
        </if>
        <if test="dto.itemNos != null and dto.itemNos.size > 0">
            AND sku.product_sku_code IN
            <foreach collection="dto.itemNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.deleteItemNoList != null and dto.deleteItemNoList.size > 0">
            AND sku.product_sku_code NOT IN
            <foreach collection="dto.deleteItemNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.productSkuPriceIds != null and dto.productSkuPriceIds.size > 0">
            AND psp.id IN
            <foreach collection="dto.productSkuPriceIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        GROUP BY sku.id
    </select>

    <select id="existProductSkuCode" resultType="java.lang.Boolean">
        SELECT COUNT(ps.id)
        FROM product_sku ps
        WHERE ps.product_sku_code = #{productSkuCode}
    </select>

    <select id="existSku" resultType="java.lang.Boolean">
        SELECT COUNT(ps.id)
        FROM product_sku ps
                 JOIN product p on ps.product_id = p.id
        WHERE ps.sku = #{sku}
          AND ps.tenant_id = #{tenantId}
        <if test="excludeId != null">
            AND ps.id != #{excludeId}
        </if>
        AND ps.del_flag = '0'
        AND p.del_flag = '0'
    </select>

    <select id="existUpc" resultType="java.lang.Boolean">
        SELECT COUNT(ps.id)
        FROM product_sku ps
                 JOIN product p on ps.product_id = p.id
        WHERE ps.upc = #{upc}
        <if test="excludeId != null">
            AND ps.id != #{excludeId}
        </if>
        AND ps.del_flag = '0'
        AND p.del_flag = '0'
    </select>

    <select id="countSkuDuplicate" resultType="java.lang.Integer">
        SELECT COUNT(ps.id)
        FROM product_sku ps
                 JOIN product p ON p.id = ps.product_id
        WHERE ps.del_flag = '0'
          AND p.del_flag = '0'
        <if test="id != null">
            AND ps.id != #{id}
        </if>
        <if test="sku != null and sku != ''">
            AND ps.sku != #{sku}
        </if>
        <if test="productId != null">
            AND p.id = #{productId}
        </if>
        AND ps.shelf_state IN ('OnShelf', 'OffShelf')
        AND p.shelf_state IN ('OnShelf', 'OffShelf')
    </select>

    <select id="countUpcDuplicate" resultType="java.lang.Integer">
        SELECT COUNT(ps.id)
        FROM product_sku ps
                 JOIN product p ON p.id = ps.product_id
        WHERE ps.del_flag = '0'
          AND p.del_flag = '0'
        <if test="upc != null and upc != ''">
            AND ps.upc = #{upc}
        </if>
        <if test="id != null">
            AND ps.id != #{id}
        </if>
        AND ps.shelf_state IN ('OnShelf', 'OffShelf')
        AND p.shelf_state IN ('OnShelf', 'OffShelf')
    </select>

    <select id="queryStockTotal" resultType="java.lang.Integer">
        SELECT IFNULL(ps.stock_total, 0)
        FROM product_sku ps
        WHERE ps.product_sku_code = #{productSkuCode}
          AND ps.shelf_state = 'OnShelf'
          AND ps.del_flag = '0'
    </select>

    <select id="queryProductSkuListVoByProductId"
            resultType="com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo">
        SELECT ps.sku                                      AS 'sku',
               ps.product_sku_code                         AS 'productSkuCode',
               ps.spec_val_name                            AS 'specValName',
               ps.stock_total                              AS 'stockTotal',
               (vpss.sold_quantity - vpss.refund_quantity) AS 'stockSold',
               ps.shelf_state                              AS 'skuShelfState',
               psp.original_pick_up_price                  AS 'pickUpPrice',
               psp.original_drop_shipping_price            AS 'dropShippingPrice',
               ps.product_id                               AS 'productId'
        FROM product_sku ps
                 JOIN product_sku_price psp on ps.id = psp.product_sku_id
                 JOIN view_product_sku_sales vpss on ps.id = vpss.sku_id
        WHERE ps.del_flag = '0'
          AND ps.product_id = #{productId}
    </select>
    <select id="queryProductSkuListVoByProductIds"
            resultMap="ProductSkuResultMap">
        SELECT
        ps.id AS 'productSkuId',
        ps.product_id AS 'productId',
        ps.sku AS 'sku',
        ps.product_sku_code AS 'productSkuCode',
        ps.spec_val_name AS 'specValName',
        ps.spec_compose_name AS 'specComposeName',
        ps.stock_total AS 'stockTotal',
<!--        (vpss.rvpss.sold_quantity - efund_quantity) AS 'stockSold',-->
        ps.shelf_state AS 'skuShelfState',
        psp.original_pick_up_price AS 'pickUpPrice',
        psp.original_drop_shipping_price AS 'dropShippingPrice',
        psp.original_unit_price AS 'unitPrice',
        psp.original_operation_fee AS 'operationFee',
        psp.original_final_delivery_fee AS 'finalDeliveryFee',
        psp.id AS 'productSkuPriceId',
        psp.site_id as siteId,
        psp.currency as currency,
        psp.currency_symbol as currencySymbol,
        psp.country_code as countryCode
        FROM product_sku ps
        JOIN product_sku_price psp on ps.id = psp.product_sku_id
<!--        JOIN view_product_sku_sales vpss on ps.id = vpss.sku_id-->
        WHERE ps.del_flag = '0' and psp.del_flag = '0'
        <if test="productIds != null and productIds.size() > 0">
            and ps.product_id in
            <foreach collection="productIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="siteId != null">
            and psp.site_id =#{siteId}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.queryValue)">

            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(bo.queryType, 'Sku')">
                AND ps.sku LIKE CONCAT('%',
                #{bo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(bo.queryType, 'ProductSkuCode')">
                AND ps.product_sku_code LIKE CONCAT('%',
                #{bo.queryValue}, '%')
            </if>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.skuShelfState)">
            AND ps.shelf_state = #{bo.skuShelfState}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.skuAuditStatus)">
            AND ps.verify_state = #{bo.skuAuditStatus}
        </if>
    </select>

    <select id="queryWholesaleProductSkuListVoByProductId"
            resultType="com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo">
        SELECT ps.id                                       AS 'skuId',
               ps.sku                                      AS 'sku',
               ps.product_sku_code                         AS 'productSkuCode',
               ps.spec_val_name                            AS 'specValName',
               ps.stock_total                              AS 'stockTotal',
               IFNULL((vpss.sold_quantity - vpss.refund_quantity), 0) AS 'stockSold',
               ps.shelf_state                              AS 'skuShelfState'
        FROM product_sku ps
                 LEFT JOIN view_product_sku_sales vpss on ps.id = vpss.sku_id
        WHERE ps.del_flag = '0'
          AND ps.product_id = #{productId}
        <if test="rejected != null">
            AND ps.verify_state != #{rejected}
        </if>
        GROUP BY ps.id
    </select>

    <select id="queryByProductIdIncludeDel" resultMap="BaseResultMap">
        SELECT ps.*
        FROM product_sku ps
        WHERE ps.product_id = #{productId}
    </select>

    <select id="queryIdsByProductId" resultType="java.lang.Long">
        SELECT ps.id
        FROM product_sku ps
        WHERE ps.product_id = #{productId}
          AND ps.del_flag = '0'
    </select>

    <select id="queryByProductSkuCodeAndWarehouseSystemCode"
            resultType="com.zsmall.product.entity.domain.ProductSku">
        SELECT sku.*
        FROM product_sku sku
                 JOIN product p ON sku.product_id = p.id
        WHERE sku.product_sku_code = #{productSkuCode}
          AND sku.shelf_state = 'OnShelf'
          AND sku.del_flag = '0'
          AND p.shelf_state = 'OnShelf'
          AND p.del_flag = '0'
        <if test="warehouseSystemCode != null">
            AND EXISTS (SELECT 1
                        FROM product_sku_stock pss
                        WHERE pss.product_sku_code = sku.product_sku_code
                          AND pss.warehouse_system_code =
                              #{warehouseSystemCode})
        </if>
    </select>

    <select id="queryAdjustStockVo"
            resultType="com.zsmall.product.entity.domain.vo.productSku.ProductSkuAdjustStockVo">
        SELECT p.shelf_state AS 'spuShelfState', ps.shelf_state AS 'skuShelfState', ps.stock_total AS 'stockTotal'
        FROM product_sku ps
                 JOIN product p ON ps.product_id = p.id
        WHERE p.del_flag = '0'
          AND ps.del_flag = '0'
          AND ps.product_sku_code = #{productSkuCode}
    </select>

    <select id="queryStockManagerCountry" resultType="java.lang.String">
        SELECT DISTINCT(wa.country)
        FROM product_sku ps
                 JOIN product_sku_stock pss on ps.product_sku_code = pss.product_sku_code
                 JOIN warehouse w on pss.warehouse_system_code = w.warehouse_system_code
                 JOIN warehouse_address wa ON w.id = wa.warehouse_id
        WHERE ps.del_flag = '0'
          AND pss.del_flag = '0'
          AND w.del_flag = '0'
          AND ps.product_sku_code = #{productSkuCode}
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(specifyWarehouse)">
            AND w.warehouse_system_code = #{specifyWarehouse}
        </if>
    </select>

    <select id="queryByProductSkuCodeIncludeDel" resultType="com.zsmall.product.entity.domain.ProductSku">
        SELECT ps.*
        FROM product_sku ps
        WHERE ps.product_sku_code = #{productSkuCode}
    </select>

    <select id="queryByProductSkuCodesIncludeDel" resultType="com.zsmall.product.entity.domain.ProductSku">
        SELECT ps.*
        FROM product_sku ps
        WHERE ps.product_sku_code IN
        <foreach collection="productSkuCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getPage" resultMap="BaseResultMap">
        SELECT sku.*
        FROM product_sku sku
        WHERE sku.del_flag = '0'
        <if test="skuShelfState != null and skuShelfState !=''">
            AND sku.shelf_state = #{skuShelfState}
        </if>
        <if test="skuAuditStatus != null and skuAuditStatus !=''">
            AND sku.verify_state = #{skuAuditStatus}
        </if>
        <if test="queryValue != null">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductName')">
                AND EXISTS (SELECT 1
                            FROM product p
                            WHERE p.del_flag = '0'
                              AND p.id = sku.product_id
                              AND p.shelf_state != 'ForcedOffShelf'
                              AND p.name LIKE CONCAT('%', #{queryValue}, '%'))
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'Sku')">
                AND sku.sku LIKE CONCAT('%', #{queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductSkuCode')">
                AND sku.product_sku_code LIKE CONCAT('%', #{queryValue}, '%')
            </if>
        </if>
        <if test="siteId != null and siteId != ''">
            AND EXISTS (
            SELECT 1
            FROM product_sku_price psp
            WHERE psp.product_sku_id = sku.id
            AND psp.site_id = #{siteId}
            AND psp.del_flag = '0'
            )
        </if>
        AND EXISTS (
        SELECT 1
        FROM product_sku_price psp
        WHERE psp.product_sku_id = sku.id
        AND psp.del_flag = '0'
        )
        AND EXISTS (
        SELECT 1
        FROM product p
        WHERE p.id = sku.product_id
        AND p.del_flag = '0'
        )
        ORDER BY sku.create_time DESC, sku.id DESC
    </select>

    <select id="queryProductSkuControlPage" resultType="com.zsmall.product.entity.domain.ProductSku">
        SELECT ps.*
        FROM product_sku ps WHERE ps.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductName')">
                AND EXISTS (SELECT 1
                            FROM product p
                            WHERE p.del_flag = '0'
                              AND p.id = ps.product_id
                              AND p.name LIKE CONCAT('%', #{queryValue}, '%'))
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'Sku')">
                AND ps.sku LIKE CONCAT('%', #{queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductSkuCode')">
                AND ps.product_sku_code LIKE CONCAT('%', #{queryValue}, '%')
            </if>
        </if>
        ORDER BY ps.create_time DESC
    </select>

    <resultMap id="ProductSkuAndStockMap" type="com.zsmall.product.entity.domain.vo.productSku.ProductSkuAndStockVo">
        <result column="productImg" property="productImg"/>
        <result column="name" property="productName"/>
        <result column="sku" property="productSku"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="stock_total" property="stockTotal"/>
        <result column="supported_logistics" property="supportedLogistics"/>
        <result column="original_pick_up_price" property="pickUpPrice"/>
        <result column="original_drop_shipping_price" property="dropShippingPrice"/>
        <collection property="stockList" column="product_sku_code" javaType="list"
                    select="com.zsmall.product.entity.mapper.ProductSkuStockMapper.querySimpleVoByProductSkuCode"/>
    </resultMap>
    <select id="queryProductSkuAndStock"
            resultMap="ProductSkuAndStockMap">
        SELECT (SELECT psa.attachment_show_url
                FROM product_sku_attachment psa
                WHERE psa.product_sku_id = ps.id
                  AND psa.del_flag = '0'
                ORDER BY psa.attachment_sort ASC
                LIMIT 0,1) AS 'productImg',
               p.name,
               ps.sku,
               ps.product_sku_code,
               ps.stock_total,
               p.supported_logistics,
               psp.original_pick_up_price,
               psp.original_drop_shipping_price
        FROM product_sku ps
                 JOIN product p ON ps.product_id = p.id
                 JOIN product_sku_price psp on ps.id = psp.product_sku_id
        WHERE p.del_flag = '0'
          AND p.shelf_state != 'ForcedOffShelf'
          AND ps.del_flag = '0'
          AND ps.shelf_state != 'ForcedOffShelf'
        <if test="queryValue != null">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductName')">
                AND p.name LIKE CONCAT('%', #{queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'Sku')">
                AND ps.sku LIKE CONCAT('%', #{queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductSkuCode')">
                AND ps.product_sku_code LIKE CONCAT('%', #{queryValue}, '%')
            </if>
        </if>
        ORDER BY ps.create_time DESC, ps.id DESC
    </select>

    <select id="queryProductSkuAndStockByCode" resultMap="ProductSkuAndStockMap">
        SELECT (SELECT psa.attachment_show_url
                FROM product_sku_attachment psa
                WHERE psa.product_sku_id = ps.id
                  AND psa.del_flag = '0'
                ORDER BY psa.attachment_sort ASC
                LIMIT 0,1) AS 'productImg',
               p.name,
               ps.sku,
               ps.product_sku_code,
               ps.stock_total,
               p.supported_logistics,
               psp.original_pick_up_price,
               psp.original_drop_shipping_price
        FROM product_sku ps
                 JOIN product p ON ps.product_id = p.id
                 JOIN product_sku_price psp on ps.id = psp.product_sku_id
        WHERE p.del_flag = '0'
          AND p.shelf_state != 'ForcedOffShelf'
          AND ps.del_flag = '0'
          AND ps.shelf_state != 'ForcedOffShelf'
          AND ps.product_sku_code = #{productSkuCode}
    </select>

    <resultMap id="ProductCustomExportVoMap" type="com.zsmall.product.entity.domain.vo.product.ProductCustomExportVo">
        <result column="product_id"/>
        <result column="tenant_id" property="supplierId"/>
        <result column="belong_category_id"/>
        <result column="email" property="supplierEmail"/>
        <result column="phonenumber" property="phoneNumber"/>
        <result column="product_type" property="productType"/>
        <result column="product_code" property="productCode"/>
        <result column="name" property="productName"/>
        <result column="create_time" property="createTime"/>
        <result column="sku" property="sku"/>
        <result column="erp_sku" property="erpSku"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="spec_val_name" property="skuSpec"/>
        <result column="platform_pick_up_price" property="pickUpPrice"/>
        <result column="platform_drop_shipping_price" property="dropShippingPrice"/>
        <result column="msrp" property="msrp"/>
        <result column="stock_total" property="stockQuantity"/>
        <result column="stock_manager" property="stockManager"/>
        <result column="verify_state" property="reviewState"/>
        <result column="shelf_state" property="shelfState"/>
        <result column="forbidden_channel" property="forbiddenChannel"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="supported_logistics" property="supportedLogistics"/>
        <result column="length" property="length"/>
        <result column="width" property="width"/>
        <result column="height" property="height"/>
        <result column="length_unit" property="lengthUnit"/>
        <result column="weight" property="weight"/>
        <result column="weight_unit" property="weightUnit"/>
        <result column="pack_length" property="packLength"/>
        <result column="pack_width" property="packWidth"/>
        <result column="pack_height" property="packHeight"/>
        <result column="pack_length_unit" property="packLengthUnit"/>
        <result column="pack_weight" property="packWeight"/>
        <result column="pack_weight_unit" property="packWeightUnit"/>
        <result column="sku_shelf_state" property="skuShelfState"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="warehouse_code" property="warehouseCode"/>
        <result column="warehouse_system_code" property="warehouseSystemCode"/>
        <result column="platform_unit_price" property="unitPrice"/>
        <result column="platform_operation_fee" property="operateFee"/>
        <result column="platform_final_delivery_fee" property="finalDeliveryFee"/>
        <association property="productCategory" column="belong_category_id"
                     select="com.zsmall.product.entity.mapper.ProductCategoryMapper.queryCategoryNameChainByIdOrderByLevelASC"/>
        <association property="productLabel" column="product_id"
                     select="com.zsmall.product.entity.mapper.ProductLabelRelationMapper.queryLabelNameByProductId"/>
        <association property="skuShowUrl" column="product_sku_code"
                     select="com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper.queryAllImageByProductSkuCode"/>
        <association property="productShowUrl" column="product_sku_code"
                     select="com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper.queryFirstImageUrlByProductSkuCode"/>
    </resultMap>

    <select id="queryCustomExportList" resultMap="ProductCustomExportVoMap">
        SELECT p.tenant_id,
               p.id                                  AS 'product_id',
               p.belong_category_id,
               su.email,
               su.phonenumber,
               p.product_type,
               ps.product_code,
               ps.name,
               ps.create_time,
               ps.sku,
               ps.erp_sku,
               ps.product_sku_code,
               ps.spec_val_name,
               psp.platform_pick_up_price,
               psp.platform_drop_shipping_price,
               psp.msrp,
               psp.platform_unit_price,
               psp.platform_operation_fee,
               psp.platform_final_delivery_fee,
               ps.stock_total,
               ps.stock_manager,
               p.verify_state,
               p.shelf_state,
               p.forbidden_channel,
               p.supported_logistics,
               psd.length,
               psd.width,
               psd.height,
               psd.length_unit,
               psd.weight,
               psd.weight_unit,
               psd.pack_length,
               psd.pack_width,
               psd.pack_height,
               psd.pack_length_unit,
               psd.pack_weight,
               psd.pack_weight_unit,
               ps.shelf_state                        AS 'sku_shelf_state',
               GROUP_CONCAT(w.warehouse_name)        AS 'warehouse_name',
               GROUP_CONCAT(w.warehouse_code)        AS 'warehouse_code',
               GROUP_CONCAT(w.warehouse_system_code) AS 'warehouse_system_code'
        FROM product_sku ps
                 JOIN product_sku_detail psd on ps.id = psd.product_sku_id
                 JOIN product_sku_price psp on ps.id = psp.product_sku_id
                 JOIN product p on ps.product_id = p.id
                 JOIN product_sku_stock pss ON pss.product_sku_code = ps.product_sku_code
                 JOIN warehouse w ON pss.warehouse_system_code = w.warehouse_system_code
                 JOIN sys_user su on p.tenant_id = su.tenant_id
        WHERE ps.del_flag = '0'
          AND p.del_flag = '0'
          AND pss.del_flag = '0'
          AND w.del_flag = '0'
        <if test="bo.labelId != null">
            AND EXISTS (SELECT 1
                        FROM product_label_relation plr
                        WHERE plr.product_id = p.id
                          AND plr.label_id = #{bo.labelId}
                          AND plr.del_flag = '0')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isAllNotBlank(bo.createDateStart, bo.createDateEnd)">
            AND ps.create_time BETWEEN #{bo.createDateStart} AND #{bo.createDateEnd}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.shelfState)">
            AND p.shelf_state = #{bo.shelfState}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.stockManager)">
            AND ps.stock_manager = #{bo.stockManager}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.verifyState)">
            AND p.verify_state = #{bo.verifyState}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.supportedLogistics)">
            AND p.supported_logistics = #{bo.supportedLogistics}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.skuShelfState)">
            AND ps.shelf_state = #{bo.skuShelfState}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.queryValue)">
            <if test="bo.queryType == 'Sku'">
                AND ps.sku LIKE CONCAT('%', #{bo.queryValue}, '%')
            </if>
            <if test="bo.queryType == 'ProductSkuCode'">
                AND ps.product_sku_code LIKE CONCAT('%', #{bo.queryValue}, '%')
            </if>
            <if test="bo.queryType == 'SupplierId'">
                AND p.tenant_id = #{bo.queryValue}
            </if>
        </if>
        GROUP BY ps.id, p.create_time
        ORDER BY p.create_time DESC
    </select>

    <select id="queryProductFavorites" resultType="com.zsmall.product.entity.domain.vo.product.ProductFavoritesVo">
        SELECT p.id                                  AS 'productId',
               p.product_code                        AS 'productCode',
               vps.stock_total                       AS 'stockTotal',
               MIN(psp.platform_pick_up_price)       AS 'platformPickUpPrice',
               MIN(psp.platform_drop_shipping_price) AS 'platformDropShippingPrice'
        FROM product p
                 LEFT JOIN view_product_stock vps ON p.id = vps.product_id
                 LEFT JOIN product_sku ps ON p.id = ps.product_id
                 LEFT JOIN product_sku_price psp ON ps.id = psp.product_sku_id
        WHERE p.del_flag = '0'
          AND p.shelf_state = 'OnShelf'
          AND ps.del_flag = '0'
          AND ps.shelf_state = 'OnShelf'
          AND p.product_code = #{productCode}
        GROUP BY ps.product_id
    </select>

    <select id="getWholesaleProductPage" resultType="com.zsmall.product.entity.domain.dto.wholesale.WholesaleProductPageDTO">
        SELECT p.name,
               p.id                     AS productId,
               p.product_code,
               p.belong_category_id,
               sku.product_sku_code,
               pswp.origin_unit_price,
               pswp.platform_unit_price AS mainPrice,
<!--               SUM(sku.stock_total)     AS stock,-->
               p.create_time            AS createTime
        FROM product_sku sku
                 JOIN product p ON p.id = sku.product_id AND p.product_type = 'WholesaleProduct'
                 JOIN (SELECT wp.*
                       FROM product_sku_wholesale_price wp
                                JOIN product_wholesale_tiered_price wtp ON wtp.id = wp.tiered_price_id
                       WHERE wp.del_flag = '0'
                         AND wtp.del_flag = '0'
                       GROUP BY wp.product_sku_id
                       having 1
                       ORDER BY wp.platform_unit_price ASC) pswp ON pswp.product_sku_id = sku.id
        WHERE sku.del_flag = '0'
          AND sku.verify_state = 'Accepted'
          AND p.del_flag = '0'
          AND p.shelf_state = 'Onshelf'
          AND p.product_type = 'WholesaleProduct'
        <if test="param.minPrice != null and param.maxPrice != null">
            AND pswp.platform_unit_price &gt;= #{param.minPrice}
            AND pswp.platform_unit_price &lt; #{param.maxPrice}
        </if>
        <if test="param.queryValue != null and param.queryValue != ''">
            AND (p.tenant_id like CONCAT('%', #{param.queryValue}, '%')
                OR p.name like CONCAT('%', #{param.queryValue}, '%')
                OR sku.sku like CONCAT('%', #{param.queryValue}, '%')
                OR sku.product_sku_code like CONCAT('%', #{param.queryValue}, '%'))
        </if>
        GROUP BY p.id
    </select>

    <resultMap id="ProductSkuSimpleVoMap" type="com.zsmall.product.entity.domain.vo.product.ProductSkuSimpleVo">
        <result column="tenantId"></result>
        <result column="product_code" property="productCode"/>
        <result column="product_sku_code" property="itemNo"/>
        <result column="name" property="name"/>
        <result column="sku" property="sku"/>
        <association property="favorites" select="com.zsmall.product.entity.mapper.TenantFavoritesMapper.favoriteOrNot"
                     column="{ productCode = product_code, tenantId = tenantId }"/>
        <association property="imageShowUrl"
                     select="com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper.queryFirstImageUrlByProductSkuCode"
                     column="product_sku_code"/>
    </resultMap>
    <select id="getPageForQA" resultMap="ProductSkuSimpleVoMap">
        SELECT sku.product_code,
               sku.product_sku_code,
               sku.name,
               sku.sku,
               #{tenantId} AS 'tenantId'
        FROM product_sku sku
                 JOIN product p ON sku.product_id = p.id
        WHERE sku.del_flag = '0'
          AND sku.shelf_state = 'OnShelf'
          AND p.del_flag = '0'
          AND p.shelf_state = 'OnShelf'
        <if test="queryValue != null">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductName')">
                AND sku.name LIKE CONCAT('%', #{queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'Sku')">
                AND sku.sku LIKE CONCAT('%', #{queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductSkuCode')">
                AND sku.product_sku_code LIKE CONCAT('%', #{queryValue}, '%')
            </if>
        </if>
        ORDER BY sku.create_time DESC, sku.id DESC
    </select>

    <select id="queryByFavoritesIdsForFavorites"
            resultType="com.zsmall.product.entity.domain.vo.product.ProductSkuFavoritesSimpleVo">
        SELECT sku.name                                                                        AS 'productName',
               sku.product_code                                                                AS 'productCode',
               sku.product_sku_code                                                            AS 'productSkuCode',
               IF(p.supported_logistics = 'DropShippingOnly', '-', psp.platform_pick_up_price) AS 'pickUpPrice',
               IF(p.supported_logistics = 'PickUpOnly', '-', psp.platform_drop_shipping_price) AS 'dropShippingPrice',
               sku.stock_total                                                                 AS 'stockTotal'
        FROM product_sku sku
                 JOIN product p on sku.product_id = p.id
                 JOIN product_sku_price psp on sku.id = psp.product_sku_id
        WHERE p.del_flag = '0'
          AND sku.del_flag = '0'
          AND p.shelf_state = 'OnShelf'
          AND sku.shelf_state = 'OnShelf'
          AND EXISTS(SELECT 1
                     FROM tenant_favorites tf WHERE tf.del_flag = '0'
                                                AND tf.product_id = p.id
                                                AND tf.id IN<foreach collection="favoritesIds" item="item" open="("
                                                                     separator="," close=")">
                                                                #{item}
                                                                </foreach>)
    ORDER BY sku.create_time DESC, sku.id DESC;
    </select>

    <resultMap id="ProductSkuForMarketplaceShowVoMap" type="com.zsmall.product.entity.domain.vo.product.ProductSkuForMarketplaceShowVo">
        <result column="product_id"/>
        <result column="product_type" property="productType"/>
        <result column="product_sku_code" property="itemNo"/>
        <result column="name" property="name"/>
        <result column="product_code" property="productCode"/>
        <result column="stock_total" property="stock"/>
        <result column="transport_method" property="transportMethod"/>
        <result column="supported_logistics" property="supportedLogistics"/>
<!--        <result column="price" property="price"/>-->
        <association property="labelName" column="product_id" select="com.zsmall.product.entity.mapper.ProductLabelRelationMapper.queryLabelNameByProductId"/>
        <association property="img" column="product_sku_code" select="com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper.queryFirstImageUrlByProductSkuCode"/>
    </resultMap>
    <select id="queryForMarketplaceShows" resultMap="ProductSkuForMarketplaceShowVoMap">
        select
        p.supported_logistics,
        p.product_type ,
        ps.product_sku_code ,
        ps.name as name,
<!--        psp.original_pick_up_price as price,-->
        p.product_code
        from product_sku ps
        inner join product p on ps.product_id = p.id
<!--        inner join product_sku_price psp on ps.id = psp.product_sku_id-->
        where
        ps.del_flag=0
        and ps.product_sku_code in
        <foreach collection="productSkuCode" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>

<!--        SELECT p.product_type,-->
<!--        sku.product_id,-->
<!--        sku.product_sku_code,-->
<!--        sku.name,-->
<!--        sku.product_code,-->
<!--        sku.stock_total,-->
<!--        psd.transport_method,-->
<!--        p.supported_logistics,-->
<!--        (CASE-->
<!--        WHEN p.product_type = 'WholesaleProduct' THEN pswp.platform_unit_price-->
<!--        ELSE-->
<!--        (CASE-->
<!--        WHEN p.supported_logistics = 'DropShippingOnly' THEN psp.platform_drop_shipping_price-->
<!--        ELSE psp.platform_pick_up_price END) END) AS 'price'-->
<!--        FROM product_sku sku-->
<!--        JOIN product_sku_detail psd ON sku.id = psd.product_sku_id-->
<!--        LEFT JOIN product_sku_price psp ON sku.id = psp.product_sku_id-->
<!--        LEFT JOIN (SELECT wp.*-->
<!--        FROM product_sku_wholesale_price wp-->
<!--        JOIN product_wholesale_tiered_price wtp ON wtp.id = wp.tiered_price_id-->
<!--        WHERE wp.del_flag = '0'-->
<!--        AND wtp.del_flag = '0'-->
<!--        GROUP BY wp.product_sku_id-->
<!--        having 1-->
<!--        ORDER BY wp.platform_unit_price ASC) pswp ON pswp.product_sku_id = sku.id-->
<!--        JOIN product p ON sku.product_id = p.id-->
<!--        WHERE sku.del_flag = '0'-->
<!--        AND sku.product_sku_code in-->
<!--        <foreach collection="productSkuCode" item="name" open="(" separator="," close=")">-->
<!--            #{name}-->
<!--        </foreach>-->
    </select>
    <select id="queryForMarketplaceShow" resultMap="ProductSkuForMarketplaceShowVoMap">
        SELECT p.product_type,
               sku.product_id,
               sku.product_sku_code,
               sku.name,
               sku.product_code,
               sku.stock_total,
               psd.transport_method,
               p.supported_logistics,
               (CASE
                    WHEN p.product_type = 'WholesaleProduct' THEN pswp.platform_unit_price
                    ELSE
                        (CASE
                             WHEN p.supported_logistics = 'DropShippingOnly' THEN psp.platform_drop_shipping_price
                             ELSE psp.platform_pick_up_price END) END) AS 'price'
        FROM product_sku sku
                 JOIN product_sku_detail psd ON sku.id = psd.product_sku_id
                 LEFT JOIN product_sku_price psp ON sku.id = psp.product_sku_id
                 LEFT JOIN (SELECT wp.*
                            FROM product_sku_wholesale_price wp
                                     JOIN product_wholesale_tiered_price wtp ON wtp.id = wp.tiered_price_id
                            WHERE wp.del_flag = '0'
                              AND wtp.del_flag = '0'
                            GROUP BY wp.product_sku_id
                            having 1
                            ORDER BY wp.platform_unit_price ASC) pswp ON pswp.product_sku_id = sku.id
                 JOIN product p ON sku.product_id = p.id
        WHERE sku.del_flag = '0'
          AND sku.product_sku_code = #{productSkuCode}
    </select>

    <select id="queryDealEffectiveness" resultType="java.lang.Long">
        SELECT CEIL(SUM(IF(oi.dispatched_time IS NULL, 0, ABS(TIMESTAMPDIFF(SECOND, o.pay_time, oi.dispatched_time)))) /
                    COUNT(oi.dispatched_time) / 3600)
        FROM order_item oi
                 INNER JOIN orders o
                            ON o.id = oi.order_id
        WHERE o.order_state IN ('Paid', 'Refunded', 'Verifying')
          and o.order_type = 'Normal'
          AND oi.product_sku_code = #{productSkuCode}
    </select>

    <select id="selectSkuListByPage" resultType="java.lang.String">
        select p.product_sku_code from product_sku p where p.tenant_id != 'S0BJHUA' and p.del_flag = 0
    </select>

    <select id="selectProductSkuStockBySkus" resultType="com.zsmall.product.entity.domain.vo.ProductSkuStockApiVo">
        SELECT w.warehouse_code,
               w.warehouse_system_code,
               ps.product_sku_code as sku,
               pss.stock_available as inventoryAvailable,
               CASE
                   WHEN drop_shipping_stock_available = 0 THEN 0
                   WHEN drop_shipping_stock_available = 1 THEN stock_available
                   ELSE 0
                   END
                                   AS dropShippingStockAvailable,
               pss.update_time
        FROM product_sku ps
                 INNER JOIN product_sku_stock pss
                            ON ps.product_sku_code = pss.product_sku_code
                 INNER JOIN warehouse w
                            ON pss.warehouse_system_code = w.warehouse_system_code
        WHERE pss.del_flag = 0
          AND w.del_flag = 0
          and w.warehouse_state = 1
          AND ps.product_sku_code in
        <foreach collection="skuSet" item="item" index="index" open="(" close=")" separator=",">
            #{item, jdbcType=VARCHAR}
        </foreach>
        <if test="supplierTenantId != null and supplierTenantId != ''">
            and w.tenant_id=#{supplierTenantId}
        </if>
    </select>


    <select id="getProductSkuStocksBySku" resultType="java.lang.String">
        select
        w.warehouse_code
        from  product_sku as ps
        inner join product_sku_stock as pss  on ps.product_sku_code=pss.product_sku_code
        inner join warehouse as w on pss.warehouse_system_code=w.warehouse_system_code
        where ps.product_sku_code=#{productSkuCode,jdbcType=VARCHAR}
        and ps.del_flag=0
        and pss.del_flag=0
        and w.del_flag=0
        and w.warehouse_state=1
        and ps.tenant_id=w.tenant_id
        <if test="type==1">
            and  pss.stock_available >0
        </if>

    </select>

    <select id="selectSkuInventoryByPage" resultType="com.zsmall.product.entity.domain.vo.ProductSkuStockApiVo">
        select w.warehouse_code,
               pss.warehouse_system_code as warehouseSystemCode,
               pss.product_sku_code              as sku,
               pss.stock_available               as inventoryAvailable,
               pss.drop_shipping_stock_available as dropShippingStockAvailable,
               pss.update_time
        from warehouse w
        inner join product_sku_stock pss
        on w.warehouse_system_code = pss.warehouse_system_code
        where pss.del_flag = 0
        and w.del_flag = 0
        <if test="tenantId != null and tenantId.size() > 0">
            and w.tenant_id in
            <foreach collection="tenantId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        and w.warehouse_state=1
        <if test="warehouseCodes != null and !warehouseCodes.isEmpty()">
            and w.warehouse_code in
            <foreach collection="warehouseCodes" item="item" open="(" close=")" separator=",">
               #{item}
            </foreach>
        </if>
        order by pss.id desc
    </select>

    <select id="getWarehouseCodes" resultType="java.lang.String">
        select  w.warehouse_code from warehouse w
        where w.del_flag=0
          and w.warehouse_state=1
        group by w.warehouse_code
    </select>

    <resultMap id="ProductSkuResultMap" type="com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo">
        <id property="productSkuId" column="productSkuId"/>

        <!-- 多对一关系映射 -->
        <collection property="sitePriceBos" ofType="com.zsmall.product.entity.domain.bo.product.ProductSitePriceBo">
            <id property="id" column="productSkuPriceId"/>
            <!-- 其他字段映射... -->
        </collection>
    </resultMap>

    <select id="queryProductSkuListVoByProductIdsAndShelfState"
            resultMap="ProductSkuResultMap">
        SELECT
        ps.id AS 'productSkuId',
        ps.product_id AS 'productId',
        ps.sku AS 'sku',
        ps.product_sku_code AS 'productSkuCode',
        ps.spec_val_name AS 'specValName',
        ps.spec_compose_name AS 'specComposeName',
        ps.stock_total AS 'stockTotal',
<!--        (vpss.sold_quantity - vpss.refund_quantity) AS 'stockSold',-->
        ps.shelf_state AS 'skuShelfState',
        psp.original_pick_up_price AS 'pickUpPrice',
        psp.original_drop_shipping_price AS 'dropShippingPrice',
        psp.original_unit_price AS 'unitPrice',
        psp.original_operation_fee AS 'operationFee',
        psp.original_final_delivery_fee AS 'finalDeliveryFee',
        psp.id AS 'productSkuPriceId',
        psp.site_id as siteId,
        psp.currency as currency,
        psp.currency_symbol as currencySymbol,
        psp.country_code as countryCode
        FROM product_sku ps
        JOIN product_sku_price psp on ps.id = psp.product_sku_id
<!--        JOIN view_product_sku_sales vpss on ps.id = vpss.sku_id-->
        WHERE ps.del_flag = '0' and psp.del_flag = '0'
        <if test="productIds != null and productIds.size() > 0">
            and ps.product_id in
            <foreach collection="productIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="null != skuShelfState and skuShelfState != ''">
            and ps.shelf_state = #{skuShelfState}
        </if>
    </select>

    <select id="getInStockWarehouseBySku" resultType="com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseVo">
        select
        w.*
        from  product_sku as ps
        inner join product_sku_stock as pss  on ps.product_sku_code=pss.product_sku_code
        inner join warehouse as w on pss.warehouse_system_code=w.warehouse_system_code
        where ps.product_sku_code=#{productSkuCode,jdbcType=VARCHAR}
        and ps.del_flag=0
        and pss.del_flag=0
        and w.del_flag=0
        and w.warehouse_state=1
        and ps.tenant_id=w.tenant_id
        <if test="type==1">
            and  pss.stock_available >0
        </if>
        <if test="warehouseSystemCode != null and warehouseSystemCode != ''">
            and w.warehouse_system_code=#{warehouseSystemCode}
        </if>
    </select>



    <select id="getGuessYouLikeCategoryId" resultType="java.lang.String">
        SELECT p.belong_category_id
        FROM order_item oi
                 inner join product_sku ps on oi.product_sku_code = ps.product_sku_code
                 inner join product p on ps.product_id = p.id
        WHERE fulfillment_progress = 'Fulfilled'
          AND oi.del_flag = 0
          and ps.del_flag = 0
          and ps.shelf_state='OnShelf'
          AND ps.sku NOT LIKE 'ZJHJ%'
            <if test="tenantId != null and tenantId != ''">
                and oi.tenant_id=#{tenantId}
            </if>
        GROUP BY p.belong_category_id
        ORDER BY COUNT(*) DESC
        limit 10;
    </select>

    <select id="getGuessYouLikeProduct" resultMap="BaseResultMap">
        select ps.*
        from product_sku ps
        inner join product p
        where p.belong_category_id = #{caId}
        and p.del_flag=0
        and ps.del_flag=0
        and ps.shelf_state='OnShelf'
        AND ps.sku NOT LIKE 'ZJHJ%'
        limit 10;
    </select>

    <select id="selectMarketplaceShowList" resultMap="ProductSkuForMarketplaceShowVoMap">
        select
            p.supported_logistics as supportedLogistics,
            p.product_type as productType,
            ps.id as productSkuId,
            ps.product_sku_code as itemNo,
            ps.name as name,
            ps.tenant_id as tenantId,
            psp.original_pick_up_price as price,
            p.product_code as productCode
        from product_sku ps
            inner join product p on ps.product_id = p.id
            inner join product_sku_price psp on ps.id = psp.product_sku_id
        where ps.product_sku_code in
        <foreach collection="productSkuCodes" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>

    <select id="getSalesRankingProduct" resultType="java.lang.String">
        SELECT final.product_sku_code, final.net_sales_quantity
        FROM (
                 SELECT s.product_sku_code, (s.sales_quantity - r.refund_quantity) AS net_sales_quantity
                 FROM (
                          SELECT p.product_sku_code, sum(oi.total_quantity) AS sales_quantity
                          FROM product_sku p
                                   LEFT JOIN order_item oi ON p.product_sku_code = oi.product_sku_code
                              AND oi.fulfillment_progress = 'Fulfilled'
                              AND oi.del_flag = 0
                              AND p.del_flag=0
                          WHERE p.shelf_state = 'OnShelf'
                          GROUP BY p.product_sku_code
                      ) s
                          LEFT JOIN (
                     SELECT ps.product_sku_code, SUM(ori.refund_quantity) AS refund_quantity
                     FROM order_refund orr
                              JOIN order_refund_item ori ON orr.id = ori.order_refund_id
                              JOIN product_sku ps ON ps.product_sku_code = ori.product_sku_code
                     WHERE ps.shelf_state = 'OnShelf'
                       AND ps.del_flag=0
                       AND orr.del_flag=0
                       AND orr.refund_amount_state = 'Refunded'
                       AND orr.refund_state='Refunded'
                     GROUP BY ps.product_sku_code
                 ) r ON s.product_sku_code = r.product_sku_code
             ) final
        ORDER BY final.net_sales_quantity DESC
        LIMIT 19;
    </select>
    <sql id="commonQuerySql">
        FROM product_sku_price psp
        LEFT JOIN product_sku ps ON psp.product_sku_id = ps.id
        LEFT JOIN product p ON ps.product_id = p.id
        where psp.del_flag = 0 and ps.del_flag = 0 and p.del_flag = 0
        <if test="queryValue != null">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductName')">
                AND p.shelf_state != 'ForcedOffShelf'
                AND p.name LIKE CONCAT('%', #{queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'Sku')">
                AND ps.sku LIKE CONCAT('%', #{queryValue}, '%')
            </if>

            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductSkuCode')">
                AND ps.product_sku_code LIKE CONCAT('%', #{queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'ProductSkuCode')">
                AND ps.product_sku_code LIKE CONCAT('%', #{queryValue}, '%')
            </if>
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(itemNos)">
            AND ps.product_sku_code IN
            <foreach collection="itemNos" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="skuAuditStatus != null and skuAuditStatus != ''">
            AND ps.verify_state = #{skuAuditStatus}
        </if>
        <if test="siteId != null and siteId != ''">
            AND psp.site_id = #{siteId}
        </if>
        ORDER BY psp.create_time DESC, psp.id DESC;
    </sql>

    <select id="getQueryCount" resultType="com.zsmall.product.entity.domain.ProductSku">
        SELECT count(psp.id)
        <include refid="commonQuerySql"/>
    </select>
    <select id="getProductPrice"
            resultType="com.zsmall.product.entity.domain.vo.product.ProductPriceExportDto">
        select
        psp.product_sku_code    as      itemNo,
        p.supported_logistics,
        ps.shelf_state           as      skuShelfState,
        psp.original_unit_price as      unitPrice,
        psp.original_final_delivery_fee finalDeliveryFee,
        psp.original_operation_fee      operationFee,
        ps.sku,
        psp.site_id

        <include refid="commonQuerySql"/>
    </select>

    <select id="getWarehouseAdminInfos" resultType="com.zsmall.warehouse.entity.domain.WarehouseAdminInfo">
        select *
        from warehouse_admin_info wai
        where del_flag=0
        and wai.warehouse_state=1
    </select>
</mapper>
