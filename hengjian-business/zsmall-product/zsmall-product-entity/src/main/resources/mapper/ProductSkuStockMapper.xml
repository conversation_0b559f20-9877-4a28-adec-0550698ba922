<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuStockMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuStock">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="stockCode" column="stock_code" jdbcType="VARCHAR"/>
        <result property="stockTotal" column="stock_total" jdbcType="INTEGER"/>
        <result property="stockReserved" column="stock_reserved" jdbcType="INTEGER"/>
        <result property="stockAvailable" column="stock_available" jdbcType="INTEGER"/>
        <result property="stockState" column="stock_state" jdbcType="INTEGER"/>
        <result property="erpSku" column="erp_sku" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="warehouseSystemCode" column="warehouse_system_code" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryPageList" resultType="com.zsmall.product.entity.domain.vo.productSkuStock.SkuStockInfoVo">
        SELECT
        p.id AS 'productId',
        p.product_code,
        p.product_type,
        p.supported_logistics,
        sku.sku,
        p.name AS 'productName',
        sku.id as productSkuId,
        sku.product_sku_code,
        sku.shelf_state as shelfState,
        pss.stock_total,
        sku.spec_val_name,
        w.warehouse_type,
        w.warehouse_name,
        w.warehouse_system_code,
        pss.stock_code,
        pss.stock_state,
        pss.drop_shipping_stock_available,
        pss.pickup_lock_used + pss.pickup_lock_reserved as pickupLockUsed,
        pss.drop_shipping_lock_used + pss.drop_shipping_lock_reserved as dropShippingLockUsed,
        pss.lock_exception_code,
        if(pss.drop_shipping_stock_available = 1, pss.stock_total + pss.drop_shipping_lock_used + pss.drop_shipping_lock_reserved, pss.drop_shipping_lock_used + pss.drop_shipping_lock_reserved) as totalDropShippingLockUsed,
        pss.stock_total + pss.pickup_lock_used + pss.pickup_lock_reserved as totalPickupLockUsed
        FROM product_sku_stock pss
        JOIN warehouse w ON w.warehouse_system_code = pss.warehouse_system_code
        JOIN product_sku sku ON sku.product_sku_code = pss.product_sku_code
        JOIN product p ON sku.product_id = p.id
        WHERE p.del_flag = '0'
        AND sku.del_flag = '0'
        AND w.del_flag = '0'
        and w.warehouse_state=1
        AND pss.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(param.queryType, 'ProductName')">
                AND p.name LIKE CONCAT('%', #{param.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(param.queryType, 'ProductSkuCode')">
                AND sku.product_sku_code LIKE CONCAT('%', #{param.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(param.queryType, 'Sku')">
                AND sku.sku LIKE CONCAT('%', #{param.queryValue}, '%')
            </if>
        </if>
        <if test="param.warehouseSystemCode != null and param.warehouseSystemCode != ''">
            AND w.warehouse_system_code =#{param.warehouseSystemCode}
        </if>
        <if test="param.shelfState != null and param.shelfState != ''">
            AND sku.shelf_state =#{param.shelfState}
        </if>
        <if test="param.tenantId != null">
            and w.tenant_id=#{param.tenantId}
        </if>
        <if test="null != param.lockExceptionCode">
            and pss.lock_exception_code = #{param.lockExceptionCode}
        </if>
    </select>

    <select id="existStockCode" resultType="java.lang.Boolean">
        SELECT COUNT(pss.id)
        FROM product_sku_stock pss
        WHERE pss.stock_code = #{stockCode}
    </select>

    <select id="sumStockTotal" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(pss.stock_available), 0)
        FROM product_sku_stock pss
        WHERE pss.product_sku_code = #{productSkuCode}
          AND pss.stock_state = 1
          AND pss.del_flag = '0'
          AND EXISTS(SELECT 1
                     FROM product_sku ps
                     WHERE ps.product_sku_code = pss.product_sku_code
                       AND ps.del_flag = '0')
    </select>

    <resultMap id="ProductSkuStockSimpleMap" type="com.zsmall.product.entity.domain.vo.productSkuStock.ProductSkuStockSimpleVo">
        <result column="warehouse_name" property="warehouseName"/>
        <result column="warehouse_system_code" property="warehouseSystemCode"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="logistics_template_no" property="logisticsTemplateNo"/>
        <result column="quantity" property="quantity"/>
        <result column="drop_shipping_stock_available" property="dropShippingStockAvailable"/>
    </resultMap>
    <select id="querySimpleVoByProductSkuCode"
            resultMap="ProductSkuStockSimpleMap">
        SELECT w.warehouse_name,
               pss.warehouse_system_code,
               pss.product_sku_code,
               pss.logistics_template_no,
               pss.stock_available AS 'quantity',
               pss.drop_shipping_stock_available
        FROM product_sku_stock pss
                 JOIN warehouse w on pss.warehouse_system_code = w.warehouse_system_code
        WHERE pss.product_sku_code = #{productSkuCode}
          AND pss.del_flag = '0'
    </select>

    <select id="querySimpleVoByProductSkuCodes"
            resultMap="ProductSkuStockSimpleMap">
        SELECT w.warehouse_name,
               pss.warehouse_system_code,
               pss.product_sku_code,
               pss.logistics_template_no,
               pss.stock_available AS 'quantity',
               pss.drop_shipping_stock_available
        FROM product_sku_stock pss
                 JOIN warehouse w on pss.warehouse_system_code = w.warehouse_system_code
        WHERE pss.del_flag = '0'
        <if test="productSkuCodes != null and productSkuCodes.size() != 0">
            AND pss.product_sku_code IN
            <foreach collection="productSkuCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryIdsByProductSkuIds" resultType="java.lang.Long">
        SELECT pss.id
        FROM product_sku_stock pss
        WHERE pss.product_sku_code IN (SELECT ps.product_sku_code FROM product_sku ps WHERE ps.id IN
        <foreach collection="productSkuIds" item="item" index="index" open="(" separator="," close=")">#{item}</foreach>
        )
        AND pss.del_flag = '0'
    </select>

    <select id="queryIdsByProductSkuCode" resultType="java.lang.Long">
        SELECT pss.id
        FROM product_sku_stock pss
        WHERE pss.product_sku_code = #{productSkuCode}
          AND pss.del_flag = '0'
    </select>

    <select id="queryAdequateStockByParams" resultType="com.zsmall.product.entity.domain.ProductSkuStock">
        SELECT pss.*, w.country
        FROM product_sku_stock pss JOIN warehouse w on pss.warehouse_system_code = w.warehouse_system_code
        WHERE pss.product_sku_code = #{productSkuCode}
        AND pss.stock_available >= ABS(#{adjustQuantity})

        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(logisticsType, 'DropShipping')">
            # 仅代发需要判断国家、物流模板，自提订单全世界范围的仓库都可以发货
            <if test="destCountry != 'US'">
                AND NOT EXISTS(SELECT 1 FROM warehouse_address wa WHERE wa.warehouse_id = w.id AND wa.country = 'US')
            </if>
            <if test="destCountry == 'US'">
                AND EXISTS(SELECT 1 FROM warehouse_address wa WHERE wa.warehouse_id = w.id AND wa.country = 'US')
            </if>
            <if test="logisticsTemplateNo != null">
                AND pss.logistics_template_no = #{logisticsTemplateNo}
            </if>
            <if test="logisticsTemplateNo == null">
                AND pss.logistics_template_no IS NULL
            </if>
        </if>

        <if test="logisticsAccount == true">
            AND w.support_logistics_account = #{logisticsAccount}
        </if>
        AND w.warehouse_state = '1'
        AND w.del_flag = '0'
        AND pss.stock_state = '1'
        AND pss.del_flag = '0'
        <if test="@cn.hutool.core.text.CharSequenceUtil@equals(logisticsType, 'PickUp') and @cn.hutool.core.text.CharSequenceUtil@isNotBlank(destCountry)">
            ORDER BY CASE WHEN w.country = #{destCountry} THEN 0 ELSE 1 END
        </if>
    </select>

    <update id="updateByProductSkuCodeAndWarehouse">
        UPDATE product_sku_stock pss
        SET pss.stock_total     = #{stockQuantity},
            pss.stock_available = #{stockQuantity}
        WHERE pss.del_flag = '0'
          AND pss.product_sku_code = #{productSkuCode}
          AND EXISTS(SELECT 1
                     FROM warehouse w
                     WHERE w.warehouse_system_code = pss.warehouse_system_code
                       AND w.del_flag = '0'
                       AND w.warehouse_code = #{warehouseCode})
    </update>

    <select id="countValidByWarehouseSystemCode" resultType="java.lang.Long">
        SELECT COUNT(pss.id)
        FROM product_sku_stock pss
        WHERE pss.del_flag = '0'
          AND pss.warehouse_system_code = #{warehouseSystemCode}
          AND EXISTS(SELECT 1
                     FROM product_sku ps
                              JOIN product p ON ps.product_id = p.id
                     WHERE ps.del_flag = '0'
                       AND p.del_flag = '0')
    </select>
    <select id="queryAdequateInStock" resultType="com.zsmall.product.entity.domain.ProductSkuStock">
        SELECT pss.*, w.country
        FROM product_sku_stock pss JOIN warehouse w on pss.warehouse_system_code = w.warehouse_system_code
        WHERE pss.product_sku_code = #{productSkuCode}
          AND pss.stock_available >= ABS(#{adjustQuantity})
          AND w.warehouse_state = '1'
          AND w.del_flag = '0'
          AND pss.stock_state = '1'
          AND pss.del_flag = '0'
    </select>

    <select id="sumInventoryByWarehouseSystemCode" resultType="java.lang.Long">
        select IFNULL(SUM(stock_available),0)
        from product_sku_stock
        where warehouse_system_code = #{warehouseSystemCode}
          and tenant_id = #{tenantId}
          and del_flag = 0
          and stock_state = 1
    </select>
    <select id="getWarehouseCode" resultType="java.lang.String">
        select warehouse_code from warehouse
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(warehouseSystemCodes)">
            where warehouse_system_code in
            <foreach collection="warehouseSystemCodes" item="warehouseSystemCode" open="(" separator="," close=")">
                #{warehouseSystemCode}
            </foreach>
        </if>
    </select>
    <select id="getChannelFlag" resultType="java.lang.String">
        select third_channel_flag from sys_tenant where tenant_id = #{tenantId}
    </select>

    <select id="getChannelFlags" resultType="com.hengjian.system.domain.SysTenant">
        SELECT *
        FROM sys_tenant
        WHERE tenant_id IN
        <foreach collection="tenantIds" item="tenantId" open="(" separator="," close=")">
            #{tenantId}
        </foreach>
    </select>
    <select id="selectConfigByKey" resultType="java.lang.String">
        SELECT config_value
        FROM sys_config
        WHERE config_key = #{configKey}
    </select>

    <select id="queryAdequateInStockAndDropShippingStockAvailable"
            resultType="com.zsmall.product.entity.domain.ProductSkuStock">
        SELECT pss.*, w.country
        FROM product_sku_stock pss JOIN warehouse w on pss.warehouse_system_code = w.warehouse_system_code
        WHERE pss.product_sku_code = #{productSkuCode}
          AND pss.stock_available >= ABS(#{adjustQuantity})
          and pss.drop_shipping_stock_available = #{dropShippingStockAvailable}
          <if test="null != warehouseSystemCode and warehouseSystemCode != ''">
              AND pss.warehouse_system_code = #{warehouseSystemCode}
          </if>
          AND w.warehouse_state = '1'
          AND w.del_flag = '0'
          AND pss.stock_state = '1'
          AND pss.del_flag = '0'
    </select>

    <update id="updateByProductSkuCodeAndWarehouseV2">
        UPDATE product_sku_stock pss
        SET pss.stock_total     = #{stockQuantity},
            pss.stock_available = #{stockQuantity}
            <if test="dropShippingStockAvailable != null">
            ,pss.drop_shipping_stock_available = #{dropShippingStockAvailable}
            </if>
        WHERE pss.del_flag = '0'
          AND pss.product_sku_code = #{productSkuCode}
          and pss.warehouse_system_code = #{warehouseSysCode}
    </update>

    <select id="getSkuStock" resultType="com.zsmall.product.entity.domain.dto.stock.SkuStock">
        SELECT product_sku_code,
               SUM(
                   stock_available
               ) AS pickUpStockTotal,
               SUM(
                   CASE
                       WHEN drop_shipping_stock_available = 0 THEN 0
                       WHEN drop_shipping_stock_available = 1 THEN stock_available
                       ELSE 0
                       END
               ) AS dropShippingStockTotal

        FROM product_sku_stock
        WHERE
        del_flag=0
        and product_sku_code = #{productSkuCode};
    </select>

    <select id="getSpuStock" resultType="com.zsmall.product.entity.domain.dto.stock.SpuStock">
        select
            p.product_code,
            sum(pss.stock_available) as pickUpStockTotal,
            SUM(
                CASE
                    WHEN drop_shipping_stock_available = 0 THEN 0
                    WHEN drop_shipping_stock_available = 1 THEN stock_available
                    ELSE 0
                    END
            ) AS dropShippingStockTotal
        from product p
                 inner  join product_sku ps on p.id = ps.product_id
                 inner  join product_sku_stock pss on pss.product_sku_code=ps.product_sku_code
        where
        p.del_flag=0
        and pss.del_flag=0
        and  p.product_code=#{productCode};
    </select>

    <select id="getSkuStockBySite" resultType="com.zsmall.product.entity.domain.dto.stock.SkuStock">
        SELECT product_sku_code,
               SUM(stock_available) AS pickUpStockTotal,
               SUM(CASE
                       WHEN drop_shipping_stock_available = 0 THEN 0
                       WHEN drop_shipping_stock_available = 1 THEN stock_available
                       ELSE 0 END)  AS dropShippingStockTotal
        FROM product_sku_stock pss
                 inner join warehouse w on w.warehouse_system_code = pss.warehouse_system_code
                 inner join warehouse_delivery_country wdc on w.id = wdc.warehouse_id
                 inner join site_country_currency scc on scc.country_code = wdc.country_code
        WHERE pss.del_flag=0
        and w.del_flag=0
        and w.warehouse_state=1
        and wdc.del_flag=0
        and scc.del_flag=0
        and product_sku_code = #{productSkuCode}
        and scc.country_code = #{site}
    </select>

    <select id="queryDropShippingStockAvailable" resultType="com.zsmall.product.entity.domain.ProductSkuStock">
        SELECT pss.*, w.warehouse_code as warehouseSystemCode
        FROM product_sku_stock pss
        inner  join warehouse w on pss.warehouse_system_code = w.warehouse_system_code
        inner join warehouse_delivery_country wdc on w.id = wdc.warehouse_id
        WHERE pss.product_sku_code = #{productSkuCode}
        and pss.tenant_id=#{tenantId}
        AND pss.stock_available >= ABS(#{quantity})
        and pss.drop_shipping_stock_available =1
        AND pss.stock_state = '1'
        AND pss.del_flag = '0'
        AND w.warehouse_state = '1'
        AND w.del_flag = '0'
        AND wdc.country_code=#{countryCode}
    </select>

    <select id="getSkuStockBySiteAndWarehouse"
            resultType="com.zsmall.product.entity.domain.dto.stock.SkuStock">
        SELECT pss.id,
                pss.product_sku_code,
               SUM(stock_available) AS pickUpStockTotal,
               SUM(CASE
                       WHEN drop_shipping_stock_available = 0 THEN 0
                       WHEN drop_shipping_stock_available = 1 THEN stock_available
                       ELSE 0 END)  AS dropShippingStockTotal,
               w.warehouse_code as warehouseCode,
               w.warehouse_system_code as warehouseSystemCode
        FROM product_sku_stock pss
                 inner join warehouse w on w.warehouse_system_code = pss.warehouse_system_code
                 inner join warehouse_delivery_country wdc on w.id = wdc.warehouse_id
                 inner join site_country_currency scc on scc.country_code = wdc.country_code
        WHERE pss.del_flag = 0
          and w.del_flag = 0
          and w.warehouse_state = 1
          and wdc.del_flag = 0
          and scc.del_flag = 0
          and product_sku_code = #{productSkuCode}
          and scc.country_code = #{site}
        <if test="warehouseSystemCode != null and warehouseSystemCode != ''">
            and w.warehouse_system_code = #{warehouseSystemCode}
        </if>
    </select>

    <update id="updateProductLockNum">
        update product_sku_stock pss
        <set>
            <if test="supportedLogistics != null and supportedLogistics.equals('DropShippingOnly')">
                pss.drop_shipping_lock_used = pss.drop_shipping_lock_used
                <if test="isAdd != null and isAdd == true">+ #{quantityTotal}</if>
                <if test="isAdd != null and isAdd == false">- #{quantityTotal}</if>
            </if>
            <if test="supportedLogistics != null and supportedLogistics.equals('PickUpOnly')">
                pss.pickup_lock_used = pss.pickup_lock_used
                <if test="isAdd != null and isAdd == true">+ #{quantityTotal}</if>
                <if test="isAdd != null and isAdd == false">- #{quantityTotal}</if>
            </if>
        </set>
        where pss.product_sku_code = #{productSkuCode}
        and pss.warehouse_system_code = #{warehouseSystemCode}
    </update>

    <update id="updateProductLockReserved">
        update product_sku_stock pss
        <set>
            <if test="supportedLogistics != null and supportedLogistics.equals('DropShippingOnly')">
                pss.drop_shipping_lock_reserved = pss.drop_shipping_lock_reserved
                <if test="isAdd != null and isAdd == true">+ #{quantityTotal}</if>
                <if test="isAdd != null and isAdd == false">- #{quantityTotal}</if>
            </if>
            <if test="supportedLogistics != null and supportedLogistics.equals('PickUpOnly')">
                pss.pickup_lock_reserved = pss.pickup_lock_reserved
                <if test="isAdd != null and isAdd == true">+ #{quantityTotal}</if>
                <if test="isAdd != null and isAdd == false">- #{quantityTotal}</if>
            </if>
        </set>
        where pss.product_sku_code = #{productSkuCode}
        and pss.warehouse_system_code = #{warehouseSystemCode}
    </update>

    <select id="getSupplierSkuStockByProductSkuCode"
            resultType="com.zsmall.product.entity.domain.dto.stock.SkuStock">
        SELECT pss.id,
               pss.product_sku_code,
               stock_available  AS pickUpStockTotal,
               (CASE
                       WHEN drop_shipping_stock_available = 0 THEN 0
                       WHEN drop_shipping_stock_available = 1 THEN stock_available
                       ELSE 0 END)     AS dropShippingStockTotal,
               w.warehouse_code        as warehouseCode,
               w.warehouse_system_code as warehouseSystemCode
        FROM product_sku_stock pss
                 inner join warehouse w on w.warehouse_system_code = pss.warehouse_system_code
                 inner join warehouse_delivery_country wdc on w.id = wdc.warehouse_id
                 inner join site_country_currency scc on scc.country_code = wdc.country_code
        WHERE pss.del_flag = 0
          and w.del_flag = 0
          and w.warehouse_state = 1
          and wdc.del_flag = 0
          and scc.del_flag = 0
          and product_sku_code = #{productSkuCode}
          and scc.country_code = #{site}
          and pss.stock_available > 0
    </select>

    <select id="getSkuStocks" resultType="com.zsmall.product.entity.domain.dto.stock.SkuStock">
        SELECT product_sku_code,
               SUM(stock_available) AS pickUpStockTotal,
               SUM(CASE
                       WHEN drop_shipping_stock_available = 0 THEN 0
                       WHEN drop_shipping_stock_available = 1 THEN stock_available
                       ELSE 0 END)  AS dropShippingStockTotal
        FROM product_sku_stock
        WHERE
        del_flag=0
        and product_sku_code in
        <foreach collection="productSkuCode" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY product_sku_code
    </select>

    <select id="getDropShippingAllStockBySku" resultType="com.zsmall.product.entity.domain.dto.stock.SkuStock">
        SELECT
            product_sku_code,
            pss.product_code,
            SUM(stock_available) as pickUpStockTotal,
            SUM(CASE WHEN drop_shipping_stock_available = 1 THEN stock_available ELSE 0 END) AS dropShippingStockTotal
        FROM product_sku_stock pss
        WHERE pss.product_sku_code in
        <foreach collection="productSkuCode" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
          AND pss.del_flag = 0
          AND pss.stock_state = 1
          AND EXISTS (
            SELECT 1 FROM warehouse w
            WHERE w.warehouse_system_code = pss.warehouse_system_code
              AND w.warehouse_state = 1 AND w.del_flag = 0
        )
          AND EXISTS (
            SELECT 1 FROM product p
            WHERE p.product_code = pss.product_code
              AND p.supported_logistics IN
              <foreach collection="supplierLogs" item="item" index="index" open="(" close=")" separator="," >
                  #{item}
              </foreach>
        )
        GROUP BY product_sku_code;
    </select>

    <select id="getDropShippingAllStock" resultType="com.zsmall.product.entity.domain.dto.stock.SkuStock">
        SELECT
            product_sku_code,
            pss.product_code,
            SUM(stock_available) as pickUpStockTotal,
            SUM(CASE WHEN drop_shipping_stock_available = 1 THEN stock_available ELSE 0 END) AS dropShippingStockTotal
        FROM product_sku_stock pss
        WHERE pss.del_flag = 0
          AND pss.stock_state = 1
          AND EXISTS (
            SELECT 1 FROM warehouse w
            WHERE w.warehouse_system_code = pss.warehouse_system_code
              AND w.warehouse_state = 1 AND w.del_flag = 0
        )
          AND EXISTS (
            SELECT 1 FROM product p
            WHERE p.product_code = pss.product_code
              AND p.supported_logistics IN
            <foreach collection="supplierLogs" item="item" index="index" open="(" close=")" separator="," >
                #{item}
            </foreach>

        )
        GROUP BY product_sku_code;
    </select>

    <select id="getDropShippingAllStockBySpu" resultType="com.zsmall.product.entity.domain.dto.stock.SkuStock">
        SELECT
            product_sku_code,
            pss.product_code,
            SUM(stock_available) as pickUpStockTotal,
            SUM(CASE WHEN drop_shipping_stock_available = 1 THEN stock_available ELSE 0 END) AS dropShippingStockTotal
        FROM product_sku_stock pss
        WHERE
           pss.del_flag = 0
          AND pss.stock_state = 1
          AND EXISTS (
            SELECT 1 FROM warehouse w
            WHERE w.warehouse_system_code = pss.warehouse_system_code
              AND w.warehouse_state = 1 AND w.del_flag = 0
        )
          AND EXISTS (
            SELECT 1 FROM product p
            WHERE p.product_code = pss.product_code
              AND p.product_code in
                <foreach collection="productCode" item="item" index="index" open="(" close=")" separator="," >
                    #{item}
                </foreach>
              AND p.supported_logistics IN
                <foreach collection="supplierLogs" item="item" index="index" open="(" close=")" separator="," >
                    #{item}
                </foreach>
        )
        GROUP BY product_sku_code;
    </select>


</mapper>
