package com.zsmall.product.entity.domain.vo.productChannelControl;

import cn.hutool.json.JSONArray;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商品渠道管控-通用信息
 *
 * <AUTHOR>
 * @create 2022/5/3 22:47
 */
@Data
@NoArgsConstructor
public class ProductChannelControlBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 管控编号
     */
    private Long controlId;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 商品ItemNo
     */
    private String productSkuCode;

    /**
     * 受允许的用户编号
     */
    private JSONArray allowCodes;

}
