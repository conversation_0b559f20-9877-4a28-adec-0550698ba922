package com.zsmall.product.entity.domain.dto.productMapping;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 商品映射导入DTO
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@Data
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class ProductMappingImportDTO  {
    //商品图片
    @ExcelProperty(index = 0)
    private  String imageShowUrl;
    //商品名称
    @ExcelProperty(index = 1)
    private  String productName;
    //渠道SKU
    @ExcelProperty(index = 2)
    private  String channelSku;
    //渠道sku货号
    @ExcelProperty(index = 3)
    private  String channelSkuItemNumber;
    //渠道销售价
//    @ExcelProperty(index = 4)
//    private  String finalPrice;
    //渠道类型
    @ExcelProperty(index = 4)
    private  String channelType;
    @ExcelProperty(index = 5)
    private  String productSkuCode;
    @ExcelProperty(index = 6)
    private  String channelName;
}
