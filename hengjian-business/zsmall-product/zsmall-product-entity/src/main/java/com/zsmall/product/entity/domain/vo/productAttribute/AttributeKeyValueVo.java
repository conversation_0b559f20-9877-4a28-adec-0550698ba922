package com.zsmall.product.entity.domain.vo.productAttribute;

import lombok.Data;

import java.io.Serializable;

/**
 * 响应体-商品属性KV
 *
 * <AUTHOR>
 * @date 2023/8/10
 */
@Data
public class AttributeKeyValueVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * key-对应属性名attributeName
     */
    private String key;

    /**
     * key-对应属性值attributeValue
     */
    private String value;

}
