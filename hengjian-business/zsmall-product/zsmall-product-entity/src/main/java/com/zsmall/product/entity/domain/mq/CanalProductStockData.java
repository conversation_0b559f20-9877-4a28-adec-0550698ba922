package com.zsmall.product.entity.domain.mq;

import lombok.Data;

import java.io.Serializable;

/**
 * 库存数据对象
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
public class CanalProductStockData implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 商品SKU编码
     */
    private String productSkuCode;
    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 库存总数
     */
    private Integer stockTotal;
    /**
     * 库存可用数量
     */
    private Integer stockAvailable;
    /**
     * 库存状态
     */
    private Integer stockState;
    /**
     *   一件代发库存可用数量
     */
    private Integer dropShippingStockAvailable;
    /**
     * 删除标志（0代表存在，1代表删除）
     */
    private Integer delFlag;
    /**
     * 支持的物流
     */
    private String  supportedLogistics;
}
