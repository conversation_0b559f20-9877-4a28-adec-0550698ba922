package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.product.ProductTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 商品SPU对象 product
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product", autoResultMap = true)
public class Product extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 产品描述
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String description;

    /**
     * SPU唯一编号
     */
    private String productCode;

    /**
     * 商品类型
     */
    private ProductTypeEnum productType;

    /**
     * 货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
    private ShelfStateEnum shelfState;

    /**
     * 初次上架时间
     */
    private Date firstOnShelfTime;

    /**
     * 最新上架时间
     */
    private Date lastOnShelfTime;

    /**
     * 审核状态
     */
    private ProductVerifyStateEnum verifyState;

    /**
     * 审核时间（商家商品字段。商家修改商品后员工审核操作的时间）
     */
    private Date verifyTime;

    /**
     * 支持的物流：All-都支持，PickUpOnly-仅支持自提，DropShippingOnly-仅支持代发
     */
    private SupportedLogisticsEnum supportedLogistics;

    /**
     * 禁售渠道（多选，&号分隔）
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONArray forbiddenChannel;

    /**
     * 归属分类id（一般情况下为商品所属分类树的最底层）
     */
    private Long belongCategoryId;

    /**
     * 发货时间
     */
    private String deliverGoodsTime;


    /**
     * 送达时间
     */
    private String deliveryTime;

    /**
     * 产品被下载次数
     */
    private Integer downloadCount;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 库存推送时间
     */
    private Date inventoryPushTime;
    @TableField(exist = false)
    private boolean isHasPriceChange = false;

    @TableField(exist = false)
    private Integer soldQuantity = 0;

    @TableField(exist = false)
    private Integer stockTotal = 0;

    @TableField(exist = false)
    private ProductAttachment productAttachmentFirst;

}
