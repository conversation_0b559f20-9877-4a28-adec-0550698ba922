package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 商品导入记录对象 product_import_record
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_import_record", autoResultMap = true)
public class ProductImportRecord extends NoDeptTenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 存储对象主键
     */
    private Long ossId;

    /**
     * 导入记录编号
     */
    private String importRecordNo;

    /**
     * 导入文件名
     */
    private String importFileName;

    /**
     * 导入的商品数量
     */
    private Integer importProducts;

    /**
     * 导入信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject importMessage;

    /**
     * 导入状态：Failed，Cancel，Pending，Importing，Success
     */
    private ImportStateEnum importState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
