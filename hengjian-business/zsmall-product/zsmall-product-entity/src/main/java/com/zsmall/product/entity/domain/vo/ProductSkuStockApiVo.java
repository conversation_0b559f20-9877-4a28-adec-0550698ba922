package com.zsmall.product.entity.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ProductSkuStockApiVo {
    /**
     * 仓库编码
     */
    private String warehouseCode;
    /**
     * 仓库系统编码
     */
    private String warehouseSystemCode;
    /**
     * 商品sku
     */
    private String sku;
    /**
     * 自提库存数量
     */
    private Integer inventoryAvailable;
    /**
     * 代发库存数量
     */
    private Integer dropShippingStockAvailable;
    /**
     * 库存更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
