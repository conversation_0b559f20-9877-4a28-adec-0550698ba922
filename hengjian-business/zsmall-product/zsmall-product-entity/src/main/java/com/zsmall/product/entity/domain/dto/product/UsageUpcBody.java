package com.zsmall.product.entity.domain.dto.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-upc信息")
public class UsageUpcBody {

    @Schema(title = "UPC码")
    private String code;

    @Schema(title = "使用状态：已使用、未使用")
    private String status;

    @Schema(title = "品牌代码")
    private String brand;

    @Schema(title = "渠道类型")
    private String channel;

    @Schema(title = "最近一次使用UPC编码的时间")
    private String date;

}
