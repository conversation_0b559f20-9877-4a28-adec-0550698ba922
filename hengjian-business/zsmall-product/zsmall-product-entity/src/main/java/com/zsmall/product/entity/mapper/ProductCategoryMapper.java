package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductCategory;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品分类Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-05-18
 */
public interface ProductCategoryMapper extends BaseMapperPlus<ProductCategory, ProductCategoryVo> {

    /**
     * 根据给定的分类id，查询分类链文本，示例：分类A/分类B/分类C（左至右为从父级到子级）
     */
    @InterceptorIgnore(tenantLine = "true")
    String queryCategoryNameChainByIdOrderByLevelASC(@Param("belongCategoryId") Long belongCategoryId);

    /**
     * 根据给定的分类id，查询分类文本，示例：分类A/分类B/分类C（左至右为从父级到子级）
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ProductCategory> queryCategoryChainByIdOrderByLevelASC(@Param("belongCategoryId") Long belongCategoryId);

    /**
     * 根据给定的分类id，倒查（由子级查父级）整个分类关系链
     * @param belongCategoryId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ProductCategory> queryCategoryChainById(@Param("belongCategoryId") Long belongCategoryId);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductCategory> queryAllCategoryChainById(@Param("belongCategoryId") Long belongCategoryId);

    /**
     * 根据给定的分类id，倒查（由父级查子级）整个分类关系链
     * @param belongCategoryId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ProductCategory> queryCategoryChainByIdDesc(@Param("belongCategoryId") Long belongCategoryId);

}
