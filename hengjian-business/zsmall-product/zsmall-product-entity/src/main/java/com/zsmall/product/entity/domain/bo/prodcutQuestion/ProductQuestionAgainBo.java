package com.zsmall.product.entity.domain.bo.prodcutQuestion;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 商品问答业务对象 product_question
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@Data
@EqualsAndHashCode
public class ProductQuestionAgainBo {

    /**
     * 提问编码
     */
    @NotBlank(message = "{zsmall.productQA.questionCodeIsBlank}", groups = { AddGroup.class, EditGroup.class })
    private String questionCode;

    /**
     * 问题
     */
    @NotBlank(message = "{zsmall.productQA.textQuestionsError}", groups = { AddGroup.class, EditGroup.class })
    private String question;
}
