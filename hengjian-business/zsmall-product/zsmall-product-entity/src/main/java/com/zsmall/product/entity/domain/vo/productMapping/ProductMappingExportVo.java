package com.zsmall.product.entity.domain.vo.productMapping;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024年8月23日  14:51
 * @description: 产品映射excel导出Vo
 */
@Data
@EqualsAndHashCode
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
@HeadFontStyle(fontHeightInPoints = 12)
public class ProductMappingExportVo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 商品名
     */
    @ExcelProperty("商品名称")
    private String productName;

    /**
     * 渠道sku
     */
    @ExcelProperty("渠道sku")
    private String channelSku;

    /**
     * 货号
     */
    @ExcelProperty("渠道sku货号")
    private String channelSkuItemNumber;

    /**
     * 商品SKU唯一编号
     */
    @ExcelProperty("Sku ID")
    private String productSkuCode;

    /**
     * 当前库存
     */
//    @ExcelProperty("库存")
//    private Integer stockTotal;

    /**
     * 最终单价
     */
//    @ExcelProperty("渠道销售价")
//    private BigDecimal finalPrice;
    /**
     * 站点
     */
    @ExcelProperty(value = "站点/币种")
    private String siteCurrency;
    /**
     * 目前成本价
     */
    @ExcelProperty("成本价")
    private BigDecimal costPrice;

    /**
     * 利润
     */
//    @ExcelProperty("收益")
//    private BigDecimal margin;


    /**
     * 铺货渠道别名
     */
    @ExcelProperty("商店名称")
    private String channelAlias;

    /**
     * 渠道类型
     */
    @ExcelProperty("渠道类型")
    private String channelType;


    /**
     * 渠道同步状态
     */
    @ExcelProperty("状态")
    private String syncState;


//    private Long productSkuId;
//
//
//    /**
//     * 渠道id
//     */
//    private Long channelId;
//
//
//
//
//
//    /**
//     * 活动编号
//     */
//    private String activityCode;
//
//    /**
//     * 参与的活动状态
//     */
//    private String activityState;
//
//    /**
//     * 规格组成名称，示例：尺寸-大;颜色-白色
//     */
//    private String specComposeName;
//
//    /**
//     * 规格值名称，示例：大/白色
//     */
//    private String specValName;
//
//    /**
//     * 映射sku
//     */
//    private String mappingSku;
//
//
//
//    /**
//     * 提价值
//     */
//    private BigDecimal markUpValue;
//
//
//
//
//
//
//
//
//    /**
//     * 渠道同步信息
//     */
//    private JSONObject syncMessage;
//
//    /**
//     * 主图存放路径
//     */
//    private String imageSavePath;
//
//
//
//    /**
//     * 渠道skuID
//     */
//    private String channelSkuId;
//    /**
//     * 主图展示地址
//     */
//    private String imageShowUrl;
//
//    /**
//     * 可能的价格变化
//     */
//    private Boolean priceChanges;
//
//    /**
//     * 供应商租户id
//     */
//    private String supplierTenantId;


}
