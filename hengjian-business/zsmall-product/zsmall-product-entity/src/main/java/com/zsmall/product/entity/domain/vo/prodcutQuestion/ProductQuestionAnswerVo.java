package com.zsmall.product.entity.domain.vo.prodcutQuestion;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import com.hengjian.common.excel.annotation.ExcelTranslationFormat;
import com.hengjian.common.excel.conovert.ExcelTranslationConvert;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.hengjian.common.translation.annotation.Translation;
import com.hengjian.common.translation.constant.TransConstant;
import com.zsmall.product.entity.domain.ProductQuestionAnswer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 商品问答视图对象 product_question_answer
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductQuestionAnswer.class)
public class ProductQuestionAnswerVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 回复编码
     */
    @ExcelProperty(value = "回复编码")
    private String answerCode;

    /**
     * 提问id
     */
//    @ExcelProperty(value = "提问id")
//    private Long questionId;

    /**
     * 回复内容
     */
    @ExcelProperty(value = "回复内容")
    private String answer;

    /**
     * 回复类型：question-追加提问，answer-回复
     */
    @ExcelProperty(value = "回复类型：question-追加提问，answer-回复")
    private String type;

    /**
     * 排序号
     */
    @ExcelProperty(value = "排序号")
    private Integer sort;

    /**
     * 追加提问状态：reported-已举报
     */
    @ExcelProperty(value = "追加提问状态：reported-已举报", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "biz_product_question_status")
    private String questionStatus;

    /**
     * 创建人
     */
    @ExcelProperty(value = "操作人", converter = ExcelTranslationConvert.class)
    @ExcelTranslationFormat(type = TransConstant.NOT_TENANT_USER_ID_TO_NAME)
    @ExcelI18nFormat(code = "zsmall.excel.role")
    private Long createBy;

    /**
     * 创建人账号
     */
    @ExcelIgnore
    @Translation(type = TransConstant.NOT_TENANT_USER_ID_TO_NAME, mapper = "createBy", other = "Sensitive")
    private String createByName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 日志总数量
     */
    @ExcelIgnore
    private long logCounts;
}
