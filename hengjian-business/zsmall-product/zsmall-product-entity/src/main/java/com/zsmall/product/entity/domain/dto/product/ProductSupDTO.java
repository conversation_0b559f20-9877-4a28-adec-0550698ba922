package com.zsmall.product.entity.domain.dto.product;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-11-23
 **/
@Data
public class ProductSupDTO {

  private Long productId;

  private Long productSkuId;

  private String productSku;

  private String productCode;

  private String productName;

  private String pickUpType;

  private String img;

  /**
   * 平台自提价
   */
  private String pickUpPrice;

  /**
   * 平台代发价
   */
  private String dropshippingPrice;

  private Integer stock;

  /**
   * 供应商单价
   */
  private BigDecimal unitPriceSup;

  /**
   * 供应商操作费
   */
  private BigDecimal operationFeeSup;

  /**
   * 供应商尾程配送费
   */
  private BigDecimal finalDeliveryFeeSup;

}
