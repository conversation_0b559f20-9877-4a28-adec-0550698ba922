package com.zsmall.product.entity.domain.vo.productMapping;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.ProductMappingExtendWayfairWarehouse;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品映射扩展-Wayfair仓库视图对象 product_mapping_extend_wayfair_warehouse
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductMappingExtendWayfairWarehouse.class)
public class ProductMappingExtendWayfairWarehouseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 销售渠道主键
     */
    @ExcelProperty(value = "销售渠道主键")
    private Long channelId;

    /**
     * 仓库主键
     */
    @ExcelProperty(value = "仓库主键")
    private Long warehouseId;

    /**
     * 仓库唯一系统编号
     */
    @ExcelProperty(value = "仓库唯一系统编号")
    private String warehouseSystemCode;

    /**
     * 第三方仓库id
     */
    @ExcelProperty(value = "第三方仓库id")
    private String thirdWarehouseId;

}
