package com.zsmall.product.entity.domain.bo.distributorProduct;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求体-铺货至商店
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ImportToStoreBo {

    /**
     * 自定义商品名
     */
    private String productName;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 渠道id集合
     */
    private List<Long> channelIdList;

    /**
     * Sku集合
     */
    private List<ImportToStoreSkuBo> skuList;

}
