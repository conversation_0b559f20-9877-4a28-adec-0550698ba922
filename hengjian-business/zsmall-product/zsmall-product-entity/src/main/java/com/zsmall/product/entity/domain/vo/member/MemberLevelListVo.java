package com.zsmall.product.entity.domain.vo.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/7 11:12
 */
@Data
public class MemberLevelListVo {

    private Long id;

    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("等级名称")
    @Length(max= 255,message="编码长度不能超过255")
    private String levelName;
    /**
     * 等级启用状态
     */
    @NotNull(message="[等级启用状态]不能为空")
    @ApiModelProperty("等级启用状态 0关闭 1启用")
    private Integer status;

    @ApiModelProperty("创建者")
    private Long createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 操作人名称
     */
    @ApiModelProperty("操作人名称")
    private String operatorName;

    /**
     * 规则遵循者名称
     */
    @ApiModelProperty("经销商昵称")
    private String ruleFollowerName;

    private Long dictCode;

}
