package com.zsmall.product.entity.domain.vo.productSkuPrice;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品sku价格计算公式关联视图对象 product_sku_price_rule_relation
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductSkuPriceRuleRelation.class)
public class ProductSkuPriceRuleRelationVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 商品sku价格表id
     */
    @ExcelProperty(value = "商品sku价格表id")
    private Long productSkuPriceId;

    /**
     * 商品sku表id
     */
    @ExcelProperty(value = "商品sku表id")
    private Long productSkuId;

    /**
     * 定价计算公式表id
     */
    @ExcelProperty(value = "定价计算公式表id")
    private Long productSkuPriceRuleId;


}
