package com.zsmall.product.entity.domain.vo.member;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/9 14:32
 */
@Data
@AutoMapper(target = MemberRuleRelation.class)
public class MemberRuleRelationVO implements Serializable {
    @ApiModelProperty("删除/编辑必传")
    private Long id;

    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("规则定制方租户标识")
    @Length(max= 20,message="编码长度不能超过20")
    private String ruleCustomizerTenantId;
    /**
     * 规则遵守方的租户标识
     */
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty(value = "规则遵守方的租户标识 ",required = true)
    @Length(max= 20,message="编码长度不能超过20")
    @NotBlank(message = "分销商租户标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleFollowerTenantId;
    /**
     * 手机号
     */
    @Size(max= 11,message="号码长度11")
    @ApiModelProperty("手机号")
    @Length(max= 11,message="号码长度11")
    private String phoneNumber;
    /**
     * 邮箱地址
     */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("邮箱地址")
    @Length(max= 255,message="编码长度不能超过255")
    private String emailAddress;

    @ApiModelProperty(value = "等级表标识")
    @NotNull(message = "等级标识不能为空")
    private Long levelId;

    private String nickName;


}
