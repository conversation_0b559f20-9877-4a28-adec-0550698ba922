package com.zsmall.product.entity.domain.vo.productSkuPrice;

import cn.hutool.json.JSONArray;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.hengjian.common.excel.convert.ExcelJSONArrayConvert;
import com.zsmall.product.entity.domain.ProductSkuPriceRule;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 商品sku价格计算公式视图对象 product_sku_price_rule
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductSkuPriceRule.class)
public class ProductSkuPriceRuleVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 计算公式名称
     */
    @ExcelProperty(value = "计算公式名称")
    private String ruleName;

    /**
     * 定价公式
     */
    @ExcelProperty(value = "定价公式")
    private String priceFormula;

    /**
     * 计算公式编码
     */
    @ExcelProperty(value = "计算公式编码")
    private String ruleCode;

    /**
     * 适用类型
     */
    @ExcelProperty(value = "适用类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "biz_applicable_type")
    private Integer applicableType;

    /**
     * 适用的值
     */
    @ExcelProperty(value = "适用的值", converter = ExcelJSONArrayConvert.class)
    private JSONArray applicableValue;

    /**
     * 运算符-单价
     */
    private Integer unitPriceCal;

    /**
     * 提价-单价
     */
    private BigDecimal unitPrice;

    /**
     * 运算符-操作费
     */
    private Integer operationFeeCal;

    /**
     * 提价-操作费
     */
    private BigDecimal operationFee;

    /**
     * 运算符-运费
     */
    private Integer finalDeliveryFeeCal;

    /**
     * 提价-运费
     */
    private BigDecimal finalDeliveryFee;



}
