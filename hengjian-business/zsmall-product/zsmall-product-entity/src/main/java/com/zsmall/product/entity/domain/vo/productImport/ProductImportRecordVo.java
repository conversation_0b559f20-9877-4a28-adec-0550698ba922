package com.zsmall.product.entity.domain.vo.productImport;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.ProductImportRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 商品导入记录视图对象 product_import_record
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductImportRecord.class)
public class ProductImportRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 导入记录编号
     */
    private String importRecordNo;

    /**
     * 导入文件名
     */
    @ExcelProperty(value = "导入文件名")
    private String importFileName;

    /**
     * 导入的商品数量
     */
    @ExcelProperty(value = "导入的商品数量")
    private Integer importProducts;

    /**
     * 导入信息
     */
    @ExcelProperty(value = "导入信息")
    private JSONObject importMessage;

    /**
     * 导入状态：Failed，Cancel，Pending，Importing，Success
     */
    @ExcelProperty(value = "导入状态：Failed，Cancel，Pending，Importing，Success")
    private String importState;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
