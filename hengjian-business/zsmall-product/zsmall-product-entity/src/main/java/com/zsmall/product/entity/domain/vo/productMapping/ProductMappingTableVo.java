package com.zsmall.product.entity.domain.vo.productMapping;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 响应体-商品映射列表信息（分销商商品列表）
 *
 * <AUTHOR>
 * @date 2023-06-20
 */
@Data
public class ProductMappingTableVo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品SKU唯一编号
     */
    private String productSkuCode;
    private Long productSkuId;
    /**
     * 铺货渠道别名
     */
    private String channelAlias;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 活动编号
     */
    private String activityCode;

    /**
     * 参与的活动状态
     */
    private String activityState;

    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;

    /**
     * 映射sku
     */
    private String mappingSku;

    /**
     * 当前库存
     */
    private Integer stockTotal;

    /**
     * 提价值
     */
    private BigDecimal markUpValue;

    /**
     * 最终单价
     */
    private BigDecimal finalPrice;

    /**
     * 目前成本价
     */
    private BigDecimal costPrice;

    /**
     * 利润
     */
    private BigDecimal margin;

    /**
     * 渠道同步状态
     */
    private String syncState;

    /**
     * 渠道同步信息
     */
    private JSONObject syncMessage;

    /**
     * 主图存放路径
     */
    private String imageSavePath;

    /**
     * 渠道sku
     */
    private String channelSku;

    /**
     * 渠道skuID
     */
    private String channelSkuId;
    /**
     * 主图展示地址
     */
    private String imageShowUrl;

    /**
     * 可能的价格变化
     */
    private Boolean priceChanges;

    /**
     * 供应商租户id
     */
    private String supplierTenantId;

    /**
     * 仓库地址集合
     */
    private List<SkuWarehouseMappingVo> warehouseConfigs;
    /**
     * 货号
     */
    private String channelSkuItemNumber;
    /**
     * 站点
     */
    private String site;
    /**
     * 币种
     */
    private String currency;
}
