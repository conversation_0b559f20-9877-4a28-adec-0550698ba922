package com.zsmall.product.entity.domain;

import com.zsmall.system.entity.domain.vo.TenantShippingAddressVo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 询价前端请求对象
 * <AUTHOR>
 */
@Data
public class DeliveryFeeRequest {

    /**
     * 发货仓库编码
     */
    private String deliveryWarehouseCode;
    /**
     * 收货地址邮编
     */
    public  String receivingPostcode;
    /**
     * 商品编码
     */
    private String  productSkuCode;
    /**
     * 站点
     */
    private String site;
    /**
     * 收货国家编码
     */
    public  String receivingCountryCode;
}
