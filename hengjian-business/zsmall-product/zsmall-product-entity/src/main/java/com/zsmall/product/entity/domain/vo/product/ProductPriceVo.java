package com.zsmall.product.entity.domain.vo.product;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 响应参数-商品价格信息
 */
@Data
public class ProductPriceVo {

    /**
     * 商品信息
     */
    private Product product;

    private List<SitePrice> sitePrice;

    /**
     * 原价格
     */
//    private OriginPrice originPrice;
    /**
     * 设置价格
     */
//    private OriginPrice setPrice = new OriginPrice();
    /**
     * Item NO.
     */
    private String itemNo;
    /**
     * SKU上架状态
     */
    private String skuShelfState;
    /**
     * sku审核状态
     */
    private String skuAuditStatus;

    public List<SitePrice> addSitePrice(List<SitePrice> sitePrices){
        this.sitePrice = sitePrices;
        return this.sitePrice;
    }

    /**
     * 商品信息
     */
    @Data
    public static class Product {
        /**
         * 商品图片
         */
        private String productImg;
        /**
         * 商品名称
         */
        private String productName;
        /**
         * Item NO.
         */
        private String itemNo;
        /**
         * sku
         */
        private String sku;
        /**
         * 发货方式
         */
        private String supportedLogistics;
        /**
         * 支持的物流方式
         */
        private String supportedLogisticsMethod;
        /**
         * 价格变化标志位
         */
        private Boolean hasPriceChange;
    }

    /**
     * 原价格
     */
    @Data
    public static class OriginPrice {
        /**
         * Item NO.
         */
        private String itemNo;
        /**
         * unitPrice
         */
        private BigDecimal originalUnitPrice;
        /**
         * operationFee
         */
        private BigDecimal originalOperationFee;
        /**
         * finalDeliveryFee
         */
        private BigDecimal originalFinalDeliveryFee;
        /**
         * 建议零售价
         */
        private BigDecimal msrp;
        /**
         * 数据变更标志位
         */
        private Boolean isDataChange = false;
    }

    @Data
    public static class SitePrice {
        // 站点价格id
        private Long id;
        private String currencyCode;
        private String currencySymbol;
        private String countryCode;
        private Long siteId;
        private OriginPrice originPrice;
        private OriginPrice setSitePrice = new OriginPrice();
    }
}
