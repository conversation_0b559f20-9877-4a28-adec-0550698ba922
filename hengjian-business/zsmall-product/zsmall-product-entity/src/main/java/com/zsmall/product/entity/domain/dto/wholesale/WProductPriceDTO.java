package com.zsmall.product.entity.domain.dto.wholesale;

import com.zsmall.product.entity.domain.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 国外现货商品价格DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class WProductPriceDTO {

    /**
     * 商品类型（国外现货批发：WholesaleProduct）
     */
    private String productType;

    private List<ProductWholesaleTieredPrice> pwtps;
    private List<ProductWholesaleTieredPrice> delTieredPrices;
    private List<ProductWholesaleTieredPriceLog> pwtpLogs;
    private List<ProductSkuWholesalePrice> pswps;
    private List<ProductSkuWholesalePriceLog> pswpLogs;
    private ProductWholesaleDetail productWholesaleDetailEntity;


}
