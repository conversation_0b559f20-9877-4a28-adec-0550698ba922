package com.zsmall.product.entity.domain.bo.tenantFavorites;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hengjian.common.mybatis.core.domain.QueryBaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 请求体-租户收藏夹列表
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Data
public class TenantFavoritesListBo extends QueryBaseEntity {
    /**
     * 商品名称
     */
    private String productSkuName;
    /**
     * 商品类型：NormalProduct-普通商品，WholesaleProduct-批发商品
     */
    private String productType;
    /**
     * sku
     */
    private String sku;
    /**
     * sku id
     */
    private String skuId;
    /**
     * 上下架状态状态 OnShelf上架 OffShelf下架 ForcedOffShelf审核中
     */
    private String skuShelfState;
    /**
     * 勾选导出
     */
    private List<Long> tenantFavoritesIds;
    private String tenantId;
    /**
     * 站点
     */
    private String site;
}
