package com.zsmall.product.entity.domain.dto.productImport;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.fastjson.JSONObject;
import com.zsmall.common.annotaion.ExcelFieldAnnotation;
import com.zsmall.common.domain.dto.ExcelBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/7/16 13:50
 */
@Data
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class LevelPriceV2DTO extends ExcelBaseDTO {

    @ExcelFieldAnnotation(required = true)
    private String itemNo;

    /**
     * 国家代码
     */
    @ExcelFieldAnnotation(required = true)
    private String countryCode;

    private List<JSONObject> priceJSON;

    @ExcelIgnore
    private BigDecimal unitPrice;
    @ExcelIgnore
    private BigDecimal originalFinalDeliveryFee;
    @ExcelIgnore
    private BigDecimal originalOperationFee;


}
