package com.zsmall.product.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductReviewChangeDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品审核变更详情业务对象 product_review_change_detail
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductReviewChangeDetail.class, reverseConvertGenerate = false)
public class ProductReviewChangeDetailBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 审核记录id
     */
    @NotNull(message = "审核记录id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long reviewRecordId;

    /**
     * 字段变更所属商品编号
     */
    @NotBlank(message = "字段变更所属商品编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productCode;

    /**
     * 字段变更所属商品SKU编号（若是Product变更则不需要填此字段）
     */
    @NotBlank(message = "字段变更所属商品SKU编号（若是Product变更则不需要填此字段）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productSkuCode;

    /**
     * 字段名
     */
    @NotBlank(message = "字段名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldName;

    /**
     * 字段值（修改前）
     */
    @NotBlank(message = "字段值（修改前）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldValueBefore;

    /**
     * 字段值（修改后）
     */
    @NotBlank(message = "字段值（修改后）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldValueAfter;

    /**
     * 允许更新（1-允许，0-不允许，是否允许定时器更新此字段，默认都是允许，若出现立即生效的价格变更，该SKU的所有指定日期生效的价格都将变为不允许更新）
     */
    @NotNull(message = "允许更新（1-允许，0-不允许，是否允许定时器更新此字段，默认都是允许，若出现立即生效的价格变更，该SKU的所有指定日期生效的价格都将变为不允许更新）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long allowUpdate;


}
