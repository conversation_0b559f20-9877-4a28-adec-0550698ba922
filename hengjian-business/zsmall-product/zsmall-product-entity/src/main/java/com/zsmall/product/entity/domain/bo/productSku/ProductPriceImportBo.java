package com.zsmall.product.entity.domain.bo.productSku;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/7/16 16:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductPriceImportBo extends BaseEntity {
//    private boolean isUpdate;
    private List<RuleLevelProductPrice> ruleLevelProductPrices = new ArrayList<>();
    private List<RuleLevelProductPrice> ruleLevelProductPricesForUpdate = new ArrayList<>();


    /**
     * 功能描述：添加规则价格
     *
     * @param ruleLevelProductPrice 规则级产品价格
     * <AUTHOR>
     * @date 2024/07/11
     */
    public void addRulePrice(RuleLevelProductPrice ruleLevelProductPrice) {
        ruleLevelProductPrices.add(ruleLevelProductPrice);
    }

    /**
     * 功能描述：为upate添加规则价格
     *
     * @param ruleLevelProductPrice 规则级产品价格
     * <AUTHOR>
     * @date 2024/07/11
     */
    public void addRulePriceForUpate(RuleLevelProductPrice ruleLevelProductPrice) {
        ruleLevelProductPricesForUpdate.add(ruleLevelProductPrice);
    }
}
