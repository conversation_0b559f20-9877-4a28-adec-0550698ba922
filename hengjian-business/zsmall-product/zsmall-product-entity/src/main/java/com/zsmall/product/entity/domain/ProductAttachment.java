package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 商品SPU附件对象 product_attachment
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_attachment")
@NoArgsConstructor
public class ProductAttachment extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 存储对象主键
     */
    private Long ossId;

    /**
     * 商品SPU表主键
     */
    private Long productId;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件原名
     */
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    private Integer attachmentSort;

    /**
     * 附件类型
     */
    private AttachmentTypeEnum attachmentType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
