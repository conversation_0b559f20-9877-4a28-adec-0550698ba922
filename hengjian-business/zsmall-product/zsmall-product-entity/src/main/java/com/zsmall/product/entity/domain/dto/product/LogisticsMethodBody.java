package com.zsmall.product.entity.domain.dto.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 通用信息-履约方式
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "通用信息-履约方式")
public class LogisticsMethodBody {

    @Schema(title = "key")
    private String key;

    @Schema(title = "value")
    private BigDecimal value;

}
