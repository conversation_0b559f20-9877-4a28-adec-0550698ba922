package com.zsmall.product.entity.domain.dto.productSku;

import lombok.Data;

/**
 * 商品数据统计详情接收类
 *
 * <AUTHOR>
 * @date 2022/10/9 15:04
 */
@Data
public class AnalysisProductSkuByDayDto {


    /**
     * 日期
     */
    private String day;

    /**
     * itemNo
     */
    private String productSkuCode;

    /**
     * 有铺货的sku
     */
    private Integer skuDistributionNum;

    /**
     * 出单的sku
     */
    private Integer skuOrderNum;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 订单数
     */
    private Integer orderNum;

    /**
     * 售后订单数
     */
    private Integer restockOrderNum;

    /**
     * 总价
     */
    private Double totalDropShippingPrice;

    /**
     * 代发总价
     */
    private Double issuedOnBehalfTotalPrice;

    /**
     * 自提总价
     */
    private Double selfLiftingTotalPrice;

    /**
     * 自提价格
     */
    private Double pickUpPrice;

    /**
     * 代发价格
     */
    private Double originPrice;

    /**
     * 销售额
     */
    private Double totalOriginPrice;

    /**
     * 售后数量
     */
    private Integer restockNum;

    /**
     * 销量
     */
    private Integer salesNum;

    /**
     * 库存数量
     */
    private Integer inventoryNum;

    /**
     * 代发订单数量
     */
    private Integer issuedOnBehalfOrderNum;

    /**
     * 自提订单数量
     */
    private Integer selfLiftingOrderNum;

    /**
     * 分销商数
     */
    private Integer bulkNum;

    /**
     * 商户ID
     */
    private String tenantId;


}
