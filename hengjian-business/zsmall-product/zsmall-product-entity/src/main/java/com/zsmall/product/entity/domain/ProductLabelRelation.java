package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 商品标签关联对象 product_label_relation
 *
 * <AUTHOR> Li
 * @date 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_label_relation")
public class ProductLabelRelation extends BaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
