package com.zsmall.product.entity.domain.bo.productBatch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求参数-Excel类型
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "请求参数-Excel类型")
public class ReqExcelTypeBody {

    @Schema(title = "Excel类型：Product_Price_Update-商品价格更新Excel，Product_Stock_Update-商品库存更新Excel")
    private String excelType;

}
