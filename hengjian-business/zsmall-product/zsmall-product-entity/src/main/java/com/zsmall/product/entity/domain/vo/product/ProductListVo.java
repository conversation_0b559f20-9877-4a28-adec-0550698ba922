package com.zsmall.product.entity.domain.vo.product;

import cn.hutool.json.JSONArray;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 商品SPU类表展示对象
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
public class ProductListVo {

    /**
     * 商品名
     */
    private String productName;

    /**
     * 商品类型
     */
    private String productType;

    /**
     *
     */
    private String productCode;

    /**
     * 图片地址
     */
    private String imageShowUrl;

    /**
     * 审核状态
     */
    private String verifyState;

    /**
     * 货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
    private String shelfState;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 审核意见选项
     */
    private JSONArray reviewOpinionOption;

    /**
     * 存在价格变更审核
     */
    private Boolean hasPriceChange;

    /**
     * 支持的物流
     */
    private String supportedLogistics;

    /**
     * 库存推送时间
     */
    private Date inventoryPushTime;

    /**
     * 商品SKU集合
     */
    private List<ProductSkuListVo> skuList;

}
