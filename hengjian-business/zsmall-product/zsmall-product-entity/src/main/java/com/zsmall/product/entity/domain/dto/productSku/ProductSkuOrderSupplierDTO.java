package com.zsmall.product.entity.domain.dto.productSku;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单合计数 dto
 *
 * <AUTHOR>
 * @date 2022/9/21 15:33
 */
@Data
@ExcelIgnoreUnannotated
public class ProductSkuOrderSupplierDTO {

    /**
     * SKU总数
     */
    @ExcelProperty(value = "SKU总数", order = 6)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.skuNum")
    private Integer skuNum;

    /**
     * 供货商ID
     */
    @ExcelProperty(value = "供货商ID", order = 1)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.tenantId")
    private String tenantId;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "公司名称", order = 2)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.companyName")
    private String companyName;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱", order = 3)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.email")
    private String email;

    /**
     * 公司电话
     */
    @ExcelProperty(value = "公司电话", order = 4)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.phoneNumber")
    private String phoneNumber;
    /**
     * 注册时间
     */
    @ExcelProperty(value = "注册时间", order = 5)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.createTime")
    private String createTime;

    /**
     * 主营产品 TODO
     */
    /*@ExcelProperty(value = "主营产品", order = 11)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.mainProducts")
    private String mainProducts;*/

    /**
     * 有库存的SKU总数
     */
    @ExcelProperty(value = "有库存的SKU总数", order = 7)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.skuInventoryNum")
    private Long skuStockNum;

    /**
     * 有出单的SKU总数
     */
    @ExcelProperty(value = "有出单的SKU总数", order = 8)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.skuOrderNum")
    private Integer skuOrderNum;

    /**
     * 销售件数总数
     */
    @ExcelProperty(value = "销售件数总数", order = 10)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.salesNum")
    private Integer salesNum;

    /**
     * 订单总数
     */
    @ExcelProperty(value = "订单总数", order = 9)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.orderTotalNum")
    private Integer orderTotalNum;

    /**
     * 出单总金额
     */
    @ExcelProperty(value = "出单总金额", order = 11)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.orderTotalPrice")
    private BigDecimal orderTotalPrice;

    /**
     * 售后订单总数
     */
    @ExcelProperty(value = "售后订单总数", order = 12)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.restockTotalNum")
    private Integer restockTotalNum;

    /**
     * 收藏数（分销商数）
     */
    @ExcelProperty(value = "收藏数（分销商数）", order = 13)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.dropNumByDis")
    private Integer dropNumByDis;

    /**
     * 处理订单时效（小时）
     */
    @ExcelProperty(value = "处理订单时效（小时）", order = 14)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.supplier.orderDealEffectiveness")
    private Long orderDealEffectiveness;


}
