package com.zsmall.product.entity.domain.vo.product;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 仓库临时对象（查询商品详情时自带）
 * <AUTHOR>
 * @date 2023-05-22
 */
@Data
public class WarehouseTempVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库类型
     */
    @ExcelProperty(value = "仓库类型")
    private String warehouseType;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 仓库唯一系统编号
     */
    @ExcelProperty(value = "仓库唯一系统编号")
    private String warehouseSystemCode;

    /**
     * 可选物流模板
     */
    @ExcelProperty(value = "可选物流模板")
    private List<JSONObject> logisticsTemplateSelect = new ArrayList<>();
}
