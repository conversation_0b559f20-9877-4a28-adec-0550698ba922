package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.bo.UniversalBo.UniversalQueryBo;
import com.zsmall.product.entity.domain.bo.product.CustomExportBo;
import com.zsmall.product.entity.domain.dto.productSku.ApplicableProductSkuDTO;
import com.zsmall.product.entity.domain.dto.productSku.ApplicableProductSkuParamDTO;
import com.zsmall.product.entity.domain.dto.wholesale.WholesaleProductPageDTO;
import com.zsmall.product.entity.domain.dto.wholesale.WholesaleProductParamDTO;
import com.zsmall.product.entity.domain.vo.ProductSkuStockApiVo;
import com.zsmall.product.entity.domain.vo.product.*;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAdjustStockVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAndStockVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuVo;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 商品SKUMapper接口
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
public interface ProductSkuMapper extends BaseMapperPlus<ProductSku, ProductSkuVo> {

    List<ProductSku> queryByIdsIncludeDel(@Param("ids") Long... ids);

    /**
     * 获取已绑定的适用商品数量
     * @param dto
     * @return
     */
    Integer getBoundApplicableProductSkuNum(@Param("dto") ApplicableProductSkuParamDTO dto);

    /**
     * 根据条件获取适用产品
     * @param pageQuery
     * @param dto
     * @return
     */
    IPage<ApplicableProductSkuDTO> getApplicableProductSku(@Param("dto") ApplicableProductSkuParamDTO dto, Page<ApplicableProductSkuDTO> pageQuery);

    List<ApplicableProductSkuDTO> getApplicableProductSku(@Param("dto") ApplicableProductSkuParamDTO dto);

    /** 查询Sku编号是否存在 */
    @InterceptorIgnore(tenantLine = "true")
    boolean existProductSkuCode(@Param("productSkuCode") String productSkuCode);

    /**
     * SKU是否存在
     * @param sku
     * @return
     */
    boolean existSku(@Param("sku") String sku, @Param("tenantId") String tenantId, @Param("excludeId") Long excludeId);

    /**
     * UPC是否存在
     * @param upc
     * @return
     */
    boolean existUpc(@Param("upc") String upc, @Param("excludeId") Long excludeId);

    /**
     * 查询在售库存总数（根据Sku唯一编号）
     * @param productSkuCode
     * @return
     */
    Integer queryStockTotal(@Param("productSkuCode") String productSkuCode);

    /**
     * 查询SKU重复
     * @param id
     * @param productId
     * @param sku
     * @return
     */
    int countSkuDuplicate(@Param("id") Long id, @Param("productId") Long productId, @Param("sku") String sku);

    /**
     * 查询UPC重复
     * @param id
     * @param upc
     * @return
     */
    int countUpcDuplicate(@Param("id") Long id, @Param("upc") String upc);

    /**
     * 根据商品主键查询列表展示用响应体
     * @param productId
     * @return
     */
    List<ProductSkuListVo> queryProductSkuListVoByProductId(@Param("productId") Long productId);
    List<ProductSkuListVo> queryProductSkuListVoByProductIds(@Param("productIds") List<Long> productIds, @Param("siteId") Long siteId,
                                                             @Param("bo") UniversalQueryBo bo);
    List<ProductSkuListVo> queryProductSkuListVoByProductIdsAndShelfState(@Param("productIds") List<Long> productIds,@Param("skuShelfState") String skuShelfState);
    List<ProductSkuListVo> queryWholesaleProductSkuListVoByProductId(@Param("productId") Long productId, @Param("rejected")  String rejected);

    List<ProductSku> queryByProductIdIncludeDel(@Param("productId") Long productId);

    /**
     * 通过商品主键获取未删除的商品Sku主键集合
     * @param productId
     * @return
     */
    List<Long> queryIdsByProductId(@Param("productId") Long productId);

    /**
     * 根据商品SKU唯一编号和仓库查询商品SKU
     * @param productSkuCode
     * @param warehouseSystemCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    ProductSku queryByProductSkuCodeAndWarehouseSystemCode(@Param("productSkuCode") String productSkuCode, @Param("warehouseSystemCode") String warehouseSystemCode);

    @InterceptorIgnore(tenantLine = "true")
    ProductSkuAdjustStockVo queryAdjustStockVo(@Param("productSkuCode") String productSkuCode);

    /**
     * 查询商品SKU库存管理方的国家
     * @param productSkuCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> queryStockManagerCountry(@Param("productSkuCode") String productSkuCode, @Param("specifyWarehouse") String specifyWarehouse);

    @InterceptorIgnore(tenantLine = "true")
    ProductSku queryByProductSkuCodeIncludeDel(@Param("productSkuCode") String productSkuCode);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductSku> queryByProductSkuCodesIncludeDel(@Param("productSkuCodes") Collection productSkuCodes);

    IPage<ProductSku> getPage(@Param("queryType") String queryType, @Param("queryValue") String queryValue, Page<ProductSku> page,
                              @Param("siteId")Long siteId, @Param("skuShelfState")String skuShelfState, @Param("skuAuditStatus")String skuAuditStatus);

    @InterceptorIgnore(tenantLine = "true")
    IPage<ProductSku> queryProductSkuControlPage(@Param("queryType") String queryType, @Param("queryValue") String queryValue, Page<ProductSku> page);

    IPage<ProductSkuAndStockVo> queryProductSkuAndStock(@Param("queryType") String queryType, @Param("queryValue") String queryValue, Page<ProductSku> page);

    @InterceptorIgnore(tenantLine = "true")
    ProductSkuAndStockVo queryProductSkuAndStockByCode(@Param("productSkuCode") String productSkuCode);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductCustomExportVo> queryCustomExportList(@Param("bo") CustomExportBo bo);

    @InterceptorIgnore(tenantLine = "true")
    ProductFavoritesVo queryProductFavorites(@Param("productCode") String productCode);

    /**
     * 获取国外现货批发商品
     * @param param
     * @param queryPage
     * @return
     */
    IPage<WholesaleProductPageDTO> getWholesaleProductPage(@Param("param") WholesaleProductParamDTO param, Page<WholesaleProductPageDTO> queryPage);

    @InterceptorIgnore(tenantLine = "true")
    Page<ProductSkuSimpleVo> getPageForQA(@Param("tenantId") String tenantId, @Param("queryType") String queryType, @Param("queryValue") String queryValue, Page<ProductSku> page);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductSkuFavoritesSimpleVo> queryByFavoritesIdsForFavorites(@Param("favoritesIds") List<String> favoritesIds);

    @InterceptorIgnore(tenantLine = "true")
    ProductSkuForMarketplaceShowVo queryForMarketplaceShow(@Param("productSkuCode") String productSkuCode);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductSkuForMarketplaceShowVo> queryForMarketplaceShows(@Param("productSkuCode") List<String> productSkuCode);
    @InterceptorIgnore(tenantLine = "true")
    Long queryDealEffectiveness(@Param("productSkuCode") String productSkuCode);

    /**
     * 功能描述：更新库存总批次
     *
     * @param stockTotalMap 库存总量图
     * <AUTHOR>
     * @date 2024/03/21
     */
    @InterceptorIgnore(tenantLine = "true")
    int updateStockTotalBatch(@Param("stockTotalMap")LinkedHashMap<Long, Integer> stockTotalMap);

    /**
     * 查询供应商SKU集合
     * @param objectPage
     * @return
     */
    IPage<String> selectSkuListByPage(Page<Object> objectPage);

    List<ProductSkuStockApiVo> selectProductSkuStockBySkus(HashSet<String> skuSet, String supplierTenantId);

    /**
     * 查询商品的库存信息
     *
     * @param productSkuCode 商品编码
     * @param type           1查询所有库存信息 2 查询有库存的仓库信息
     * @return
     */
    List<String> getProductSkuStocksBySku(@Param("productSkuCode") String productSkuCode, @Param("type") int type);

    /**
     * 查询商品的库存信息
     *
     * @param productSkuCode 商品编码
     * @param type           1查询所有库存信息 2 查询有库存的仓库信息
     * @return
     */
    List<WarehouseVo> getInStockWarehouseBySku(@Param("productSkuCode") String productSkuCode, @Param("type") int type,@Param("warehouseSystemCode") String warehouseSystemCode);
    /**
     * 查询供应商SKU集合
     * @param objectPage
     * @return
     */
    IPage<ProductSkuStockApiVo> selectSkuInventoryByPage(Page<ProductSkuStockMapper> objectPage,@Param("warehouseCodes") List<String> warehouseCodes,@Param("tenantId") List<String> tenantId);

    /**
     * 查询所有的仓库编码
     * @param tenantId
     * @return
     */
    List<String> getWarehouseCodes(@Param("tenantId") String tenantId);

    /**
     * 查询销量排行榜商品数据
     * @return
     */
    List<String> getSalesRankingProduct();

    /**
     * 查询猜你喜欢商品品类
     * @param tenantId
     * @return
     */
    List<String> getGuessYouLikeCategoryId(@Param("tenantId") String tenantId);

    /**
     * 根据品类查询商品
     * @param caId
     * @return
     */
    List<ProductSku> getGuessYouLikeProduct(@Param("caId") String caId);

    IPage<ProductSku> getQueryCount(@Param("queryType")String queryType, @Param("queryValue")String queryValue, Page<ProductSku> page, Long siteId,
                                    @Param("itemNos")List<String> itemNos,@Param("skuAuditStatus")String skuAuditStatus);

    List<ProductPriceExportDto> getProductPrice(@Param("queryType")String queryType, @Param("queryValue")String queryValue, Long siteId,@Param("itemNos") List<String> itemNos,@Param("skuAuditStatus")String skuAuditStatus);

    List<WarehouseAdminInfo> getWarehouseAdminInfos();

}
