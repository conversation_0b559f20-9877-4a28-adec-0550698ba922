package com.zsmall.product.entity.domain.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 通用参数-商品信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-商品（标签）信息")
public class ProductToLabelBody {

    @Schema(title = "商品code")
    private String productCode;

    @Schema(title = "创建时间")
    private String createDateTime;

    @Schema(title = "中文名称")
    private String name;

    @Schema(title = "排序")
    private Integer sort;

    @Schema(title = "商品图片显示链接")
    private String productImageShowUrl;

    @Schema(title = "最低价格")
    private BigDecimal minPrice;

    @Schema(title = "最高价格")
    private BigDecimal maxPrice;

    @Schema(title = "标签id")
    private List<Long> labelIdList;

    @Schema(title = "标签")
    private List<String> labelNameList;

    @Schema(title = "spuId（商品id）")
    private Long productId;

    @Schema(title = "销量")
    private Integer sold;

    @Schema(title = "库存")
    private Integer quantity;

}
