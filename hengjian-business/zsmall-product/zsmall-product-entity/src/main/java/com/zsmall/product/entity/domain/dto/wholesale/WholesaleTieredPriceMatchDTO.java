package com.zsmall.product.entity.domain.dto.wholesale;

import com.zsmall.product.entity.domain.ProductSkuWholesalePrice;
import com.zsmall.product.entity.domain.ProductWholesaleTieredPrice;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 国外现货批发商品阶梯价匹配DTO
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Data
@NoArgsConstructor
public class WholesaleTieredPriceMatchDTO {

    /**
     * 阶梯价主表
     */
    private ProductWholesaleTieredPrice tieredPrice;

    /**
     * 商品SKU价格MAP，key为ProductSkuCode，Value为ProductSkuWholesalePriceEntity
     */
    private Map<String, ProductSkuWholesalePrice> productSkuWholesalePriceMap;

}
