package com.zsmall.product.entity.domain.member;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2024/05/09
 */
@TableName(value ="member_rule_relation_log")
@Data
public class MemberRuleRelationLog implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 出发等级id
     */
    private String departureLevelId;

    /**
     * 目的等级等级id
     */
    private String destinationLevelId;

    /**
     * 出发等级
     */
    private String departureLevel;

    /**
     * 目的等级
     */
    private String destinationLevel;

    /**
     * 邮箱
     */
    private String emailAddress;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 操作类型 新增:0 更新:1 删除:2
     */
    private Integer operationType;
    private Long memberRuleRelationId;

    /**
     * 修改前联系方式
     */
    private String departurePhoneNumber;
    /**
     * 修改后电话号码
     */
    private String destinationPhoneNumber;

    /**
     * 规则遵守者租户标识
     */
    private String ruleFollowerTenantId;

    /**
     * 规则制定者租户标识
     */
    private String ruleCustomizerTenantId;

    /**
     *
     */
    private Long createBy;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Long updateBy;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
