package com.zsmall.product.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuAttachment;
import com.zsmall.product.entity.domain.bo.ProductSkuAttachmentBo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品SKU附件表-数据库层
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuAttachmentService extends ServiceImpl<ProductSkuAttachmentMapper, ProductSkuAttachment> {

    private final ProductSkuAttachmentMapper baseMapper;

    /**
     * 查询商品SKU附件
     */
    public ProductSkuAttachment queryById(Long id){
        return baseMapper.selectById(id);
    }

    /**
     * 查询商品SKU附件列表
     */
    public TableDataInfo<ProductSkuAttachmentVo> queryPageList(ProductSkuAttachmentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSkuAttachment> lqw = buildQueryWrapper(bo);
        Page<ProductSkuAttachmentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品SKU附件列表
     */
    public List<ProductSkuAttachmentVo> queryList(ProductSkuAttachmentBo bo) {
        LambdaQueryWrapper<ProductSkuAttachment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuAttachment> buildQueryWrapper(ProductSkuAttachmentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductSkuId() != null, ProductSkuAttachment::getProductSkuId, bo.getProductSkuId());
        lqw.like(StringUtils.isNotBlank(bo.getAttachmentName()), ProductSkuAttachment::getAttachmentName, bo.getAttachmentName());
        lqw.like(StringUtils.isNotBlank(bo.getAttachmentOriginalName()), ProductSkuAttachment::getAttachmentOriginalName, bo.getAttachmentOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentSuffix()), ProductSkuAttachment::getAttachmentSuffix, bo.getAttachmentSuffix());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentSavePath()), ProductSkuAttachment::getAttachmentSavePath, bo.getAttachmentSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentShowUrl()), ProductSkuAttachment::getAttachmentShowUrl, bo.getAttachmentShowUrl());
        lqw.eq(bo.getAttachmentSort() != null, ProductSkuAttachment::getAttachmentSort, bo.getAttachmentSort());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentType()), ProductSkuAttachment::getAttachmentType, bo.getAttachmentType());
        return lqw;
    }

    /**
     * 新增商品SKU附件
     */
    public Boolean insertByBo(ProductSkuAttachmentBo bo) {
        ProductSkuAttachment add = MapstructUtils.convert(bo, ProductSkuAttachment.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 批量新增商品SKU附件
     *
     * @param entityList
     * @return
     */
    public Boolean insertBatch(Collection<ProductSkuAttachment> entityList) {
        log.info("进入【批量新增商品SKU附件】 entityCollections = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.insertBatch(entityList);
    }

    /**
     * 修改商品SKU附件
     */
    public Boolean updateByBo(ProductSkuAttachmentBo bo) {
        ProductSkuAttachment update = MapstructUtils.convert(bo, ProductSkuAttachment.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuAttachment entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SKU附件
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    /**
     * 通过skuId获取sku图片集合（按排序升序）
     * @param skuId
     * @return
     */
    public List<ProductSkuAttachment> findBySkuIdOrderBySortAsc(Long skuId) {
        LambdaQueryWrapper<ProductSkuAttachment> lqw = Wrappers.lambdaQuery();
         lqw.eq(ProductSkuAttachment::getProductSkuId, skuId)
            .orderByAsc(ProductSkuAttachment::getAttachmentSort);
         return baseMapper.selectList(lqw);
    }

    /**
     * 通过skuId和附件类型获取sku图片集合（按排序升序）
     * @param skuId
     * @param attachmentType
     * @return
     */
    public List<ProductSkuAttachment> queryBySkuIdAndAttachmentTypeOrderBySortAsc(Long skuId, AttachmentTypeEnum attachmentType) {
        LambdaQueryWrapper<ProductSkuAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuAttachment::getProductSkuId, skuId)
            .eq(ProductSkuAttachment::getAttachmentType, attachmentType)
            .orderByAsc(ProductSkuAttachment::getAttachmentSort);
        return baseMapper.selectList(lqw);
    }

    public List<ProductSkuAttachmentVo> queryFirstByProductSkuCode(String productSkuCode) {
        return MapstructUtils.convert(baseMapper.getFirstByProductSkuCode(productSkuCode), ProductSkuAttachmentVo.class);
    }

    /**
     * 根据商品主键查询首图
     *
     * @param productId
     * @return
     */
    public ProductSkuAttachmentVo queryFirstImageByProductId(Long productId) {
        log.info("进入【根据商品主键查询首图】 productId = {}", productId);
        return baseMapper.queryFirstImageByProductId(productId);
    }
    /**
     * 根据商品主键查询首图
     *
     * @param productIds
     * @return
     */
    public List<ProductSkuAttachmentVo> queryFirstImageByProductIds(List<Long> productIds,String tenantId) {
        log.info("进入【根据商品主键查询首图】 productId = {}", productIds);
        return baseMapper.queryFirstImageByProductIds(productIds,tenantId);
    }
    @InMethodLog("根据商品SKU主键查询首图")
    public ProductSkuAttachmentVo queryFirstImageByProductSkuId(Long productSkuId) {
        return baseMapper.queryFirstImageByProductSkuId(productSkuId);
    }

    @InMethodLog("根据商品SKU编号查询首图")
    public ProductSkuAttachmentVo queryFirstImageByProductSkuCode(String productSkuCode) {
        return baseMapper.queryFirstImageByProductSkuCode(productSkuCode);
    }

    @InMethodLog("根据Sku主键获取主图")
    public String getPrimaryImgBySkuId(Long productSkuId) {
        LambdaQueryWrapper<ProductSkuAttachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuAttachment::getProductSkuId, productSkuId)
            .eq(ProductSkuAttachment::getAttachmentType, AttachmentTypeEnum.Image.name())
            .orderByAsc(ProductSkuAttachment::getAttachmentSort);
        List<ProductSkuAttachmentVo> list = baseMapper.selectVoList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return null;
        } else {
            return list.get(0).getAttachmentShowUrl();
        }
    }

    public List<String> getAllImgBySkuId(Long skuId) {
        log.info("进入【根据SkuId获取所有图片】方法,  skuId= {}", skuId);
        LambdaQueryWrapper<ProductSkuAttachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuAttachment::getProductSkuId, skuId)
            .eq(ProductSkuAttachment::getAttachmentType, AttachmentTypeEnum.Image.name())
            .orderByAsc(ProductSkuAttachment::getAttachmentSort);
        List<ProductSkuAttachmentVo> list = baseMapper.selectVoList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(ProductSkuAttachmentVo::getAttachmentShowUrl).collect(Collectors.toList());
    }

    /**
     * 根据商品SKU主键查询所有有效的附件
     * @param productSkuId
     * @return
     */
    public List<Long> queryIdsByProductSkuId(Long productSkuId) {
        log.info("进入【根据商品SKU主键查询所有有效的附件】方法 productSkuId = {}", productSkuId);
        return baseMapper.queryIdsByProductSkuId(productSkuId);
    }

    /**
     * 根据商品SKU主键删除
     * @param productSkuIdList
     * @return
     */
    public Boolean deleteByProductSkuIdList(List<Long> productSkuIdList) {
        log.info("进入【根据商品SKU主键删除】 productSkuIdList = {}", JSONUtil.toJsonStr(productSkuIdList));
        LambdaQueryWrapper<ProductSkuAttachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuAttachment::getProductSkuId, productSkuIdList);
        return baseMapper.delete(queryWrapper) > 0;
    }

    /**
     * 批量查询商品SKU附件（按类型）
     * @param productSkuIds SKU主键集合
     * @param attachmentType 附件类型
     * @return SKU附件列表
     */
    public List<ProductSkuAttachment> queryByProductSkuIdsAndType(List<Long> productSkuIds, AttachmentTypeEnum attachmentType) {
        log.info("进入【批量查询商品SKU附件】 productSkuIds = {}, attachmentType = {}", JSONUtil.toJsonStr(productSkuIds), attachmentType);
        if (CollUtil.isEmpty(productSkuIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProductSkuAttachment> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSkuAttachment::getProductSkuId, productSkuIds)
            .eq(ProductSkuAttachment::getAttachmentType, attachmentType)
            .orderByAsc(ProductSkuAttachment::getAttachmentSort);
        // 注意：与原始 queryBySkuIdAndAttachmentTypeOrderBySortAsc() 方法保持一致，不使用特殊的租户处理
        return baseMapper.selectList(lqw);
    }

    public void cleanAndCopySourceTenantId(HashMap<String, List<ProductSku>> sourceTargetSkuMap,
                                           HashMap<String, String> sourceTargetSkuCodeMap, ArrayList<Long> sourceIds,
                                           HashMap<Long, Long> sourceTargetSkuIdMap) {
        IProductSkuAttachmentService proxy = (IProductSkuAttachmentService) AopContext.currentProxy();
        List<ProductSku> sourceProductSkus = sourceTargetSkuMap.get("sourceSku");
        List<ProductSku> targetProductSkus = sourceTargetSkuMap.get("targetSku");

        Map<Long, String> sourceSkuMap = sourceProductSkus.stream()
                                                          .collect(Collectors.toMap(
                                                              ProductSku::getId,
                                                              ProductSku::getProductSkuCode
                                                          ));

        Map<String, Long> targetSkuMap = targetProductSkus.stream()
                                                          .collect(Collectors.toMap(
                                                              ProductSku::getProductSkuCode,
                                                              ProductSku::getId
                                                          ));

        // sourceProductSkus转换成map,key是 productSkuId,value是productSkuCode;targetProductSkus的转换成map,key是productSkuCode,value是productSkuId;sourceTargetSkuCodeMap 是sourceProductSkus与targetProductSkus的productSkuCode映射

        LambdaQueryWrapper<ProductSkuAttachment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductSkuAttachment::getProductSkuId,sourceSkuMap.keySet());
        List<ProductSkuAttachment> skuAttachments = TenantHelper.ignore(() -> baseMapper.selectList(lambdaQueryWrapper));
        ArrayList<ProductSkuAttachment> productSkuAttachments = new ArrayList<>();
        for (ProductSkuAttachment skuAttachment : skuAttachments) {
            ProductSkuAttachment productSkuAttachment = new ProductSkuAttachment();
            BeanUtil.copyProperties(skuAttachment,productSkuAttachment);
            Long productSkuId = skuAttachment.getProductSkuId();
            String sourceCode = sourceSkuMap.get(productSkuId);
            // 通过映射找到目标SKU代码
            if(CollUtil.isEmpty(sourceTargetSkuCodeMap)||(CollUtil.isNotEmpty(sourceTargetSkuCodeMap)&&!sourceTargetSkuCodeMap.containsKey(sourceCode))){
                continue;
            }
            String targetCode = sourceTargetSkuCodeMap.get(sourceCode);

            if (targetCode != null && targetSkuMap.containsKey(targetCode)) {
                // 通过目标SKU代码找到目标SKU ID
                Long targetId = targetSkuMap.get(targetCode);
                sourceTargetSkuIdMap.put(productSkuId,targetId);

                productSkuAttachment.setProductSkuId(targetId);
            }
            productSkuAttachment.setId(null);
            productSkuAttachments.add(productSkuAttachment);
        }
        proxy.saveBatch(productSkuAttachments);
    }
}
