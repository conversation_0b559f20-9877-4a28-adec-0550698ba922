package com.zsmall.product.entity.domain.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 获取使用产品接收实体
 *
 * <AUTHOR>
 * @date 2022/10/26 11:59
 */
@Data
@Schema(name = "响应参数-适用产品信息")
public class ApplicableProductSkuVo {

    @Schema(title = "itemNo")
    private String itemNo;

    @Schema(title = "产品单价")
    private BigDecimal unitPrice;

    @Schema(title = "操作费")
    private BigDecimal operationFee;

    @Schema(title = "尾程派送费")
    private BigDecimal finalDeliveryFee;

    @Schema(title = "")
    private BigDecimal fob;

    @Schema(title = "建议零售价")
    private BigDecimal retailPrice;

    @Schema(title = "代发价")
    private BigDecimal dropShippingPrice;

    @Schema(title = "自提价")
    private BigDecimal pickUpPrice;

    @Schema(title = "代发价差")
    private String dropShippingPriceDifference;

    @Schema(title = "自提价差")
    private String pickUpPriceDifference;

    @Schema(title = "已绑定数量")
    private Integer boundNum;

    private BoundApplicableProductVo respBoundBody;

    public ApplicableProductSkuVo() {
    }

    public ApplicableProductSkuVo(String itemNo, BigDecimal unitPrice, BigDecimal operationFee, BigDecimal finalDeliveryFee, BigDecimal fob, BigDecimal retailPrice) {
        this.itemNo = itemNo;
        this.unitPrice = unitPrice;
        this.operationFee = operationFee;
        this.finalDeliveryFee = finalDeliveryFee;
        this.fob = fob;
        this.retailPrice = retailPrice;
    }

    public ApplicableProductSkuVo(String itemNo, BigDecimal unitPrice, BigDecimal operationFee, BigDecimal finalDeliveryFee, BigDecimal fob, BigDecimal retailPrice, BigDecimal dropShippingPrice, BigDecimal pickUpPrice) {
        this.itemNo = itemNo;
        this.unitPrice = unitPrice;
        this.operationFee = operationFee;
        this.finalDeliveryFee = finalDeliveryFee;
        this.fob = fob;
        this.retailPrice = retailPrice;
        this.dropShippingPrice = dropShippingPrice;
        this.pickUpPrice = pickUpPrice;
    }
}
