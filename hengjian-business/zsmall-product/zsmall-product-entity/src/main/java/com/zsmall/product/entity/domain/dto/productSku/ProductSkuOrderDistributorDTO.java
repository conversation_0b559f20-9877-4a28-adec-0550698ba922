package com.zsmall.product.entity.domain.dto.productSku;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单合计数 dto
 *
 * <AUTHOR>
 * @date 2022/9/21 15:33
 */
@Data
@ExcelIgnoreUnannotated
public class ProductSkuOrderDistributorDTO {

    /**
     * 分销商租户编码
     */
    @ExcelProperty(value = "分销商租户编码", order = 1)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.tenantId")
    private String tenantId;

    /**
     * 公司名字
     */
    @ExcelProperty(value = "公司名字", order = 2)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.companyName")
    private String companyName;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱", order = 3)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.email")
    private String email;

    /**
     * 公司电话
     */
    @ExcelProperty(value = "公司电话", order = 4)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.phoneNumber")
    private String phoneNumber;
    /**
     * 注册时间
     */
    @ExcelProperty(value = "注册时间", order = 5)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.createTime")
    private String createTime;

    /**
     * 主要销售产品 TODO
     */
    /*@ExcelProperty(value = "主要销售产品", order = 1)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.mainProducts")
    private String mainProducts;*/

    /**
     * 铺货的SKU总数
     */
    @ExcelProperty(value = "铺货的SKU总数", order = 6)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.skuDistributionNum")
    private Integer skuDistributionNum;

    /**
     * 有出单的SKU总数
     */
    @ExcelProperty(value = "有出单的SKU总数", order = 7)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.skuOrderNum")
    private Integer skuOrderNum;

    /**
     * 销售件数总数
     */
    @ExcelProperty(value = "销售件数总数", order = 9)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.salesNum")
    private Integer salesNum;

    /**
     * 订单总数
     */
    @ExcelProperty(value = "订单总数", order = 8)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.orderTotalNum")
    private Integer orderTotalNum;

    /**
     * 出单总金额
     */
    @ExcelProperty(value = "出单总金额", order = 10)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.orderTotalPrice")
    private BigDecimal orderTotalPrice;

    /**
     * 售后订单总数
     */
    @ExcelProperty(value = "售后订单总数", order = 11)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.restockTotalNum")
    private Integer restockTotalNum;

    /**
     * 下载数
     */
    @ExcelProperty(value = "下载数", order = 13)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.downloadedByDis")
    private Integer downloadedByDis;

    /**
     * 已收藏产品的SKU
     */
    @ExcelProperty(value = "已收藏产品数", order = 12)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.dropNumByDis")
    private Integer dropNumByDis;

    /**
     * 已点击的产品SKU
     */
    /*@ExcelProperty(value = "已点击的产品SKU", order = 1)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.distributor.clickedSku")
    private Integer clickedSku;*/


}
