package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 商品SKU对象 product_sku
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_sku")
public class ProductSku extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品SPU表主键
     */
    private Long productId;

    /**
     * 商品SPU唯一编号
     */
    private String productCode;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 最小库存单位（Sku）
     */
    private String sku;

    /**
     * 第三方系统库存单位
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String erpSku;

    /**
     * 商品统一代码（UPC）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String upc;

    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;

    /**
     * Sku库存总数
     */
    private Integer stockTotal;

    /**
     * 库存管理方（区分自有仓库管理还是第三方平台管理）
     */
    private StockManagerEnum stockManager;

    /**
     * Sku销售状态：OnShelf-在售；OffShelf-停售；ForcedOffShelf-未审核
     */
    private ShelfStateEnum shelfState;

    /**
     * Sku审核状态
     */
    private ProductVerifyStateEnum verifyState;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    @TableField(exist = false)
    private Integer stockChange;
    @TableField(exist = false)
    private Boolean stockChanged;

    @TableField(exist = false)
    private ProductSkuDetail skuDetail;
    @TableField(exist = false)
    private List<ProductSkuAttachment> skuAttachmentList;


}
