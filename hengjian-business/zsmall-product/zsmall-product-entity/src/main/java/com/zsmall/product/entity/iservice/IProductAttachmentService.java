package com.zsmall.product.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.product.entity.domain.ProductAttachment;
import com.zsmall.product.entity.domain.bo.ProductAttachmentBo;
import com.zsmall.product.entity.domain.vo.ProductAttachmentVo;
import com.zsmall.product.entity.mapper.ProductAttachmentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 商品SPU附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductAttachmentService extends ServiceImpl<ProductAttachmentMapper, ProductAttachment> {

    private final ProductAttachmentMapper baseMapper;

    /**
     * 查询商品SPU附件
     */
    public ProductAttachmentVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品SPU附件列表
     */
    public TableDataInfo<ProductAttachmentVo> queryPageList(ProductAttachmentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductAttachment> lqw = buildQueryWrapper(bo);
        Page<ProductAttachmentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品SPU附件列表
     */
    public List<ProductAttachmentVo> queryList(ProductAttachmentBo bo) {
        LambdaQueryWrapper<ProductAttachment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductAttachment> buildQueryWrapper(ProductAttachmentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOssId() != null, ProductAttachment::getOssId, bo.getOssId());
        lqw.eq(bo.getProductId() != null, ProductAttachment::getProductId, bo.getProductId());
        lqw.like(StringUtils.isNotBlank(bo.getAttachmentName()), ProductAttachment::getAttachmentName, bo.getAttachmentName());
        lqw.like(StringUtils.isNotBlank(bo.getAttachmentOriginalName()), ProductAttachment::getAttachmentOriginalName, bo.getAttachmentOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentSuffix()), ProductAttachment::getAttachmentSuffix, bo.getAttachmentSuffix());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentSavePath()), ProductAttachment::getAttachmentSavePath, bo.getAttachmentSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentShowUrl()), ProductAttachment::getAttachmentShowUrl, bo.getAttachmentShowUrl());
        lqw.eq(bo.getAttachmentSort() != null, ProductAttachment::getAttachmentSort, bo.getAttachmentSort());
        lqw.eq(StringUtils.isNotBlank(bo.getAttachmentType()), ProductAttachment::getAttachmentType, bo.getAttachmentType());
        return lqw;
    }

    /**
     * 新增商品SPU附件
     */
    public Boolean insertByBo(ProductAttachmentBo bo) {
        ProductAttachment add = MapstructUtils.convert(bo, ProductAttachment.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        return flag;
    }

    /**
     * 新增商品SPU附件
     *
     * @param entity
     * @return
     */
    public Boolean insert(ProductAttachment entity) {
        log.info("进入【新增商品SPU附件】 entity = {}", JSONUtil.toJsonStr(entity));
        return baseMapper.insert(entity) > 0;
    }

    /**
     * 批量新增商品SPU附件
     *
     * @param entityList
     * @return
     */
    public Boolean insertOrUpdateBatch(List<ProductAttachment> entityList) {
        log.info("进入【批量新增商品SPU附件】 entityList = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.insertOrUpdateBatch(entityList);
    }

    /**
     * 新增/更新商品SPU附件
     *
     * @param entity
     * @return
     */
    public Boolean insertOrUpdate(ProductAttachment entity) {
        log.info("进入【新增/更新商品SPU附件】 entity = {}", JSONUtil.toJsonStr(entity));
        return baseMapper.insertOrUpdate(entity);
    }

    /**
     * 修改商品SPU附件
     */
    public Boolean updateByBo(ProductAttachmentBo bo) {
        ProductAttachment update = MapstructUtils.convert(bo, ProductAttachment.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 根据实体类批量删除
     *
     * @param entityList
     * @return
     */
    public Boolean deleteByEntityList(Collection<ProductAttachment> entityList) {
        log.info("进入【根据实体类批量删除】 entityList = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.deleteBatchIds(entityList) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductAttachment entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SPU附件
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    public List<ProductAttachment> queryByProductIdOrderBySortAsc(Long productId, AttachmentTypeEnum attachmentType) {
        log.info("【通过productId获取product图片集合（按排序升序）】 productSkuId = {}", productId);
        LambdaQueryWrapper<ProductAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductAttachment::getProductId, productId);
        if (attachmentType != null) {
            lqw.eq(ProductAttachment::getAttachmentType, attachmentType.name());
        }
        lqw.orderByAsc(ProductAttachment::getAttachmentSort);
        return baseMapper.selectList(lqw);
    }

    public ProductAttachment getFirstImgByProductId(Long productId) {
        log.info("【根据productId获取主图】 productSkuId = {}", productId);
        List<ProductAttachment> productAttachmentVos = this.queryByProductIdOrderBySortAsc(productId, AttachmentTypeEnum.Image);
        return CollUtil.isNotEmpty(productAttachmentVos) ? productAttachmentVos.get(0) : null;
    }

    public ProductAttachment getFirstVideoByProductId(Long productId) {
        log.info("【根据productId获取首视频】 productSkuId = {}", productId);
        List<ProductAttachment> productAttachmentVos = this.queryByProductIdOrderBySortAsc(productId, AttachmentTypeEnum.Video);
        return CollUtil.isNotEmpty(productAttachmentVos) ? productAttachmentVos.get(0) : null;
    }

    public ProductAttachment getByProductId(Long productId) {
        log.info("进入【根据商品id查询排序在最先的图片】方法, productId = {}", productId);
        LambdaQueryWrapper<ProductAttachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductAttachment::getProductId, productId)
            .eq(ProductAttachment::getDelFlag, 0)
            .eq(ProductAttachment::getAttachmentType, AttachmentTypeEnum.Image.name());
        List<ProductAttachment> list = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().min(Comparator.comparing(ProductAttachment::getAttachmentSort)).get();
    }

    /**
     * 根据商品id和附件类型查询附件
     *
     * @param productId
     * @param attachmentTypeEnum
     * @return
     */
    public List<ProductAttachment> queryByProductIdAndType(Long productId, AttachmentTypeEnum attachmentTypeEnum) {
        log.info("进入【根据商品id和附件类型查询附件】方法, productId = {},  attachmentTypeEnum = {}", productId, attachmentTypeEnum);
        LambdaQueryWrapper<ProductAttachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductAttachment::getProductId, productId)
            .eq(ProductAttachment::getAttachmentType, AttachmentTypeEnum.File)
            .orderByDesc(ProductAttachment::getCreateTime);
        List<ProductAttachment> list = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list;
    }

    /**
     * 根据商品主键删除
     * @param productId
     * @return
     */
    public Boolean deleteByProductId(Long productId) {
        log.info("进入【根据商品主键删除】 productId = {}", productId);
        LambdaQueryWrapper<ProductAttachment> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductAttachment::getProductId, productId);
        return baseMapper.delete(lqw) > 0;
    }


    /**
     * 功能描述：清除并复制源租户id
     *
     * @param productIdMapping 产品id映射
     * <AUTHOR>
     * @date 2025/08/25
     */
    public void cleanAndCopySourceTenantId(Map<Long, Long> productIdMapping) {
        ArrayList<ProductAttachment> productAttachments = new ArrayList<>();
        List<Long> keyList = new ArrayList<>(productIdMapping.keySet());
        LambdaQueryWrapper<ProductAttachment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductAttachment::getProductId,keyList);
        lambdaQueryWrapper.eq(ProductAttachment::getDelFlag,0);
        List<ProductAttachment> categoryRelations = TenantHelper.ignore(() -> baseMapper.selectList(lambdaQueryWrapper));
        // categoryRelations根据productIdMapping 将集合内符合productId =key的值替换成productIdMapping的value
        for (ProductAttachment relation : categoryRelations) {
            ProductAttachment productAttachment = new ProductAttachment();

            BeanUtil.copyProperties(relation,productAttachment);
            Long oldId = relation.getProductId();
            if (productIdMapping.containsKey(oldId)) {
                productAttachment.setProductId(productIdMapping.get(oldId));
                productAttachment.setId(null);
            }else {
                throw new RuntimeException("商品数据迁移异常");
            }
            productAttachments.add(productAttachment);
        }
        saveBatch(productAttachments);
    }
}
