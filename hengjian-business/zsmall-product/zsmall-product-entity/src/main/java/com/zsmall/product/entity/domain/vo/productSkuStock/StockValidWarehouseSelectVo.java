package com.zsmall.product.entity.domain.vo.productSkuStock;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 响应体：库存可用仓库
 *
 * <AUTHOR>
 * @date 2023/7/10
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class StockValidWarehouseSelectVo {

    /**
     *仓库系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 是否禁用
     */
    private Boolean disabled;

}
