package com.zsmall.product.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.ProductSkuDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商品SKU详情业务对象 product_sku_detail
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuDetail.class, reverseConvertGenerate = false)
public class ProductSkuDetailBo extends SortEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品SKU表主键
     */
    @NotNull(message = "商品SKU表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productSkuId;

    /**
     * 长
     */
    @NotNull(message = "长不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal length;

    /**
     * 宽
     */
    @NotNull(message = "宽不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal width;

    /**
     * 高
     */
    @NotNull(message = "高不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal height;

    /**
     * 长度单位
     */
    @NotBlank(message = "长度单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String lengthUnit;

    /**
     * 重量
     */
    @NotNull(message = "重量不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal weight;

    /**
     * 重量单位
     */
    @NotBlank(message = "重量单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String weightUnit;

    /**
     * 打包长
     */
    @NotNull(message = "打包长不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal packLength;

    /**
     * 打包宽
     */
    @NotNull(message = "打包宽不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal packWidth;

    /**
     * 打包高
     */
    @NotNull(message = "打包高不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal packHeight;

    /**
     * 打包长度单位
     */
    @NotBlank(message = "打包长度单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String packLengthUnit;

    /**
     * 打包重量
     */
    @NotNull(message = "打包重量不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal packWeight;

    /**
     * 打包重量单位
     */
    @NotBlank(message = "打包重量单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String packWeightUnit;

    /**
     * 打包尺寸重量与商品尺寸重量相同：0-否，1-是
     */
    @NotNull(message = "打包尺寸重量与商品尺寸重量相同：0-否，1-是不能为空", groups = { AddGroup.class, EditGroup.class })
    private Boolean samePacking;

    /**
     * 处理时效（天）
     */
    @NotNull(message = "处理时效（天）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal processingTime;

    /**
     * 运输方式
     */
    @NotBlank(message = "运输方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transportMethod;

    /**
     * SKU描述
     */
    @NotBlank(message = "SKU描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;


}
