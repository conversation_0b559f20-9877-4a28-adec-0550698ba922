package com.zsmall.product.entity.domain.bo.productSkuStock;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 请求体-库存状态切换
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求体-库存状态切换")
public class StockStateSwitchBo {

    /**
     * 库存编号
     */
    @NotBlank(message = "{zsmall.validated.apiRequired}")
    private String stockCode;

    /**
     * 库存状态（0-停用，1-启用）
     */
    @NotNull(message = "{zsmall.validated.apiRequired}")
    private Integer stockState;

}
