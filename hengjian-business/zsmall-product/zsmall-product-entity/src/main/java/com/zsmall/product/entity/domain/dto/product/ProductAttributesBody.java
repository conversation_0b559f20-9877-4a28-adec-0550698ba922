package com.zsmall.product.entity.domain.dto.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-商品属性信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-商品属性信息")
public class ProductAttributesBody {

    @Schema(title = "categoryId")
    private Long categoryId;

    @Schema(title = "attributesId")
    private Long attributesId;

    @Schema(title = "categoryAttributesId")
    private Long categoryAttributesId;

    @Schema(title = "属性名称")
    private String attributesName;

    @Schema(title = "属性值集合")
    private List<String> attributesValueList;

//  @Schema(title = "备注")
//  private String remark;

    @Schema(title = "自定义字段")
    private Boolean customizeField;

//  @Schema(title = "sku属性")
//  private Boolean isSkuAttributes;

    @Schema(title = "requiredType")
    private String requiredType;

//  @Schema(title = "有效状态")
//  private String statusType;

    @Schema(title = "属性值")
    private String attributesValue;

    @Schema(title = "属性作用域类型")
    private String scopeType;

}
