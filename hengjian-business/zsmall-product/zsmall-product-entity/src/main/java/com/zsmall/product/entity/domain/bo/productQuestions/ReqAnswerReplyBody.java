package com.zsmall.product.entity.domain.bo.productQuestions;

/**
 * <AUTHOR>
 * @date 2022/1/8
 **/

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "新增回复请求参数")
public class ReqAnswerReplyBody {

    @Schema(title = "问题id")
    private Long questionId;

    @Schema(title = "回复内容")
    private String answer;

}
