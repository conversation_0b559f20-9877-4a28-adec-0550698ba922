package com.zsmall.product.entity.domain.dto.productSku;

import com.zsmall.common.domain.dto.ChangeFieldDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品Sku审核DTO
 *
 * <AUTHOR>
 */
@Data
public class ProductSkuReviewDTO {
    private Long siteId;
    private String productSkuCode;
    private String reviewType;
    private Long productSkuPriceId;

    private List<ChangeFieldDTO> changeFields = new ArrayList<>();

    public void addField(String fieldName, String fieldValueBefore, String fieldValueAfter) {
        this.changeFields.add(new ChangeFieldDTO(fieldName, fieldValueBefore, fieldValueAfter));
    }

}
