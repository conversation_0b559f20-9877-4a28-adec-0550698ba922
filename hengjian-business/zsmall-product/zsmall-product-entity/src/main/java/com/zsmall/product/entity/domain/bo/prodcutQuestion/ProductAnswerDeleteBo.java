package com.zsmall.product.entity.domain.bo.prodcutQuestion;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 删除回复
 */
@Data
@EqualsAndHashCode
public class ProductAnswerDeleteBo {

    /**
     * 回复编码
     */
    @NotBlank(message = "{zsmall.productQA.answerCodeIsBlank}")
    private String answerCode;

    /**
     * 类型：Answer/Question
     */
    @NotBlank(message = "{zsmall.productQA.answerTypeIsBlank}")
    private String type;

}
