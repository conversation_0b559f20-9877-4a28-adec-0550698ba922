package com.zsmall.product.entity.domain.vo.product;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/1/6 17:57
 */
@Data
@ExcelIgnoreUnannotated
@NoArgsConstructor
@AllArgsConstructor
public class ProductPriceExportDto {

    @ExcelProperty(value = "SKU ID")
    private String itemNo;

    @ExcelProperty(value = "SKU")
    private String sku;

    @ExcelProperty(value = "支持的物流方式")
    private String supportedLogistics;

    @ExcelProperty(value = "站点/币种")
    private String countryCodeAndCurrencyCode;

    @ExcelProperty(value = "产品单价")
    private String unitPrice;

    @ExcelProperty(value = "操作费")
    private String operationFee;

    @ExcelProperty(value = "尾程派送费")
    private String finalDeliveryFee;

    @ExcelProperty(value = "SKU上下架状态")
    private String skuShelfState;

    private Long siteId;
}
