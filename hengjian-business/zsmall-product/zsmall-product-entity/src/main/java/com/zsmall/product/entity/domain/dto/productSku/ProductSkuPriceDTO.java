package com.zsmall.product.entity.domain.dto.productSku;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 商品价格DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ProductSkuPriceDTO {

    /**
     * 商品sku价格Id
     */
    private Long productSkuPriceId;

    private BigDecimal msrp;

    private BigDecimal dropShippingPrice;

    private BigDecimal pickUpPrice;

    private BigDecimal unitPrice;

    private BigDecimal operationFee;

    private BigDecimal finalDeliveryFee;

    private BigDecimal fob;

    private Long ruleId;

    private Long productSkuId;
    private String productSkuCode;

}
