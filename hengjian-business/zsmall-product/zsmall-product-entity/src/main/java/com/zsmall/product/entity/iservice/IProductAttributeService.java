package com.zsmall.product.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductAttribute;
import com.zsmall.product.entity.domain.ProductGlobalAttribute;
import com.zsmall.product.entity.domain.bo.ProductAttributeBo;
import com.zsmall.product.entity.domain.vo.ProductAttributeVo;
import com.zsmall.product.entity.mapper.ProductAttributeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品SPU属性Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IProductAttributeService extends ServiceImpl<ProductAttributeMapper, ProductAttribute> {

    private final ProductAttributeMapper baseMapper;

    /**
     * 查询商品SPU属性
     */
    public ProductAttributeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品SPU属性（实体类）
     *
     * @param id
     * @return
     */
    public ProductAttribute queryEntityById(Long id) {
        log.info("进入【查询商品SPU属性（实体类）】 id = {}", id);
        return baseMapper.selectById(id);
    }

    /**
     * 查询商品SPU属性列表
     */
    public TableDataInfo<ProductAttributeVo> queryPageList(ProductAttributeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductAttribute> lqw = buildQueryWrapper(bo);
        Page<ProductAttributeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品SPU属性列表
     */
    public List<ProductAttributeVo> queryList(ProductAttributeBo bo) {
        LambdaQueryWrapper<ProductAttribute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询商品SPU属性（最多一个结果）
     *
     * @param bo
     * @return
     */
    public ProductAttribute queryOneByBo(ProductAttributeBo bo) {
        log.info("进入【查询商品SPU属性（最多一个结果）】 bo = {}", JSONUtil.toJsonStr(bo));
        return baseMapper.selectOne(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ProductAttribute> buildQueryWrapper(ProductAttributeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductId() != null, ProductAttribute::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeType()), ProductAttribute::getAttributeType, bo.getAttributeType());
        lqw.like(StringUtils.isNotBlank(bo.getAttributeName()), ProductAttribute::getAttributeName, bo.getAttributeName());
        // lqw.eq(StringUtils.isNotBlank(bo.getAttributeValue()), ProductAttribute::getAttributeValue, bo.getAttributeValue());
        lqw.eq(bo.getAttributeSort() != null, ProductAttribute::getAttributeSort, bo.getAttributeSort());
        lqw.eq(bo.getAttributeSourceId() != null, ProductAttribute::getAttributeSourceId, bo.getAttributeSourceId());
        return lqw;
    }

    /**
     * 新增商品SPU属性
     */
    public Boolean insertByBo(ProductAttributeBo bo) {
        ProductAttribute add = MapstructUtils.convert(bo, ProductAttribute.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品SPU属性
     */
    public Boolean updateByBo(ProductAttributeBo bo) {
        ProductAttribute update = MapstructUtils.convert(bo, ProductAttribute.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductAttribute entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SPU属性
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据商品主键和属性类型查询
     *
     * @param productId
     * @param attachmentTypeEnum
     * @return
     */
    public List<ProductAttribute> queryByProductIdAndAttributeType(Long productId, AttributeTypeEnum attachmentTypeEnum) {
        log.info("进入【根据商品主键和属性类型查询】 productId = {}, attachmentTypeEnum = {}", productId, attachmentTypeEnum);
        LambdaQueryWrapper<ProductAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductAttribute::getProductId, productId).eq(ProductAttribute::getAttributeType, attachmentTypeEnum)
            .orderByAsc(ProductAttribute::getAttributeSort);
        return baseMapper.selectList(lqw);
    }

    @InMethodLog("根据商品主键和属性类型查询")
    public List<ProductAttribute> queryByProductId(Long productId) {
        LambdaQueryWrapper<ProductAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductAttribute::getProductId, productId)
            .orderByAsc(ProductAttribute::getAttributeSort);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据商品主键查询所有已存在的属性主键
     * @param productId
     * @return
     */
    public List<Long> queryIdsByProductId(Long productId) {
        log.info("进入【根据商品主键查询所有已存在的属性主键】 productId = {}", productId);
        return baseMapper.queryIdsByProductId(productId);
    }

    /**
     * 批量保存
     * @param entityList
     * @return
     */
    public Boolean insertOrUpdateBatch(Collection<ProductAttribute> entityList) {
        log.info("进入【批量保存】 entityList = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.insertOrUpdateBatch(entityList);
    }

    /**
     * 根据商品主键删除
     * @param productId
     * @return
     */
    public Boolean deleteByProductId(Long productId) {
        log.info("进入【根据商品主键删除】 productId = {}", productId);
        LambdaQueryWrapper<ProductAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductAttribute::getProductId, productId);
        return baseMapper.delete(lqw) > 0;
    }

    @InMethodLog("是否存在必填的属性")
    public Boolean existRequiredAttribute(Long productId) {
        return baseMapper.existRequiredAttribute(productId);
    }
    @InMethodLog("商品数据源租户")
    public List<ProductAttribute> queryListByProductIds(List<String> productIds) {
        LambdaQueryWrapper<ProductAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductAttribute::getProductId,productIds);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 功能描述：清除并复制源租户id
     *
     * @param productCleanedAndCopyMap  产品清洗并复制地图
     * @param productGlobalAttributeMap 产品全局属性图
     * <AUTHOR>
     * @date 2025/08/21
     */
    public void cleanAndCopySourceTenantId(HashMap<String, List<Product>> productCleanedAndCopyMap,
                                           HashMap<String, List<ProductGlobalAttribute>> productGlobalAttributeMap,HashMap<String, String> sourceTargetCodeMap,Map<Long, Long> productIdMapping) {
        IProductAttributeService proxy = (IProductAttributeService) AopContext.currentProxy();
        List<Product> sourceTenantIdProducts = productCleanedAndCopyMap.get("sourceTenantIdProduct");
        List<Product> targetTenantIdProducts = productCleanedAndCopyMap.get("targetTenantIdProduct");
        // 创建ID映射关系Map（源ID -> 目标ID）

        // sourceTenantIdProducts 和 targetTenantIdProducts 根据 sourceTargetCodeMap的映射关系,生成 Product.id的映射关系
        Map<String, Long> targetProductCodeToIdMap = new HashMap<>();
        for (Product targetProduct : targetTenantIdProducts) {
            targetProductCodeToIdMap.put(targetProduct.getProductCode(), targetProduct.getId());
        }

        // 根据映射关系生成ID映射
        for (Product sourceProduct : sourceTenantIdProducts) {
            // 获取当前源产品对应的目标产品代码
            String targetCode = sourceTargetCodeMap.get(sourceProduct.getProductCode());

            if (targetCode != null) {
                // 查找目标产品ID并建立映射
                Long targetId = targetProductCodeToIdMap.get(targetCode);
                if (targetId != null) {
                    productIdMapping.put(sourceProduct.getId(), targetId);
                } else {
                    // 处理目标产品不存在的情况（根据需求记录日志或抛出异常）
                   log.error("目标产品不存在，code: " + targetCode);
                }
            } else {
                // 处理映射关系不存在的情况
                log.error("缺少代码映射关系，源产品code: " + sourceProduct.getProductCode());
            }
        }

        List<Long> sourceProductIds = sourceTenantIdProducts.stream().map(Product::getId).collect(Collectors.toList());
        List<ProductAttribute> sourceTenantIdAttributes = cleanAndCopySourceTenantIdAttribute(sourceProductIds);
        List<ProductAttribute> targetTenantIdAttributes = new ArrayList<>();

//        List<ProductGlobalAttribute> sourceTenantIdProductGlobalAttribute = productGlobalAttributeMap.get("sourceTenantIdProductGlobalAttribute");
        List<ProductGlobalAttribute> targetTenantIdProductGlobalAttribute = productGlobalAttributeMap.get("targetTenantIdProductGlobalAttribute");
        Map<Long, ProductGlobalAttribute> globalAttributeMap = targetTenantIdProductGlobalAttribute.stream()
                                                                                                   .collect(Collectors.toMap(ProductGlobalAttribute::getMigrationId, Function.identity()));

        for (ProductAttribute sourceTenantIdAttribute : sourceTenantIdAttributes) {
            Long sourceProductId = sourceTenantIdAttribute.getProductId();
            Long attributeSourceId = sourceTenantIdAttribute.getAttributeSourceId();

            ProductGlobalAttribute productGlobalAttribute = globalAttributeMap.get(attributeSourceId);
            ProductAttribute targetProductAttribute = new ProductAttribute();

            BeanUtil.copyProperties(sourceTenantIdAttribute,targetProductAttribute);

            targetProductAttribute.setId(null);
            Long targetProductId = productIdMapping.get(sourceProductId);
            targetProductAttribute.setProductId(targetProductId);
            if(ObjectUtil.isEmpty(attributeSourceId)||ObjectUtil.isEmpty(ObjectUtil.isEmpty(productGlobalAttribute))){
                targetProductAttribute.setAttributeSourceId(null);
            }else {
                targetProductAttribute.setAttributeSourceId(productGlobalAttribute.getId());
            }

            targetTenantIdAttributes.add(targetProductAttribute);
        }
        proxy.saveBatch(targetTenantIdAttributes);
        // 做一个清洗 targetTenantIdProducts通过sku相同来做key映射



        // p.id = pa.p_id
    }

    /**
     * 功能描述：清理并复制源租户id属性
     *
     * @param sourceProductIds 源产品ID
     * @return {@link List }<{@link ProductAttribute }>
     * <AUTHOR>
     * @date 2025/08/21
     */
    private List<ProductAttribute> cleanAndCopySourceTenantIdAttribute(List<Long> sourceProductIds) {
        LambdaQueryWrapper<ProductAttribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductAttribute::getProductId,sourceProductIds);
        wrapper.eq(ProductAttribute::getDelFlag,0);

        return TenantHelper.ignore(()->baseMapper.selectList(wrapper));
    }
}
