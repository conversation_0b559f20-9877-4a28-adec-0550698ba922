package com.zsmall.product.entity.domain.vo;

import cn.hutool.json.JSONArray;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.ProductAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品SPU属性视图对象 product_attribute
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductAttribute.class)
public class ProductAttributeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品SPU表主键
     */
    @ExcelProperty(value = "商品SPU表主键")
    private Long productId;

    /**
     * 属性类型
     */
    @ExcelProperty(value = "属性类型")
    private String attributeType;

    /**
     * 属性名
     */
    @ExcelProperty(value = "属性名")
    private String attributeName;

    /**
     * 属性值
     */
    @ExcelProperty(value = "属性值")
    private JSONArray attributeValues;

    /**
     * 属性排序
     */
    @ExcelProperty(value = "属性排序")
    private Integer attributeSort;

    /**
     * 属性来源id
     */
    @ExcelProperty(value = "属性来源id")
    private Long attributeSourceId;


}
