package com.zsmall.product.entity.domain.dto.product;

import com.zsmall.common.domain.vo.AttachmentDto;
import com.zsmall.common.domain.vo.KeyValueBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 通用参数-marketplace商品sku信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-marketplace商品sku信息")
public class MarketplaceProductSkuBody {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "skuCode")
    private String skuCode;

    @Schema(title = "itemNo")
    private String itemNo;

    @Schema(title = "商品统一代码")
    private String upc;

    @Schema(title = "规格属性值，格式：分类属性id-属性值;分类属性id-属性值;")
    private String specValue;

    @Schema(title = "sku库存数量")
    private Integer stock;

    @Schema(title = "价格（包含运费）")
    private BigDecimal price;

    @Schema(title = "有效状态")
    private String statusType;

    @Schema(title = "重量")
    private BigDecimal weight;

    @Schema(title = "长")
    private BigDecimal length;

    @Schema(title = "宽")
    private BigDecimal width;

    @Schema(title = "高")
    private BigDecimal height;

    @Schema(title = "重量单位")
    private String weightUnit;

    @Schema(title = "长度单位")
    private String lengthUnit;

    @Schema(title = "打包重量")
    private BigDecimal packWeight;

    @Schema(title = "打包长")
    private BigDecimal packLength;

    @Schema(title = "打包宽")
    private BigDecimal packWidth;

    @Schema(title = "打包高")
    private BigDecimal packHeight;

    @Schema(title = "打包重量单位")
    private String packWeightUnit;

    @Schema(title = "打包长度单位")
    private String packLengthUnit;

    @Schema(title = "打包尺寸重量和商品尺寸重量相同")
    private Boolean samePacking = true;

    @Schema(title = "运费")
    private BigDecimal shippingCost;

    @Schema(title = "sku规格集合")
    private List<KeyValueBody> skuAttributesList;

    @Schema(title = "代发价")
    private BigDecimal dropShippingPrice;

    @Schema(title = "建议零售价（二期新增）")
    private BigDecimal msrp;

    @Schema(title = "物流模板编号（二期新增）")
    private String logisticsTemplateNo;

    @Schema(title = "自提价（二期新增）")
    private BigDecimal pickUpPrice;

    @Schema(title = "发货国家")
    private String shippingFrom;

    @Schema(title = "运输方式")
    private String transportMethod;

    @Schema(title = "SKU附件（二期新增）")
    private List<AttachmentDto> attachmentList;

    @Schema(title = "是否允许销售（若SKU开启了全渠道管控，只有指定的用户才能销售）")
    private boolean allowSale = true;

    @Schema(title = "订单处理时效")
    private String processingTime;
}
