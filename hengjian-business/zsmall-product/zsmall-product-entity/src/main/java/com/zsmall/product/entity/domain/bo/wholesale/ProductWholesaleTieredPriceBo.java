package com.zsmall.product.entity.domain.bo.wholesale;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductWholesaleTieredPrice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 国外现货批发商品阶梯价主业务对象 product_wholesale_tiered_price
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductWholesaleTieredPrice.class, reverseConvertGenerate = false)
public class ProductWholesaleTieredPriceBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品主键
     */
    private Long productId;

    /**
     * 阶梯价-最小数量（包含）
     */
    @NotNull(message = "阶梯价-最小数量（包含）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer minimumQuantity;

    /**
     * 预估操作费
     */
    @NotNull(message = "预估操作费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal estimatedOperationFee;

    /**
     * 预估运费
     */
    @NotNull(message = "预估运费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal estimatedShippingFee;

    /**
     * 预估处理时间（天）
     */
    @NotNull(message = "预估处理时间（天）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer estimatedHandleTime;


}
