package com.zsmall.product.entity.domain.vo.category;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductCategory;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 商品分类视图对象 product_category
 *
 * <AUTHOR> Li
 * @date 2023-05-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductCategory.class)
public class ProductCategorySelectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 父级分类主键
     */
    @ExcelProperty(value = "父级分类主键")
    private Long parentId;

    /**
     * 分类名称（主名称）
     */
    @ExcelProperty(value = "分类名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "主=名称")
    private String categoryName;

    /**
     * 分类其他语种名称（格式：key-语种，value-对应语种分类名）
     */
    @ExcelProperty(value = "分类其他语种名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "格=式：key-语种，value-对应语种分类名")
    private JSONObject categoryOtherName;

    private boolean leaf = false;

}
