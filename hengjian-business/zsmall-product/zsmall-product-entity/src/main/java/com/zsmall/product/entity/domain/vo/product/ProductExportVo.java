package com.zsmall.product.entity.domain.vo.product;

import cn.hutool.json.JSONArray;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/12 11:51
 */
@Data
@ExcelIgnoreUnannotated
@NoArgsConstructor
@AllArgsConstructor
public class ProductExportVo {

    /**
     * 商品名
     */
//    @ExcelProperty(value = "商品名")
    private String productName;

    /**
     *商品编号
     */
//    @ExcelProperty(value = "商品编号")
    private String productCode;


    /**
     * Sku唯一编号ItemNo.
     */
//    @ExcelProperty(value = "Item.No")
    private String productSkuCode;

//    @ExcelProperty(value = "参考sku")
    private String sku;
    /**
     * 货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
//    @ExcelProperty(value = "sku销售状况")
    private String skuShelfState;
    /**
     * 规格值名称
     */
//    @ExcelProperty(value = "规格")
    private String specValName;
    /**
     * 库存数量
     */
//    @ExcelProperty(value = "库存数量")
    private Integer stockTotal;
    /**
     * 自提价
     */
//    @ExcelProperty(value = "自提价")
    private String pickUpPrice;

    /**
     * 代发价格
     */
//    @ExcelProperty(value = "代发价格")
    private String dropShippingPrice;

//    @ExcelProperty(value = "已售")
    private Integer stockSold = 0;
    /**
     * 审核状态
     */
//    @ExcelProperty(value = "审核状态")
    private String verifyState;

//    @ExcelIgnore
    private List<LevelPriceVo> levelPriceList;

    public List<LevelPriceVo> pushLevelPriceList(List<LevelPriceVo> levelPriceList) {
        this.levelPriceList = levelPriceList;
        return this.levelPriceList;
    }
}
