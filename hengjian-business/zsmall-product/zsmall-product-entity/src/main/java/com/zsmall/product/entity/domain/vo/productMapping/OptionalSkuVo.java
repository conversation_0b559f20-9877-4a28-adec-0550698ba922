package com.zsmall.product.entity.domain.vo.productMapping;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 可选SKU
 *
 * <AUTHOR>
 * @date 2023/8/24
 */
@Data
public class OptionalSkuVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Boolean check = false;

    private String imageShowUrl;

    private String productSkuCode;

    private String specValName;

    private BigDecimal basePrice;

    private BigDecimal originPrice;

    private BigDecimal finalPrice;

    private BigDecimal platformPickUpPrice;

    private BigDecimal platformDropShippingPrice;

    private List<MappingAvailableActivityVo> optionalActivityList;

}
