package com.zsmall.product.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.TaskStockSync;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 任务-库存同步业务对象 task_stock_sync
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TaskStockSync.class, reverseConvertGenerate = false)
public class TaskStockSyncBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 来源sku_id
     */
    @NotNull(message = "来源sku_id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productSkuId;

    /**
     * 任务状态
     */
    @NotBlank(message = "任务状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskState;

    /**
     * 任务执行信息
     */
    @NotBlank(message = "任务执行信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskExecuteMessage;


}
