package com.zsmall.product.entity.domain.bo.productSkuPrice;

import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 商品sku价格计算公式关联业务对象 product_sku_price_rule_relation
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuPriceRuleRelation.class, reverseConvertGenerate = false)
public class ProductSkuPriceRuleRelationBo extends SortEntity {

    /**
     *
     */
    private Long id;

    /**
     * 商品sku价格表id
     */
    private Long productSkuPriceId;

    /**
     * 商品sku表id
     */
    private Long productSkuId;

    /**
     * 商品sku编码
     */
    private String productSkuCode;

    /**
     * 定价计算公式表id
     */
    private Long productSkuPriceRuleId;

    public ProductSkuPriceRuleRelationBo(Long productSkuPriceId, Long productSkuId, String productSkuCode, Long productSkuPriceRuleId) {
        this.productSkuPriceId = productSkuPriceId;
        this.productSkuId = productSkuId;
        this.productSkuCode = productSkuCode;
        this.productSkuPriceRuleId = productSkuPriceRuleId;
    }
}
