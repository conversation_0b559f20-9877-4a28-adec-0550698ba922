package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;


/**
 * 商品SPU属性对象 product_attribute
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_attribute", autoResultMap = true)
public class ProductAttribute extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品SPU表主键
     */
    private Long productId;

    /**
     * 属性类型
     */
    private AttributeTypeEnum attributeType;

    /**
     * 属性名
     */
    private String attributeName;

    /**
     * 属性值
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONArray attributeValues;

    /**
     * 属性排序
     */
    private Integer attributeSort;

    /**
     * 属性来源id
     */
    private Long attributeSourceId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    public void addAttributeValue(Object attributeValue) {
        if (this.attributeValues == null) {
            this.attributeValues = new JSONArray();
        }

        if (attributeValue == null) {
            return;
        }

        if (attributeValue instanceof Collection) {
            this.attributeValues.addAll(JSONUtil.parseArray(attributeValue));
        } else {
            this.attributeValues.add(attributeValue);
        }
    }

    public String getAttributeValue() {
        if (this.attributeValues == null) {
            return null;
        } else {
            return this.attributeValues.getStr(0);
        }
    }
}
