package com.zsmall.product.entity.domain.bo.userShippingCart;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 请求体-购物车下单
 *
 * <AUTHOR>
 * @date 2023/9/1
 */
@Data
public class ShippingCartOrderBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 购物车
     */
    @NotEmpty(message = ValidationMessage.API_REQUIRED)
    private List<Long> shippingCartIds;

    /**
     * 收货地址
     */
    @NotEmpty(message = ValidationMessage.API_REQUIRED)
    private Long shippingAddressId;

}
