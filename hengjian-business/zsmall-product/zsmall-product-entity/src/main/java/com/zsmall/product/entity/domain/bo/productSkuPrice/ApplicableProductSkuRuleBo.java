package com.zsmall.product.entity.domain.bo.productSkuPrice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/10/26 11:18
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-获取适用产品")
public class ApplicableProductSkuRuleBo {

    @Schema(title = "查询类型")
    private String queryType;

    @Schema(title = "查询关键字")
    private String queryValue;

    @Schema(title = "公式规则 编码")
    private String ruleCode;

    @Schema(title = "itemNo")
    private String itemNo;

    @Schema(title = "导出类型")
    private String exportType;


    @Schema(title = "供应商ID")
    private String userCode;

}
