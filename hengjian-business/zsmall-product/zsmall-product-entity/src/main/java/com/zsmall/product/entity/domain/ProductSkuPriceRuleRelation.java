package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 商品sku价格计算公式关联对象 product_sku_price_rule_relation
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_sku_price_rule_relation")
public class ProductSkuPriceRuleRelation extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品sku价格表id
     */
    private Long productSkuPriceId;

    /**
     * 商品sku表id
     */
    private Long productSkuId;

    /**
     * 商品sku编码
     */
    private String productSkuCode;

    /**
     * 定价计算公式表id
     */
    private Long productSkuPriceRuleId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    public ProductSkuPriceRuleRelation(Long productSkuPriceId, Long productSkuId, String productSkuCode, Long productSkuPriceRuleId) {
        this.productSkuPriceId = productSkuPriceId;
        this.productSkuId = productSkuId;
        this.productSkuCode = productSkuCode;
        this.productSkuPriceRuleId = productSkuPriceRuleId;
    }
}
