package com.zsmall.product.entity.domain.dto.productSku;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 获取使用产品接收实体
 *
 * <AUTHOR>
 * @date 2022/10/26 11:59
 */
@Data
@NoArgsConstructor
public class ApplicableProductSkuDTO {

    private Long productSkuId;

    private String itemNo;

    /**
     * 商品价格id
     */
    private Long productSkuPriceId;

    /**
     * 价格公式id
     */
    private Long skuPriceRuleId;

    /**
     * 产品单价
     */
    private BigDecimal unitPrice;

    /**
     * 操作费
     */
    private BigDecimal operationFee;

    /**
     * 尾程派送费
     */
    private BigDecimal finalDeliveryFee;

    /**
     *
     */
    private BigDecimal fob;

    /**
     * 建议零售价
     */
    private BigDecimal retailPrice;


}
