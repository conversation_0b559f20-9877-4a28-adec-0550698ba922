package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.product.LengthUnitEnum;
import com.zsmall.common.enums.product.WeightUnitEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 商品SKU详情对象 product_sku_detail
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_sku_detail")
public class ProductSkuDetail extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品SKU表主键
     */
    private Long productSkuId;

    /**
     * 长
     */
    private BigDecimal length;

    /**
     * 宽
     */
    private BigDecimal width;

    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 长度单位
     */
    private LengthUnitEnum lengthUnit;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 重量单位
     */
    private WeightUnitEnum weightUnit;

    /**
     * 打包长
     */
    private BigDecimal packLength;

    /**
     * 打包宽
     */
    private BigDecimal packWidth;

    /**
     * 打包高
     */
    private BigDecimal packHeight;

    /**
     * 打包长度单位
     */
    private LengthUnitEnum packLengthUnit;

    /**
     * 打包重量
     */
    private BigDecimal packWeight;

    /**
     * 打包重量单位
     */
    private WeightUnitEnum packWeightUnit;

    /**
     * 打包尺寸重量与商品尺寸重量相同：0-否，1-是
     */
    private Boolean samePacking;

    /**
     * 处理时效（天）
     */
    private BigDecimal processingTime;

    /**
     * 运输方式
     */
    private String transportMethod;

    /**
     * SKU描述
     */
    private String description;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
