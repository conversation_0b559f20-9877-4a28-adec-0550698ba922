package com.zsmall.product.entity.domain.bo.productMapping;

import lombok.Data;

import java.util.List;

/**
 * 请求体-分销商映射商品列表
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
public class ProductMappingTableQueryBo {

    /**
     * 查询类型：ProductName-商品名，ItemNo-SKU唯一编号, channelSku - 渠道SKU , channelSkuItemNumber - 渠道SKU货号
     */
    private String queryType;

    /**
     * 查询关键字
     */
    private String queryValue;

    /**
     * 渠道id集合
     */
    private List<Long> channelIds;

    /**
     * 渠道：OneLink，Wayfair，Shopify，Amazon
     */
    private String channelType;

    /**
     * 映射状态状态
     */
    private String syncState;
    /**
     * 站点
     */
    private String site;
    private String tenantId;
}
