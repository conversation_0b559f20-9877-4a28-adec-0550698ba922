package com.zsmall.product.entity.domain.bo.productSkuPrice;

import cn.hutool.json.JSONArray;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.ProductSkuPriceRule;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品sku价格计算公式业务对象 product_sku_price_rule
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuPriceRule.class, reverseConvertGenerate = false)
public class ProductSkuPriceRuleBo extends SortEntity {

    /**
     *
     */
    private Long id;

    private Long productSkuId;

    /**
     * 计算公式名称
     */
    private String ruleName;


    /**
     * 计算公式编码
     */
    private String ruleCode;

    /**
     * 原计算公式编码
     */
    private String originalRuleCode;

    /**
     * 适用类型
     */
    @NotNull(message = "适用类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer applicableType;

    /**
     * 适用的值
     */
    @NotNull(message = "适用的值不能为空", groups = { AddGroup.class, EditGroup.class })
    private JSONArray applicableValue;

    /**
     * 运算符-单价
     */
    private Integer unitPriceCal;

    /**
     * 提价-单价
     */
    private BigDecimal unitPrice;

    /**
     * 运算符-操作费
     */
    private Integer operationFeeCal;

    /**
     * 提价-操作费
     */
    private BigDecimal operationFee;

    /**
     * 运算符-运费
     */
    private Integer finalDeliveryFeeCal;

    /**
     * 提价-运费
     */
    private BigDecimal finalDeliveryFee;

    /**
     * 商品编码集合
     */
    private List<String> itemNoList;

    /**
     * 删除的itemNo集合
     */
    private List<String> deleteItemNoList;

    /**
     * 替换的itemNo集合
     */
    private List<String> replaceItemNoList;

    public ProductSkuPriceRuleBo(Long id, String ruleName, String ruleCode, Integer applicableType, JSONArray applicableValue) {
        this.id = id;
        this.ruleName = ruleName;
        this.ruleCode = ruleCode;
        this.applicableType = applicableType;
        this.applicableValue = applicableValue;
    }
}
