package com.zsmall.product.entity.domain.bo.productSku;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductSku;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品SKU业务对象 product_sku
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSku.class, reverseConvertGenerate = false)
public class ProductSkuBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品SPU表主键
     */
    @NotNull(message = "商品SPU表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 商品SPU唯一编号
     */
    @NotBlank(message = "商品SPU唯一编号")
    private String productCode;

    /**
     * Sku唯一编号（ItemNo.）
     */
    @NotBlank(message = "Sku唯一编号（ItemNo.）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productSkuCode;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 最小库存单位（Sku）
     */
    @NotBlank(message = "最小库存单位（Sku）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sku;

    /**
     * 系统库存单位
     */
    private String erpSku;

    /**
     * 商品统一代码（UPC）
     */
    @NotBlank(message = "商品统一代码（UPC）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String upc;

    /**
     * Sku库存总数
     */
    @NotNull(message = "Sku库存总数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer stockTotal;

    /**
     * 库存管理方（区分自有仓库管理还是第三方平台管理）
     */
    @NotBlank(message = "库存管理方（区分自有仓库管理还是第三方平台管理）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String stockManager;

    /**
     * Sku货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
    @NotBlank(message = "Sku货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架不能为空", groups = { AddGroup.class, EditGroup.class })
    private String shelfState;

    /**
     * Sku审核状态
     */
    @NotBlank(message = "Sku审核状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String verifyState;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer sort;


}
