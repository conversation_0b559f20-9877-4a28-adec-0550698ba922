package com.zsmall.product.entity.domain.vo.wholesale;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/20 15:52
 */
@Data
@Schema(name = "通用参数-国外现货批发商品价格信息")
public class ProductWholesalePriceVo {
  @Schema(title = "最小数量")
  private Integer minQuantity;

  @Schema(title = "最大数量")
  private Integer maxQuantity;

  @Schema(title = "单规格价格信息")
  private List<AttributePrice> attributePrices;

  @Schema(title = "预估操作费")
  private BigDecimal estimatedOperationalFee;

  @Schema(title = "预估运费")
  private BigDecimal estimatedShoppingFee;

  @Schema(title = "预估处理时间(天)")
  private Integer dealDate;


  /**
   * 单规格价格信息
   */
  @Data
  public static class AttributePrice {

    @Schema(title = "属性")
    private String attribute;

    @Schema(title = "单价")
    private BigDecimal price;
  }


}
