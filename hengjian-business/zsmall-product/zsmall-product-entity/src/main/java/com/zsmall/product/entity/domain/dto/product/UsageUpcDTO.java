package com.zsmall.product.entity.domain.dto.product;


import com.zsmall.common.enums.UpcUsageStateType;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UsageUpcDTO {

    /**
     * UPC码
     */
    private String id;

    /**
     * 使用状态类型：Preempt-预占；Assigned-已分配；Deleted-已删除
     */
    private UpcUsageStateType stateType;

    /**
     * 品牌代码
     */
    private String brand;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 最近一次使用UPC编码的时间
     */
    private Date updateTime;

    public UsageUpcDTO(String id, String brand, ChannelTypeEnum channelType, Date updateDateTime,
                       UpcUsageStateType stateType) {
        this.id = id;
        this.stateType = stateType;
        this.brand = brand;
        this.channelType = channelType;
        this.updateTime = updateDateTime;
    }
}
