package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 商品问答回复编辑日志对象 product_question_answer_log
 *
 * <AUTHOR> Li
 * @date 2023-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_question_answer_log")
public class ProductQuestionAnswerLog extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 回复编辑日志表id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 回复编码
     */
    private String answerCode;

    /**
     * 编辑前的回复内容
     */
    private String answer;

    /**
     * 追加提问状态：reported-已举报
     */
    private String questionStatus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
