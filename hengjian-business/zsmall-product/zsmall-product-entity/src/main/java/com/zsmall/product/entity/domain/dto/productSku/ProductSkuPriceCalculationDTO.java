package com.zsmall.product.entity.domain.dto.productSku;

import com.zsmall.common.annotaion.TargetPriceEnum;
import com.zsmall.common.enums.OrderItemPriceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 商品价格DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuPriceCalculationDTO {

    /**
     * 商品价格公式Id（旧）
     */
    private Long oldProductSkuPriceRuleId;

    /**
     * 商品价格公式Id（新）
     */
    private Long productSkuPriceRuleId;

    /**
     * 商品价格公式编码
     */
    private String productSkuPriceRuleCode;

    /**
     * 商品价格Id
     */
    private Long productSkuPriceId;

    /**
     * 原产品单价
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.UnitPriceSup)
    private BigDecimal unitPrice;

    /**
     * 原操作费
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.OperationFeeSup)
    private BigDecimal operationFee;

    /**
     * 原尾程派送费
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.FinalDeliveryFeeSup)
    private BigDecimal finalDeliveryFee;

    /**
     * 原订金单价
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.DepositUnitPriceSup)
    private BigDecimal depositUnitPrice;

    /**
     * 原尾款单价
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.BalanceUnitPriceSup)
    private BigDecimal balanceUnitPrice;

    /**
     * 代发价
     */
    private BigDecimal dropShippingPrice;

    /**
     * 自提价
     */
    private BigDecimal pickUpPrice;

    /**
     * 原始自提价（供货商，产品单价+操作费）
     */
    private BigDecimal originalPickUpPrice;

    /**
     * 原始代发价（供货商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal originalDropShippingPrice;

    /**
     * 提价后的原价（自提公式）
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.UnitPriceMd)
    private BigDecimal unitPriceMd;

    /**
     * 提价后的操作费（自提公式）
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.OperationFeeMd)
    private BigDecimal operationFeeMd;

    /**
     * 提价后的尾程派送费（代发公式）
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.FinalDeliveryFeeMd)
    private BigDecimal finalDeliveryFeeMd;

    /**
     * 平台订金单价
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.DepositUnitPriceMd)
    private BigDecimal depositUnitPriceMd;

    /**
     * 平台尾款单价
     */
    @TargetPriceEnum(OrderItemPriceTypeEnum.BalanceUnitPriceMd)
    private BigDecimal balanceUnitPriceMd;

}
