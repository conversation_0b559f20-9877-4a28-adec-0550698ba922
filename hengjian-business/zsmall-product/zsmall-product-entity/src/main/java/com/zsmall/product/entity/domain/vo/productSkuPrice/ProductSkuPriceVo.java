package com.zsmall.product.entity.domain.vo.productSkuPrice;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 商品SKU定价视图对象 product_sku_price
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductSkuPrice.class)
public class ProductSkuPriceVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;
    @ExcelIgnore
    private Long siteId;
    /**
     * 商品SKU表主键
     */
    @ExcelProperty(value = "商品SKU表主键")
    private Long productSkuId;

    @ExcelProperty(value = "商品SKU编码")
    private String productSkuCode;

    @ExcelProperty(value = "商品SKU价格表主键")
    private Long productSkuPriceId;

    /**
     * 原始产品单价（供货商）
     */
    @ExcelProperty(value = "原始产品单价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商")
    private BigDecimal originalUnitPrice;

    /**
     * 原始操作费（供货商）
     */
    @ExcelProperty(value = "原始操作费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商")
    private BigDecimal originalOperationFee;

    /**
     * 原始尾程派送费（供货商）
     */
    @ExcelProperty(value = "原始尾程派送费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商")
    private BigDecimal originalFinalDeliveryFee;

    /**
     * 原始自提价（供货商，产品单价+操作费）
     */
    @ExcelProperty(value = "原始自提价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商，产品单价+操作费")
    private BigDecimal originalPickUpPrice;

    /**
     * 原始代发价（供货商，产品单价+操作费+尾程派送费）
     */
    @ExcelProperty(value = "原始代发价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商，产品单价+操作费+尾程派送费")
    private BigDecimal originalDropShippingPrice;

    /**
     * 平台产品单价（平台+分销商）
     */
    @ExcelProperty(value = "平台产品单价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台+分销商")
    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台+分销商）
     */
    @ExcelProperty(value = "平台操作费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台+分销商")
    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台+分销商）
     */
    @ExcelProperty(value = "平台尾程派送费", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台+分销商")
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 平台自提价（平台+分销商，产品单价+操作费）
     */
    @ExcelProperty(value = "平台自提价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台+分销商，产品单价+操作费")
    private BigDecimal platformPickUpPrice;

    /**
     * 平台代发价（平台+分销商，产品单价+操作费+尾程派送费）
     */
    @ExcelProperty(value = "平台代发价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "平=台+分销商，产品单价+操作费+尾程派送费")
    private BigDecimal platformDropShippingPrice;

    /**
     * 建议零售价
     */
    @ExcelProperty(value = "建议零售价")
    private BigDecimal msrp;

    public ProductSkuPriceVo(BigDecimal originalUnitPrice, BigDecimal originalOperationFee, BigDecimal originalFinalDeliveryFee, BigDecimal originalPickUpPrice, BigDecimal originalDropShippingPrice, BigDecimal platformUnitPrice, BigDecimal platformOperationFee, BigDecimal platformFinalDeliveryFee, BigDecimal platformPickUpPrice, BigDecimal platformDropShippingPrice) {
        this.originalUnitPrice = originalUnitPrice;
        this.originalOperationFee = originalOperationFee;
        this.originalFinalDeliveryFee = originalFinalDeliveryFee;
        this.originalPickUpPrice = originalPickUpPrice;
        this.originalDropShippingPrice = originalDropShippingPrice;
        this.platformUnitPrice = platformUnitPrice;
        this.platformOperationFee = platformOperationFee;
        this.platformFinalDeliveryFee = platformFinalDeliveryFee;
        this.platformPickUpPrice = platformPickUpPrice;
        this.platformDropShippingPrice = platformDropShippingPrice;
    }
}
