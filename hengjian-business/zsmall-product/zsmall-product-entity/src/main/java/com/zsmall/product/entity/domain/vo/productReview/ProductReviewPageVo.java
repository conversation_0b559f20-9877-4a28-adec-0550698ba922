package com.zsmall.product.entity.domain.vo.productReview;

import cn.hutool.json.JSONArray;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuBody;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-商品审核数组
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ProductReviewPageVo {


    /**
     * 审核ID
     */
    private Long reviewRecordId;
    /**
     * 商品code
     */
    private String productCode;

    /**
     * 商品类型
     */
    private String productType;

    /**
     * 中文名称
     */
    private String name;
    /**
     * 审核状态
     */
    private String reviewStatus;
    /**
     * 创建时间
     */
    private String submitDateTime;
    /**
     * 审核时间
     */
    private String reviewDateTime;
    /**
     * 审核类型
     */
    private String reviewType;
    /**
     * 商品图片显示链接
     */
    private String productImageShowUrl;
    /**
     * 自提类型：PickUpOnly-仅自提，DropShippingOnly-仅代发，Different-都支持
     */
    private String supportedLogistics;
    /**
     * 自提类型：PickUpOnly-仅自提，DropShippingOnly-仅代发，Different-都支持
     */
    private String supportedLogisticsBefore;
    /**
     * 自提类型：PickUpOnly-仅自提，DropShippingOnly-仅代发，Different-都支持
     */
    private String supportedLogisticsAfter;
    /**
     * 审核意见
     */
    private String reviewOpinion;
    /**
     * 商家
     */
    private String submitUser;
    /**
     * 审核意见选项
     */
    private JSONArray reviewOpinionOption;
    /**
     * sku表格数据集合
     */
    private List<ProductSkuBody> skuList;
    /**
     * 生效时间
     */
    private String effectiveDate;

    private String tenantID;
}
