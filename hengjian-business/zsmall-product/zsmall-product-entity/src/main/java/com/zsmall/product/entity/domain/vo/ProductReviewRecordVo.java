package com.zsmall.product.entity.domain.vo;

import cn.hutool.json.JSONArray;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.common.enums.product.ProductReviewTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.product.entity.domain.ProductReviewRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;



/**
 * 商品审核记录视图对象 product_review_record
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductReviewRecord.class)
public class ProductReviewRecordVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品编号
     */
    @ExcelProperty(value = "商品编号")
    private String productCode;

    /**
     * 审核意见
     */
    @ExcelProperty(value = "审核意见")
    private String reviewOpinion;

    /**
     * 审核意见选项（多选
     */
    @ExcelProperty(value = "审核意见选项", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "审核意见选项（多选")
    private JSONArray reviewOpinionOption;

    /**
     * 提交审核时间
     */
    @ExcelProperty(value = "提交审核时间")
    private Date submitDateTime;

    /**
     * 提交审核用户ID
     */
    @ExcelProperty(value = "提交审核用户ID")
    private Long submitUserId;

    /**
     * 审核员工ID
     */
    @ExcelProperty(value = "审核员工ID")
    private Long reviewUserId;

    /**
     * 审核时间
     */
    @ExcelProperty(value = "审核时间")
    private Date reviewDateTime;

    /**
     * 审核类型（目前只有价格）：Price-价格
     */
    @ExcelProperty(value = "审核类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "目=前只有价格")
    private ProductReviewTypeEnum reviewType;

    /**
     * 审核状态：Pending-审核中，Accepted-已审核，Rejected-已驳回
     */
    @ExcelProperty(value = "审核状态：Pending-审核中，Accepted-已审核，Rejected-已驳回")
    private ProductVerifyStateEnum reviewState;
    @ExcelIgnore
    private String tenantID;

}
