package com.zsmall.product.entity.domain.vo.product;

import cn.hutool.json.JSONArray;
import com.zsmall.product.entity.domain.bo.product.ProductSitePriceBo;
import lombok.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 响应体-商品完整信息
 */
@Data
public class ProductIntactInfoVo {
    /**
     * 主键
     */
    private Long id;
    /**
     * 商品SPU唯一编号
     */
    private String productCode;
    /**
     * 商品SPU名名称
     */
    private String productName;
    /**
     * 最底层分类id
     */
    private Long belongCategoryId;
    /**
     * 分类树id集合
     */
    private List<Long> categoryIdList;
    /**
     * 支持的物流，All-都支持，PickUpOnly-仅支持自提，DropShippingOnly-仅支持代发
     */
    private String supportedLogistics;
    /**
     * 商品SPU描述
     */
    private String description;
    /**
     * 禁售渠道数组
     */
    private JSONArray forbiddenChannel;
    /**
     * 已经上架
     */
    private Boolean alreadyOnShelf;
    /**
     * 通用规格数组
     */
    private List<GenericSpecList> genericSpecList;
    /**
     * 可选规格数组
     */
    private List<OptionalSpecList> optionalSpecList;
    /**
     * 商品特色数组
     */
    private List<ProductFeatureList> productFeatureList;
    /**
     * 商品SKU数组
     */
    private List<ProductSkuList> productSkuList;
    /**
     * 其他附件
     */
    private Attachment otherAttachment;

    /**
     * 审核状态
     */
    private String verifyState;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 审核意见选项
     */
    private JSONArray reviewOpinionOption;

    /**
     * 全局价格审核标志位
     */
    private Boolean globalPriceChange;

    @Data
    public static class GenericSpecList {
        /**
         * 主键
         */
        private Long id;
        /**
         * 通用规格来源主键
         */
        private Long sourceId;
        /**
         * 通用规格key
         */
        private String key;
        /**
         * 通用规格value
         */
        private String value;
        /**
         * 是否必填
         */
        private Boolean isRequired;
        /**
         * 是否支持自定义值
         */
        private Boolean isSupportCustom;
    }

    @Data
    public static class OptionalSpecList {
        /**
         * 主键
         */
        private Long id;
        /**
         * 可选规格来源主键
         */
        private Long sourceId;
        /**
         * 可选规格名
         */
        private String key;
        /**
         * 可选值数组
         */
        private JSONArray values;
        /**
         * 是否支持自定义值
         */
        private Boolean isSupportCustom;

        public void addValue(String value) {
            if (this.values == null) {
                values = new JSONArray();
            }
            values.add(value);
        }
    }

    @Data
    public static class ProductFeatureList {
        /**
         * 主键
         */
        private Long id;
        /**
         * 特色key
         */
        private String key;
        /**
         * 特色value
         */
        private String value;
    }

    @Data
    public static class ProductSkuList {
        /**
         * 主键
         */
        private Long id;
        /**
         * 最小库存单位
         */
        private String sku;
        /**
         * UPC代号
         */
        private String upc;
        /**
         * 系统库存单位
         */
        private String erpSku;
        /**
         * 商品单价，限制两位小数
         */
        private BigDecimal unitPrice;
        /**
         * 操作费，限制两位小数
         */
        private BigDecimal operationFee;
        /**
         * 尾程派送费，限制两位小数
         */
        private BigDecimal finalDeliveryFee;
        /**
         * 厂商建议零售价，限制两位小数
         */
        private BigDecimal msrp;
        /**
         * 库存管理方
         */
        private String stockManager;
        /**
         * 长
         */
        private String length;
        /**
         * 宽
         */
        private String width;
        /**
         * 高
         */
        private String height;
        /**
         * 长度单位
         */
        private String lengthUnit;
        /**
         * 重量
         */
        private String weight;
        /**
         * 重量单位
         */
        private String weightUnit;
        /**
         * 打包长度
         */
        private String packLength;
        /**
         * 打包宽度
         */
        private String packWidth;
        /**
         * 打包高度
         */
        private String packHeight;
        /**
         * 打包长度单位
         */
        private String packLengthUnit;
        /**
         * 打包重量
         */
        private String packWeight;
        /**
         * 打包重量单位
         */
        private String packWeightUnit;
        /**
         * 商品尺寸与打包尺寸是否一致
         */
        private boolean samePacking;
        /**
         * 运输方式
         */
        private String transportMethod;
        /**
         * 规格组成名称，示例：尺寸-大;颜色-白色
         */
        private String specComposeName;
        /**
         * 规格值名称，示例：大/白色
         */
        private String specValName;
        /**
         * 是否上架
         */
        private Boolean isOnShelf;
        /**
         * 审核状态
         */
        private String verifyState;
        /**
         * 审核意见
         */
        private String reviewOpinion;
        /**
         * 审核意见选项
         */
        private JSONArray reviewOpinionOption;
        /**
         * 存在价格变更审核
         */
        private Boolean hasPriceChange;
        /**
         * 规格详细信息数组
         */
        private List<SpecComposeList> specComposeList;
        /**
         * 图片数组
         */
        private List<Attachment> imageList;
        /**
         * 视频数组
         */
        private List<Attachment> videoList;
        /**
         * 库存配置数组
         */
        private List<StockConfigList> stockConfigList;

        /**
         * 可选仓库列表（前端用）
         */
        private List<WarehouseTempVo> warehouseList;

        /**
         * 可选仓库列表2（前端用）
         */
        private List<WarehouseTempVo> warehouseListTemp;

        private List<ProductSitePriceBo> sitePriceBos;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attachment {
        /**
         * 主键
         */
        private Long id;
        /**
         * 对象存储主键
         */
        private String ossId;
        /**
         * 附件原名
         */
        private String attachmentName;
        /**
         * 附件存储路径
         */
        private String attachmentSavePath;
        /**
         * 附件展示URL
         */
        private String attachmentShowUrl;
        /**
         * 附件排序
         */
        private Integer attachmentSort;
        /**
         * 附件类型
         */
        private String attachmentType;
    }

    @Data
    public static class SpecComposeList {
        /**
         * 主键
         */
        private Long id;
        /**
         * 规格来源主键
         */
        private Long sourceId;
        /**
         * 规格名
         */
        private String key;
        /**
         * 规格值
         */
        private String value;
    }

    @Data
    public static class StockConfigList {
        /**
         * 仓库名称
         */
        private String warehouseName;
        /**
         * 仓库系统编号
         */
        private String warehouseSystemCode;
        /**
         * 关联物流模板编号
         */
        private String logisticsTemplateNo;

        private List<LogisticsTemplateSelectVo> logisticsTemplateSelect = new ArrayList<>();
        /**
         * 库存数量
         */
        private Integer quantity;

        /**
         * 代发库存数量
         */
        private Integer proxyStockQuantity;

        /**
         *  代发库存标识 0单仓,1非单仓(等于自提库存)
         */
        private Integer dropShippingStockAvailable;
    }

    @Getter
    @Setter
    public class LogisticsTemplateSelectVo {

        /**
         * 模板名称
         */
        private String templateName;

        /**
         * 物流模板编号
         */
        private String logisticsTemplateNo;

    }

}

