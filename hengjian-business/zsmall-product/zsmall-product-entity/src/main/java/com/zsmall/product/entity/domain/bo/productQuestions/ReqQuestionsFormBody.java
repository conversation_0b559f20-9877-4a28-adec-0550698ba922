package com.zsmall.product.entity.domain.bo.productQuestions;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/10
 **/

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "新增提问请求参数")
public class ReqQuestionsFormBody {

    @Schema(title = "提问内容")
    private String question;

    @Schema(title = "商品SkuCode")
    private String productSkuCode;

}
