package com.zsmall.product.entity.domain.dto.price;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class OpenApiProductSkuPriceWithSite {
    /**
     * 商品编码
     */
    private String productSkuCode;

    private List<ProductSkuPriceBySite> productSkuPriceBySites;

    @Data
    public static class  ProductSkuPriceBySite{

        /**
         * 货币
         */
        private String currency;
        /**
         * 币种code
         */
        private String countryCode;
        /**
         * 货币符号
         */
        private String currencySymbol;
        /**
         * 商品编码
         */
        private String productSkuCode;
        /**
         * SKU编码
         */
        private String skuCode;
        /**
         * 是否会员价
         */
        private Boolean isMemberPrice;
        /**
         * 分销商产品单价
         */
        private BigDecimal platformUnitPrice;

        /**
         * 分销商操作费
         */
        private BigDecimal platformOperationFee;

        /**
         * 分销商尾程派送费
         */
        private BigDecimal platformFinalDeliveryFee;

        /**
         * 分销商总价
         */
        private BigDecimal platformDropShippingPrice;
        /**
         * 分销自提价格
         */
        private BigDecimal platformPickUpPrice;
        /**
         * 价格更新时间
         */
        private Date updateTime;
    }

}
