package com.zsmall.product.entity.domain.member;



import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.common.OperationTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2024/05/07
 */
@Data
@TableName(value = "member_level_log", autoResultMap = true)
@EqualsAndHashCode(callSuper = false)
public class MemberLevelLog extends NoDeptTenantEntity {


    private Long id;
    /**
     * 等级id
     */
    @ApiModelProperty("等级id")
    private Long levelId;

    /**
     * '删除标志（0代表存在 2代表删除)'
     */
    @ApiModelProperty("'删除标志（0代表存在 2代表删除)'")
    @TableLogic
    private Integer delFlag;
    @ApiModelProperty("改动前状态")
    private String departureStatus;
    @ApiModelProperty("改动后状态")
    private String destinationStatus;

    /**
     * 0 关闭 1开启
     */
    private String status;
    /**
     * 操作类型
     */
    private String operationType;
}
