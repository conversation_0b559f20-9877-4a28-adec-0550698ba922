package com.zsmall.product.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.product.entity.domain.ProductSkuStock;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品SKU库存业务对象 product_sku_stock
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuStock.class, reverseConvertGenerate = false)
public class ProductSkuStockBo extends SortEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 库存编号
     */
    @NotBlank(message = "库存编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String stockCode;

    /**
     * 总库存
     */
    @NotNull(message = "总库存不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer stockTotal;

    /**
     * 预留库存
     */
    @NotNull(message = "预留库存不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer stockReserved;

    /**
     * 可用库存
     */
    @NotNull(message = "可用库存不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer stockAvailable;

    /**
     * 库存状态（0-停用，1-启用等）
     */
    @NotNull(message = "库存状态（0-停用，1-启用等）不能为空", groups = { AddGroup.class, EditGroup.class })
    private GlobalStateEnum stockState;

    /**
     * 第三方系统库存单位
     */
    private String erpSku;

    /**
     * SPU唯一编号
     */
    @NotBlank(message = "SPU唯一编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productCode;

    /**
     * SKU唯一编号（ItemNo.）
     */
    @NotBlank(message = "SKU唯一编号（ItemNo.）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productSkuCode;

    /**
     * 仓库系统编号
     */
    @NotBlank(message = "仓库系统编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseSystemCode;

    /**
     * 关联物流模板编号
     */
    private String logisticsTemplateNo;


}
