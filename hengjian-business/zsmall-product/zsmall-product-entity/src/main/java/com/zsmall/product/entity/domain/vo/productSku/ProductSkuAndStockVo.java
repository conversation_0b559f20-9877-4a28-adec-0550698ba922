package com.zsmall.product.entity.domain.vo.productSku;

import com.zsmall.product.entity.domain.vo.productSkuStock.ProductSkuStockSimpleVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 响应体-商品SKU与库存信息
 **/
@Data
public class ProductSkuAndStockVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图片
     */
    private String productImg;
    /**
     * 商品名
     */
    private String productName;
    /**
     * sku
     */
    private String productSku;
    /**
     * Item NO.
     */
    private String productSkuCode;
    /**
     * 总库存
     */
    private Integer stockTotal;
    /**
     * 支持的物流：All-都支持，PickUpOnly-仅支持自提，DropShippingOnly-仅支持代发
     */
    private String supportedLogistics;
    /**
     * 自提价
     */
    private BigDecimal pickUpPrice;
    /**
     * 代发价
     */
    private BigDecimal dropShippingPrice;
    /**
     * 库存信息
     */
    private List<ProductSkuStockSimpleVo> stockList;

}
