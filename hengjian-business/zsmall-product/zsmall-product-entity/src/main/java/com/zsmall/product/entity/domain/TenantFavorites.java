package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 租户收藏夹表
 * <AUTHOR>
 * @date 2023/8/16
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "tenant_favorites", autoResultMap = true)
public class TenantFavorites extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 已铺货渠道名称（JSON数组）
     */
    @TableField(value = "channel_names", typeHandler = JacksonTypeHandler.class)
    private JSONArray channelNames;

    /**
     * 商品主键
     */
    @TableField(value = "product_id")
    private Long productId;

    /**
     * 商品编号
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 商品SKU主键
     */
    @TableField(value = "product_sku_id")
    private Long productSkuId;

    /**
     * 商品SKU编号（选定时）
     */
    @TableField(value = "product_sku_code")
    private String productSkuCode;

    /**
     * 库存总数（若是SPU维度收藏，则取库存最多的SKU，若是SKU维度收藏，则取指定SKU的）
     */
    @TableField(value = "stock_total")
    private Integer stockTotal;

//    /**
//     * 单仓数量
//     */
//    @TableField(value = "quantity")
//    private Integer quantity;

    /**
     * 平台自提价（商品加入时SKU最低价格）
     */
    @TableField(value = "platform_pick_up_price")
    private BigDecimal platformPickUpPrice;

    /**
     * 平台代发价（商品加入时SKU最低价格）
     */
    @TableField(value = "platform_drop_shipping_price")
    private BigDecimal platformDropShippingPrice;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 站点
     */
    @TableField(value = "site")
    private String site;
    /**
     * 币种
     */
    @TableField(value = "currency")
    private String currency;

}
