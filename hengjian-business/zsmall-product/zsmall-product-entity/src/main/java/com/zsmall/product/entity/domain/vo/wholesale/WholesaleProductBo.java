package com.zsmall.product.entity.domain.vo.wholesale;

import cn.hutool.json.JSONArray;
import com.zsmall.product.entity.domain.bo.product.ProductAttachmentBo;
import com.zsmall.product.entity.domain.bo.product.ProductIntactInfoUpdateBo;
import com.zsmall.product.entity.domain.bo.wholesale.ProductSkuWholesaleBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/23 18:43
 */
@Data
@Schema(name = "请求信息-国外批发商品")
public class WholesaleProductBo {

    /**
     * 商品编码
     */
    private String productCode;
    /**
     *商品名称
     */
    private String productName;
    /**
     *商品目录ID集合
     */
    private List<Long> categoryIdList;
    /**
     *商品目录ID
     */
    private Long belongCategoryId;
    /**
     *最小起订量
     */
    private Integer minimumOrderQuantity;
    /**
     *订金比例
     */
    private BigDecimal depositRatio;
    /**
     *预留天数
     */
    private Integer reservedDay;
    /**
     *发货方式
     */
    private JSONArray deliveryTypeList;
    /**
     *仓库编码
     */
    private String warehouseSystemCode;
    /**
     *物流
     */
    private String logisticsTemplateNo;
    /**
     *描述
     */
    private String description;
    /**
     *其他附件（PDF、说明书等）
     */
    private List<ProductAttachmentBo> otherAttachment;
    /**
     *sku数据集合
     */
    private List<ProductSkuWholesaleBo> skuList;
    /**
     *批发价格体系
     */
    private List<ProductWholesalePriceVo> wholesalePriceBodyList;

    /**
     * 商品可选规格
     */
    private List<ProductIntactInfoUpdateBo.OptionalSpecList> optionalSpecList;


}
