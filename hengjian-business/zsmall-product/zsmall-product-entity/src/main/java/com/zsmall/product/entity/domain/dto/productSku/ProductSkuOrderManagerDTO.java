package com.zsmall.product.entity.domain.dto.productSku;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单合计数 dto
 *
 * <AUTHOR>
 * @date 2022/9/21 15:33
 */
@Data
@ExcelIgnoreUnannotated
public class ProductSkuOrderManagerDTO implements Serializable {


    /**
     * 商品图片地址
     */
    @ExcelProperty(value = "商品图片地址")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.showUrl")
    private String showUrl;

    /**
     * itemNo
     */
    @ExcelProperty(value = "Item No")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.itemNo")
    private String itemNo;

    /**
     * 商品名称
     */
    /*@ExcelProperty(value = "商品名称", index= 3)
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.name")
    private String name;*/

    /**
     * SKU编码
     */
    @ExcelProperty(value = "SKU编码")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.sku")
    private String sku;

    /**
     * 供应商租户编码
     */
    @ExcelProperty(value = "供应商租户编码")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.tenantId")
    private String tenantId;

    /**
     * 注册时间
     */
    @ExcelProperty(value = "注册时间")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.createTime")
    private String createTime;

    /**
     *更新时间
     */
    @ExcelProperty(value = "修改时间")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.updateTime")
    private String updateTime;

    /**
     * 代发价格
     */
    @ExcelProperty(value = "代发价格")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.dropShippingPrice")
    private BigDecimal dropShippingPrice;

    /**
     * 自提价格
     */
    @ExcelProperty(value = "自提价格")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.pickUpPrice")
    private BigDecimal pickUpPrice;

    /**
     * 库存数
     */
    @ExcelProperty(value = "总库存")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.stockTotal")
    private Long stockTotal;

    /**
     * 销售件数总数
     */
    @ExcelProperty(value = "销售件数总数")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.salesNum")
    private Integer salesNum;

    /**
     * 订单总数
     */
    @ExcelProperty(value = "订单总数")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.orderTotalNum")
    private Integer orderTotalNum;

    /**
     * 出单总金额
     */
    @ExcelProperty(value = "出单总金额")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.orderTotalPrice")
    private BigDecimal orderTotalPrice;

    /**
     * 售后订单总数
     */
    @ExcelProperty(value = "售后订单总数")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.restockTotalNum")
    private Integer restockTotalNum;

    /**
     * 收藏数总数（分销商）
     */
    @ExcelProperty(value = "收藏数总数（分销商）")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.dropNumByDis")
    private Integer dropNumByDis;


    /**
     * 处理订单时效
     */
    @ExcelProperty(value = "处理订单时效")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.orderDealEffectiveness")
    private Long orderDealEffectiveness;

    /**
     * 下载数
     */
    @ExcelProperty(value = "下载数")
    @ExcelI18nFormat(code = "zsmall.excel.statistics.manager.downloadedByDis")
    private Integer downloadedByDis;




}
