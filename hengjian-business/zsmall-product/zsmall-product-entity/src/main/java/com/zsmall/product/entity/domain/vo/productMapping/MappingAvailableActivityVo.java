package com.zsmall.product.entity.domain.vo.productMapping;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 映射可用活动
 *
 * <AUTHOR>
 * @date 2023/8/2
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MappingAvailableActivityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String label;

    private String value;

    private List<MappingAvailableActivityInfoVo> children;

}
