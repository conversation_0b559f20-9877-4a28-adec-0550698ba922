package com.zsmall.product.entity.domain.bo.distributorProduct;

import com.zsmall.common.constant.ValidationMessage;
import com.zsmall.common.enums.productMapping.MarkUpTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * 请求体-批量铺货至商店
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BatchImportToStoreBo {

    /**
     * 商品编号集合
     */
    @NotEmpty(message = ValidationMessage.API_REQUIRED)
    private List<String> productCodeList;

    /**
     * 渠道类型
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String channelType;

    /**
     * 渠道id集合
     */
    @NotEmpty(message = ValidationMessage.API_REQUIRED)
    private List<Long> channelIdList;

    /**
     * 提价类型
     */
    private MarkUpTypeEnum markUpType;

    /**
     * 提价值
     */
    private BigDecimal markUpValue;

}
