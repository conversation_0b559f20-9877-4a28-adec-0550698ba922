package com.zsmall.product.entity.domain.vo.member;

import lombok.Data;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/17 16:45
 */
@Data
public class MemberLevelSkuPriceVo {

    /**
     * 原始产品单价（供货商）
     */
    private Long id;

    /**
     * 规则定制方租户标识
     */
    private String ruleCustomizerTenantId;

    /**
     * 等级id
     */
    private Long levelId;

    /**
     * 原始产品单价（供货商）
     */
    private BigDecimal originalUnitPrice;

    /**
     * 原始操作费（供货商）
     */
    private BigDecimal originalOperationFee;

    /**
     * 原始尾程派送费（供货商）
     */
    private BigDecimal originalFinalDeliveryFee;

    /**
     * 原始自提价（供货商，产品单价+操作费）
     */
    private BigDecimal originalPickUpPrice;

    /**
     * 原始代发价（供货商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal originalDropShippingPrice;

    /**
     *
     */
    private Long productSkuId;

    /**
     *
     */
    private Long productId;

    /**
     * 平台产品单价
     */
    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台+分销商）
     */
    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台+分销商）
     */
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 平台自提价格
     */
    private BigDecimal platformPickUpPrice;

    /**
     *
     */
    private BigDecimal platformDropShippingPrice;
}
