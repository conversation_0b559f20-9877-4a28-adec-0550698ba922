package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 定时任务-sku价格变更对象 task_sku_price_change
 *
 * <AUTHOR> Li
 * @date 2023-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("task_sku_price_change")
public class TaskSkuPriceChange extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 执行日期
     */
    private String executeDate;

    /**
     * 审核记录id
     */
    private Long reviewRecordId;

    /**
     * 任务状态
     */
    private String taskState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
