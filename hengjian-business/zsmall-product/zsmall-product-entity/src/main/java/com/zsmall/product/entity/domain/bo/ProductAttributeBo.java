package com.zsmall.product.entity.domain.bo;

import cn.hutool.json.JSONArray;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.product.entity.domain.ProductAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品SPU属性业务对象 product_attribute
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductAttribute.class, reverseConvertGenerate = false)
public class ProductAttributeBo extends NoDeptBaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品SPU表主键
     */
    @NotNull(message = "商品SPU表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 属性类型
     */
    @NotBlank(message = "属性类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeType;

    /**
     * 属性名
     */
    @NotBlank(message = "属性名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeName;

    /**
     * 属性值
     */
    @NotBlank(message = "属性值不能为空", groups = { AddGroup.class, EditGroup.class })
    private JSONArray attributeValues;

    /**
     * 属性排序
     */
    @NotNull(message = "属性排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer attributeSort;

    /**
     * 属性来源id
     */
    @NotNull(message = "属性来源id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long attributeSourceId;


}
