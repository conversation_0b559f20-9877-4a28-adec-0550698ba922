package com.zsmall.product.entity.domain.member;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;


/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2024/05/08
 */
@Data
@TableName(value = "member_rule_relation", autoResultMap = true)
@EqualsAndHashCode(callSuper = false)
public class MemberRuleRelation implements Serializable {

    /**
    *
    */
    @ApiModelProperty("")
    private Long id;
    /**
    * 等级表标识
    */
    @ApiModelProperty("等级表标识")
    private Long levelId;
    /**
    * 规则定制方租户标识
    */
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("规则定制方租户标识")
    @Length(max= 20,message="编码长度不能超过20")
    private String ruleCustomizerTenantId;
    /**
    * 规则遵守方的租户标识
    */
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("规则遵守方的租户标识 ")
    @Length(max= 20,message="编码长度不能超过20")
    private String ruleFollowerTenantId;
    /**
    * 手机号
    */
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("手机号")
    private String phoneNumber;
    /**
    * 邮箱地址
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("邮箱地址")
    @Length(max= 255,message="编码长度不能超过255")
    private String emailAddress;

    /**
     * 昵称
     */
    private String nickName;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
    private Long createBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 操作人
    */
    @ApiModelProperty("操作人")
    private Long updateBy;
    /**
    * 更新时间
    */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 删除标志（0代表存在 2代表删除)
    */
    @ApiModelProperty("删除标志（0代表存在 2代表删除)")
    private String delFlag;



}
