package com.zsmall.product.entity.domain.vo.product;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/12 11:51
 */
@Data
@ExcelIgnoreUnannotated
@NoArgsConstructor
@AllArgsConstructor
public class ProductExportDto {
    // 仓库id
    @ExcelIgnore
    private Long id;
    /**
     * 商品名
     */
    @ExcelProperty(value = "商品名")
    private String productName;

    /**
     *商品编号
     */
    @ExcelProperty(value = "SPU")
    private String productCode;

    private Long siteId;
    /**
     * 国家代码
     */
    @ExcelProperty(value = "站点")
    private String countryCode;
    /**
     * Sku唯一编号ItemNo.
     */
    @ExcelProperty(value = "SKU ID")
    private String productSkuCode;

    @ExcelProperty(value = "SKU")
    private String sku;
    /**
     * 货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
    @ExcelProperty(value = "SKU上/下架")
    private String skuShelfState;
    /**
     * 规格值名称
     */
    @ExcelProperty(value = "SKU规格")
    private String specValName;
    /**
     * 库存数量
     */
//    @ExcelProperty(value = "库存(自提)")
    @ExcelIgnore
    private Integer stockTotal;

    /**
     * 自提价
     */
    @ExcelProperty(value = "自提价")
    private String pickUpPrice;

    /**
     * 代发价格
     */
    @ExcelProperty(value = "代发价格")
    private String dropShippingPrice;

    @ExcelProperty(value = "已售")
    private Integer stockSold = 0;
    /**
     * 审核状态
     */
    @ExcelProperty(value = "审核状态")
    private String verifyState;

    @ExcelProperty(value = "仓库名称")
    private String warehouseName;

    @ExcelProperty(value = "可用自提库存")
    private String stockAvailable;

    @ExcelProperty(value = "可用代发库存")
    private String proxyStockTotal;

    @ExcelProperty(value = "已锁定自提库存")
    private String pickupLockUsed;

    @ExcelProperty(value = "已锁定代发库存")
    private String dropShippingLockUsed;

    @ExcelProperty(value = "仓库地址")
    private String warehouseAddress;
    @ExcelProperty(value = "详细地址")
    private String warehouseAddressDetail;
    /**
     * 产品ID,用于排序
     */
    @ExcelIgnore
    private Date productCreateTime;

    @ExcelIgnore
    private Long productSkuId;
    @ExcelIgnore
    private Long productId;
    @ExcelIgnore
    private String supportedLogistics;

    /**
     * 仓库系统代码
     */
    @ExcelIgnore
    private String warehouseSystemCode;


    /**
     * 代发可用库存标识 0单仓,1非单仓(等于自提库存)
     */
    @ExcelIgnore
    private Integer dropShippingStockAvailable;


//    @ExcelProperty(value = "自提价(黄金会员)")
//    private BigDecimal pickUpPrice1;
//
//    @ExcelProperty(value = "代发价格(黄金会员)")
//    private BigDecimal dropPrice1;
//
//    @ExcelProperty(value = "自提价(白银会员)")
//    private BigDecimal pickUpPrice2;
//
//    @ExcelProperty(value = "代发价格(白银会员)")
//    private BigDecimal dropPrice2;
//
//    @ExcelProperty(value = "自提价(青铜会员)")
//    private BigDecimal pickUpPrice3;
//
//    @ExcelProperty(value = "代发价格(青铜会员)")
//    private BigDecimal dropPrice3;

    @ExcelProperty("产品尺寸")
    private String productSize;
    @ExcelProperty("产品重量")
    private String productWeight;
    @ExcelProperty("包裹尺寸")
    private String packageSize;
    @ExcelProperty("包裹重量")
    private String packageWeight;

}
