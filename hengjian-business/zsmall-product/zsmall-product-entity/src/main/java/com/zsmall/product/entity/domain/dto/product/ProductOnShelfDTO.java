package com.zsmall.product.entity.domain.dto.product;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年8月30日
 **/
@Data
public class ProductOnShelfDTO {

    private Long productId;

    private Long productSkuId;

    private String productSku;

    private String productCode;

    private String productName;

    private String pickUpType;

    private List<String> labelName;

    private String price;

    private String dropshippingPrice;

    private String img;

    private Integer stock;

    private String transportMethod;

}
