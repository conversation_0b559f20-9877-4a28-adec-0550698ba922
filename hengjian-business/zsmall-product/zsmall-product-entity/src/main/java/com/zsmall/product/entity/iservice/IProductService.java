package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.product.ProductTypeEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductLabel;
import com.zsmall.product.entity.domain.ProductLabelRelation;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.bo.member.RulePriceQueryBo;
import com.zsmall.product.entity.domain.bo.product.ProductBo;
import com.zsmall.product.entity.domain.bo.product.ProductQueryBo;
import com.zsmall.product.entity.domain.dto.product.ProductOnShelfDTO;
import com.zsmall.product.entity.domain.vo.product.ProductNewestVo;
import com.zsmall.product.entity.domain.vo.productMapping.ImportReadyVo;
import com.zsmall.product.entity.mapper.ProductLabelMapper;
import com.zsmall.product.entity.mapper.ProductLabelRelationMapper;
import com.zsmall.product.entity.mapper.ProductMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品SPU表-数据库层
 *
 * <AUTHOR>
 * @date 2023/6/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IProductService extends ServiceImpl<ProductMapper, Product> {

    private final ProductMapper baseMapper;
    private final ProductLabelMapper productLabelMapper;
    private final ProductLabelRelationMapper productLabelRelationMapper;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductSkuService iProductSkuService;
    public IPage<Product> queryPageList(Page<Product> queryPage, @Param("queryBo") ProductQueryBo queryBo) {
        return baseMapper.queryPageList(queryPage, queryBo);
    }

    /**
     * 通过商品编码获取商品信息（未删除的）
     *
     * @param productCode
     * @return
     */
    public Product queryByProductCodeNotDelete(String productCode) {
        String tenantType = LoginHelper.getTenantType();
        LambdaQueryWrapper<Product> lqw = Wrappers.lambdaQuery();
        lqw.eq(Product::getProductCode, productCode);
        if (TenantType.Manager.name().equals(tenantType)) {
            return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
        } else {
            return baseMapper.selectOne(lqw);
        }
    }

    /**
     * 通过商品编码获取商品信息（未删除的）
     *
     * @param productCode
     * @return
     */
    public Product queryByProductCodeAndProductTypeNotDelete(String productCode, ProductTypeEnum productType) {
        String tenantType = LoginHelper.getTenantType();
        LambdaQueryWrapper<Product> lqw = Wrappers.lambdaQuery();
        lqw.eq(Product::getProductCode, productCode);
        lqw.eq(Product::getProductType, productType);
        if (TenantType.Supplier.name().equals(tenantType)) {
            return baseMapper.selectOne(lqw);
        } else {
            return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
        }
    }

    /**
     * 通过商品编码获取商品信息（包括删除的）
     *
     * @param productCode
     * @return
     */
    public Product queryByProductCode(String productCode) {
        return TenantHelper.ignore(() -> baseMapper.queryByProductSkuCode(productCode), TenantType.Manager, TenantType.Distributor);
    }

    /**
     * 通过商品编码获取商品信息（包括删除的，无视租户）
     *
     * @param productCode
     * @return
     */
    public Product queryByProductCodeNotTenant(String productCode) {
        return TenantHelper.ignore(() -> baseMapper.queryByProductSkuCode(productCode));
    }

    /**
     * 根据商品SKU唯一编号查询商品SPU
     *
     * @param productSkuCode
     * @return
     */
    public Product queryByProductSkuCode(String productSkuCode) {
        log.info("进入【根据商品SKU唯一编号查询商品SPU】方法 productSkuCode = {}", productSkuCode);
        return baseMapper.queryByProductSkuCodeNoDelete(productSkuCode);
    }

    @InMethodLog("根据商品编号集合查询商品SPU集合")
    public List<Product> queryByProductCodesIncludeDel(Collection productCodes) {
        log.info("进入【根据商品SKU唯一编号查询商品SPU】方法 productSkuCode = {}", productCodes);
        return baseMapper.queryByProductCodesIncludeDel(productCodes);
    }

    @InMethodLog("查询支持的物流（商品SKU编号）")
    public SupportedLogisticsEnum querySupportedLogistics(String productSkuCode) {
        return baseMapper.querySupportedLogisticsByProductSkuCode(productSkuCode);
    }

    /**
     * 商品编号是否存在
     *
     * @param productCode
     * @return
     */
    public boolean existProductCode(String productCode) {
        return baseMapper.existProductCode(productCode);
    }

    /**
     * 通过商品编码集合获取商品信息集合
     *
     * @param productCodes
     * @return
     */
    public List<Product> findListByProductCodes(List<String> productCodes) {
        LambdaQueryWrapper<Product> lqw = Wrappers.lambdaQuery();
        lqw.in(Product::getProductCode, productCodes);
        return baseMapper.selectList(lqw);
    }

    /**
     * 翻页查询商品列表（标签绑定）
     *
     * @param queryValue
     * @param labelIdList
     * @param queryPage
     * @return
     */
    public IPage<Product> getProductPage2Label(String queryValue, List<Long> labelIdList, Page<Product> queryPage) {
        log.info("进入【翻页查询商品列表（标签绑定）】方法, queryValue = {}， labelIdList = {}", queryValue, JSONUtil.toJsonStr(labelIdList));
        return TenantHelper.ignore(() -> baseMapper.getProductPage2Label(queryPage, queryValue, labelIdList));
    }

    private LambdaQueryWrapper<Product> buildQueryWrapper(ProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Product> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), Product::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), Product::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), Product::getProductCode, bo.getProductCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductType()), Product::getProductType, bo.getProductType());
        lqw.eq(StringUtils.isNotBlank(bo.getShelfState()), Product::getShelfState, bo.getShelfState());
        lqw.eq(bo.getVerifyTime() != null, Product::getVerifyTime, bo.getVerifyTime());
        lqw.eq(StringUtils.isNotBlank(bo.getVerifyState()), Product::getVerifyState, bo.getVerifyState());
        lqw.eq(StringUtils.isNotBlank(bo.getSupportedLogistics()), Product::getSupportedLogistics, bo.getSupportedLogistics());
        // lqw.eq(StringUtils.isNotBlank(bo.getForbiddenChannel()), Product::getForbiddenChannel, bo.getForbiddenChannel());
        lqw.eq(bo.getBelongCategoryId() != null, Product::getBelongCategoryId, bo.getBelongCategoryId());
        lqw.eq(bo.getDownloadCount() != null, Product::getDownloadCount, bo.getDownloadCount());
        return lqw;
    }

    @InMethodLog("无租户 - 根据商品Id查询商品")
    public Product queryByIdNotTenant(Long productId) {
        return TenantHelper.ignore(() -> baseMapper.selectById(productId));
    }

    @InMethodLog("根据主键集合查询商品集合（包括删除）")
    public List<Product> queryByIdIncludeDel(Long... ids) {
        return TenantHelper.ignore(() -> baseMapper.queryByIdsIncludeDel(ids), TenantType.Distributor, TenantType.Manager);
    }

    @InMethodLog("根据主键查询商品")
    public Product queryById(Long id) {
        return TenantHelper.ignore(() -> baseMapper.selectById(id), TenantType.Distributor, TenantType.Manager);
    }

    @InMethodLog("无租户 - 根据商品Id查询商品（包括已删除的，慎用）")
    public Product queryByIdIncludeDelete(Long productId) {
        return baseMapper.queryByIdIncludeDelete(productId);
    }

    @InMethodLog("查询铺货准备信息")
    public ImportReadyVo queryImportReadyInfo(String disId, String productCode, String channelType) {
        return baseMapper.queryImportReadyInfo(disId, productCode, channelType);
    }



    @InMethodLog("根据商品Sku编号查询商品信息")
    public ProductOnShelfDTO getProductOnShelfDTOByProductSkuCode(String productSkuCode) {
        ProductOnShelfDTO dto = baseMapper.getProductOnShelfDTOByProductSkuCode(productSkuCode);
        if (ObjectUtil.isNotNull(dto)) {
            List<ProductLabel> labelList = getLabelByProductId(dto.getProductId());
            String primaryImg = iProductSkuAttachmentService.getPrimaryImgBySkuId(dto.getProductSkuId());
            if (CollUtil.isNotEmpty(labelList)) {
                List<String> labelNameList = labelList.stream().map(ProductLabel::getLabelName).collect(Collectors.toList());
                dto.setLabelName(labelNameList);
            }
            dto.setImg(primaryImg);
            return dto;
        }
        return null;
    }

    @InMethodLog("根据商品主键查询商品标签")
    private List<ProductLabel> getLabelByProductId(Long productId) {
        LambdaQueryWrapper<ProductLabelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductLabelRelation::getProductId, productId).eq(ProductLabelRelation::getDelFlag, 0);
        List<ProductLabelRelation> relationList = productLabelRelationMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        List<Long> labelIdList = relationList.stream().map(ProductLabelRelation::getLabelId).collect(Collectors.toList());
        return productLabelMapper.selectBatchIds(labelIdList);
    }

    @InMethodLog("分页查询普通商品（用于ES同步）")
    public List<Product> queryNormalProductForES() {
        return baseMapper.queryNormalProductForES();
    }

    @InMethodLog("分页查询最新商品")
    public List<ProductNewestVo> queryNewestProduct(Page<Product> queryPage, Boolean isZjHj) {
        return baseMapper.queryNewestProduct(queryPage,isZjHj);
    }

    /**
     * 根据productSkuCode 更新商品SPU表 库存推送时间
     * @param productSkuCode  Sku唯一编号
     */
    public void updateProductInventoryPushTime(String productSkuCode) {
        baseMapper.updateProductInventoryPushTime(productSkuCode);
    }

    public IPage<Product> queryPageListForMemberLevel(Page<Product> queryPage, RulePriceQueryBo queryBo) {
        return baseMapper.queryPageListForMemberLevel(queryPage, queryBo);
    }

    /**
     * 校验当前产品下面的所有商品是否下架
     * @param productCode
     * @return
     */
    public boolean isAllSkuOffShelfBySpu(String productCode) {
        return baseMapper.isAllSkuOffShelfBySpu(productCode);
    }

    /**
     * 校验当前产品下面的所有商品是否下架
     * @param productSkuCode
     * @return
     */
    public boolean isAllSkuOffShelfBySku(String productSkuCode) {
        LambdaQueryWrapper<ProductSku> q = new LambdaQueryWrapper<>();
        q.eq(ProductSku::getProductSkuCode,productSkuCode);
        List<ProductSku> productSkus = iProductSkuService.getBaseMapper().selectList(q);
        return productSkus.stream()
                          .allMatch(sku -> !ShelfStateEnum.OnShelf.equals(sku.getShelfState()));
    }

    /**
     * 功能描述：按租户id查询列表
     *
     * @param sourceTenantId 源租户id
     * @return {@link List }<{@link Product }>
     * <AUTHOR>
     * @date 2025/08/21
     */
    public List<Product> queryListByTenantId(String sourceTenantId) {
        LambdaQueryWrapper<Product> q = new LambdaQueryWrapper<>();
        q.eq(Product::getTenantId,sourceTenantId);
        q.eq(Product::getDelFlag,0);
        return TenantHelper.ignore(()->baseMapper.selectList(q));
    }


}
