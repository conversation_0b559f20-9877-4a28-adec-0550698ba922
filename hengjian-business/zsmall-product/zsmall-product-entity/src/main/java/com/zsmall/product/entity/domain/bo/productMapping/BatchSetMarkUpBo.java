package com.zsmall.product.entity.domain.bo.productMapping;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 请求参数-批量设置Mark UP
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "请求参数-批量设置MarkUP")
public class BatchSetMarkUpBo {

    /**
     * 涨价类型
     */
    @Schema(title = "涨价类型")
    private String markUpType;

    /**
     * 涨价值
     */
    private BigDecimal markUpValue;

    /**
     * 商品映射信息id集合
     */
    private List<Long> idList;

}
