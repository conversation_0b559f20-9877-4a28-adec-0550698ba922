package com.zsmall.product.entity.domain.bo.wholesale;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductWholesaleTieredPriceLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 国外现货批发商品阶梯价日志业务对象 product_wholesale_tiered_price_log
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductWholesaleTieredPriceLog.class, reverseConvertGenerate = false)
public class ProductWholesaleTieredPriceLogBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品主键
     */
    @NotNull(message = "商品主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 商品阶梯价主表id
     */
    @NotNull(message = "商品阶梯价主表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productWholesaleTieredPriceId;

    /**
     * 操作人编号
     */
    @NotBlank(message = "操作人编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String handleUser;

    /**
     * 阶梯价-最小数量（包含）
     */
    @NotNull(message = "阶梯价-最小数量（包含）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long minimumQuantity;

    /**
     * 预估操作费
     */
    @NotNull(message = "预估操作费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal estimatedOperationFee;

    /**
     * 预估运费
     */
    @NotNull(message = "预估运费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal estimatedShippingFee;

    /**
     * 预估处理时间（天）
     */
    @NotNull(message = "预估处理时间（天）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long estimatedHandleTime;


}
