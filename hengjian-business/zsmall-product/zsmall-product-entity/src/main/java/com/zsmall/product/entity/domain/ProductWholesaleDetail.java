package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 国外现货批发商品详情对象 product_wholesale_detail
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_wholesale_detail", autoResultMap = true)
public class ProductWholesaleDetail extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品主键
     */
    private Long productId;

    /**
     * 最小起订数量
     */
    private Integer minimumQuantity;

    /**
     * 订金比例
     */
    private BigDecimal depositRatio;

    /**
     * 预留时间（天）
     */
    private Integer reservedTime;

    /**
     * 发货方式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray deliveryType;

    /**
     * 仓库编码
     */
    private String warehouseSystemCode;

    /**
     * 物流模板编号
     */
    private String logisticsTemplateNo;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
