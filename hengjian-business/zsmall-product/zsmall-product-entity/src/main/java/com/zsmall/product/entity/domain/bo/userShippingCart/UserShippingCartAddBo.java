package com.zsmall.product.entity.domain.bo.userShippingCart;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 请求体-用户购物车添加商品
 *
 * <AUTHOR>
 * @date 2023/9/22
 */
@Data
public class UserShippingCartAddBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Sku唯一编号（ItemNo.）
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String productSkuCode;

    /**
     * 数量
     */
    @NotNull(message = ValidationMessage.API_REQUIRED)
    private Integer quantity;
    /**
     * 物流方式
     */
    private String supportedLogistics;

}
