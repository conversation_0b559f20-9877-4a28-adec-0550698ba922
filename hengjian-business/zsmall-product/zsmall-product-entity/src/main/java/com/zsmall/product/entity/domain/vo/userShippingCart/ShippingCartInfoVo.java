package com.zsmall.product.entity.domain.vo.userShippingCart;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 响应体-购物车数据
 *
 * <AUTHOR>
 * @date 2023/9/20
 */
@Data
public class ShippingCartInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long siteId;

    /**
     * productSkuId
     */
    private Long productSkuId;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 商品类型
     */
    private String productType;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;
    /**
     * Sku
     */
    private String sku;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 图片URL
     */
    private String imageShowUrl;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 会员价格
     */
    private BigDecimal memberPrice;

    /**
     * 代发价格
     */
    private BigDecimal dropPrice;

    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;

    /**
     * 商品是否可用（下架或删除后，该字段为false，可以正常下单时为true）
     */
    private Boolean available;
    /**
     * 支持的物流方式
     */
    private String supportedLogistics;
    /**
     * 自提仓库库存总数
     */
    private Integer pickUpStockTotal;
    /**
     * 代发仓库库存总数
     */
    private Integer dropShippingStockTotal;
}
