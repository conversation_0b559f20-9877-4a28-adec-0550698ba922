package com.zsmall.product.entity.domain.bo.productMapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-sku映射
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SkuMappingBo {

    /**
     * 分销商品映射信息ID
     */
    private String id;

    /**
     * 映射sku
     */
    private String mappingSku;

    private String channelType;
    /**
     * 仓库配置
     */
    private List<FulfillWarehouseBo> warehouseConfigs;

}
