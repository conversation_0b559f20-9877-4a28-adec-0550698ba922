package com.zsmall.product.entity.domain.dto.productImport;

import lombok.Data;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/7/16 15:28
 */
@Data
public class LevelPriceParseV2DTO {
    private String levelName;

    private String dictCode;

//    //    @ExcelProperty(value = "自提价")
//    private BigDecimal pickUpPrice;
//
//    //    @ExcelProperty(value = "代发价格")
//    private BigDecimal dropPrice;


    private BigDecimal unitPrice;

    private BigDecimal operationFee;

    private BigDecimal finalDeliveryFee;
}
