package com.zsmall.product.entity.domain.vo.category;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.ProductCategoryRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品SPU-商品分类关联视图对象 product_category_relation
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductCategoryRelation.class)
public class ProductCategoryRelationVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品SPU表主键
     */
    @ExcelProperty(value = "商品SPU表主键")
    private Long productId;

    /**
     * 商品分类表主键
     */
    @ExcelProperty(value = "商品分类表主键")
    private Long productCategoryId;


}
