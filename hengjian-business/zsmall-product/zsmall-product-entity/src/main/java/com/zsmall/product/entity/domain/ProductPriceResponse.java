package com.zsmall.product.entity.domain;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductPriceResponse {
    /**
     * 供应商单价
     */
    private BigDecimal supplierUnitPrice;

    /**
     * 供应商会员单价
     */
    private BigDecimal supplierMemberUnitPrice;
    /**
     * 分销商单价
     */
    private BigDecimal distributorUnitPrice;
    /**
     * 分销商会员单价
     */
    private BigDecimal distributorMemberUnitPrice;


    /**
     * 分销商操作费
     */
    private BigDecimal supplierOperationFee;

    /**
     * 分销商会员操作费
     */
    private BigDecimal supplierMemberOperationFee;

    /**
     * 供应商操作费
     */
    private BigDecimal distributorOperationFee;

    /**
     * 供应商会员操作费
     */
    private BigDecimal distributorMemberOperationFee;

    /**
     * 尾程派送费
     */
    private BigDecimal deliveryFee;

    /**
     *供应商代发价
     */
    private BigDecimal supplierDropShippingPrice;

    /**
     * 供应商会员代发价
     */
    private BigDecimal supplierMemberDropShippingPrice;
    /**
     *分销商代发价
     */
    private BigDecimal distributorDropShippingPrice;

    /**
     * 分销商会员代发价
     */
    private BigDecimal distributorMemberDropShippingPrice;

    /**
     *供应商自提价
     */
    private BigDecimal supplierPickUpPrice;

    /**
     * 供应商会员自提价
     */
    private BigDecimal supplierMemberPickUpPrice;
    /**
     *分销商自提价
     */
    private BigDecimal distributorPickUpPrice;

    /**
     * 分销商会员自提价
     */
    private BigDecimal distributorMemberPickUpPrice;


    /**
     * 商品编码
     */
    private String productSkuCode;
    /**
     * sku
     */
    private String sku;
    /**
     * 是否会员
     */
    private Boolean isMember;
}
