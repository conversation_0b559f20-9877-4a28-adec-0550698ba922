package com.zsmall.product.entity.domain.dto.productSku;

import com.zsmall.common.domain.vo.AttachmentDto;
import com.zsmall.common.domain.vo.RespProductActivitySelectBody;
import com.zsmall.common.domain.vo.SkuAttributesValueBody;
import com.zsmall.product.entity.domain.bo.productSku.ProductSkuReviewBo;
import com.zsmall.product.entity.domain.dto.product.LogisticsMethodBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 通用参数-商品sku信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-商品sku信息")
public class ProductSkuBody {


    @Schema(title = "id")
    private Long id;

    @Schema(title = "商品价格站点信息")
    private List<ProductSkuReviewBo> productSkuReviewBos;

    @Schema(title = "商品编号")
    private String productCode;

    @Schema(title = "skuCode")
    private String skuCode;

    @Schema(title = "映射sku")
    private String mappingSku;

    @Schema(title = "itemNo")
    private String itemNo;

    @Schema(title = "商品名称")
    private String name;

    @Schema(title = "商品统一代码")
    private String upc;

    @Schema(title = "erpSku集合")
    private List<String> erpSku;

    @Schema(title = "规格属性值集合")
    private List<String> specValueList;

    @Schema(title = "规格属性值集合")
    private List<SkuAttributesValueBody> skuSpecValueList;

    @Schema(title = "数量")
    private Object quantity;

    @Schema(title = "价格")
    private BigDecimal dropShippingPrice;

    @Schema(title = "销量")
    private Integer sold;

    @Schema(title = "总价？")
    private BigDecimal total;

    @Schema(title = "收入")
    private BigDecimal revenue;

    @Schema(title = "上/下架状态（分销商渠道sku的上下架状态表示显示和隐藏）")
    private String shelfState;

    @Schema(title = "有效状态")
    private String statusType;

    @Schema(title = "重量")
    private BigDecimal weight;

    @Schema(title = "长")
    private BigDecimal length;

    @Schema(title = "宽")
    private BigDecimal width;

    @Schema(title = "高")
    private BigDecimal height;

    @Schema(title = "重量单位")
    private String weightUnit;

    @Schema(title = "长度单位")
    private String lengthUnit;

    @Schema(title = "打包重量")
    private BigDecimal packWeight;

    @Schema(title = "打包长")
    private BigDecimal packLength;

    @Schema(title = "打包宽")
    private BigDecimal packWidth;

    @Schema(title = "打包高")
    private BigDecimal packHeight;

    @Schema(title = "打包重量单位")
    private String packWeightUnit;

    @Schema(title = "打包长度单位")
    private String packLengthUnit;

    @Schema(title = "打包尺寸重量和商品尺寸重量相同")
    private Boolean samePacking = true;

    @Schema(title = "涨价类型")
    private String markUpType;

    @Schema(title = "涨价值")
    private BigDecimal markUpValue;

    @Schema(title = "运费")
    private BigDecimal shippingCost;

    @Schema(title = "notForSale")
    private Boolean notForSale;

    @Schema(title = "锁货每天的仓库管理费用")
    private BigDecimal warehouseFee;

    @Schema(title = "是否自有库存（可以修改数量，非自有库存不能修改数量）")
    private Boolean ownInventory;

    @Schema(title = "渠道同步状态")
    private String syncStatus;

    @Schema(title = "渠道同步响应信息")
    private String syncResultInfo;

    @Schema(title = "选中的履约方式")
    private String logisticsMethod;

    @Schema(title = "选中的履约方式价格")
    private BigDecimal logisticsMethodPrice;

    @Schema(title = "图片展示地址")
    private String imageShowUrl;

    @Schema(title = "商店名")
    private String storeName;

    @Schema(title = "建议零售价（二期新增，商品增改时使用）")
    private BigDecimal msrp = BigDecimal.ZERO;

    @Schema(title = "物流模板编号（二期新增，商品增改时使用）")
    private String logisticsTemplateNo;

    @Schema(title = "自提价格（二期新增，商品增改时使用）")
    private BigDecimal pickUpPrice = BigDecimal.ZERO;

    @Schema(title = "SKU附件（二期新增，商品增改时使用）")
    private List<AttachmentDto> attachmentList;

    @Schema(title = "库存管理方国籍")
    private String inventoryManagerCountry;

    @Schema(title = "库存管理方")
    private String inventoryManager;

    @Schema(title = "运输方式")
    private String transportMethod;

    @Schema(title = "审核状态")
    private String verifyType;

//    @Schema(title = "改动前代发价格")
//    private BigDecimal before_dropShippingPrice;
//
//    @Schema(title = "改动后代发价格")
//    private BigDecimal after_dropShippingPrice;
//
//    @Schema(title = "改动前自提价格")
//    private BigDecimal before_pickUpPrice;
//
//    @Schema(title = "改动后自提价格")
//    private BigDecimal after_pickUpPrice;
//
//    @Schema(title = "改动前建议零售价")
//    private BigDecimal before_msrp;
//
//    @Schema(title = "改动后建议零售价")
//    private BigDecimal after_msrp;
//
//    @Schema(title = "改动前产品单价")
//    private BigDecimal before_unitPrice;
//
//    @Schema(title = "改动后产品单价")
//    private BigDecimal after_unitPrice;
//
//    @Schema(title = "改动前操作费")
//    private BigDecimal before_operationFee;
//
//    @Schema(title = "改动后操作费")
//    private BigDecimal after_operationFee;
//
//    @Schema(title = "改动前尾程配送费")
//    private BigDecimal before_finalDeliveryFee;
//
//    @Schema(title = "改动后尾程派送费")
//    private BigDecimal after_finalDeliveryFee;



    @Schema(title = "可选履约方式集合")
    private List<LogisticsMethodBody> LogisticsMethodList;

    @Schema(title = "参与的活动编号")
    private String activityCode;

    @Schema(title = "存在价格审核")
    private boolean hasPriceChange = false;

    @Schema(title = "可选活动集合")
    private List<RespProductActivitySelectBody> activityList;

    @Schema(title = "库存配置")
    private List<ProductSkuInventoryBody> inventoryConfigs;

    @Schema(title = "商品单价")
    private BigDecimal unitPrice;

    @Schema(title = "操作费")
    private BigDecimal operationFee;

    @Schema(title = "尾程派送费")
    private BigDecimal finalDeliveryFee;

}
