package com.zsmall.product.entity.domain.vo.member;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/17 10:04
 */
@Data
public class MemberSkuPriceVo implements Serializable {


    private String ruleCustomizerTenantId;

    @ApiModelProperty(value="skuId",required = true)
    @NotNull(message = "产品skuID不能为空",groups = {EditGroup.class, AddGroup.class})
    private Long productSkuId;

    @ApiModelProperty(value="商品id",required = true)
    @NotNull(message = "商品id不能为空",groups = {EditGroup.class, AddGroup.class})
    private Long productId;



    @ApiModelProperty("商品会员等级价格列表")
    private List<MemberLevelPriceVo> memberLevelPriceListVo;

    public List<MemberLevelPriceVo> pushSaleOrderItemsList(List<MemberLevelPriceVo> memberLevelPriceListVo) {
        this.memberLevelPriceListVo = memberLevelPriceListVo;
        return this.memberLevelPriceListVo;
    }}
