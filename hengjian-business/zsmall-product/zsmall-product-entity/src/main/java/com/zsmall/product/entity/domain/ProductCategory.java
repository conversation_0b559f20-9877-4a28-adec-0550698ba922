package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 商品分类对象 product_category
 *
 * <AUTHOR> Li
 * @date 2023-05-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_category", autoResultMap = true)
public class ProductCategory extends SortEntity implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 父级分类主键
     */
    private Long parentId;

    /**
     * 类目等级
     */
    private Integer categoryLevel;

    /**
     * 分类名称（主名称）
     */
    private String categoryName;

    /**
     * 分类名称（英文名称）
     */
    private String categoryEnglishName;

    /**
     * 分类其他语种名称（格式：key-语种，value-对应语种分类名）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject categoryOtherName;

    /**
     * 分类排序号
     */
    private Integer categorySort;

    /**
     * 大图存储桶Id
     */
    private Long imageOssId;
    /**
     * 图标存储桶Id
     */
    private Long iconOssId;

    /**
     * 分类大图存储路径
     */
    private String categoryImageSavePath;

    /**
     * 分类大图展示地址
     */
    private String categoryImageShowUrl;

    /**
     * 分类图标存储路径
     */
    private String categoryIconSavePath;

    /**
     * 分类图标展示地址
     */
    private String categoryIconShowUrl;

    /**
     * 分类状态（0-停用，1-启用等）
     */
    private Integer categoryState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
