package com.zsmall.product.entity.domain.dto.price;

import lombok.Data;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/24 10:35
 */
@Data
public class PriceDTO {
    private BigDecimal platformPickUpPrice;
    private BigDecimal platformDropShippingPrice;
    /**
     * 订单商品代发单价
     */
    private BigDecimal orderDropShippingPrice;
    /**
     * 订单商品代发总价
     */
    private BigDecimal dropShippingTotalAmount;
    /**
     * 订单商品自提总价
     */
    private BigDecimal pickUpTotalAmount;
}
