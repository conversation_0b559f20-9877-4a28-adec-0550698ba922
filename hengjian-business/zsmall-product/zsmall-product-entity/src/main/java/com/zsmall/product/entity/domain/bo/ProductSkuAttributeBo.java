package com.zsmall.product.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.ProductSkuAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品SKU属性业务对象 product_sku_attribute
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuAttribute.class, reverseConvertGenerate = false)
public class ProductSkuAttributeBo extends SortEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品SKU表主键
     */
    @NotNull(message = "商品SKU表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productSkuId;

    /**
     * 商品SPU属性表主键
     */
    @NotNull(message = "商品SPU属性表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productAttributeId;

    /**
     * 属性类型
     */
    @NotBlank(message = "属性类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeType;

    /**
     * 属性名
     */
    @NotBlank(message = "属性名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeName;

    /**
     * 属性值
     */
    @NotBlank(message = "属性值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeValue;

    /**
     * 属性排序
     */
    @NotNull(message = "属性排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer attributeSort;

    /**
     * 属性来源id
     */
    @NotNull(message = "属性来源id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long attributeSourceId;


}
