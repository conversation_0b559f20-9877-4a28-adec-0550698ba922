package com.zsmall.product.entity.domain.vo.wholesale;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/23 18:43
 */
@Data
@EqualsAndHashCode(callSuper=false)
@Schema(name = "响应信息-国外批发商品")
public class WholesaleProductInfoVo extends WholesaleProductBo {

  @Schema(title  = "变体维度集合")
  private List<WholesaleVariant> variantList;


  /**
   * 变体
   */
  @Data
  public static class WholesaleVariant {

    private String attributeName;
    private List<String> attributeValues;



  }



}
