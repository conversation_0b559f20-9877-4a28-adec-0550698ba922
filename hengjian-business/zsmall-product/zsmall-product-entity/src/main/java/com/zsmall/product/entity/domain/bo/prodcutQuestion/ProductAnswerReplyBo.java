package com.zsmall.product.entity.domain.bo.prodcutQuestion;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 新增回复
 */
@Data
@EqualsAndHashCode
public class ProductAnswerReplyBo {

    /**
     * 问题编码
     */
    @NotBlank(message = "{zsmall.productQA.questionCodeIsBlank}", groups = { AddGroup.class, EditGroup.class })
    private String questionCode;

    /**
     * 回复内容
     */
    @NotBlank(message = "{zsmall.productQA.textAnswersError}", groups = { AddGroup.class, EditGroup.class })
    private String answer;

}
