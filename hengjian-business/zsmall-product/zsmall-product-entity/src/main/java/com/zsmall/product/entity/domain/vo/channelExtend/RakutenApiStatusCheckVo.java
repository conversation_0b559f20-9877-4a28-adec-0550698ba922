package com.zsmall.product.entity.domain.vo.channelExtend;

import lombok.Data;

import java.io.Serializable;


/**
 * 响应体-乐天Api状态检查
 */
@Data
public class RakutenApiStatusCheckVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件柜-文件录入（Enable-已启用，Disabled-未启用，Unknown-未知）
     */
    private String cabinetFileInsert;

    /**
     * 库存-录入/更新（Enable-已启用，Disabled-未启用，Unknown-未知）
     */
    private String inventoryVariantsUpsert;

    /**
     * 商品-录入/更新（Enable-已启用，Disabled-未启用，Unknown-未知）
     */
    private String itemsUpsert;

    /**
     * 商品-删除（Enable-已启用，Disabled-未启用，Unknown-未知）
     */
    private String itemsDelete;

    /**
     * 订单-检索订单（Enable-已启用，Disabled-未启用，Unknown-未知）
     */
    private String orderSearchOrder;

    /**
     * 订单-获取订单详情（Enable-已启用，Disabled-未启用，Unknown-未知）
     */
    private String orderGetOrder;

    /**
     * 订单-更新订单发货状态（Enable-已启用，Disabled-未启用，Unknown-未知）
     */
    private String orderUpdateOrderShipping;

}
