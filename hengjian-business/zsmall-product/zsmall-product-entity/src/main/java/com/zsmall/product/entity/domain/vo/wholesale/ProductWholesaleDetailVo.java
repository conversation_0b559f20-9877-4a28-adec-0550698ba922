package com.zsmall.product.entity.domain.vo.wholesale;

import cn.hutool.json.JSONArray;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductWholesaleDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 国外现货批发商品详情视图对象 product_wholesale_detail
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductWholesaleDetail.class)
public class ProductWholesaleDetailVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品主键
     */
    @ExcelProperty(value = "商品主键")
    private Long productId;

    /**
     * 最小起订数量
     */
    @ExcelProperty(value = "最小起订数量")
    private Integer minimumQuantity;

    /**
     * 订金比例
     */
    @ExcelProperty(value = "订金比例")
    private BigDecimal depositRatio;

    /**
     * 预留时间（天）
     */
    @ExcelProperty(value = "预留时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "天=")
    private Integer reservedTime;

    /**
     * 发货方式
     */
    @ExcelProperty(value = "发货方式")
    private JSONArray deliveryType;

    /**
     * 仓库编码
     */
    @ExcelProperty(value = "仓库编码")
    private String warehouseSystemCode;

    /**
     * 物流模板编号
     */
    @ExcelProperty(value = "物流模板编号")
    private String logisticsTemplateNo;


}
