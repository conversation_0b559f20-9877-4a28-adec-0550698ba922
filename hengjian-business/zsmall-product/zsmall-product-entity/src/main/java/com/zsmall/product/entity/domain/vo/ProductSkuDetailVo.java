package com.zsmall.product.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductSkuDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 商品SKU详情视图对象 product_sku_detail
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductSkuDetail.class)
public class ProductSkuDetailVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品SKU表主键
     */
    @ExcelProperty(value = "商品SKU表主键")
    private Long productSkuId;

    /**
     * 长
     */
    @ExcelProperty(value = "长")
    private BigDecimal length;

    /**
     * 宽
     */
    @ExcelProperty(value = "宽")
    private BigDecimal width;

    /**
     * 高
     */
    @ExcelProperty(value = "高")
    private BigDecimal height;

    /**
     * 长度单位
     */
    @ExcelProperty(value = "长度单位")
    private String lengthUnit;

    /**
     * 重量
     */
    @ExcelProperty(value = "重量")
    private BigDecimal weight;

    /**
     * 重量单位
     */
    @ExcelProperty(value = "重量单位")
    private String weightUnit;

    /**
     * 打包长
     */
    @ExcelProperty(value = "打包长")
    private BigDecimal packLength;

    /**
     * 打包宽
     */
    @ExcelProperty(value = "打包宽")
    private BigDecimal packWidth;

    /**
     * 打包高
     */
    @ExcelProperty(value = "打包高")
    private BigDecimal packHeight;

    /**
     * 打包长度单位
     */
    @ExcelProperty(value = "打包长度单位")
    private String packLengthUnit;

    /**
     * 打包重量
     */
    @ExcelProperty(value = "打包重量")
    private BigDecimal packWeight;

    /**
     * 打包重量单位
     */
    @ExcelProperty(value = "打包重量单位")
    private String packWeightUnit;

    /**
     * 打包尺寸重量与商品尺寸重量相同：0-否，1-是
     */
    @ExcelProperty(value = "打包尺寸重量与商品尺寸重量相同：0-否，1-是")
    private boolean samePacking;

    /**
     * 处理时效（天）
     */
    @ExcelProperty(value = "处理时效", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "天=")
    private BigDecimal processingTime;

    /**
     * 运输方式
     */
    @ExcelProperty(value = "运输方式")
    private String transportMethod;

    /**
     * SKU描述
     */
    @ExcelProperty(value = "SKU描述")
    private String description;





}
