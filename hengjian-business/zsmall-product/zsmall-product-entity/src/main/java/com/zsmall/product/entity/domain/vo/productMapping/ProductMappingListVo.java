package com.zsmall.product.entity.domain.vo.productMapping;

import com.hengjian.common.mybatis.core.page.TableDataInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 响应体-商品映射列表
 *
 * <AUTHOR>
 * @date 2023/7/5
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ProductMappingListVo extends TableDataInfo<ProductMappingTableVo> {

    /**
     * 仓库映射配置
     */
    private Map<String, SkuWarehouseMappingVo> warehouseConfigs;

}
