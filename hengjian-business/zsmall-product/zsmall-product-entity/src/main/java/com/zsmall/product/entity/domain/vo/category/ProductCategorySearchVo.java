package com.zsmall.product.entity.domain.vo.category;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Access;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年9月23日  17:36
 * @description: 类目搜索返回VO
 */
@Data
@Accessors(chain = true)
public class ProductCategorySearchVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 父级分类主键
     */
    private Long parentId;

    /**
     * 分类名称（主名称）
     */
    private String categoryName;

    /**
     * 分类其他语种名称（格式：key-语种，value-对应语种分类名）
     */
    private JSONObject categoryOtherName;

    /**
     * 子级分类
     */
    private List<ProductCategorySearchVo> childrenList;

    private String title;

    private List<Long> idList;

    private String ids;

    public ProductCategorySearchVo() {}

    public ProductCategorySearchVo(Long id, Long parentId, String categoryName) {
        this.id = id;
        this.parentId = parentId;
        this.categoryName = categoryName;
    }

    public ProductCategorySearchVo(String title, List<Long> idList,String ids) {
        this.title = title;
        this.idList = idList;
        this.ids = ids;
    }

    public ProductCategorySearchVo(String title, String ids) {
        this.title = title;
        this.ids = ids;
    }
}
