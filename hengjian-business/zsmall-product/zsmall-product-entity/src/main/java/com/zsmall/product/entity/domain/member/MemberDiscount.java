package com.zsmall.product.entity.domain.member;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 会员等级折扣对象 member_discount
 *
 * <AUTHOR> Li
 * @date 2024-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_discount")
public class MemberDiscount extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字典表等级相关code
     */
    private Long dictCode;

    /**
     * 会员等级名称
     */
    private String memberName;

    /**
     * 折扣系数(用于会员算价)
     */
    private Integer memberDiscount;
    /**
     * 禁用前折扣系数(用于前端显示)
     */
    private Integer beforeMemberDiscount;

    /**
     * 状态
     */
    private Integer memberState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;


}
