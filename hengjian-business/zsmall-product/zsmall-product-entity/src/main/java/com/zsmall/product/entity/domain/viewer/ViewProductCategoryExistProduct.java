package com.zsmall.product.entity.domain.viewer;

import lombok.Data;

import java.io.Serializable;

/**
 * 存在有效商品的分类视图
 *
 * <AUTHOR>
 * @create 2021/10/22 17:45
 */
@Data
public class ViewProductCategoryExistProduct implements Serializable {

    private Long id;

    private String name;

    private Integer level;

    private Long parentId;

    private String showUrl;

    private Integer sort;

    private Integer categoryState;

    private String channelType;

    private Integer childNum;

}
