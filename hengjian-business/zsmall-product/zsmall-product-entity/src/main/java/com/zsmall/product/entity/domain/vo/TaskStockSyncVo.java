package com.zsmall.product.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.TaskStockSync;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 任务-库存同步视图对象 task_stock_sync
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TaskStockSync.class)
public class TaskStockSyncVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 来源sku_id
     */
    @ExcelProperty(value = "来源sku_id")
    private Long productSkuId;

    /**
     * 任务状态
     */
    @ExcelProperty(value = "任务状态")
    private String taskState;

    /**
     * 任务执行信息
     */
    @ExcelProperty(value = "任务执行信息")
    private String taskExecuteMessage;


}
