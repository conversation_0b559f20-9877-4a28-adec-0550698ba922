package com.zsmall.product.entity.domain.bo.productQuestions;

/**
 * <AUTHOR>
 * @date 2022/1/8
 **/

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "编辑回复请求参数")
public class ReqAnswerReplyUpBody {

    @Schema(title = "回复code")
    private String answerCode;

    @Schema(title = "回复内容")
    private String answer;

}
