package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 商品渠道管控对象 product_channel_control
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_channel_control",autoResultMap = true)
public class ProductChannelControl extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 受允许的租户编号（JSON数组）
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONArray allowTenantId;

    /**
     * 是否全渠道管控
     */
    private Boolean allChannelControl;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
