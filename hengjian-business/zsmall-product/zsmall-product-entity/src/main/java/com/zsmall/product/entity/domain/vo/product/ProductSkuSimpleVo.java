package com.zsmall.product.entity.domain.vo.product;

import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品SKU简单信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuSimpleVo {

    /**
     * 商品SPU唯一编号
     */
    private String productCode;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String itemNo;

    /**
     * 名称
     */
    private String name;

    /**
     * 最小库存单位（Sku）
     */
    private String sku;

    /**
     * 是否已被收藏
     */
    private Boolean favorites;

    /**
     * 图片URL
     */
    private String imageShowUrl;

    /**
     * 商品主图
     */
    ProductSkuAttachmentVo productSkuAttachment;

    public ProductSkuSimpleVo(String productCode, String itemNo, String name, String sku) {
        this.productCode = productCode;
        this.itemNo = itemNo;
        this.name = name;
        this.sku = sku;
    }
}
