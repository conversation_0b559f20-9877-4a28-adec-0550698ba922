package com.zsmall.product.entity.domain.vo.productChannelControl;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 响应体-商品渠道管控列表信息
 * <AUTHOR>
 * @create 2022/5/3 22:55
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ProductChannelControlListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品图片
     */
    private String productImg;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 商品ItemNo
     */
    private String productSkuCode;

    /**
     * 商品sku
     */
    private String sku;

    /**
     * 是否全渠道管控
     */
    private Boolean allChannelControl = false;

    /**
     * 全渠道管控信息（若商品是全渠道管控则会使用改字段，若是各渠道单独管控则使用下面的controlInfoList数组）
     */
    private ProductChannelControlBase allChannelControlInfo;

    /**
     * 管控信息集合
     */
    private List<ProductChannelControlBase> controlInfoList;

}
