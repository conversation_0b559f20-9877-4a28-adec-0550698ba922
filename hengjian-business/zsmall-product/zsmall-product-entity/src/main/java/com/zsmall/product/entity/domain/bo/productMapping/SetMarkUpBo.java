package com.zsmall.product.entity.domain.bo.productMapping;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 请求参数-设置商品SKU mark up
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SetMarkUpBo {

    /**
     * 分销商映射信息id
     */
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 基础价格（成本价）
     */
    private BigDecimal basePrice;

    /**
     * 涨价类型
     */
    private String markUpType;

    /**
     * 涨价值
     */
    private BigDecimal markUpValue;

    /**
     * 前端计算的总价
     */
    private BigDecimal markUpTotal;

    /**
     * 选中的活动
     */
    private String activityCode;

    /**
     * 当前选中的乐天品类（Rakuten渠道专属）
     */
    private String curRakutenGenreId;

}
