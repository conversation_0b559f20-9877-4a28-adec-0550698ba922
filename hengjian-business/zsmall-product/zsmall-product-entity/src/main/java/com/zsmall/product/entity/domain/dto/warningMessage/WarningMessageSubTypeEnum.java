package com.zsmall.product.entity.domain.dto.warningMessage;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 预警消息子类型枚举
 *
 * <AUTHOR> Assistant
 * @date 2024-12-27
 */
public  enum WarningMessageSubTypeEnum implements IEnum<Integer> {

    /**
     * SKU库存更新提示
     */
    SKU_HAVE_STOCK(1),
    /**
     * SKU缺货提示
     */
    SKU_NOT_HAVE_STOCK(2);

    private Integer code;

    WarningMessageSubTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return this.code;
    }
}
