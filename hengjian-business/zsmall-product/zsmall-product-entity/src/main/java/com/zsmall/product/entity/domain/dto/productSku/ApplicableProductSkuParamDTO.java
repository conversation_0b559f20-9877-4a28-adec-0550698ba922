package com.zsmall.product.entity.domain.dto.productSku;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 获取使用产品参数实体
 *
 * <AUTHOR>
 * @date 2022/10/26 11:59
 */
@Data
public class ApplicableProductSkuParamDTO {

    /**
     * 公式编码
     */
    private String ruleCode;

    /**
     * 筛选类型
     */
    private String type;

    /**
     * 筛选类型
     */
    private String queryValue;

    /**
     * 供应商Id
     */
    private String userCode;

    /**
     * sku
     */
    private List<String> sku;

    /**
     * 类目
     */
    private String categoryIdStr;

    /**
     * 价格区间 - 开始价格
     */
    private BigDecimal startPrice;

    /**
     * 价格区间 - 结束价格
     */
    private BigDecimal endPrice;

    /**
     * 时间区间 - 开始时间
     */
    private String startTime;

    /**
     * 时间区间 - 结束时间
     */
    private String endTime;

    /**
     * itemNo集合
     */
    private List<String> itemNos;

    /**
     * 设置适用产品中删除掉的itemNo集合
     */
    private List<String> deleteItemNoList;

    /**
     * 商品价格id集合
     */
    private List<Long> productSkuPriceIds;
}
