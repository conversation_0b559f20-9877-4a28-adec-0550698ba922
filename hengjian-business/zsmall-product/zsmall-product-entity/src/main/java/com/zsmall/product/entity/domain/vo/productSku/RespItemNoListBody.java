package com.zsmall.product.entity.domain.vo.productSku;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 通用参数-商品分类信息集合
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-确认ItemNo是否上架")
public class RespItemNoListBody {

    @Schema(title = "商品ItemNo状态")
    private Boolean itemNoStatus;

    @Schema(title = "itemNo")
    private String itemNo;

}
