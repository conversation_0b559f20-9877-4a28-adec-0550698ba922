package com.zsmall.product.entity.domain.vo.product;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 响应体-商品资料包信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ProductPackageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 可选SKU
     */
    private List<ProductSkuPackageVo> skuList;

}
