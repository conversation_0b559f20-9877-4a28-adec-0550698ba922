package com.zsmall.product.entity.domain.vo.productSkuPrice;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 商品sku价格计算公式视图对象 product_sku_price_rule_item
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductSkuPriceRuleItem.class)
public class ProductSkuPriceRuleItemVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 计算公式表id
     */
    @ExcelProperty(value = "计算公式表id")
    private Long productSkuPriceRuleId;

    /**
     * 具体的价格类型(单价、操作费和尾程派送费)
     */
    @ExcelProperty(value = "具体的价格类型(单价、操作费和尾程派送费)")
    private String priceItemType;

    /**
     * 提价计算方式（1-加，2-减，3-乘，4-除）
     */
    @ExcelProperty(value = "提价计算方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-加，2-减，3-乘，4-除")
    private Integer priceCal;

    /**
     * 提价的值
     */
    @ExcelProperty(value = "提价的值")
    private BigDecimal priceCalValue;


    public ProductSkuPriceRuleItemVo(Long productSkuPriceRuleId, String priceItemType, Integer priceCal, BigDecimal priceCalValue) {
        this.productSkuPriceRuleId = productSkuPriceRuleId;
        this.priceItemType = priceItemType;
        this.priceCal = priceCal;
        this.priceCalValue = priceCalValue;
    }
}
