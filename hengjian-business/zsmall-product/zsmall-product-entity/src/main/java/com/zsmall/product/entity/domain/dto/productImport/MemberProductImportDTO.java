package com.zsmall.product.entity.domain.dto.productImport;

import com.zsmall.common.annotaion.ExcelFieldAnnotation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/7/5 14:03
 */
@Data
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class MemberProductImportDTO {
    @ExcelFieldAnnotation(required = true)
    private String group;
    private String category;
    private String productName;
    private String supportedLogistics;
    private String forbiddenChannel;

    @ExcelFieldAnnotation(required = true)
    private String sku;
    private String erpSku;
    private String upc;
    private String optionalSpecName1;
    @ExcelFieldAnnotation(required = true)
    private String optionalSpecValue1;
    private String optionalSpecName2;
    private String optionalSpecValue2;
    @ExcelFieldAnnotation(required = true)
    private String warehouseSystemCode;
    private String logisticsTemplateName;
    @ExcelFieldAnnotation(required = true)
    private Integer quantity;
    private BigDecimal packLength;
    private BigDecimal packWidth;
    private BigDecimal packHeight;
    private BigDecimal packWeight;
    @ExcelFieldAnnotation(required = true)
    private BigDecimal length;
    @ExcelFieldAnnotation(required = true)
    private BigDecimal width;
    @ExcelFieldAnnotation(required = true)
    private BigDecimal height;
    @ExcelFieldAnnotation(required = true)
    private BigDecimal weight;
    @ExcelFieldAnnotation(required = true)
    private String samePacking;
    @ExcelFieldAnnotation(required = true)
    private BigDecimal originalUnitPrice;
    @ExcelFieldAnnotation(required = true)
    private BigDecimal originalOperationFee;
    @ExcelFieldAnnotation(required = true)
    private BigDecimal originalFinalDeliveryFee;
    private BigDecimal msrp;
    @ExcelFieldAnnotation(required = true)
    private String image1;
    private String image2;
    private String image3;
    private String image4;
    private String image5;
    private String image6;
    private String otherImage;

    private String featureName1;
    private String featureValue1;
    private String featureName2;
    private String featureValue2;
    private String featureName3;
    private String featureValue3;
    private String featureName4;
    private String featureValue4;
    private String featureName5;
    private String featureValue5;
    private String description;

    private String genericSpecName1;
    private String genericSpecValue1;
    private String genericSpecName2;
    private String genericSpecValue2;
    private String genericSpecName3;
    private String genericSpecValue3;
    private String genericSpecName4;
    private String genericSpecValue4;
    private String genericSpecName5;
    private String genericSpecValue5;

    // 1 黄金 2 白银 3 青铜

//    private List<>

    private BigDecimal levelOriginalUnitPrice1;

    private BigDecimal levelOriginalOperationFee1;

    private BigDecimal levelOriginalFinalDeliveryFee1;

    private BigDecimal levelOriginalUnitPrice2;

    private BigDecimal levelOriginalOperationFee2;

    private BigDecimal levelOriginalFinalDeliveryFee2;

    private BigDecimal levelOriginalUnitPrice3;

    private BigDecimal levelOriginalOperationFee3;

    private BigDecimal levelOriginalFinalDeliveryFee3;
}
