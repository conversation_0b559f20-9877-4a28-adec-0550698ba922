package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleItemVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;



/**
 * 商品sku价格计算公式对象 product_sku_price_rule_item
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuPriceRuleItemVo.class)
@TableName("product_sku_price_rule_item")
public class ProductSkuPriceRuleItem extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 计算公式表id
     */
    private Long productSkuPriceRuleId;

    /**
     * 具体的价格类型(单价、操作费和尾程派送费)
     */
    private String priceItemType;

    /**
     * 提价计算方式（1-加，2-减，3-乘，4-除）
     */
    private Integer priceCal;

    /**
     * 提价的值
     */
    private BigDecimal priceCalValue;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    public ProductSkuPriceRuleItem(Long productSkuPriceRuleId, String priceItemType, Integer priceCal, BigDecimal priceCalValue) {
        this.productSkuPriceRuleId = productSkuPriceRuleId;
        this.priceItemType = priceItemType;
        this.priceCal = priceCal;
        this.priceCalValue = priceCalValue;
    }
}
