package com.zsmall.product.entity.domain.bo.productSkuStock;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 请求体-库存编辑
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class StockEditBo {

    /**
     * 库存编号
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String stockCode;

    /**
     * 仓库系统编号
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String warehouseSystemCode;

    /**
     * 库存数量
     */
    @NotNull(message = ValidationMessage.API_REQUIRED)
    private Integer stockTotal;

}
