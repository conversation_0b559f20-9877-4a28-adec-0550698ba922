package com.zsmall.product.entity.domain.vo.wholesale;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductSkuWholesalePriceLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 国外现货批发商品SKU价格日志视图对象 product_sku_wholesale_price_log
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductSkuWholesalePriceLog.class)
public class ProductSkuWholesalePriceLogVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品主键
     */
    @ExcelProperty(value = "商品主键")
    private Long productId;

    /**
     * 商品SKU主键
     */
    @ExcelProperty(value = "商品SKU主键")
    private Long productSkuId;

    /**
     * 国外现货商品阶梯价日志表主键
     */
    @ExcelProperty(value = "国外现货商品阶梯价日志表主键")
    private Long productWholesaleTieredPriceLogId;

    /**
     * 国外现货商品SKU价格表主键
     */
    @ExcelProperty(value = "国外现货商品SKU价格表主键")
    private Long productSkuWholesalePriceId;

    /**
     * 阶梯定价表主键
     */
    @ExcelProperty(value = "阶梯定价表主键")
    private Long tieredPriceId;

    /**
     * 原始单价（供货商）
     */
    @ExcelProperty(value = "原始单价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "供=货商")
    private BigDecimal originUnitPrice;

    /**
     * 平台单价（员工、分销商）
     */
    @ExcelProperty(value = "平台单价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "员=工、分销商")
    private BigDecimal platformUnitPrice;


}
