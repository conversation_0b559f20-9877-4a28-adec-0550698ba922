package com.zsmall.product.entity.domain.vo.prodcutQuestion;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import com.hengjian.common.excel.annotation.ExcelTranslationFormat;
import com.hengjian.common.excel.conovert.ExcelTranslationConvert;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.hengjian.common.translation.annotation.Translation;
import com.hengjian.common.translation.constant.TransConstant;
import com.zsmall.product.entity.domain.ProductQuestion;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 商品问答视图对象 product_question
 *
 * <AUTHOR> <PERSON>
 * @date 2023-07-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductQuestion.class)
public class ProductQuestionVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 问题编码
     */
    @ExcelProperty(value = "问题编码")
    private String questionCode;

    /**
     * 租户Id
     */
    private String tenantId;

    /**
     * 商品表product_code
     */
    @ExcelProperty(value = "商品表product_code")
    private String productCode;

    /**
     * 商品sku表product_sku_code
     */
    @ExcelProperty(value = "商品sku表product_sku_code")
    private String productSkuCode;

    /**
     * 商品Sku表name
     */
    @ExcelProperty(value = "商品名")
    private String productSkuName;

    /**
     * 问题
     */
    @ExcelProperty(value = "问题")
    private String question;

    /**
     * 回复状态：Pending，Solved
     */
    @ExcelProperty(value = "回复状态：Pending，Solved", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "biz_product_question_status")
    private String status;


    /**
     * 创建人
     */
    @ExcelProperty(value = "操作人", converter = ExcelTranslationConvert.class)
    @ExcelTranslationFormat(type = TransConstant.NOT_TENANT_USER_ID_TO_NAME)
    @ExcelI18nFormat(code = "zsmall.excel.role")
    private Long createBy;

    /**
     * 创建人账号
     */
    @ExcelIgnore
    @Translation(type = TransConstant.NOT_TENANT_USER_ID_TO_NAME, mapper = "createBy", other = "Sensitive")
    private String createByName;

    /**
     * 创建时间
     */
    private Date createTime;

    @ExcelIgnore
    private List<ProductQuestionAnswerVo> questionAnswerVos;

    /**
     * 追问举报数量
     */
    @ExcelIgnore
    private long reportedCounts;
}
