package com.zsmall.product.entity.domain.vo.prodcutQuestion;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.ProductQuestionAnswerLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品问答回复编辑日志视图对象 product_question_answer_log
 *
 * <AUTHOR> Li
 * @date 2023-07-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductQuestionAnswerLog.class)
public class ProductQuestionAnswerLogVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 回复编辑日志表id
     */
    @ExcelProperty(value = "回复编辑日志表id")
    private Long id;

    /**
     * 回复编码
     */
    @ExcelProperty(value = "回复编码")
    private String answerCode;

    /**
     * 编辑前的回复内容
     */
    @ExcelProperty(value = "编辑前的回复内容")
    private String answer;

    /**
     * 追加提问状态：reported-已举报
     */
    @ExcelProperty(value = "追加提问状态：reported-已举报")
    private String questionStatus;


}
