package com.zsmall.product.entity.domain.dto.productSku;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/21 10:41
 */
@Data
public class ProductSkuDTO {


  private String queryType;

  private String queryValue;

  private String startTime;

  private String endTime;

  private Date newDate;

  /**
   * 用户类型（如MD，Sup等等）
   */
  private String storeType;
  private String itemNo;
  private String userCode;
  private String companyName;










}
