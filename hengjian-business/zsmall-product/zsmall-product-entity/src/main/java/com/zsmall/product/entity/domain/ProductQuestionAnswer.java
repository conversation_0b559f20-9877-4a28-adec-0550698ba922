package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.productQuestion.QuestionStatusEnum;
import com.zsmall.common.enums.productQuestion.ReplyTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 商品问答对象 product_question_answer
 *
 * <AUTHOR> Li
 * @date 2023-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_question_answer")
public class ProductQuestionAnswer extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 回答提问自增主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 回复编码
     */
    private String answerCode;

    /**
     * 提问id
     */
    private Long questionId;

    /**
     * 回复内容
     */
    private String answer;

    /**
     * 回复类型：question-追加提问，answer-回复
     */
    private ReplyTypeEnum type;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 追加提问状态：reported-已举报
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private QuestionStatusEnum questionStatus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
