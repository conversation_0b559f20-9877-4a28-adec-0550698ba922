package com.zsmall.product.entity.domain.vo.product;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/7/8 10:19
 */
@Data
//@ExcelIgnoreUnannotated
@NoArgsConstructor
@AllArgsConstructor
public class LevelPriceVo {

    private Long siteId;

    private String countryCode;
    private String currencyCode;

    private String currencySymbol;

    private String levelName;

    private String levelCode;

    private String dictCode;

    private String levelId;

//    @ExcelProperty(value = "自提价")
    private BigDecimal pickUpPrice;

//    @ExcelProperty(value = "代发价格")
    private BigDecimal dropPrice;
}
