package com.zsmall.product.entity.domain.bo.productQuestions;

/**
 * <AUTHOR>
 * @date 2022/1/8
 **/

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "删除提问请求参数")
public class ReqQuestionsUpBody {

    @Schema(title = "问题id")
    private Long questionId;

}
