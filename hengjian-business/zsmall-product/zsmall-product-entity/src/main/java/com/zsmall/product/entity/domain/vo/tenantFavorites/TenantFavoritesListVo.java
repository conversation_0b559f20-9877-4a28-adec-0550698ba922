package com.zsmall.product.entity.domain.vo.tenantFavorites;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import lombok.Data;

/**
 * 响应体-租户收藏夹列表
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Data
public class TenantFavoritesListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 收藏夹编号
     */
    private Long favoritesId;
    /**
     * 商品图片
     */
    private String productSkuImage;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * SKU
     */
    private String sku;
    /**
     * SKU ID
     */
    private String productSkuCode;
    /**
     * 货架状态：OnShelf-上架、OffShelf-下架 中文
     */
    private String cnShelfState;
    /**
     * 货架状态：OnShelf-上架、OffShelf-下架 英文
     */
    private String enShelfState;
    /**
     * 商品类型
     */
    private String cnProductType;

    private String enProductType;

    /**
     * 支持的物流
     */
    private String cnSupportedLogistics;
    private String enSupportedLogistics;
    /**
     * 自提价
     */
    private String pickUpPrice;
    /**
     * 代发价
     */
    private String dropShippingPrice;
    /**
     * 分仓库存集合
     */
    private List<HashMap<String,Object>> skuStockList;
    /**
     * 库存总数
     */
    private Integer stockTotal;

    /**
     * 代发库存总数
     */
    private Integer proxyStockTotal;
    /**
     * product_code
     */
    private String productCode;
    /**
     * 站点
     */
    private String site;
    /**
     * 币种
     */
    private String currency;

    private String productSize;

    private String productWeight;

    private String packageSize;

    private String packageWeight;
    /**
     * 产品描述
     */
    private String description;
}
