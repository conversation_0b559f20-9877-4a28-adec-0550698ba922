package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.TaskStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 任务-库存同步对象 task_stock_sync
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "task_stock_sync", autoResultMap = true)
public class TaskStockSync extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 来源SKU主键
     */
    private Long productSkuId;

    /**
     * 来源SKU主键集合
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> productSkuIdList;

    /**
     * 任务状态
     */
    private TaskStateEnum taskState;

    /**
     * 任务执行信息
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String taskExecuteMessage;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
