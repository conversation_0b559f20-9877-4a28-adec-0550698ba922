package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.domain.bo.productMapping.ProductMappingTableQueryBo;
import com.zsmall.product.entity.domain.vo.productMapping.ProductMappingTableVo;
import com.zsmall.product.entity.domain.vo.productMapping.ProductMappingVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品映射Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-20
 */
public interface ProductMappingMapper extends TenantMapperPlus<ProductMapping, ProductMappingVo> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<ProductMappingTableVo> queryProductMappingPage(Page<ProductMapping> page, @Param("queryBo") ProductMappingTableQueryBo queryBo, @Param("tenantId") String tenantId);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductMapping> queryListByChannelAndItemNoAndState(@Param("channelType") String channelType, @Param("productSkuCode") String productSkuCode, @Param("syncState") String syncState);
    @InterceptorIgnore(tenantLine = "true")
    IPage<ProductMappingTableVo> queryProductMappingPageForTikTok(Page<ProductMapping> page,@Param("queryBo") ProductMappingTableQueryBo queryBo,@Param("tenantId") String tenantId);

    List<ProductMapping> getProductMappingBySendMq(@Param("channelType")  List<String> channelType);


}
