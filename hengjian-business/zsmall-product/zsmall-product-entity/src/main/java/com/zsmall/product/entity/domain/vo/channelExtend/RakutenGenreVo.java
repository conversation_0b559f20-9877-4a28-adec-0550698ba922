package com.zsmall.product.entity.domain.vo.channelExtend;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;


/**
 * 响应体-乐天品类
 */
@Data
public class RakutenGenreVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Alias("genreId")
    private String id;

    /**
     * 父级分类主键
     */
    @Alias("parentGenreId")
    private String parentId;

    /**
     * 分类名称（主名称）
     */
    @Alias("nameJa")
    private String categoryName;

    /**
     * 是否可以注册商品（是否禁止选择）
     */
    @Alias("itemRegisterFlg")
    private Boolean disabled;

    /**
     * 是否叶子节点
     */
    private boolean leaf = false;

    /**
     * 因为数据库中保存的itemRegisterFlg是是否可以注册商品（true-是，false-否），而前端的Tree组件的disabled是反过来的（true-不可选，false-可选），所以在set的时候要取反
     * @param disabled
     */
    public void setDisabled(Boolean disabled) {
        this.disabled = !disabled;
    }
}
