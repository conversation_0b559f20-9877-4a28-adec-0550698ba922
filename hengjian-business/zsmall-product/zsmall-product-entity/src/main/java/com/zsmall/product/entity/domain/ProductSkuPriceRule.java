package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.common.domain.SortEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * 商品sku价格计算公式对象 product_sku_price_rule
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_sku_price_rule", autoResultMap = true)
public class ProductSkuPriceRule extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 计算公式名称
     */
    private String ruleName;

    /**
     * 计算公式编码
     */
    private String ruleCode;

    /**
     * 适用类型
     */
    private Integer applicableType;

    /**
     * 适用的值
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray applicableValue;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
