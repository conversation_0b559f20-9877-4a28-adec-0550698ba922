package com.zsmall.product.entity.domain.bo.productSkuPrice;

import cn.hutool.json.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/26 11:18
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-获取适用产品（适用产品设置）")
public class ApplicableProductSkuBo {

    @Schema(title = "公式名称")
    private String ruleName;

    @Schema(title = "适用类型")
    private Integer applicableType;

    @Schema(title = "适用值")
    private JSONArray applicableValue;

    @Schema(title = "原价的提价计算方式（1-加，2-减，3-乘，4-除）")
    private Integer unitPriceCal;

    @Schema(title = "原价的提价的值")
    private BigDecimal unitPriceCalValue;

    @Schema(title = "操作费的提价计算方式（1-加，2-减，3-乘，4-除）")
    private Integer operationFeeCal;

    @Schema(title = "操作费的提价的值")
    private BigDecimal operationFeeCalValue;

    @Schema(title = "尾程派送费的提价计算方式（1-加，2-减，3-乘，4-除）")
    private Integer finalDeliveryFeeCal;

    @Schema(title = "尾程派送费的提价的值")
    private BigDecimal finalDeliveryFeeCalValue;

    @Schema(title = "删除的itemNo集合")
    private List<String> deleteItemNoList;

    @Schema(title = "替换的itemNo集合")
    private List<String> replaceItemNoList;

    @Schema(title = "公式编码")
    private String ruleCode;

}
