package com.zsmall.product.entity.domain.viewer;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * @TableName view_product_inventory_num
 */
@TableName(value ="view_product_inventory_num")
@Data
public class ViewProductInventoryNumEntity implements Serializable {
    /**
     * 主键
     */
    @TableField(value = "product_id")
    private Long productId;

    /**
     *
     */
    @TableField(value = "inventory_num")
    private Long inventoryNum;

    /**
     *
     */
    @TableField(value = "sales_num")
    private Long salesNum;

    /**
     *
     */
    @TableField(value = "return_num")
    private Long returnNum;

    /**
     *
     */
    @TableField(value = "real_num")
    private Long realNum;

    /**
     * 代发价
     */
    @TableField(value = "drop_shipping_price")
    private BigDecimal dropShippingPrice;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
