package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.bo.UniversalBo.UniversalQueryBo;
import com.zsmall.product.entity.domain.bo.product.CustomExportBo;
import com.zsmall.product.entity.domain.bo.product.ProductPriceBo;
import com.zsmall.product.entity.domain.bo.productSku.ProductSkuBo;
import com.zsmall.product.entity.domain.dto.productSku.ApplicableProductSkuDTO;
import com.zsmall.product.entity.domain.dto.productSku.ApplicableProductSkuParamDTO;
import com.zsmall.product.entity.domain.dto.wholesale.WholesaleProductPageDTO;
import com.zsmall.product.entity.domain.dto.wholesale.WholesaleProductParamDTO;
import com.zsmall.product.entity.domain.vo.product.*;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAdjustStockVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAndStockVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuVo;
import com.zsmall.product.entity.mapper.ProductSkuMapper;
import com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseVo;
import com.zsmall.warehouse.entity.iservice.IWarehouseAddressService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.hengjian.common.core.enums.TenantType.*;

/**
 * 商品SKU表-数据库层
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Slf4j
@Service
public class IProductSkuService extends ServiceImpl<ProductSkuMapper, ProductSku> {
    @Resource
   private IWarehouseAddressService warehouseAddressService;


    @InMethodLog("根据主键查询商品SKU")
    public ProductSku queryById(Long id) {
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        if (Supplier.equals(tenantType)) {
            return baseMapper.selectById(id);
        }
        return TenantHelper.ignore(() -> baseMapper.selectById(id));
    }

    @InMethodLog("根据主键集合查询商品SKU集合（包括删除）")
    public List<ProductSku> queryByIdIncludeDel(Long... ids) {
        return TenantHelper.ignore(() -> baseMapper.queryByIdsIncludeDel(ids), Distributor, Manager);
    }


    /**
     * 根据商品SKU主键查询商品SKU（无视租户）
     *
     * @param id
     * @return
     */
    public ProductSku queryByIdNoTenant(Long id) {
        return TenantHelper.ignore(() -> baseMapper.selectById(id));
    }

    /**
     * 查询商品SKU列表
     */
    public TableDataInfo<ProductSkuVo> queryPageList(ProductSkuBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSku> lqw = buildQueryWrapper(bo);
        Page<ProductSkuVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品SKU列表
     */
    public List<ProductSkuVo> queryList(ProductSkuBo bo) {
        LambdaQueryWrapper<ProductSku> lqw = buildQueryWrapper(bo);
        return TenantHelper.ignore(() -> baseMapper.selectVoList(lqw), Distributor, Manager);
    }

    private LambdaQueryWrapper<ProductSku> buildQueryWrapper(ProductSkuBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductId() != null, ProductSku::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductSku::getProductCode, bo.getProductCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), ProductSku::getProductSkuCode, bo.getProductSkuCode());
        lqw.like(StringUtils.isNotBlank(bo.getName()), ProductSku::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getSku()), ProductSku::getSku, bo.getSku());
        lqw.eq(StringUtils.isNotBlank(bo.getUpc()), ProductSku::getUpc, bo.getUpc());
        lqw.eq(bo.getStockTotal() != null, ProductSku::getStockTotal, bo.getStockTotal());
        lqw.eq(StringUtils.isNotBlank(bo.getStockManager()), ProductSku::getStockManager, bo.getStockManager());
        lqw.eq(StringUtils.isNotBlank(bo.getShelfState()), ProductSku::getShelfState, bo.getShelfState());
        lqw.eq(StringUtils.isNotBlank(bo.getVerifyState()), ProductSku::getVerifyState, bo.getVerifyState());
        lqw.eq(bo.getSort() != null, ProductSku::getSort, bo.getSort());
        return lqw;
    }

    /**
     * 新增商品SKU
     */
    public Boolean insertByBo(ProductSkuBo bo) {
        ProductSku add = MapstructUtils.convert(bo, ProductSku.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增商品SKU
     *
     * @param entity
     * @return
     */
    public Boolean insert(ProductSku entity) {
        log.info("进入【新增商品SKU】 entity = {}", JSONUtil.toJsonStr(entity));
        return baseMapper.insert(entity) > 0;
    }

    /**
     * 修改商品SKU
     */
    public Boolean updateByBo(ProductSkuBo bo) {
        ProductSku update = MapstructUtils.convert(bo, ProductSku.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSku entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SKU
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog("通过ProductId获取未删除的商品sku集合")
    public List<ProductSku> queryByProductIdNotDelete(Long productId) {
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSku::getProductId, productId);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw), TenantType.Manager, TenantType.Distributor);
    }

    @InMethodLog("通过ProductId获取商品sku集合（包括已删除）")
    public List<ProductSku> queryByProductIdIncludeDel(Long productId) {
        return TenantHelper.ignore(() -> baseMapper.queryByProductIdIncludeDel(productId), TenantType.Manager, TenantType.Distributor);
    }

    @InMethodLog("进入【通过productId获取已上架的商品Sku集合】")
    public List<ProductSku> queryByProductIdAndOnShelf(Long productId) {
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSku::getProductId, productId);
        lqw.eq(ProductSku::getShelfState, ShelfStateEnum.OnShelf);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    @InMethodLog("进入【通过productId获取未删除的商品sku集合】")
    public List<ProductSku> queryByProductIdNotDelete(Long productId, ProductVerifyStateEnum verifyStateEnum) {
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSku::getProductId, productId);
        lqw.eq(ProductSku::getVerifyState, verifyStateEnum);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw), TenantType.Manager, TenantType.Distributor);
    }

    @InMethodLog("通过商品主键获取未删除的商品Sku主键集合")
    public List<Long> queryIdsByProductId(Long productId) {
        return baseMapper.queryIdsByProductId(productId);
    }

    /**
     * 根据主键和商品主键查询单个商品SKU
     *
     * @param id
     * @param productId
     * @return
     */
    public ProductSku queryByIdAndProductId(Long id, Long productId) {
        log.info("进入【根据主键和商品主键查询单个商品SKU】 id = {}, productId = {}", id, productId);
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSku::getId, id);
        lqw.eq(ProductSku::getProductId, productId);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 通过ProductSkuCode查询商品SKU
     *
     * @param productSkuCode
     * @return
     */
    public ProductSku queryByProductSkuCode(String productSkuCode) {
        log.info("通过ProductSkuCode查询商品SKU, ProductSkuCode = {}", productSkuCode);
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSku::getProductSkuCode, productSkuCode);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw), TenantType.Manager, TenantType.Distributor);
    }
    @Deprecated
    public ProductSku queryBySku(String sku) {
//        log.info("通过sku查询商品SKU, sku = {}", sku);
//        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
//        lqw.eq(ProductSku::getSku, sku);
//        lqw.eq(ProductSku::getDelFlag,0);
//        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw), TenantType.Manager, TenantType.Distributor);
        throw new RuntimeException("该方法已被废弃，禁止调用");
    }

    @InMethodLog("忽略租户 - 通过ProductSkuCode查询商品SKU")
    public ProductSku queryByProductSkuCodeWithNotTenant(String productSkuCode) {
        log.info("通过ProductSkuCode查询商品SKU, ProductSkuCode = {}", productSkuCode);
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSku::getProductSkuCode, productSkuCode);
        return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
    }

    @InMethodLog("通过ProductSkuCode集合查询商品SKU集合")
    public List<ProductSku> queryByProductSkuCodeList(List<String> productSkuCode) {
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSku::getProductSkuCode, productSkuCode);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw), TenantType.Manager, TenantType.Distributor);
    }

    @InMethodLog("通过ProductSkuCode集合和货架状态查询商品SKU集合")
    public List<ProductSku> queryByProductSkuCodeAndShelfStateList(List<String> productSkuCode, ShelfStateEnum shelfState) {
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSku::getProductSkuCode, productSkuCode);
        lqw.in(ProductSku::getShelfState, shelfState);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw), TenantType.Manager, TenantType.Distributor);
    }

    @InMethodLog("通过商品SKU编号查询商品SKU（包括删除，慎用）")
    public ProductSku queryByProductSkuCodeIncludeDel(String productSkuCode) {
        log.info("进入【通过ProductSkuCode查询商品SKU（包括删除，慎用）】 productSkuCode = {}", productSkuCode);
        return baseMapper.queryByProductSkuCodeIncludeDel(productSkuCode);
    }

    @InMethodLog("通过商品SKU编号集合查询商品SKU集合（包括删除，慎用）")
    public List<ProductSku> queryByProductSkuCodesIncludeDel(Collection productSkuCodes) {
        return baseMapper.queryByProductSkuCodesIncludeDel(productSkuCodes);
    }

    /**
     * 通过ProductSkuCode查询商品SKU
     *
     * @param productSkuCodes
     * @return
     */
    public List<ProductSku> queryListByProductSkuCodes(List<String> productSkuCodes) {
        log.info("通过ProductSkuCode查询商品SKU, productSkuCodes = {}", JSONUtil.toJsonStr(productSkuCodes));
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSku::getProductSkuCode, productSkuCodes);
        if (!ObjectUtil.equals(TenantType.Supplier, LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        }
        return baseMapper.selectList(lqw);
    }

    /**
     * 通过productCode获取数据
     *
     * @param productCodes
     * @return
     */
    public List<ProductSku> queryListByProductCodes(List<String> productCodes) {
        log.info("通过ProductCode查询商品SKU, productCodes = {}", JSONUtil.toJsonStr(productCodes));
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSku::getProductCode, productCodes);
//        if (!ObjectUtil.equals(TenantType.Supplier, LoginHelper.getTenantTypeEnum())) {
//            return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
//        }
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 根据商品SKU唯一编号和仓库查询商品SKU
     *
     * @param productSkuCode
     * @return
     */
    public ProductSku queryByProductSkuCodeAndWarehouseSystemCode(String productSkuCode, String warehouseSystemCode) {
        log.info("根据商品SKU唯一编号和仓库查询商品SKU productSkuCode = {} warehouseSystemCode = {}", productSkuCode, warehouseSystemCode);
        return baseMapper.queryByProductSkuCodeAndWarehouseSystemCode(productSkuCode, warehouseSystemCode);
    }


    /**
     * 获取已绑定的适用商品数量
     *
     * @param dto
     * @return
     */
    public Integer getBoundApplicableProductSkuNum(ApplicableProductSkuParamDTO dto) {
        log.info("进入【获取已绑定的适用商品数量】方法, dto = {}", JSONUtil.toJsonStr(dto));
        if (ObjectUtil.equals(Supplier, LoginHelper.getTenantTypeEnum())) {
            return baseMapper.getBoundApplicableProductSkuNum(dto);
        } else {
            return TenantHelper.ignore(() -> baseMapper.getBoundApplicableProductSkuNum(dto));
        }

    }

    /**
     * 根据条件获取适用产品
     *
     * @param dto
     * @param pageQuery
     * @return
     */
    public IPage<ApplicableProductSkuDTO> getApplicableProductSkuPage(ApplicableProductSkuParamDTO dto, Page<ApplicableProductSkuDTO> pageQuery) {
        log.info("进入【根据条件获取适用产品】方法, dto = {}", JSONUtil.toJsonStr(dto));
        if (ObjectUtil.equals(Supplier, LoginHelper.getTenantTypeEnum())) {
            return baseMapper.getApplicableProductSku(dto, pageQuery);
        } else {
            return TenantHelper.ignore(() -> baseMapper.getApplicableProductSku(dto, pageQuery));
        }

    }

    /**
     * 根据条件获取适用产品(不翻页)
     *
     * @param dto
     * @return
     */
    public List<ApplicableProductSkuDTO> getApplicableProductSkuList(ApplicableProductSkuParamDTO dto) {
        log.info("进入【根据条件获取适用产品(不翻页)】方法, dto = {}", JSONUtil.toJsonStr(dto));
        return TenantHelper.ignore(() -> baseMapper.getApplicableProductSku(dto));
    }

    /**
     * SKU是否存在
     *
     * @param sku
     * @param tenantId
     * @return
     */
    public boolean existSku(String sku, String tenantId, Long excludeId) {
        log.info("进入【SKU是否存在】 sku = {}, tenantId = {}", sku, tenantId);
        return baseMapper.existSku(sku, tenantId, excludeId);
    }

    /**
     * UPC是否存在
     *
     * @param upc
     * @return
     */
    public boolean existUpc(String upc, Long excludeId) {
        log.info("进入【SKU是否存在】 upc = {}", upc);
        return baseMapper.existUpc(upc, excludeId);
    }

    /**
     * 通过productId获取审核通过的商品
     *
     * @param productId
     * @return
     */
    public List<ProductSku> getAcceptedAndOnShelfProductSkuListByProductId(Long productId) {
        log.info("进入【通过productId获取审核通过的商品】 productId = {}", productId);
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSku::getProductId, productId)
            .eq(ProductSku::getVerifyState, ProductVerifyStateEnum.Accepted)
            .eq(ProductSku::getShelfState, ShelfStateEnum.OnShelf)
            .orderByAsc(ProductSku::getSort);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 根据商品查询审核通过的商品sku集合
     *
     * @param productId
     * @return
     */
    public List<ProductSku> getNotRejectedProductSkuListByProductId(Long productId) {
        log.info("进入【通过productId获取审核通过的商品】 productId = {}", productId);
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSku::getProductId, productId)
            .ne(ProductSku::getVerifyState, ProductVerifyStateEnum.Rejected)
//            .ne(ProductSku::getShelfState, ShelfStateEnum.ForcedOffShelf)
            .orderByAsc(ProductSku::getSort);
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 根据商品主键查询列表展示用响应体
     *
     * @param productId
     * @return
     */
    public List<ProductSkuListVo> queryProductSkuListVoByProductId(Long productId) {
        log.info("进入【根据商品主键查询列表展示用响应体】 productId = {}", productId);
        return baseMapper.queryProductSkuListVoByProductId(productId);
    }

    /**
     * 根据商品主键查询列表展示用响应体
     *
     * @param productIds
     * @param siteId
     * @param bo
     * @return
     */
    public List<ProductSkuListVo> queryProductSkuListVoByProductIds(List<Long> productIds, Long siteId,
                                                                    UniversalQueryBo bo) {
        log.info("进入【根据商品主键查询列表展示用响应体】 productIds = {},站点={}", productIds, siteId);
        return baseMapper.queryProductSkuListVoByProductIds(productIds,siteId,bo);
    }

    public List<ProductSkuListVo> queryProductSkuListVoByProductIdsAndShelfState(List<Long> productIds, String skuShelfState) {
        log.info("进入【根据商品主键查询列表展示用响应体】 productIds = {},skuShelfState = {}", productIds,skuShelfState);
        return baseMapper.queryProductSkuListVoByProductIdsAndShelfState(productIds,skuShelfState);
    }

    /**
     * 根据商品主键查询列表展示用响应体(批发商品sku)
     *
     * @param productId
     * @return
     */
    public List<ProductSkuListVo> queryWholesaleProductSkuListVoByProductId(Long productId, String rejected) {
        log.info("进入【根据商品主键查询列表展示用响应体】 productId = {}", productId);
        return baseMapper.queryWholesaleProductSkuListVoByProductId(productId, rejected);
    }


    /**
     * 查询SKU重复
     *
     * @param id
     * @param productId
     * @param sku
     * @return
     */
    public int countSkuDuplicate(Long id, Long productId, String sku) {
        log.info("进入【查询SKU重复】 id = {}, productId = {}, sku = {}", id, productId, sku);
        return baseMapper.countSkuDuplicate(id, productId, sku);
    }

    /**
     * 查询UPC重复
     *
     * @param id
     * @param upc
     * @return
     */
    public int countUpcDuplicate(Long id, String upc) {
        log.info("进入【查询UPC重复】 id = {}, upc = {}", id, upc);
        return baseMapper.countUpcDuplicate(id, upc);
    }

    /**
     * 查询在售库存总数（根据Sku唯一编号）
     *
     * @param productSkuCode
     * @return
     */
    public Integer queryStockTotal(String productSkuCode) {
        log.info("进入【查询在售库存总数（根据Sku唯一编号）】 productSkuCode = {}", productSkuCode);
        return baseMapper.queryStockTotal(productSkuCode);
    }

    /**
     * 查询库存调整用Vo类
     *
     * @param productSkuCode
     * @return
     */
    public ProductSkuAdjustStockVo queryAdjustStockVo(String productSkuCode) {
        log.info("进入【查询库存调整用Vo类】 productSkuCode = {}", productSkuCode);
        return baseMapper.queryAdjustStockVo(productSkuCode);
    }

    /**
     * 更新指定SKU的库存总数
     *
     * @param productSkuCode
     * @param stockTotal
     * @return
     */
    public Boolean updateStockTotal(String productSkuCode, Integer stockTotal) {
        LambdaUpdateWrapper<ProductSku> luw = new LambdaUpdateWrapper<>();
        luw.set(ProductSku::getStockTotal, stockTotal);
        luw.eq(ProductSku::getProductSkuCode, productSkuCode);
        return TenantHelper.ignore(() -> baseMapper.update(null, luw) > 0);
    }

    @InMethodLog("查询商品SKU库存管理方的国家")
    public List<String> queryStockManagerCountry(String productSkuCode, String specifyWarehouse) {
        return baseMapper.queryStockManagerCountry(productSkuCode, specifyWarehouse);
    }

    @InMethodLog("分页查询商品SKU")
    public IPage<ProductSku> getPage(String queryType, String queryValue, Page<ProductSku> page, Long siteId,
                                     String skuShelfState, String skuAuditStatus) {
        return baseMapper.getPage(queryType, queryValue, page,siteId, skuShelfState, skuAuditStatus);
    }

    @InMethodLog("分页查询商品SKU（渠道管控用）")
    public IPage<ProductSku> queryProductSkuControlPage(String queryType, String queryValue, Page<ProductSku> page) {
        return baseMapper.queryProductSkuControlPage(queryType, queryValue, page);
    }

    @InMethodLog("分页查询商品SKU与库存信息")
    public IPage<ProductSkuAndStockVo> queryProductSkuAndStock(String queryType, String queryValue, Page<ProductSku> page) {
        return baseMapper.queryProductSkuAndStock(queryType, queryValue, page);
    }

    @InMethodLog("查询商品SKU与库存信息")
    public ProductSkuAndStockVo queryProductSkuAndStockByCode(String productSkuCode) {
        return baseMapper.queryProductSkuAndStockByCode(productSkuCode);
    }

    @InMethodLog("查询自定义导出列表")
    public List<ProductCustomExportVo> queryCustomExportList(CustomExportBo bo) {
        return baseMapper.queryCustomExportList(bo);
    }

    @InMethodLog("查询商品加入收藏夹数据")
    public ProductFavoritesVo queryProductFavorites(String productCode) {
        return baseMapper.queryProductFavorites(productCode);
    }

    @InMethodLog("分页查询国外现货商品信息")
    public IPage<WholesaleProductPageDTO> getWholesaleProductPage(WholesaleProductParamDTO param, Page<WholesaleProductPageDTO> queryPage) {
        return TenantHelper.ignore(() -> baseMapper.getWholesaleProductPage(param, queryPage));
    }

    @InMethodLog("分页查询商品SKU（QA用简略数据）")
    public Page<ProductSkuSimpleVo> getPageForQA(String tenantId, String queryType, String queryValue, Page<ProductSku> page) {
        return baseMapper.getPageForQA(tenantId, queryType, queryValue, page);
    }

    @InMethodLog("根据商品编号查询商品SKU（收藏夹导出简略数据）")
    public List<ProductSkuFavoritesSimpleVo> queryByFavoritesIdsForFavorites(List<String> favoritesIds) {
        return baseMapper.queryByFavoritesIdsForFavorites(favoritesIds);
    }

    @InMethodLog("根据商品编号查询商城展示用数据")
    public ProductSkuForMarketplaceShowVo queryForMarketplaceShow(String productSkuCode) {
        return baseMapper.queryForMarketplaceShow(productSkuCode);
    }
    @InMethodLog("根据商品编号查询商城展示用数据")
    public List<ProductSkuForMarketplaceShowVo> queryForMarketplaceShows(List<String> productSkuCode) {
        return baseMapper.queryForMarketplaceShows(productSkuCode);
    }

    @InMethodLog("根据商品查询订单处理时效")
    public Long queryDealEffectiveness(String productSkuCode) {
        return baseMapper.queryDealEffectiveness(productSkuCode);
    }
    @InMethodLog("批量更新库存")
    public int updateStockTotalBatch(LinkedHashMap<Long, Integer> stockTotalMap) {
        return baseMapper.updateStockTotalBatch( stockTotalMap);
    }

    /**
     * 根据租户id获取全部产品sku信息
     *
     * @param tenantId
     * @return
     */
    @InMethodLog("根据租户id获取productSku数据")
    public List<ProductSku> listProductSkuByTenantId(@NonNull String tenantId){
        LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSku::getTenantId, tenantId);
        return baseMapper.selectList(lqw);
    }

    /**
     * 查询商品关联的仓库信息
     *
     * @param sku 商品编码
     * @param i 业务类型 1查询有库存的
     * @return
     */
    public  List<String> getProductSkuStockBySku(String sku, int i) {
        return baseMapper.getProductSkuStocksBySku(sku, i);
    }

    /**
     * @description: 根据商品编码查询有库存的仓库信息，附带仓库地址信息
     * @author: Len
     * @date: 2024/10/24 11:22
     * @param: sku
     * @param: i
     * @return: java.util.List<com.zsmall.warehouse.entity.domain.vo.warehouse.WarehouseVo>
     **/
    public  List<WarehouseVo> getInStockWarehouseBySku(String sku, int i,String warehouseSystemCode) {
        List<WarehouseVo> inStockWarehouseBySku =TenantHelper.ignore(()->baseMapper.getInStockWarehouseBySku(sku, i,warehouseSystemCode));
        inStockWarehouseBySku.forEach(s->{
            s.setWarehouseAddressVo(warehouseAddressService.queryByWarehouseId(s.getId()));
        });
        return inStockWarehouseBySku;
    }

    /**
     * 功能描述：获取查询计数
     *
     * @param queryType      查询类型
     * @param queryValue     查询值
     * @param page           页面
     * @param itemNos
     * @param skuAuditStatus
     * @return {@link IPage }<{@link ProductSku }>
     * <AUTHOR>
     * @date 2025/01/06
     */
    public IPage<ProductSku> getQueryCount(String queryType, String queryValue, Page<ProductSku> page, Long siteId,
                                           List<String> itemNos, String skuAuditStatus) {
        return baseMapper.getQueryCount(queryType, queryValue, page,siteId,itemNos,skuAuditStatus);
    }

    public List<ProductPriceExportDto> getProductPriceExportDto(ProductPriceBo bo, String tenantId) {
        return baseMapper.getProductPrice(bo.getQueryType(), bo.getQueryValue(), bo.getSiteId(),bo.getItemNos(),bo.getSkuAuditStatus());
    }

    public Map<String,Long> getMapByCodes(List<String> priceProductSkuCodeList) {
        LambdaQueryWrapper<ProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSku::getDelFlag,0);
        queryWrapper.in(ProductSku::getProductSkuCode,priceProductSkuCodeList);
        List<ProductSku> productSkus = TenantHelper.ignore(()->baseMapper.selectList(queryWrapper));
        if(CollUtil.isNotEmpty(productSkus)){
            Map<String, Long> map = productSkus.stream()
                                               .collect(Collectors.toMap(ProductSku::getProductSkuCode, ProductSku::getId));
            return map;
        }else {
            throw new RuntimeException("商品sku不存在");
        }


    }

    /**
     * 功能描述：获取产品sku代码和sku图
     *
     * @param productSkuCodes 产品sku代码
     * @return {@link Map }<{@link String }, {@link String }>
     * <AUTHOR>
     * @date 2025/03/25
     */
    public Map<String, String> getProductSkuCodeAndSkuMap(List<String> productSkuCodes) {
        LambdaQueryWrapper<ProductSku> skuLambdaQueryWrapper = new LambdaQueryWrapper<>();
        skuLambdaQueryWrapper.in(ProductSku::getProductSkuCode, productSkuCodes);
        List<ProductSku> productSkus = list(skuLambdaQueryWrapper);

        return productSkus.stream()
                                .collect(Collectors.toMap(ProductSku::getProductSkuCode, ProductSku::getSku));
    }

    public Map<String, ProductSku> getProductSkuCodeAndProductSkuMap(List<String> productSkuCodes) {
        LambdaQueryWrapper<ProductSku> skuLambdaQueryWrapper = new LambdaQueryWrapper<>();
        skuLambdaQueryWrapper.in(ProductSku::getProductSkuCode, productSkuCodes);
        List<ProductSku> productSkus = list(skuLambdaQueryWrapper);

        return productSkus.stream()
                          .collect(Collectors.toMap(ProductSku::getProductSkuCode, Function.identity()));
    }


}
