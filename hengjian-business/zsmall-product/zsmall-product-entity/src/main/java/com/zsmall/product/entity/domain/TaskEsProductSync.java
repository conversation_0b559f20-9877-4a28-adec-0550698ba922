package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.TaskStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务-ES商品同步对象
 * <AUTHOR>
 * @date 2023/8/9
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "task_es_product_sync", autoResultMap = true)
public class TaskEsProductSync extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 商品信息
     */
    @TableField(value = "product_info", typeHandler = JacksonTypeHandler.class)
    private JSONObject productInfo;

    /**
     * 任务状态
     */
    @TableField(value = "task_state")
    private TaskStateEnum taskState;

    /**
     * 任务执行信息
     */
    @TableField(value = "task_execute_message", updateStrategy = FieldStrategy.IGNORED)
    private String taskExecuteMessage;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
