package com.zsmall.product.entity.domain.member;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2024/05/07
 */
@Data
@TableName(value = "member_level", autoResultMap = true)
@EqualsAndHashCode(callSuper=false)
public class MemberLevel extends NoDeptTenantEntity {

    private Long id;
    /**
    * 等级名称
    */
//    @Size(max= 255,message="编码长度不能超过255")
//    @ApiModelProperty("等级名称")
//    @Length(max= 255,message="编码长度不能超过255")
//    private String levelName;

    private Long dictCode;
    /**
    * 等级启用状态
    */
    @NotNull(message="[等级启用状态]不能为空,0开启 1关闭")
    @ApiModelProperty("等级启用状态")
    private Integer status;
    /**
    * icon
    */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("icon")
    @Length(max= 255,message="编码长度不能超过255")
    private String showUrl;

    /**
    * 删除标志（0代表存在 2代表删除)
    */
    @ApiModelProperty("删除标志（0代表存在 2代表删除)")
    @TableLogic
    private String delFlag;


}
