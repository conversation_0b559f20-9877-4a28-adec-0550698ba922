package com.zsmall.product.entity.domain.vo.productSkuStock;

import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 通用-Sku库存信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用-Sku库存信息")
public class SkuStockInfoVo {

    @Schema(title = "商品主键")
    private Long productId;

    @Schema(title = "商品编号")
    private String productCode;

    @Schema(title = "商品SKU主键")
    private Long productSkuId;

    @Schema(title = "商品SKU编号")
    private String productSkuCode;

    @Schema(title = "商品类型")
    private String productType;

    @Schema(title = "支持的物流")
    private SupportedLogisticsEnum supportedLogistics;

    @Schema(title = "库存编号")
    private String stockCode;

    @Schema(title = "sku")
    private String sku;

    @Schema(title = "图片展示url")
    private String imageShowUrl;

    @Schema(title = "商品名")
    private String productName;

    @Schema(title = "规格")
    private String specValName;

    @Schema(title = "可用库存（自提）")
    private Integer stockTotal;

    @Schema(title = "可用库存（一件代发）")
    private Integer proxyStockTotal;

    @Schema(title = "售出数量")
    private Integer sold;

    @Schema(title = "自提价格")
    private BigDecimal pickUpPrice;

    @Schema(title = "代发价格")
    private BigDecimal dropShippingPrice;

    @Schema(title = "单价（批发商品使用）")
    private BigDecimal unitPrice;

    @Schema(title = "仓库名")
    private String warehouseName;

    @Schema(title = "仓库类型")
    private String warehouseType;

    @Schema(title = "仓库编码")
    private String warehouseSystemCode;

    @Schema(title = "库存状态")
    private Integer stockState;

    @Schema(title = "是否可以编辑库存")
    private Boolean canModify = true;

    /**
     * 代发库存标识 0单仓,1非单仓(等于自提库存)
     */
    private Integer dropShippingStockAvailable;
    /**
     * 上下架状态
     */
    private String shelfState;

    /**
     * 自提锁货总数量
     */
    @Schema(title = "已锁定库存（自提）")
    private Integer pickupLockUsed;
    /**
     * 代发锁货总数量
     */
    @Schema(title = "已锁定库存（一件代发）")
    private Integer dropShippingLockUsed;
    /**
     * 锁货异常码(拉库存的时候去标识)
     */
    private Integer lockExceptionCode;

    /**
     * 总自提锁货数量
     */
    @Schema(title = "仓库总库存（自提）")
    private Integer totalPickupLockUsed;

    /**
     * 总代发锁货数量
     */
    @Schema(title = "仓库总库存（一件代发）")
    private Integer totalDropShippingLockUsed;
}
