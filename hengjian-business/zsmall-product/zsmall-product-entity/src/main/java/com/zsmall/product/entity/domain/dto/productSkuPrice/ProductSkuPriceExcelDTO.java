package com.zsmall.product.entity.domain.dto.productSkuPrice;

import com.zsmall.common.annotaion.ExcelFieldAnnotation;
import com.zsmall.common.domain.dto.ExcelBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 批量发货导入DTO
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@Data
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class ProductSkuPriceExcelDTO extends ExcelBaseDTO {

    @ExcelFieldAnnotation(column = "Item No")
    private String itemNo;
    @ExcelFieldAnnotation(column = "站点")
    private String countryCode;
    @ExcelFieldAnnotation(column = "Unit Price")
    private String unitPrice;
    @ExcelFieldAnnotation(column = "Operation Fee")
    private String operationFee;
    @ExcelFieldAnnotation(column = "Final Delivery Fee")
    private String finalDeliveryFee;
    @ExcelFieldAnnotation(column = "Msrp")
    private String msrp;





}
