package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.product.ProductReviewTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 商品审核记录对象 product_review_record
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_review_record", autoResultMap = true)
public class ProductReviewRecord extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;



    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 审核意见选项（多选
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray reviewOpinionOption;

    /**
     * 提交审核时间
     */
    private Date submitDateTime;

    /**
     * 提交审核用户ID
     */
    private Long submitUserId;

    /**
     * 审核员工ID
     */
    private Long reviewUserId;

    /**
     * 审核时间
     */
    private Date reviewDateTime;

    /**
     * 审核类型（目前只有价格）：Price-价格
     */
    private ProductReviewTypeEnum reviewType;

    /**
     * 审核状态
     */
    private ProductVerifyStateEnum reviewState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
