package com.zsmall.product.entity.domain.bo.productSkuPrice;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商品SKU定价业务对象 product_sku_price
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuPrice.class, reverseConvertGenerate = false)
public class ProductSkuPriceBo extends SortEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品SKU表主键
     */
    private Long productSkuId;

    /**
     * 商品SKU编码
     */
    private String productSkuCode;

    /**
     * 原始产品单价（供货商）
     */
    @NotNull(message = "原始产品单价（供货商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal originalUnitPrice;

    /**
     * 原始操作费（供货商）
     */
    @NotNull(message = "原始操作费（供货商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal originalOperationFee;

    /**
     * 原始尾程派送费（供货商）
     */
    @NotNull(message = "原始尾程派送费（供货商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal originalFinalDeliveryFee;

    /**
     * 原始自提价（供货商，产品单价+操作费）
     */
    private BigDecimal originalPickUpPrice;

    /**
     * 原始代发价（供货商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal originalDropShippingPrice;

    /**
     * 平台产品单价（平台+分销商）
     */
    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台+分销商）
     */
    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台+分销商）
     */
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 平台自提价（平台+分销商，产品单价+操作费）
     */
    private BigDecimal platformPickUpPrice;

    /**
     * 平台代发价（平台+分销商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal platformDropShippingPrice;

    /**
     * 建议零售价
     */
    private BigDecimal msrp;


}
