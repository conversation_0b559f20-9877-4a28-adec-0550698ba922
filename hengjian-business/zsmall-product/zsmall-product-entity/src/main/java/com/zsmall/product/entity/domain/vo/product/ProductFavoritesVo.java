package com.zsmall.product.entity.domain.vo.product;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 响应体-商品加入收藏夹数据
 *
 * <AUTHOR>
 * @date 2023/8/16
 */
@Data
public class ProductFavoritesVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品主键
     */
    private Long productId;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 库存总数（若是SPU维度收藏，则取库存最多的SKU，若是SKU维度收藏，则取指定SKU的）
     */
    private Integer stockTotal;

    /**
     * 平台自提价（商品加入时SKU最低价格）
     */
    private BigDecimal platformPickUpPrice;

    /**
     * 平台代发价（商品加入时SKU最低价格）
     */
    private BigDecimal platformDropShippingPrice;

}
