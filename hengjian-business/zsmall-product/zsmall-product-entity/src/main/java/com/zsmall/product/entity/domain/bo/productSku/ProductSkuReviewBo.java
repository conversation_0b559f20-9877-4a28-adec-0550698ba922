package com.zsmall.product.entity.domain.bo.productSku;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/12/27 16:20
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-商品ProductSkuReviewBo信息")
public class ProductSkuReviewBo {

    @Schema(title = "站点id")
    private Long siteId;
    @Schema(title = "站点国家")
    private String countryCode;
    @Schema(title = "币种符号")
    private String currencySymbol;
    @Schema(title = "币种")
    private String currency;

    @Schema(title = "审核类型")
    @ApiModelProperty(value = "审核类型,Update-价格更新,Add-新增的价格,Delete-删除的价格")
    /**
     * 审核类型,Update-价格更新,Add-新增的价格,Delete-删除的价格
     */
    private String reviewType;

    @Schema(title = "改动前代发价格")
    private BigDecimal before_dropShippingPrice;

    @Schema(title = "改动后代发价格")
    private BigDecimal after_dropShippingPrice;

    @Schema(title = "改动前自提价格")
    private BigDecimal before_pickUpPrice;

    @Schema(title = "改动后自提价格")
    private BigDecimal after_pickUpPrice;

    @Schema(title = "改动前建议零售价")
    private BigDecimal before_msrp;

    @Schema(title = "改动后建议零售价")
    private BigDecimal after_msrp;

    @Schema(title = "改动前产品单价")
    private BigDecimal before_unitPrice;

    @Schema(title = "改动后产品单价")
    private BigDecimal after_unitPrice;

    @Schema(title = "改动前操作费")
    private BigDecimal before_operationFee;

    @Schema(title = "改动后操作费")
    private BigDecimal after_operationFee;

    @Schema(title = "改动前尾程配送费")
    private BigDecimal before_finalDeliveryFee;

    @Schema(title = "改动后尾程派送费")
    private BigDecimal after_finalDeliveryFee;
    @Schema(title = "改动前fob")
    private BigDecimal before_fob;

    @Schema(title = "改动后fob")
    private BigDecimal after_fob;

    @Schema(title = "改动前公式ID")
    private Long before_ruleId;

    @Schema(title = "改动后公式ID")
    private Long after_ruleId;
}
