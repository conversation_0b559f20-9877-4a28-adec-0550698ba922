package com.zsmall.product.entity.domain.vo.productSkuStock;

import lombok.Data;


/**
 * 商品SKU库存简易视图对象 product_sku_stock
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
public class ProductSkuStockSimpleVo {

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库系统编号
     */
    private String warehouseSystemCode;

    /**
     * 商品SKU编号
     */
    private String productSkuCode;

    /**
     * 关联物流模板编号
     */
    private String logisticsTemplateNo;

    /**
     * 总库存
     */
    private Integer quantity;

    /**
     *  代发库存标识 0单仓,1非单仓(等于自提库存)
     */
    private Integer dropShippingStockAvailable;

}
