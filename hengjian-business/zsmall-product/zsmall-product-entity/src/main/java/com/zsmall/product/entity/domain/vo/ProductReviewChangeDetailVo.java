package com.zsmall.product.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductReviewChangeDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 商品审核变更详情视图对象 product_review_change_detail
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductReviewChangeDetail.class)
public class ProductReviewChangeDetailVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    private Long siteId;

    /**
     * 审核记录id
     */
    @ExcelProperty(value = "审核记录id")
    private Long reviewRecordId;

    @ExcelIgnore
    private String reviewType;


    /**
     * 字段变更所属商品编号
     */
    @ExcelProperty(value = "字段变更所属商品编号")
    private String productCode;

    /**
     * 字段变更所属商品SKU编号（若是Product变更则不需要填此字段）
     */
    @ExcelProperty(value = "字段变更所属商品SKU编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "若=是Product变更则不需要填此字段")
    private String productSkuCode;
    @ExcelIgnore
    private Long productSkuPriceId;

    /**
     * 字段名
     */
    @ExcelProperty(value = "字段名")
    private String fieldName;

    /**
     * 字段值（修改前）
     */
    @ExcelProperty(value = "字段值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "修=改前")
    private String fieldValueBefore;

    /**
     * 字段值（修改后）
     */
    @ExcelProperty(value = "字段值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "修=改后")
    private String fieldValueAfter;

    /**
     * 允许更新（1-允许，0-不允许，是否允许定时器更新此字段，默认都是允许，若出现立即生效的价格变更，该SKU的所有指定日期生效的价格都将变为不允许更新）
     */
    @ExcelProperty(value = "允许更新", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-允许，0-不允许，是否允许定时器更新此字段，默认都是允许，若出现立即生效的价格变更，该SKU的所有指定日期生效的价格都将变为不允许更新")
    private Long allowUpdate;


}
