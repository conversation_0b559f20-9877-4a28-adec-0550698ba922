package com.zsmall.product.entity.domain.dto.price;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OpenAPIProductPrice {
    /**
     * 商品编码
     */
    private String productSkuCode;
    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * 分销商产品单价
     */
    private BigDecimal platformUnitPrice;

    /**
     * 分销商操作费
     */
    private BigDecimal platformOperationFee;

    /**
     * 分销商尾程派送费
     */
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 分销商总价
     */
    private BigDecimal platformDropShippingPrice;
    /**
     * 分销自提价格
     */
    private BigDecimal platformPickUpPrice;
    /**
     * 价格更新时间
     */
    private Date updateTime;
}
