package com.zsmall.product.entity.domain.vo.wholesale;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductWholesaleTieredPrice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 国外现货批发商品阶梯价主视图对象 product_wholesale_tiered_price
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductWholesaleTieredPrice.class)
public class ProductWholesaleTieredPriceVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品主键
     */
    @ExcelProperty(value = "商品主键")
    private Long productId;

    /**
     * 阶梯价-最小数量（包含）
     */
    @ExcelProperty(value = "阶梯价-最小数量", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "包=含")
    private Integer minimumQuantity;

    /**
     * 预估操作费
     */
    @ExcelProperty(value = "预估操作费")
    private BigDecimal estimatedOperationFee;

    /**
     * 预估运费
     */
    @ExcelProperty(value = "预估运费")
    private BigDecimal estimatedShippingFee;

    /**
     * 预估处理时间（天）
     */
    @ExcelProperty(value = "预估处理时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "天=")
    private Integer estimatedHandleTime;


}
