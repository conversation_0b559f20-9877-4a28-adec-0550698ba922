package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品映射扩展-<PERSON><PERSON><PERSON>信息表
 * <AUTHOR>
 * @date 2023/10/27
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "product_mapping_extend_rakuten_info")
public class ProductMappingExtendRakutenInfo extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 商品映射表主键
     */
    @TableField(value = "product_mapping_id")
    private Long productMappingId;

    /**
     * 乐天宣传语
     */
    @TableField(value = "tagline")
    private String tagline;

    /**
     * 乐天分类ID
     */
    @TableField(value = "genre_id")
    private String genreId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
