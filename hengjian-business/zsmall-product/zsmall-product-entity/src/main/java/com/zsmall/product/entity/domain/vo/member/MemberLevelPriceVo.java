package com.zsmall.product.entity.domain.vo.member;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/7/4 18:37
 */
@Data
public class MemberLevelPriceVo {

    @ApiModelProperty(value = "站点id")
    private Long siteId;

    @ApiModelProperty(value = "货币符号")
    private String currencySymbol;

    @ApiModelProperty(value = "等级id")
    @NotNull(message = "等级id")
    private Long levelId;

    @ApiModelProperty(value = "等级名称")
    @NotNull(message = "等级名称")
    private String levelName;

    @ApiModelProperty("默认商品单价")
    private BigDecimal originalUnitPrice;

    @ApiModelProperty("默认操作费")
    private BigDecimal originalOperationFee;

    @ApiModelProperty("默认尾程派送费")
    private BigDecimal originalFinalDeliveryFee;

    @ApiModelProperty("价格规则id,编辑时必传")
    private Long rulePriceId;

    @ApiModelProperty("等级字典code")
    private Long dictCode;

    /**
     * 是默认值
     */
    @ApiModelProperty("是否是默认售价")
    private Boolean isDefault;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
