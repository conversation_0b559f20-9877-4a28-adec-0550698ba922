package com.zsmall.product.entity.domain.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-需要同步的商品sku集合信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-需要同步的商品sku集合信息")
public class RespEditedProductSkuListBody {

    @Schema(title = "商品sku集合")
    private List<RespEditedProductSkuBody> productSkuList;

}
