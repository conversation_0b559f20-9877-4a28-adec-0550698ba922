package com.zsmall.product.entity.domain.dto.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-圈货信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-圈货信息")
public class BuyoutActivityBody {

    @Schema(title = "活动ID")
    private String activityID;

    @Schema(title = "活动类型")
    private String activityType;

    @Schema(title = "商品图片")
    private String productImg;

    @Schema(title = "圈货单价")
    private String buyoutUnitPrice;

    @Schema(title = "剩余可圈货数量")
    private Integer buyoutQuantity;

    @Schema(title = "已售数量")
    private Integer buyoutSold;

    @Schema(title = "Item NO.")
    private String itemNo;

    @Schema(title = "仓储费")
    private String storageFee;

    @Schema(title = "最小圈货数量")
    private Integer buyoutQuantityMinimum;

    @Schema(title = "操作费")
    private String operationFee;

    @Schema(title = "尾程派送费")
    private String finalDeliveryFee;

    @Schema(title = "平台定金单价")
    private String platformDepositUnitPrice;

    @Schema(title = "仓库库存信息")
    private List<Inventory> inventoryList;

    /**
     * 仓库信息
     */
    @Data
    public static class Inventory {

        @Schema(title = "仓库ID")
        private String warehouseID;

        @Schema(title = "库存（每个仓库）")
        private Integer quantity;
    }
}
