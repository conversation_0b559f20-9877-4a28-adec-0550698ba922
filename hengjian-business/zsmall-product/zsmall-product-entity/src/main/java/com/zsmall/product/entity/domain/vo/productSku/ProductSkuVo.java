package com.zsmall.product.entity.domain.vo.productSku;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuAttachment;
import com.zsmall.product.entity.domain.ProductSkuAttribute;
import com.zsmall.product.entity.domain.ProductSkuDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 商品SKU导入视图对象 product_sku
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductSku.class)
public class ProductSkuVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    /**
     * 商品SPU表主键
     */
    @ExcelProperty(value = "商品SPU表主键")
    private Long productId;

    /**
     * 商品SPU唯一编号
     */
    @ExcelProperty(value = "商品SPU唯一编号")
    private String productCode;

    /**
     * Sku唯一编号（ItemNo.）
     */
    @ExcelProperty(value = "Sku唯一编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "I=temNo.")
    private String productSkuCode;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 最小库存单位（Sku）
     */
    @ExcelProperty(value = "最小库存单位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "S=ku")
    private String sku;

    /**
     * 系统库存单位
     */
    @ExcelProperty(value = "系统库存单位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "erpSku")
    private String erpSku;

    /**
     * 商品统一代码（UPC）
     */
    @ExcelProperty(value = "商品统一代码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "U=PC")
    private String upc;

    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;

    /**
     * Sku库存总数
     */
    @ExcelProperty(value = "Sku库存总数")
    private Integer stockTotal;

    /**
     * 库存管理方（区分自有仓库管理还是第三方平台管理）
     */
    @ExcelProperty(value = "库存管理方", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "区=分自有仓库管理还是第三方平台管理")
    private StockManagerEnum stockManager;

    /**
     * Sku货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
    @ExcelProperty(value = "Sku货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架")
    private ShelfStateEnum shelfState;

    /**
     * Sku审核状态
     */
    @ExcelProperty(value = "Sku审核状态")
    private ProductVerifyStateEnum verifyState;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 铺货渠道主键
     */
    @ExcelProperty(value = "铺货渠道主键")
    private Long channelId;

    /**
     * 铺货渠道类型
     */
    @ExcelProperty(value = "铺货渠道类型")
    private ChannelTypeEnum channelType;

    private List<ProductSkuAttribute> skuAttributeList;
    private List<ProductSkuAttachment> saveSkuAttachmentList;
    private List<Long> delSkuAttachmentIds;
    private ProductSkuDetail skuDetail;

}
