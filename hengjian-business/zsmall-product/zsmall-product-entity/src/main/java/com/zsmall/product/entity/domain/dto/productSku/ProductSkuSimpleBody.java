package com.zsmall.product.entity.domain.dto.productSku;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品SKU基础数据
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "商品SKU基础数据")
public class ProductSkuSimpleBody {

    @Schema(title = "商品编号")
    private String productCode;

    @Schema(title = "skuCode")
    private String skuCode;
}
