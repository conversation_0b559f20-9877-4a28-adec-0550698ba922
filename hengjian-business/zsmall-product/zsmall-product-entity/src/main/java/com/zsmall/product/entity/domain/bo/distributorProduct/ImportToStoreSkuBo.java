package com.zsmall.product.entity.domain.bo.distributorProduct;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 请求体-铺货至商店（Sku信息）
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ImportToStoreSkuBo {

    /**
     * 是否选中
     */
    private boolean check;

    /**
     * Item No.
     */
    private String productSkuCode;

    /**
     * 活动编号
     */
    private String activityCode;

    /**
     * 最终价格
     */
    private BigDecimal finalPrice;

}
