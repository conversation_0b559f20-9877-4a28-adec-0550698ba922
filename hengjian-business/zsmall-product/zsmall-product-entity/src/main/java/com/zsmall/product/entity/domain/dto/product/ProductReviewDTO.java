package com.zsmall.product.entity.domain.dto.product;

import com.zsmall.common.domain.dto.ChangeFieldDTO;
import com.zsmall.common.enums.product.ProductReviewTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuReviewDTO;
import lombok.Data;

import java.util.List;

/**
 * 商品审核DTO
 *
 * <AUTHOR>
 */
@Data
public class ProductReviewDTO {

    private String productCode;

    private String submitTenantId;

    private ProductReviewTypeEnum reviewType;

    private ProductVerifyStateEnum reviewStatus;

    private List<ProductSkuReviewDTO> productSkuReviewList;

    private List<ChangeFieldDTO> changeFields;

}
