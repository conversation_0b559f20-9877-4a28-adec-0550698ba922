package com.zsmall.product.entity.domain.bo;

import cn.hutool.json.JSONArray;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductReviewRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 商品审核记录业务对象 product_review_record
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductReviewRecord.class, reverseConvertGenerate = false)
public class ProductReviewRecordBo extends BaseEntity {

    /**
     * 查询类型: 商品名称-ProductName，SKU ID - SKU ID,SKU - SKU,SPU-SPU,供应商id-SupplierTenantId
     */
    private String queryType;

    private String queryValue;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 商品编号集合
     */
    private List<String> productCodeList;

    /**
     * 审核ID集合
     */
    private List<Long> reviewRecordIds;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 审核意见选项（多选
     */
    private JSONArray reviewOpinionOption;

    /**
     * 提交审核时间
     */
    private Date submitDateTime;

    /**
     * 提交审核用户ID
     */
    private Long submitUserId;

    /**
     * 审核员工ID
     */
    private Long reviewUserId;

    /**
     * 审核时间
     */
    private Date reviewDateTime;

    /**
     * 审核类型（目前只有价格）：Price-价格
     */
    private String reviewType;

    /**
     * 审核状态：Pending-审核中，Accepted-已审核，Rejected-已驳回
     */
    private String reviewStatus;

    /**
     * 生效类型（NOW-立即生效，DATE-指定日期
     */
    private String effectiveType;

    /**
     * 生效时间（仅价格审核需要用到）
     */
    private String effectiveDate;


}
