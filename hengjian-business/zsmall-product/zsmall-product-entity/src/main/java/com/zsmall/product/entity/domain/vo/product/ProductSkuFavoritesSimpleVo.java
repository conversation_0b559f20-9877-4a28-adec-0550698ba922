package com.zsmall.product.entity.domain.vo.product;

import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.CellGroupMerge;
import com.hengjian.common.excel.annotation.CellMerge;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应体-商品SKU收藏夹导出简略数据
 *
 * <AUTHOR>
 * @date 2023/8/25
 */
@Data
@CellGroupMerge("productCode")
public class ProductSkuFavoritesSimpleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @CellMerge
    @ExcelProperty(order = 0)
    @ExcelI18nFormat(code = "zsmall.excel.productCode")
    private String productCode;

    @CellMerge
    @ExcelProperty(order = 5)
    @ExcelI18nFormat(code = "zsmall.excel.productName")
    private String productName;

    @ExcelProperty(order = 10)
    @ExcelI18nFormat(code = "zsmall.excel.productSkuCode")
    private String productSkuCode;

    @ExcelProperty(order = 20)
    @ExcelI18nFormat(code = "zsmall.excel.pickUpPrice")
    private String pickUpPrice;

    @ExcelProperty(order = 30)
    @ExcelI18nFormat(code = "zsmall.excel.dropShippingPrice")
    private String dropShippingPrice;

    @ExcelProperty(order = 40)
    @ExcelI18nFormat(code = "zsmall.excel.stockTotal")
    private Integer stockTotal;

}
