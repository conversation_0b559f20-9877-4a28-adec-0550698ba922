package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@TableName(value ="rule_level_product_price")
@Data
public class RuleLevelProductPrice implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 站点id
     */
    private Long siteId;
    /**
     * 货币
     */
    private String currency;

    private String countryCode;
    /**
     * 货币符号
     */
    private String currencySymbol;

    /**
     * 规则定制方租户标识
     */
    private String ruleCustomizerTenantId;

    /**
     * 等级id
     */
    private Long levelId;

    /**
     * 原始产品单价（供货商）
     */

    private BigDecimal originalUnitPrice;

    /**
     * 原始操作费（供货商）
     */

    private BigDecimal originalOperationFee;

    /**
     * 原始尾程派送费（供货商）
     */

    private BigDecimal originalFinalDeliveryFee;

    /**
     * 原始自提价（供货商，产品单价+操作费）
     */

    private BigDecimal originalPickUpPrice;

    /**
     * 原始代发价（供货商，产品单价+操作费+尾程派送费）
     */

    private BigDecimal originalDropShippingPrice;

    /**
     *
     */
    private Long productSkuId;

    /**
     *
     */
    private Long productId;

    /**
     * 平台产品单价
     */

    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台+分销商）
     */

    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台+分销商）
     */

    private BigDecimal platformFinalDeliveryFee;

    /**
     * 平台自提价格
     */

    private BigDecimal platformPickUpPrice;

    /**
     *
     */

    private BigDecimal platformDropShippingPrice;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;

    /**
     *
     */
    private Long createBy;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Long updateBy;

    /**
     *
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String productSkuCode;
}
