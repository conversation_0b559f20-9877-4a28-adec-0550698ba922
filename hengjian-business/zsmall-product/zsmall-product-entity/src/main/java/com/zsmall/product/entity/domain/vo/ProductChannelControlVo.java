package com.zsmall.product.entity.domain.vo;

import cn.hutool.json.JSONArray;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductChannelControl;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品渠道管控视图对象 product_channel_control
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductChannelControl.class)
public class ProductChannelControlVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 渠道类型
     */
    @ExcelProperty(value = "渠道类型")
    private String channelType;

    /**
     * Sku唯一编号（ItemNo.）
     */
    @ExcelProperty(value = "Sku唯一编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "I=temNo.")
    private String productSkuCode;

    /**
     * 受允许的租户编号（JSON数组）
     */
    @ExcelProperty(value = "受允许的租户编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "J=SON数组")
    private JSONArray allowTenantId;

    /**
     * 是否全渠道管控
     */
    @ExcelProperty(value = "是否全渠道管控")
    private Boolean allChannelControl;


}
