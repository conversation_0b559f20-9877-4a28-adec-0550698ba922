package com.zsmall.product.entity.domain.bo.productQuestions;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/13
 **/

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "查询回复编辑日志请求参数")
public class ReqLogQueryBody {

    @Schema(title = "回复Code")
    private String answerCode;

}
