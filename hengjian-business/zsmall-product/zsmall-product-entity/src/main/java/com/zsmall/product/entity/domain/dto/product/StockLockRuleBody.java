package com.zsmall.product.entity.domain.dto.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-锁货规则信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-锁货规则信息")
public class StockLockRuleBody {

    @Schema(title = "锁货规则(DeductDeposit-扣定金;PayUnsoldPrice-退还定金，付清未售完的商品的价钱)")
    private List<String> stockLockRules;

    @Schema(title = "库存锁是否开启")
    private Boolean stockLock;

    @Schema(title = "锁货每天的仓库管理费用")
    private Double warehouseFee;

}
