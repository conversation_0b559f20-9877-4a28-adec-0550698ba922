package com.zsmall.product.entity.domain.vo.product;

import com.zsmall.product.entity.domain.dto.product.ProductDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-商品集合信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-商品集合信息")
public class RespProductListBody {

    @Schema(title = "是否有商品，用于区分完全没有商品和没有符合搜索条件的商品")
    private Boolean hasProduct;

    @Schema(title = "总页数")
    Integer totalPage;

    @Schema(title = "数据总条数")
    Long total;

    @Schema(title = "数据集合")
    List<ProductDto> results;

    @Schema(title = "商品来源类型")
    private String productSourceType;

}
