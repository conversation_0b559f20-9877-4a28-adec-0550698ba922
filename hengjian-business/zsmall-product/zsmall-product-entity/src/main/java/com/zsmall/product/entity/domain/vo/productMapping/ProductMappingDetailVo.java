package com.zsmall.product.entity.domain.vo.productMapping;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 响应体-商品映射信息详情
 * <AUTHOR>
 * @date 2023-06-20
 */
@Data
public class ProductMappingDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品SKU唯一编号
     */
    private String productSkuCode;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 映射SKU
     */
    private String mappingSku;

    /**
     * UPC
     */
    private String upc;

    /**
     * 主图展示地址
     */
    private String imageShowUrl;

    /**
     * 尺寸
     */
    private String size;

    /**
     * 打包尺寸
     */
    private String packSize;

    /**
     * 重量
     */
    private String weight;

    /**
     * 打包重量
     */
    private String packWeight;

    /**
     * 原始价格（未参加任何活动的）
     */
    private BigDecimal originPrice;

    /**
     * 成本价
     */
    private BigDecimal basePrice;

    /**
     * 涨价类型
     */
    private String markUpType;

    /**
     * 涨价值
     */
    private BigDecimal markUpValue;

    /**
     * 涨价后总记录
     */
    private BigDecimal markUpTotal;

    /**
     * 最终价格
     */
    private BigDecimal finalPrice;

    /**
     * 选中的活动编号
     */
    private String activityCode;

    /**
     * 选中的活动状态
     */
    private String activityState;

    /**
     * 当前选中的乐天品类ID（Rakuten专属）
     */
    private String curRakutenGenreId;

    /**
     * 当前选中的乐天品类名称（Rakuten专属）
     */
    private String curRakutenGenreName;

    /**
     * 选中的活动
     */
    private List<String> selectActivity;

    /**
     * 仓库映射配置
     */
    private List<SkuWarehouseMappingVo> warehouseConfigs;

    /**
     * 可用活动
     */
    private List<MappingAvailableActivityVo> availableActivities;
    /**
     * 渠道ID
     */
    private  Long channelId;

}
