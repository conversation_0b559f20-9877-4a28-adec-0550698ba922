package com.zsmall.product.entity.domain.bo.productQuestions;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/1/10
 **/

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "追加提问请求参数")
public class ReqQuestionsContinueFormBody {

    @Schema(title = "提问内容")
    private String question;

    @Schema(title = "提问编码（当前提问的编码，不是追加提问的的编码）")
    private String questionCode;


}
