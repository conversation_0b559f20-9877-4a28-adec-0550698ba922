package com.zsmall.product.entity.domain.viewer;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品SKU销售情况视图
 * <AUTHOR>
 */
@Data
@TableName("view_product_sku_sales")
public class ViewProductSkuSales implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品 id
     */
    private Long productId;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * 商品code
     */
    private String productCode;

    /**
     * 商品sku唯一编码
     */
    private String productSkuCode;

    /**
     * 代发价格
     */
    private BigDecimal originalPickUpPrice;

    /**
     * 代发价格
     */
    private BigDecimal originalDropShippingPrice;

    /**
     * 代发价格
     */
    private BigDecimal platformPickUpPrice;

    /**
     * 代发价格
     */
    private BigDecimal platformDropShippingPrice;

    /**
     * 库存数量
     */
    private Integer stockTotal;

    /**
     * 销售数量
     */
    private Integer soldQuantity;

    /**
     * 退货数量
     */
    private Integer refundQuantity;

    /**
     * spu 货架状态
     */
    private String spuShelfState;

    /**
     * spu 有效状态
     */
    private String spuDelFlag;

    /**
     * sku 销售状态
     */
    private String skuShelfState;

    /**
     * spu 有效状态
     */
    private String skuDelFlag;

}
