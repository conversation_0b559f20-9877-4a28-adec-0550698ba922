package com.zsmall.product.entity.domain.bo;

import cn.hutool.json.JSONArray;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductGlobalAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商品全局属性业务对象 product_global_attribute
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductGlobalAttribute.class, reverseConvertGenerate = false)
public class ProductGlobalAttributeBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "[API]关键数据丢失", groups = { EditGroup.class })
    private Long id;

    /**
     * 属性名称（主名称）
     */
    @NotBlank(message = "属性名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeName;

    /**
     * 属性值（字符串数组）
     */
    // @NotEmpty(message = "属性值至少填写一个", groups = { AddGroup.class, EditGroup.class })
    private JSONArray attributeValues;

    /**
     * 属性备注
     */
    private String attributeNotes;

    /**
     * 属性作用域（所有场景、仅通用规格，仅可选规格等）
     */
    // @NotBlank(message = "属性作用域必选", groups = { AddGroup.class, EditGroup.class })
    private String attributeScope;

    /**
     * 属性状态（0-停用，1-启用等）
     */
    @NotNull(message = "属性状态不能必选", groups = { AddGroup.class, EditGroup.class })
    private Integer attributeState;

    /**
     * 是否支持自定义值（0-否，1-是）
     */
    @NotNull(message = "是否支持自定义值必选", groups = { AddGroup.class, EditGroup.class })
    private Boolean isSupportCustom;

    /**
     * 是否是基础属性（0-否，1-是）
     */
    // @NotNull(message = "是否是基础属性必选", groups = { AddGroup.class, EditGroup.class })
    private Boolean isBasicAttribute = false;

    /**
     * 绑定分类（AllCategory-所有分类，SpecifyCategory-指定分类）
     */
    private String bindingCategory;

    /**
     * 指定分类主键数组
     */
    private List<Long> specifyCategoryIds;

    /**
     * 是否必填
     */
    private Boolean isRequired = false;

}
