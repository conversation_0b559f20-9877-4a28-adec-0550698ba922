package com.zsmall.product.entity.domain.dto.product;

import cn.hutool.json.JSONArray;
import com.zsmall.common.domain.vo.AttachmentDto;
import com.zsmall.common.domain.vo.KeyValueBody;
import com.zsmall.common.domain.vo.RespMessageBody;
import com.zsmall.common.domain.vo.SkuAttributesInfoBody;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-商品信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-商品信息")
public class ProductDto {

    /*@Schema(title = "审核记录id")
    private Long id;*/

    @Schema(title = "商品code")
    private String productCode;

    @Schema(title = "创建人code")
    private String creatorCode;

    @Schema(title = "创建时间")
    private String submitDateTime;

    @Schema(title = "审核时间")
    private String reviewDateTime;

    @Schema(title = "中文名称")
    private String name;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "商品自定义属性集合")
    private List<KeyValueBody> customAttributeList;

    @Schema(title = "上/下架状态")
    private String shelfState;

    @Schema(title = "审核状态")
    private String reviewStatus;

    @Schema(title = "排序")
    private Integer sort;

    @Schema(title = "附件")
    private List<AttachmentDto> attachmentList;

    @Schema(title = "其他附件（PDF、说明书等）")
    private AttachmentDto otherAttachment;

    @Schema(title = "商品分类id集合，分类层级对应集合下标，categoryIdList[0]无意义")
    private List<Long> categoryIdList;

    @Schema(title = "商品分类names，例: 分类1/分类2/分类3")
    private String categoryNames;

    @Schema(title = "商品图片显示链接")
    private String productImageShowUrl;

    @Schema(title = "首页新品宣传图显示链接")
    private String homePageImageShowUrl;

    @Schema(title = "短视频链接")
    private String shortVideoUrl;

    @Schema(title = "排序类型")
    private String sortType;

    @Schema(title = "渠道：OneLink，Wayfair，Shopify，Amazon")
    private String channelType;

    @Schema(title = "规格属性信息集合")
    private List<SkuAttributesInfoBody> skuAttributesList;

    @Schema(title = "sku表格数据集合")
    private List<ProductSkuBody> skuList;

    @Schema(title = "分类属性集合")
    private List<ProductAttributesBody> productAttributesList;

    @Schema(title = "审核意见选项(英文)")
    private String reviewOpinionOptionsEn;

    @Schema(title = "审核意见选项(中文)")
    private String reviewOpinionOptionsZh;

    @Schema(title = "审核意见选项")
    private JSONArray reviewOpinionOption;

    @Schema(title = "审核意见")
    private String reviewOpinion;

    @Schema(title = "商品卖点集合")
    private List<KeyValueBody> productFaturesList;

    @Schema(title = "库存锁是否开启")
    private Boolean stockLock;

    @Schema(title = "自提类型：PickUpOnly-仅自提，DropShippingOnly-仅代发，Different-都支持")
    private String supportedLogistics;

    @Schema(title = "锁货费用类型：AllSKU - 所有sku统一每天收费；DifferentSKU - 各个sku分别收不同费用")
    private String warehouseFeeType;

    @Schema(title = "锁货每天的仓库管理费用")
    private Double warehouseFee;

    @Schema(title = "商品锁货数量")
    private Integer stockLockNum;

    @Schema(title = "锁货规则")
    private List<String> stockLockRules;

    @Schema(title = "渠道同步状态")
    private String syncStatus;

    @Schema(title = "渠道同步响应信息")
    private String syncResultInfo;

    @Schema(title = "仓库地址id")
    private Long warehouseAddressId;

    @Schema(title = "商家已上架过")
    private boolean alreadyOnShelf = false;

    @Schema(title = "禁售渠道（二期新增，商品增改时使用）")
    private List<String> forbiddenChannel;

    @Schema(title = "商品须知类型")
    private String productNoticeType;

    @Schema(title = "商品须知（二期新增，商品增改时使用）")
    private String productNotice;

    @Schema(title = "消息通知")
    private RespMessageBody notifyMessage;

    @Schema(title = "修改前自提类型")
    private String before_supportedLogistics;

    @Schema(title = "修改后自提类型")
    private String after_supportedLogistics;

    @Schema(title = "价格变更生效时间")
    private String effectiveDate;

    @Schema(title = "审核类型")
    private String reviewType;

    @Schema(title = "是否存在价格变更")
    private boolean hasPriceChange = false;

    @Schema(title = "商品类型")
    private String productType;

}
