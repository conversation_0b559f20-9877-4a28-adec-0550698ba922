package com.zsmall.product.entity.domain.vo.product;

import lombok.Data;

import java.math.BigDecimal;
/**
 * 响应体-商城展示用数据
 **/
@Data
public class ProductSkuForMarketplaceShowVo {

    private String productType;

    private String itemNo;

    private Integer sort;

    private String name;

    private String img;

    private String price;

    private BigDecimal memberPrice;

    private String labelName;

    private String productCode;

    private Integer stock;

    private String transportMethod;

    private String supportedLogistics;
}
