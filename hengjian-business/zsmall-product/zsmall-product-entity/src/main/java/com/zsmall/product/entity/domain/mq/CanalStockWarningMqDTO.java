package com.zsmall.product.entity.domain.mq;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Canal库存预警消息数据传输对象
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
public class CanalStockWarningMqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据库名
     */
    private String database;

    /**
     * 表名
     */
    private String table;

    /**
     * 操作类型 INSERT/UPDATE/DELETE
     */
    private String type;

    /**
     * 最新的数据
     */
    private List<CanalProductStockData> data;

    /**
     * 变更前的数据
     */
    private List<CanalProductStockData> old;

}
