package com.zsmall.product.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.ProductAttachment;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;



/**
 * 商品SPU附件视图对象 product_attachment
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductAttachment.class)
public class ProductAttachmentVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 存储对象主键
     */
    @ExcelProperty(value = "存储对象主键")
    private Long ossId;

    /**
     * 商品SPU表主键
     */
    @ExcelProperty(value = "商品SPU表主键")
    private Long productId;

    /**
     * 附件名称
     */
    @ExcelProperty(value = "附件名称")
    private String attachmentName;

    /**
     * 附件原名
     */
    @ExcelProperty(value = "附件原名")
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    @ExcelProperty(value = "附件后缀")
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    @ExcelProperty(value = "附件存放路径")
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    @ExcelProperty(value = "附件展示地址")
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    @ExcelProperty(value = "附件排序")
    private Integer attachmentSort;

    /**
     * 附件类型
     */
    @ExcelProperty(value = "附件类型")
    private String attachmentType;


}
