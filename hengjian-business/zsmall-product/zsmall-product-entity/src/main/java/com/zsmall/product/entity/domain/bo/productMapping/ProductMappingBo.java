package com.zsmall.product.entity.domain.bo.productMapping;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductMapping;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商品映射业务对象 product_mapping
 *
 * <AUTHOR>
 * @date 2023-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductMapping.class, reverseConvertGenerate = false)
public class ProductMappingBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 供货商租户编号
     */
    @NotBlank(message = "供货商租户编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierTenantId;

    /**
     * SPU唯一编号
     */
    @NotBlank(message = "SPU唯一编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productCode;

    /**
     * 关联的商品SKU主键
     */
    @NotNull(message = "关联的商品SKU主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productSkuId;

    /**
     * 商品SKU唯一编号
     */
    @NotBlank(message = "商品SKU唯一编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productSkuCode;

    /**
     * 铺货渠道类型
     */
    @NotBlank(message = "铺货渠道类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelType;

    /**
     * 铺货渠道主键
     */
    @NotNull(message = "铺货渠道主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long channelId;

    /**
     * 参与的活动类型
     */
    @NotBlank(message = "参与的活动类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityType;

    /**
     * 参与的活动编号
     */
    @NotBlank(message = "参与的活动编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityCode;

    /**
     * 商品名
     */
    @NotBlank(message = "商品名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productName;

    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    @NotBlank(message = "规格组成名称，示例：尺寸-大;颜色-白色不能为空", groups = { AddGroup.class, EditGroup.class })
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    @NotBlank(message = "规格值名称，示例：大/白色不能为空", groups = { AddGroup.class, EditGroup.class })
    private String specValName;

    /**
     * 映射SKU
     */
    @NotBlank(message = "映射SKU不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mappingSku;

    /**
     * 提价类型
     */
    @NotBlank(message = "提价类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String markUpType;

    /**
     * 提价值
     */
    @NotNull(message = "提价值不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal markUpValue;

    /**
     * 最终单价
     */
    @NotNull(message = "最终单价不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal finalPrice;

    /**
     * 渠道同步状态
     */
    @NotBlank(message = "渠道同步状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String syncState;

    /**
     * 第三方渠道商品ID（用于与第三方接口交互）
     */
    @NotNull(message = "第三方渠道商品ID（用于与第三方接口交互）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelProductId;

    /**
     * 第三方渠道商品SKU ID（用于与第三方接口交互）
     */
    @NotNull(message = "第三方渠道商品SKU ID（用于与第三方接口交互）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelSkuId;

    /**
     * 主图存放路径
     */
    @NotBlank(message = "主图存放路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String imageSavePath;

    /**
     * 主图展示地址
     */
    @NotBlank(message = "主图展示地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String imageShowUrl;


}
