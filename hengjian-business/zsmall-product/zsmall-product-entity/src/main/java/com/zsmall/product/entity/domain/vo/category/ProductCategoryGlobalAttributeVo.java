package com.zsmall.product.entity.domain.vo.category;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductCategoryGlobalAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品分类-商品全局属性关联视图对象 product_category_global_attribute
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductCategoryGlobalAttribute.class)
public class ProductCategoryGlobalAttributeVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 关联的商品全局属性表主键
     */
    @ExcelProperty(value = "关联的商品全局属性表主键")
    private Long globalAttributeId;

    /**
     * 关联的商品分类表主键
     */
    @ExcelProperty(value = "关联的商品分类表主键")
    private Long productCategoryId;

    /**
     * 是否必填：0-否，1-是
     */
    @ExcelProperty(value = "是否必填：0-否，1-是")
    private Boolean isRequired;

    /**
     * 自定义值（json数组）
     */
    @ExcelProperty(value = "自定义值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "j=son数组")
    private String customValues;


}
