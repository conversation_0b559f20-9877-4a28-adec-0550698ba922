package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.productQuestion.QuestionStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 商品问答对象 product_question
 *
 * <AUTHOR> Li
 * @date 2023-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_question")
public class ProductQuestion extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 提问表自增主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 提问编码
     */
    private String questionCode;

    /**
     * 商品表product_code
     */
    private String productCode;

    /**
     * 商品sku表product_sku_code
     */
    private String productSkuCode;

    /**
     * 问题
     */
    private String question;

    /**
     * 回复状态：Pending，Solved
     */
    private QuestionStatusEnum status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
