package com.zsmall.product.entity.domain.bo.prodcutQuestion;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductQuestion;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 商品问答业务对象 product_question
 *
 * <AUTHOR> Li
 * @date 2023-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductQuestion.class, reverseConvertGenerate = false)
public class ProductQuestionBo extends BaseEntity {

    /**
     * 提问编码
     */
    private String questionCode;

    /**
     * 查询条件 - 商品表product_code
     */
//    @NotBlank(message = "商品表product_code不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productCode;

    /**
     * 商品sku表product_sku_code
     */
    @NotBlank(message = "{zsmall.productQA.productSkuCodeIsnullError}", groups = { AddGroup.class, EditGroup.class })
    private String productSkuCode;

    /**
     * 问题
     */
    @NotBlank(message = "{zsmall.productQA.textQuestionsError}", groups = { AddGroup.class, EditGroup.class })
    private String question;

    /**
     * 查询条件 - 问题状态：Pending，Solved
     */
//    @NotBlank(message = "问题状态：Pending，Solved不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 查询条件 - 创建时间
     */
    private List<String> createTimes;
}
