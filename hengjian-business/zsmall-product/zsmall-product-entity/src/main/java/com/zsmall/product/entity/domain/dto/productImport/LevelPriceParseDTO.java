package com.zsmall.product.entity.domain.dto.productImport;

import lombok.Data;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/7/11 10:27
 */
@Data
public class LevelPriceParseDTO {
    private String levelName;

    private String dictCode;

    private Long levelId;

    private Long siteId;
    /**
     * 货币
     */
    private String currency;

    private String countryCode;
    /**
     * 货币符号
     */
    private String currencySymbol;

//    //    @ExcelProperty(value = "自提价")
//    private BigDecimal pickUpPrice;
//
//    //    @ExcelProperty(value = "代发价格")
//    private BigDecimal dropPrice;


    private BigDecimal unitPrice;

    private BigDecimal operationFee;

    private BigDecimal finalDeliveryFee;

    private Long productSkuId;
    private Long productId;

}

