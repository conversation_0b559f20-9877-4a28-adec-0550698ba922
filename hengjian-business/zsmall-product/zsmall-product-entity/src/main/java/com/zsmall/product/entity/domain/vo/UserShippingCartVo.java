package com.zsmall.product.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.UserShippingCart;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 用户购物车视图对象 user_shipping_cart
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserShippingCart.class)
public class UserShippingCartVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户主键
     */
    @ExcelProperty(value = "用户主键")
    private Long userId;

    /**
     * Sku唯一编号（ItemNo.）
     */
    @ExcelProperty(value = "Sku唯一编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "I=temNo.")
    private String productSkuCode;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Integer quantity;

    /**
     * 加入购物车时的图片URL
     */
    @ExcelProperty(value = "加入购物车时的图片URL")
    private String beforeImageShowUrl;

    /**
     * 加入购物车时的库存数量
     */
    @ExcelProperty(value = "加入购物车时的库存数量")
    private Integer beforeStockQuantity;

    /**
     * 加入购物车时的自提价（取平台自提价）
     */
    @ExcelProperty(value = "加入购物车时的自提价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "取=平台自提价")
    private BigDecimal beforePickUpPrice;

    /**
     * 加入购物车时的代发价（取平台代发价）
     */
    @ExcelProperty(value = "加入购物车时的代发价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "取=平台代发价")
    private BigDecimal beforeDropShippingPrice;

    /**
     * 加入购物车时的建议零售价
     */
    @ExcelProperty(value = "加入购物车时的建议零售价")
    private BigDecimal beforeMsrp;


}
