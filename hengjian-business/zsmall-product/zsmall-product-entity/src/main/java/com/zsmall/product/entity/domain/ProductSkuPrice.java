package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;



/**
 * 商品SKU定价对象 product_sku_price
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("product_sku_price")
@AutoMapper(target = ProductSkuPriceVo.class)
public class ProductSkuPrice extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品SKU表主键
     */
    private Long productSkuId;

    /**
     * 商品SKU编码
     */
    private String productSkuCode;

    /**
     * 原始产品单价（供货商）----报关价
     */
    private BigDecimal originalUnitPrice;

    /**
     * 原始操作费（供货商）
     */
    private BigDecimal originalOperationFee;

    /**
     * 原始尾程派送费（供货商）
     */
    private BigDecimal originalFinalDeliveryFee;

    /**
     * 原始自提价（供货商，产品单价+操作费）
     */
    private BigDecimal originalPickUpPrice;

    /**
     * 原始代发价（供货商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal originalDropShippingPrice;

    /**
     * 平台产品单价（平台+分销商）
     */
    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台+分销商）
     */
    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台+分销商）
     */
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 平台自提价（平台+分销商，产品单价+操作费）
     */
    private BigDecimal platformPickUpPrice;

    /**
     * 平台代发价（平台+分销商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal platformDropShippingPrice;

    private Long productId;

    private Long siteId;
    /**
     * 站点
     */
    private String countryCode;
    /**
     * 货币
     */
    private String currency;
    /**
     * 货币符号
     */
    private String currencySymbol;

    /**
     * 建议零售价
     */
    private BigDecimal msrp;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private Long productSkuPriceRuleId;

    @TableField(exist = false)
    private String productSkuPriceRuleCode;


    public ProductSkuPrice(BigDecimal originalUnitPrice, BigDecimal originalOperationFee, BigDecimal originalFinalDeliveryFee, BigDecimal originalPickUpPrice, BigDecimal originalDropShippingPrice, BigDecimal platformUnitPrice, BigDecimal platformOperationFee, BigDecimal platformFinalDeliveryFee, BigDecimal platformPickUpPrice, BigDecimal platformDropShippingPrice) {
        this.originalUnitPrice = originalUnitPrice;
        this.originalOperationFee = originalOperationFee;
        this.originalFinalDeliveryFee = originalFinalDeliveryFee;
        this.originalPickUpPrice = originalPickUpPrice;
        this.originalDropShippingPrice = originalDropShippingPrice;
        this.platformUnitPrice = platformUnitPrice;
        this.platformOperationFee = platformOperationFee;
        this.platformFinalDeliveryFee = platformFinalDeliveryFee;
        this.platformPickUpPrice = platformPickUpPrice;
        this.platformDropShippingPrice = platformDropShippingPrice;
    }

    public void setMsrp(BigDecimal msrp) {
        if (msrp == null) {
            this.msrp = BigDecimal.ZERO;
        } else {
            this.msrp = msrp;
        }
    }
}
