package com.zsmall.product.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.ProductSkuAttachment;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 商品SKU附件视图对象 product_sku_attachment
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductSkuAttachment.class)
public class ProductSkuAttachmentVo implements Serializable {



    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品SKU表主键
     */
    @ExcelProperty(value = "商品SKU表主键")
    private Long productSkuId;

    /**
     * 附件名称
     */
    @ExcelProperty(value = "附件名称")
    private String attachmentName;

    /**
     * 附件原名
     */
    @ExcelProperty(value = "附件原名")
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    @ExcelProperty(value = "附件后缀")
    private String attachmentSuffix;

    /**
     * ossId
     */
    @ExcelProperty(value = "存储桶ID")
    private Long ossId;

    /**
     * 附件存放路径
     */
    @ExcelProperty(value = "附件存放路径")
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    @ExcelProperty(value = "附件展示地址")
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    @ExcelProperty(value = "附件排序")
    private Integer attachmentSort;

    /**
     * 附件类型
     */
    @ExcelProperty(value = "附件类型")
    private String attachmentType;

    @ExcelIgnore
    private Long productId;
}
