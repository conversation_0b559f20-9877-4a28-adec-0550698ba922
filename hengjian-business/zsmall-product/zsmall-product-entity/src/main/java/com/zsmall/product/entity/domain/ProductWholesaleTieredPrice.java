package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;


/**
 * 国外现货批发商品阶梯价主对象 product_wholesale_tiered_price
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_wholesale_tiered_price")
public class ProductWholesaleTieredPrice extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品主键
     */
    private Long productId;

    /**
     * 阶梯价-最小数量（包含）
     */
    private Integer minimumQuantity;

    /**
     * 预估操作费
     */
    private BigDecimal estimatedOperationFee;

    /**
     * 预估运费
     */
    private BigDecimal estimatedShippingFee;

    /**
     * 预估处理时间（天）
     */
    private Integer estimatedHandleTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    @TableField(exist = false)
    private List<ProductSkuWholesalePrice> wholesalePrices;

}
