package com.zsmall.product.entity.domain;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DeliveryFeeResponse {

    /**
     * 尾程派送费
     */
    private BigDecimal deliveryFee;

    /**
     *分销商代发价
     */
    private BigDecimal distributorDropShippingPrice;

    /**
     * 分销商会员代发价
     */
    private BigDecimal distributorMemberDropShippingPrice;
    /**
     * 发货仓地址信息
     */
    private String address;
    /**
     * 商品信息
     */
    private String productSkuCode;
    /**
     * 是否会员
     */
    private Boolean isMember;

    /**
     * 货币符号
     */
    private String currencySymbol;

}
