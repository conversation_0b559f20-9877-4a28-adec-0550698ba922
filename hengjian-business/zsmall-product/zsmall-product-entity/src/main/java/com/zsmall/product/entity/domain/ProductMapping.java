package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.common.enums.productMapping.MarkUpTypeEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;



/**
 * 商品映射对象 product_mapping
 *
 * <AUTHOR>
 * @date 2023-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_mapping", autoResultMap = true)
public class ProductMapping extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供货商租户编号
     */
    private String supplierTenantId;

    /**
     * SPU唯一编号
     */
    private String productCode;

    /**
     * 关联的商品SKU主键
     */
    private Long productSkuId;

    /**
     * 商品SKU唯一编号
     */
    private String productSkuCode;

    /**
     * 铺货渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 铺货渠道主键
     */
    private Long channelId;

    /**
     * 参与的活动类型
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private ProductActivityTypeEnum activityType;

    /**
     * 参与的活动编号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String activityCode;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;

    /**
     * 映射SKU
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String mappingSku;

    private String channelSku;

    /**
     * 提价类型
     */
    private MarkUpTypeEnum markUpType;

    /**
     * 提价值
     */
    private BigDecimal markUpValue;

    /**
     * 最终单价
     */
    private BigDecimal finalPrice;

    /**
     * 最终单价
     */
    private BigDecimal taxExclusivePrice;

    /**
     * 渠道同步状态
     */
    private SyncStateEnum syncState = SyncStateEnum.NotSynced;

    /**
     * 渠道同步信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONObject syncMessage;

    /**
     * 第三方渠道商品ID（用于与第三方接口交互）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String channelProductId;

    /**
     * 第三方渠道商品SKU ID（用于与第三方接口交互）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String channelSkuId;

    /**
     * 主图存放路径
     */
    private String imageSavePath;

    /**
     * 主图展示地址
     */
    private String imageShowUrl;

    /**
     * 可能的价格变化
     */
    private Boolean priceChanges;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 货号
     */
    private  String channelSkuItemNumber;

    /**
     * 站点
     */
    @TableField(value = "site")
    private String site;
    /**
     * 币种
     */
    @TableField(value = "currency")
    private String currency;



}
