package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 商品映射扩展-Wayfair仓库对象 product_mapping_extend_wayfair_warehouse
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_mapping_extend_wayfair_warehouse")
public class ProductMappingExtendWayfairWarehouse extends NoDeptTenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 销售渠道主键
     */
    private Long channelId;

    /**
     * 仓库主键
     */
    private Long warehouseId;

    /**
     * 仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 第三方仓库id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String thirdWarehouseId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
