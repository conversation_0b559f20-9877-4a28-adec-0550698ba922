package com.zsmall.product.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductLabel;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 标签业务对象 label
 *
 * <AUTHOR> Li
 * @date 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductLabel.class, reverseConvertGenerate = false)
public class ProductLabelBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String labelName;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = {EditGroup.class})
    private Long sort;

    /**
     * 排序规则
     */
    private String sortRule;

}
