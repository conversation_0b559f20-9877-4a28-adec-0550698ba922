package com.zsmall.product.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.ProductSkuAttachment;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品SKU附件业务对象 product_sku_attachment
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuAttachment.class, reverseConvertGenerate = false)
public class ProductSkuAttachmentBo extends SortEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品SKU表主键
     */
    @NotNull(message = "商品SKU表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productSkuId;

    /**
     * 附件名称
     */
    @NotBlank(message = "附件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentName;

    /**
     * 附件原名
     */
    @NotBlank(message = "附件原名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    @NotBlank(message = "附件后缀不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentSuffix;

    /**
     * ossId
     */
    @NotBlank(message = "ossId不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ossId;

    /**
     * 附件存放路径
     */
    @NotBlank(message = "附件存放路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    @NotBlank(message = "附件展示地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    @NotNull(message = "附件排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer attachmentSort;

    /**
     * 附件类型
     */
    @NotBlank(message = "附件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentType;


}
