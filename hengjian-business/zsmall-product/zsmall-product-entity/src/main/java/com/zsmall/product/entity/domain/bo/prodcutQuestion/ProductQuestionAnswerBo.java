package com.zsmall.product.entity.domain.bo.prodcutQuestion;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductQuestionAnswer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商品问答业务对象 product_question_answer
 *
 * <AUTHOR> Li
 * @date 2023-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductQuestionAnswer.class, reverseConvertGenerate = false)
public class ProductQuestionAnswerBo extends BaseEntity {

    /**
     * 提问id
     */
    @NotNull(message = "提问id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionId;

    /**
     * 回复内容
     */
    @NotBlank(message = "回复内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String answer;

    /**
     * 回复类型：question-追加提问，answer-回复
     */
    private String type;

    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sort;

    /**
     * 追加提问状态：reported-已举报
     */
    @NotBlank(message = "追加提问状态：reported-已举报不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionStatus;


}
