package com.zsmall.product.entity.domain.dto.productSku;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-商品SKU库存配置
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "商品SKU库存配置")
public class ProductSkuInventoryBody {

    @Schema(title = "仓库名称")
    private String warehouseName;

    @Schema(title = "仓库编号")
    private String warehouseCode;

    @Schema(title = "库存数量")
    private Integer quantity;

    @Schema(title = "库存状态，Enable-有效，Disable-禁用")
    private String statusType;

    @Schema(title = "物流模板编号集合")
    private List<String> logisticsTemplateNoList;

}
