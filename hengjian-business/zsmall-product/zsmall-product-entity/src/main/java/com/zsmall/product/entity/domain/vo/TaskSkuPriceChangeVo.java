package com.zsmall.product.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zsmall.product.entity.domain.TaskSkuPriceChange;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 定时任务-sku价格变更视图对象 task_sku_price_change
 *
 * <AUTHOR> Li
 * @date 2023-06-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TaskSkuPriceChange.class)
public class TaskSkuPriceChangeVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商品编号
     */
    @ExcelProperty(value = "商品编号")
    private String productCode;

    /**
     * 执行日期
     */
    @ExcelProperty(value = "执行日期")
    private String executeDate;

    /**
     * 审核记录id
     */
    @ExcelProperty(value = "审核记录id")
    private Long reviewRecordId;

    /**
     * 任务状态
     */
    @ExcelProperty(value = "任务状态")
    private String taskState;


}
