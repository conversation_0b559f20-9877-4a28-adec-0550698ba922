package com.zsmall.product.entity.domain.dto.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-属性信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-属性信息")
public class AttributesBody {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "属性名称")
    private String attributesName;

//  @Schema(title = "属性渠道名称集合")
//  private List<KeyValueBody> attributesChannelNameList;

    @Schema(title = "属性值集合")
    private List<String> attributesValueList;

    @Schema(title = "属性值字符串（英文逗号分隔）")
    private String attributesValues;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "自定义字段")
    private Boolean customizeField;

    @Schema(title = "是否basic属性")
    private Boolean basicField;

    @Schema(title = "有效状态")
    private String statusType;

    @Schema(title = "属性作用域类型")
    private String scopeType;

}
