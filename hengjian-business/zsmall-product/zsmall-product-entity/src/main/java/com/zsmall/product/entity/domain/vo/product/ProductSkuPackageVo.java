package com.zsmall.product.entity.domain.vo.product;

import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.CellMerge;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 响应体-商品资料包SKU信息
 *
 * <AUTHOR>
 * @date 2023/8/24
 */
@Data
public class ProductSkuPackageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @CellMerge
    @ExcelProperty(order = 0)
    @ExcelI18nFormat(code = "zsmall.excel.productName")
    private String productName;

    @CellMerge
    @ExcelProperty(order = 10)
    @ExcelI18nFormat(code = "zsmall.excel.productCode")
    private String productCode;

    @CellMerge
    @ExcelProperty(order = 20)
    @ExcelI18nFormat(code = "zsmall.excel.imageShowUrl")
    private String imageShowUrl;

    @CellMerge
    @ExcelProperty(order = 30)
    @ExcelI18nFormat(code = "zsmall.excel.productCategory")
    private String productCategory;

    @CellMerge
    @ExcelProperty(order = 40)
    @ExcelI18nFormat(code = "zsmall.excel.features")
    private String features;

    @CellMerge
    @ExcelProperty(order = 45)
    @ExcelI18nFormat(code = "zsmall.excel.genericSpec")
    private String genericSpec;

    @CellMerge
    @ExcelProperty(order = 50)
    @ExcelI18nFormat(code = "zsmall.excel.description")
    private String description;

    @CellMerge
    @ExcelProperty(order = 60)
    @ExcelI18nFormat(code = "zsmall.excel.optionalSpec")
    private String optionalSpec;

    @CellMerge
    @ExcelProperty(order = 70)
    @ExcelI18nFormat(code = "zsmall.excel.supportedLogistics")
    private String supportedLogistics;

    @CellMerge
    @ExcelProperty(order = 80)
    @ExcelI18nFormat(code = "zsmall.excel.shippingReturnDescription")
    private String shippingReturnDescription;

    @CellMerge
    @ExcelProperty(order = 90)
    @ExcelI18nFormat(code = "zsmall.excel.productLink")
    private String productLink;

    @ExcelProperty(order = 100)
    @ExcelI18nFormat(code = "zsmall.excel.productSkuCode")
    private String productSkuCode;

    @ExcelProperty(order = 110)
    @ExcelI18nFormat(code = "zsmall.excel.specComposeName")
    private String specComposeName;

    @ExcelProperty(order = 120)
    @ExcelI18nFormat(code = "zsmall.excel.length")
    private String length;

    @ExcelProperty(order = 130)
    @ExcelI18nFormat(code = "zsmall.excel.width")
    private String width;

    @ExcelProperty(order = 140)
    @ExcelI18nFormat(code = "zsmall.excel.height")
    private String height;

    @ExcelProperty(order = 150)
    @ExcelI18nFormat(code = "zsmall.excel.weight")
    private String weight;

    @ExcelProperty(order = 160)
    @ExcelI18nFormat(code = "zsmall.excel.packLength")
    private String packLength;

    @ExcelProperty(order = 170)
    @ExcelI18nFormat(code = "zsmall.excel.packWidth")
    private String packWidth;

    @ExcelProperty(order = 180)
    @ExcelI18nFormat(code = "zsmall.excel.packHeight")
    private String packHeight;

    @ExcelProperty(order = 190)
    @ExcelI18nFormat(code = "zsmall.excel.packWeight")
    private String packWeight;

    @ExcelProperty(order = 200)
    @ExcelI18nFormat(code = "zsmall.excel.pickUpPrice")
    private String platformPickUpPrice;

    @ExcelProperty(order = 210)
    @ExcelI18nFormat(code = "zsmall.excel.dropShippingPrice")
    private String platformDropShippingPrice;

    @ExcelProperty(order = 220)
    @ExcelI18nFormat(code = "zsmall.excel.msrp")
    private BigDecimal msrp;

}
