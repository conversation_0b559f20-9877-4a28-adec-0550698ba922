package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.hengjian.system.domain.SysTenant;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockListBo;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.domain.dto.stock.SpuStock;
import com.zsmall.product.entity.domain.vo.ProductSkuStockVo;
import com.zsmall.product.entity.domain.vo.productSkuStock.ProductSkuStockSimpleVo;
import com.zsmall.product.entity.domain.vo.productSkuStock.SkuStockInfoVo;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 商品SKU库存Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
public interface ProductSkuStockMapper extends TenantMapperPlus<ProductSkuStock, ProductSkuStockVo> {

    IPage<SkuStockInfoVo> queryPageList(@Param("param") StockListBo param, Page<SkuStockInfoVo> page);

    /**
     * 查询库存编号是否已经存在
     *
     * @param stockCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    boolean existStockCode(@Param("stockCode") String stockCode);

    /**
     * 统计在售库存总数（根据Sku唯一编号）
     *
     * @param productSkuCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    Integer sumStockTotal(@Param("productSkuCode") String productSkuCode);

    /**
     * 根据SKU唯一编号查询简易视图对象
     *
     * @param productSkuCode
     * @return
     */
    List<ProductSkuStockSimpleVo> querySimpleVoByProductSkuCode(@Param("productSkuCode") String productSkuCode);

    /**
     * 批量根据SKU唯一编号查询简易视图对象
     *
     * @param productSkuCodes
     * @return
     */
    List<ProductSkuStockSimpleVo> querySimpleVoByProductSkuCodes(@Param("productSkuCodes") List<String> productSkuCodes);

    /**
     * 根据商品SKU主键集合查询关联的库存主键集合
     *
     * @param ProductSkuIds
     * @return
     */
    List<Long> queryIdsByProductSkuIds(@Param("productSkuIds") List<Long> ProductSkuIds);

    /**
     * 根据商品SKU唯一编号查询关联的库存主键集合
     *
     * @param productSkuCode
     * @return
     */
    List<Long> queryIdsByProductSkuCode(@Param("productSkuCode") String productSkuCode);

    /**
     * 根据提供的参数查询充足的库存
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ProductSkuStock> queryAdequateStockByParams(@Param("destCountry") String destCountry,
                                                     @Param("productSkuCode") String productSkuCode,
                                                     @Param("logisticsType") String logisticsType,
                                                     @Param("adjustQuantity") Integer adjustQuantity,
                                                     @Param("logisticsTemplateNo") String logisticsTemplateNo,
                                                     @Param("logisticsAccount") Boolean logisticsAccount);

    /**
     * 根据商品SKU唯一编号和仓库编号更新库存
     * @param productSkuCode
     * @param warehouseCode
     * @param stockQuantity
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    Integer updateByProductSkuCodeAndWarehouse(@Param("productSkuCode") String productSkuCode,
                                               @Param("warehouseCode") String warehouseCode,
                                               @Param("stockQuantity") Integer stockQuantity);

    @InterceptorIgnore(tenantLine = "true")
    Long countValidByWarehouseSystemCode(@Param("warehouseSystemCode") String warehouseSystemCode);
    @InterceptorIgnore(tenantLine = "true")
    List<ProductSkuStock> queryAdequateInStock(@Param("productSkuCode")String productSkuCode, @Param("adjustQuantity")Integer adjustQuantity);

    /**
     * 根据商品code和代发库存和自提库存标识和满足扣减数量的条件查询充足的库存
     * @param productSkuCode
     * @param adjustQuantity
     * @param dropShippingStockAvailable
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ProductSkuStock> queryAdequateInStockAndDropShippingStockAvailable(@Param("productSkuCode")String productSkuCode, @Param("adjustQuantity")Integer adjustQuantity,
                                                                            @Param("dropShippingStockAvailable")Integer dropShippingStockAvailable,@Param("warehouseSystemCode") String warehouseSystemCode);

    @InterceptorIgnore(tenantLine = "true")
    Long sumInventoryByWarehouseSystemCode(@Param("tenantId") String tenantId,@Param("warehouseSystemCode") String warehouseSystemCode);

    List<String> getWarehouseCode(@Param("warehouseSystemCodes") List<String> warehouseSystemCodes);

    String getChannelFlag(@Param("tenantId")String tenantId);
    List<SysTenant> getChannelFlags(@Param("tenantIds")List<String> tenantIds);

    String selectConfigByKey(@Param("isDeliveryFeeAble")String isDeliveryFeeAble);

    /**
     * 根据仓库系统编码更新商品库存
     * @param productSkuCode 商品编码
     * @param warehouseSysCode 仓库系统编码
     * @param stockQuantity 数量
     */
    @InterceptorIgnore(tenantLine = "true")
    void updateByProductSkuCodeAndWarehouseV2(@Param("productSkuCode") String productSkuCode,
                                             @Param("warehouseSysCode") String warehouseSysCode,
                                             @Param("stockQuantity") Integer stockQuantity,
                                              @Param("dropShippingStockAvailable") Integer dropShippingStockAvailable);

    /**
     * 获取SKU库存总数
     * @param productSkuCode
     * @return SkuStock
     */
    @InterceptorIgnore(tenantLine = "true")
    SkuStock getSkuStock(@Param("productSkuCode") String productSkuCode);

    /**
     * 获取SKU库存总数
     * @param productSkuCodes 商品编码
     * @return SkuStock
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SkuStock> getSkuStocks(@Param("productSkuCode") List<String> productSkuCodes);

    /**
     * 获取SKU在某个站点下的库存总数
     * @param productSkuCode
     * @return SkuStock
     */
    @InterceptorIgnore(tenantLine = "true")
    SkuStock getSkuStockBySite(@Param("productSkuCode") String productSkuCode,@Param("site") String site);
    /**
     * 获取SKU在某个站点下的某个仓库的库存总数
     * @param productSkuCode
     * @return SkuStock
     */
    @InterceptorIgnore(tenantLine = "true")
    SkuStock getSkuStockBySiteAndWarehouse(@Param("productSkuCode") String productSkuCode,@Param("site") String site,@Param("warehouseSystemCode") String warehouseSystemCode);
    /**
     * 获取SPU库存总数
     * @param productCode
     * @return SpuStock
     */
    @InterceptorIgnore(tenantLine = "true")
    SpuStock getSpuStock(@Param("productCode") String productCode);

    /**
     * 测算器校验代发仓库
     * @param productSkuCode 商品编码
     * @param quantity 数量
     * @param tenantId 供应商Id
     * @param countryCode 国家编码
     * @return
     */
    List<ProductSkuStock> queryDropShippingStockAvailable(@Param("productSkuCode") String productSkuCode,
                                                          @Param("quantity") @NotNull Integer quantity,
                                                          @Param("tenantId")String tenantId,
                                                          @Param("countryCode")String countryCode);

    void updateProductLockNum(@Param("quantityTotal") Integer quantityTotal,
                              @Param("warehouseSystemCode") String warehouseSystemCode,
                              @Param("productSkuCode")String productSkuCode,
                              @Param("supportedLogistics")String supportedLogistics,
                              @Param("isAdd")Boolean isAdd);

    void updateProductLockReserved(@Param("quantityTotal") Integer quantityTotal,
                              @Param("warehouseSystemCode") String warehouseSystemCode,
                              @Param("productSkuCode")String productSkuCode,
                              @Param("supportedLogistics")String supportedLogistics,
                              @Param("isAdd")Boolean isAdd);

    List<SkuStock> getSupplierSkuStockByProductSkuCode(@Param("productSkuCode") String productSkuCode,@Param("site") String site);

    /**
     * 获取指定商品的代发库存总数
     * @return SkuStock
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SkuStock> getDropShippingAllStockBySku(@Param("productSkuCode") Set<String> productSkuCode,@Param("supplierLogs") Set<String> supplierLogs);



    /**
     * 获取指定商品的代发库存总数
     * @return SkuStock
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SkuStock> getDropShippingAllStockBySpu(@Param("productCode") Set<String> productCode,@Param("supplierLogs") Set<String> supplierLogs);
    /**
     * 获取所有商品的代发库存总数
     * @return List<SkuStock>
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SkuStock> getDropShippingAllStock(@Param("supplierLogs") Set<String> supplierLogs);

}
