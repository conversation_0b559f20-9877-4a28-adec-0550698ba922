package com.zsmall.product.entity.domain.bo.productQuestions;

/**
 * <AUTHOR>
 * @date 2022/1/8
 **/

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "删除回复请求参数")
public class ReqAnswerReplyDelBody {

    @Schema(title = "回复code）")
    private String answerCode;

    @Schema(title = "类型（question-追问，answer-回复）")
    private String type;

}
