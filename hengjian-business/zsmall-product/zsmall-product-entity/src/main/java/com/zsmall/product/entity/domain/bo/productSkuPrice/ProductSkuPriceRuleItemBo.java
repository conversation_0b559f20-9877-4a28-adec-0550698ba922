package com.zsmall.product.entity.domain.bo.productSkuPrice;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商品sku价格计算公式业务对象 product_sku_price_rule_item
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSkuPriceRuleItem.class, reverseConvertGenerate = false)
public class ProductSkuPriceRuleItemBo extends SortEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 计算公式表id
     */
    @NotNull(message = "计算公式表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productSkuPriceRuleId;

    /**
     * 具体的价格类型(单价、操作费和尾程派送费)
     */
    @NotBlank(message = "具体的价格类型(单价、操作费和尾程派送费)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String priceItemType;

    /**
     * 提价计算方式（1-加，2-减，3-乘，4-除）
     */
    @NotNull(message = "提价计算方式（1-加，2-减，3-乘，4-除）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer priceCal;

    /**
     * 提价的值
     */
    @NotNull(message = "提价的值不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal priceCalValue;


}
