package com.zsmall.product.entity.domain.vo.productMapping;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;

/**
 * 映射可用活动信息
 *
 * <AUTHOR>
 * @date 2023/8/2
 */
@Data
public class MappingAvailableActivityInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @Alias("activityID")
    private String value;

    /**
     * 活动单价
     */
    private String activityUnitPrice;

    /**
     * 剩余时间
     */
    private String remainingTime;

    /**
     * 剩余数量
     */
    private String quantitySurplus;

}
