package com.zsmall.product.entity.domain.vo.member;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.member.MemberDiscount;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


import java.io.Serializable;
import java.util.Date;



/**
 * 会员等级折扣视图对象 member_discount
 *
 * <AUTHOR> Li
 * @date 2024-07-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberDiscount.class)
public class MemberDiscountVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 字典表等级相关code
     */
    @ExcelProperty(value = "字典表等级相关code")
    private Long dictCode;

    /**
     * 会员等级名称
     */
    @ExcelProperty(value = "会员等级名称")
    private String memberName;

    /**
     * 加点系数
     */
    @ExcelProperty(value = "加点系数(仅用于前端展示)")
    private Integer beforeMemberDiscount;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private Integer memberState;

    /**
     *创建人
     */
    private  String createBy;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private  Date createTime;

}
