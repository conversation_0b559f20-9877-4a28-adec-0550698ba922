package com.zsmall.product.entity.domain.vo.prodcutQuestion;

import com.hengjian.common.translation.annotation.Translation;
import com.hengjian.common.translation.constant.TransConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 回复编辑日志
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
public class ProductAnswerLogVo {

    /**
     * 编辑前的回复内容
     */
    private String answer;

    /**
     * 操作人
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    @Translation(type = TransConstant.NOT_TENANT_USER_ID_TO_NAME, mapper = "operatorId", other = "Sensitive")
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operateTime;

    public ProductAnswerLogVo(String answer, Long operatorId, Date operateTime) {
        this.answer = answer;
        this.operatorId = operatorId;
        this.operateTime = operateTime;
    }
}
