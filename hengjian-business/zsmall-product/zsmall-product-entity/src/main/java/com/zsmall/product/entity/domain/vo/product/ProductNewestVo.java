package com.zsmall.product.entity.domain.vo.product;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 响应体-最新商品
 *
 * <AUTHOR>
 * @date 2023/8/28
 */
@Data
public class ProductNewestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品图片
     */
    private String productImage;
    /**
     * 商品编号
     */
    private String productCode;
    /**
     * 商品类型
     */
    private String productType;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 最小价格
     */
    private BigDecimal minPrice;
    /**
     * 库存总数
     */
    private Integer stockTotal;

}
