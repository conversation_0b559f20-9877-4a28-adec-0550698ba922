package com.zsmall.product.entity.domain.vo.product;

import cn.hutool.json.JSONArray;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductAttachment;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductWholesaleDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 商品SPU视图对象 product
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Product.class)
public class ProductVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String name;

    /**
     * 产品描述
     */
    @ExcelProperty(value = "产品描述")
    private String description;

    /**
     * SPU唯一编号
     */
    @ExcelProperty(value = "SPU唯一编号")
    private String productCode;

    /**
     * 商品类型
     */
    @ExcelProperty(value = "商品类型")
    private String productType;

    /**
     * 货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
    @ExcelProperty(value = "货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架")
    private String shelfState;

    /**
     * 审核时间（商家商品字段。商家修改商品后员工审核操作的时间）
     */
    @ExcelProperty(value = "审核时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "商=家商品字段。商家修改商品后员工审核操作的时间")
    private Date verifyTime;

    /**
     * 审核状态
     */
    @ExcelProperty(value = "审核状态")
    private String verifyState;

    /**
     * 支持的物流：All-都支持，PickUpOnly-仅支持自提，DropShippingOnly-仅支持代发
     */
    @ExcelProperty(value = "支持的物流：All-都支持，PickUpOnly-仅支持自提，DropShippingOnly-仅支持代发")
    private String supportedLogistics;

    /**
     * 禁售渠道（多选，&号分隔）
     */
    @ExcelProperty(value = "禁售渠道", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "多=选，&号分隔")
    private JSONArray forbiddenChannel;

    /**
     * 归属分类id（一般情况下为商品所属分类树的最底层）
     */
    @ExcelProperty(value = "归属分类id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "一=般情况下为商品所属分类树的最底层")
    private Long belongCategoryId;

    /**
     * 产品被下载次数
     */
    @ExcelProperty(value = "产品被下载次数")
    private Integer downloadCount;


    private List<Long> delStockIds;
    private List<Long> delSkuIds;
    private List<Long> delProductAttachmentIds;
    private List<ProductAttachment> saveProductAttachmentList;
    private List<ProductSku> saveProductSkuList;
    private List<Long> delProductSkuIds;
    private ProductWholesaleDetail productWholesaleDetail;

}
