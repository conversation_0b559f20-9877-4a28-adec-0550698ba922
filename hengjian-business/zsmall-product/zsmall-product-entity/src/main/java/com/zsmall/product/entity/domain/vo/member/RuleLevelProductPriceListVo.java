package com.zsmall.product.entity.domain.vo.member;

import cn.hutool.json.JSONArray;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/15 14:48
 */
@Data
public class RuleLevelProductPriceListVo implements Serializable {

    /**
     * 商品名
     */
    private String productName;

    /**
     * 商品类型
     */
    private String productType;

    /**
     *
     */
    private String productCode;

    /**
     * 图片地址
     */
    private String imageShowUrl;

    /**
     * 审核状态
     */
    private String verifyState;

    /**
     * 货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
    @ApiModelProperty(name = "货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架")
    private String shelfState;

    /**
     * 审核意见
     */
    private String reviewOpinion;

    /**
     * 审核意见选项
     */
    private JSONArray reviewOpinionOption;

    /**
     * 存在价格变更审核
     */
    private Boolean hasPriceChange;

    /**
     * 支持的物流
     */
    private String supportedLogistics;

    /**
     * 库存推送时间
     */
    private Date inventoryPushTime;

    /**
     * 商品SKU集合
     */
    @NotBlank(message = "分销商租户标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<ProductSkuListVo> skuList;
}
