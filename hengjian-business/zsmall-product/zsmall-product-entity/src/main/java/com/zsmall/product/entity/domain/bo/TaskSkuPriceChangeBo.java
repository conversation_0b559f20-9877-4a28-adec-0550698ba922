package com.zsmall.product.entity.domain.bo;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.TaskSkuPriceChange;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 定时任务-sku价格变更业务对象 task_sku_price_change
 *
 * <AUTHOR> Li
 * @date 2023-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TaskSkuPriceChange.class, reverseConvertGenerate = false)
public class TaskSkuPriceChangeBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 执行日期
     */
    private String executeDate;

    /**
     * 审核记录id
     */
    private Long reviewRecordId;

    /**
     * 任务状态
     */
    private String taskState;


}
