package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;



/**
 * 用户购物车对象 user_shipping_cart
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_shipping_cart")
public class UserShippingCart extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    private Long siteId;

    /**
     * 用户主键
     */
    private Long userId;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 加入购物车时的图片URL
     */
    private String beforeImageShowUrl;

    /**
     * 加入购物车时的库存数量
     */
    private Integer beforeStockQuantity;

    /**
     * 加入购物车时的自提价（取平台自提价）
     */
    private BigDecimal beforePickUpPrice;

    /**
     * 加入购物车时的代发价（取平台代发价）
     */
    private BigDecimal beforeDropShippingPrice;

    /**
     * 加入购物车时的建议零售价
     */
    private BigDecimal beforeMsrp;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 支持的物流方式
     */
    private String supportedLogistics;


}
