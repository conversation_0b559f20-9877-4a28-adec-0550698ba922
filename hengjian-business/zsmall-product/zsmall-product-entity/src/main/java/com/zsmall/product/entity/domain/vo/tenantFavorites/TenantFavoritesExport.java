package com.zsmall.product.entity.domain.vo.tenantFavorites;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/26 10:52
 */
@Data
@ExcelIgnoreUnannotated
public class TenantFavoritesExport {
    /**
     * 商品名称
     */
    @ExcelProperty(value = "SKU图片")
    private String productSkuImage;
    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String productName;
    /**
     * 商品编号
     */
    @ExcelProperty(value = "SKU")
    private String sku;
    /**
     * SKU ID
     */
    @ExcelProperty(value = "SKU ID")
    private String productSkuCode;

    @ExcelProperty(value = "SKU上下架")
    private String cnShelfState;
    /**
     * 商品类型
     */
    @ExcelProperty(value = "商品类型")
    private String cnProductType;
    /**
     * 支持的物流方式
     */
    @ExcelProperty(value = "支持的物流方式")
    private String cnSupportedLogistics;
    /**
     * 库存数量
     */
 //   @ExcelProperty(value = "库存数量(可用)")
    @ExcelIgnore
    private String stockTotal;
    @ExcelProperty(value = "仓库名称")
    private String warehouseName;
    @ExcelProperty(value = "自提仓库库存")
    private String stockAvailable;
    @ExcelProperty(value = "代发仓库库存")
    private String proxyStock;
    @ExcelProperty(value = "仓库地址")
    private String warehouseAddress;
    @ExcelProperty(value = "详细地址")
    private String detailedAddress;
    @ExcelProperty(value = "站点/币种")
    private String siteCurrency;
    /**
     * 自提价格
     */
    @ExcelProperty(value = "自提价格")
    private String pickUpPrice;
    /**
     * 代发价格
     */
    @ExcelProperty(value = "代发价格")
    private String dropShippingPrice;

    @ExcelProperty(value = "产品尺寸")
    private String productSize;

    @ExcelProperty(value = "产品重量")
    private String productWeight;

    @ExcelProperty(value = "包裹尺寸")
    private String packageSize;

    @ExcelProperty(value = "包裹重量")
    private String packageWeight;

    @ExcelProperty(value = "产品描述")
    private String description;
}
