package com.zsmall.product.entity.domain.bo.UniversalBo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/2/24 11:33
 */
@Data
public class UniversalQueryBo {
    @ApiModelProperty(value = "查询类型 产品名称:ProductName| 产品编号:Sku | items.no:ProductSkuCode")
    private String queryType;

    @ApiModelProperty(value = "查询值")
    private String queryValue;
    @ApiModelProperty(value = "上货状态 上架 OnShelf 下架 OffShelf ForcedOffShelf")
    private String shelfState;

    @ApiModelProperty(value = "商品类型")
    private String productType;

    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id")
    private Long siteId;
    private String auditStatus;
    /**
     * sku审计状态
     */
    private String skuAuditStatus;

    private String skuShelfState;

    /**
     * 商品SKU编号集合,用于库存异常查询
     */
    List<String> productSkuCodeList;
}
