package com.zsmall.product.entity.domain.vo.productMapping;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductMapping;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 商品映射视图对象 product_mapping
 *
 * <AUTHOR>
 * @date 2023-06-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductMapping.class)
public class ProductMappingVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 供货商租户编号
     */
    @ExcelProperty(value = "供货商租户编号")
    private String supplierTenantId;

    /**
     * SPU唯一编号
     */
    @ExcelProperty(value = "SPU唯一编号")
    private String productCode;

    /**
     * 关联的商品SKU主键
     */
    @ExcelProperty(value = "关联的商品SKU主键")
    private Long productSkuId;

    /**
     * 商品SKU唯一编号
     */
    @ExcelProperty(value = "商品SKU唯一编号")
    private String productSkuCode;

    /**
     * 铺货渠道类型
     */
    @ExcelProperty(value = "铺货渠道类型")
    private String channelType;

    /**
     * 铺货渠道主键
     */
    @ExcelProperty(value = "铺货渠道主键")
    private Long channelId;

    /**
     * 参与的活动类型
     */
    @ExcelProperty(value = "参与的活动类型")
    private String activityType;

    /**
     * 参与的活动编号
     */
    @ExcelProperty(value = "参与的活动编号")
    private String activityCode;

    /**
     * 商品名
     */
    @ExcelProperty(value = "商品名")
    private String productName;

    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    @ExcelProperty(value = "规格组成名称，示例：尺寸-大;颜色-白色")
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    @ExcelProperty(value = "规格值名称，示例：大/白色")
    private String specValName;

    /**
     * 映射SKU
     */
    @ExcelProperty(value = "映射SKU")
    private String mappingSku;

    /**
     * 提价类型
     */
    @ExcelProperty(value = "提价类型")
    private String markUpType;

    /**
     * 提价值
     */
    @ExcelProperty(value = "提价值")
    private BigDecimal markUpValue;

    /**
     * 最终单价
     */
    @ExcelProperty(value = "最终单价")
    private BigDecimal finalPrice;

    /**
     * 渠道同步状态
     */
    @ExcelProperty(value = "渠道同步状态")
    private String syncState;

    /**
     * 第三方渠道商品ID（用于与第三方接口交互）
     */
    @ExcelProperty(value = "第三方渠道商品ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "用=于与第三方接口交互")
    private String channelProductId;

    /**
     * 第三方渠道商品SKU ID（用于与第三方接口交互）
     */
    @ExcelProperty(value = "第三方渠道商品SKU ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "用=于与第三方接口交互")
    private String channelSkuId;

    /**
     * 主图存放路径
     */
    @ExcelProperty(value = "主图存放路径")
    private String imageSavePath;

    /**
     * 主图展示地址
     */
    @ExcelProperty(value = "主图展示地址")
    private String imageShowUrl;


}
