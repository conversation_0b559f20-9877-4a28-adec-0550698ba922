package com.zsmall.product.entity.domain.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 通用参数-需要同步的商品sku信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-需要同步的商品sku信息")
public class RespEditedProductSkuBody {

    @Schema(title = "渠道：OneLink，Wayfair，Shopify，Amazon")
    private String channelType;

    @Schema(title = "商品skuId")
    private Long skuId;

    @Schema(title = "商品编码")
    private String productCode;

    @Schema(title = "商品名称")
    private String productName;

    @Schema(title = "商品图片")
    private String showUrl;

    @Schema(title = "是否修改价格")
    private Boolean modifiedPrice;

    @Schema(title = "修改前价格")
    private BigDecimal beforeModifiedPrice;

    @Schema(title = "修改后价格")
    private BigDecimal afterModificationPrice;

}
