package com.zsmall.product.entity.domain.vo.productMapping;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 响应体-铺货准备信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ImportReadyVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 可选渠道店铺
     */
    private List<OptionalChannelVo> optionalChannelList;

    /**
     * 可选SKU
     */
    private List<OptionalSkuVo> skuList;

}
