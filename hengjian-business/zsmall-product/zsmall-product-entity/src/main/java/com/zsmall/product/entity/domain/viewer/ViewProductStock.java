package com.zsmall.product.entity.domain.viewer;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/15
 **/
@Data
@TableName(value ="view_product_stock")
public class ViewProductStock implements Serializable {

    private static final long serialVersionUID = 1896786655465130633L;

    /**
     * 商品 id
     */
    private Long productId;

    /**
     * 库存总数
     */
    private Integer stockTotal;

    /**
     * 销售数量
     */
    private Integer soldQuantity;

    /**
     * 退货数量
     */
    private Integer refundQuantity;

}
