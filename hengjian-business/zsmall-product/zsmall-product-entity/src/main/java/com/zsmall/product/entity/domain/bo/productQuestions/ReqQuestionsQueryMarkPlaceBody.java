package com.zsmall.product.entity.domain.bo.productQuestions;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-属性
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "翻页查询商品提问与回复列表（MarketPlace商品详情页）请求参数-属性")
public class ReqQuestionsQueryMarkPlaceBody {

    @Schema(title = "商品SkuCode")
    private String productSkuCode;

    @Schema(title = "问题查询关键字")
    private String question;
}
