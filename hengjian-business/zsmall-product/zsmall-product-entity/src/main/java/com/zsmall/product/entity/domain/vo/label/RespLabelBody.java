package com.zsmall.product.entity.domain.vo.label;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/4/25
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
@Schema(name = "响应信息-标签信息")
public class RespLabelBody {

    @Schema(title = "标签id")
    private Long id;

    @Schema(title = "标签名称")
    private String labelName;

    @Schema(title = "排序")
    private Integer sort;

    @Schema(title = "时间")
    private String date;
}
