package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 商品审核变更详情对象 product_review_change_detail
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_review_change_detail")
public class ProductReviewChangeDetail extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 站点id
     */
    private Long siteId;
    private Long productSkuPriceId;
    /**
     * 审核记录id
     */
    private Long reviewRecordId;

    private String reviewType;
    /**
     * 字段变更所属商品编号
     */
    private String productCode;

    /**
     * 字段变更所属商品SKU编号（若是Product变更则不需要填此字段）
     */
    private String productSkuCode;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 字段值（修改前）
     */
    private String fieldValueBefore;

    /**
     * 字段值（修改后）
     */
    private String fieldValueAfter;

    /**
     * 允许更新（1-允许，0-不允许，是否允许定时器更新此字段，默认都是允许，若出现立即生效的价格变更，该SKU的所有指定日期生效的价格都将变为不允许更新）
     */
    private Long allowUpdate;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
