package com.zsmall.product.entity.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.product.entity.domain.ProductSkuStock;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品SKU库存视图对象 product_sku_stock
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductSkuStock.class)
public class ProductSkuStockVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 库存编号
     */
    @ExcelProperty(value = "库存编号")
    private String stockCode;

    /**
     * 总库存
     */
    @ExcelProperty(value = "总库存")
    private Integer stockTotal;

    /**
     * 预留库存
     */
    @ExcelProperty(value = "预留库存")
    private Integer stockReserved;

    /**
     * 可用库存
     */
    @ExcelProperty(value = "可用库存")
    private Integer stockAvailable;

    /**
     * 库存状态（0-停用，1-启用等）
     */
    @ExcelProperty(value = "库存状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-停用，1-启用等")
    private GlobalStateEnum stockState;

    /**
     * 第三方系统库存单位
     */
    private String erpSku;

    /**
     * SPU唯一编号
     */
    @ExcelProperty(value = "SPU唯一编号")
    private String productCode;

    /**
     * SKU唯一编号（ItemNo.）
     */
    @ExcelProperty(value = "SKU唯一编号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "I=temNo.")
    private String productSkuCode;

    /**
     * 仓库系统编号
     */
    @ExcelProperty(value = "仓库系统编号")
    private String warehouseSystemCode;

    /**
     * 关联物流模板编号
     */
    @ExcelProperty(value = "关联物流模板编号")
    private String logisticsTemplateNo;

    private String delFlag;


}
