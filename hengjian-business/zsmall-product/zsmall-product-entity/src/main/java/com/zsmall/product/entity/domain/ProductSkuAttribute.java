package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 商品SKU属性对象 product_sku_attribute
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_sku_attribute")
public class ProductSkuAttribute extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品SKU表主键
     */
    private Long productSkuId;

    /**
     * 商品SPU属性表主键
     */
    private Long productAttributeId;

    /**
     * 属性类型
     */
    private AttributeTypeEnum attributeType;

    /**
     * 属性名
     */
    private String attributeName;

    /**
     * 属性值
     */
    private String attributeValue;

    /**
     * 属性排序
     */
    private Integer attributeSort;

    /**
     * 属性来源id
     */
    private Long attributeSourceId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
