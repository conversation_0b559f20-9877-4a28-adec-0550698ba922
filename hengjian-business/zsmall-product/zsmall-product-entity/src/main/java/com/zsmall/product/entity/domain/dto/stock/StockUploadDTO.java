package com.zsmall.product.entity.domain.dto.stock;

import com.zsmall.common.annotaion.ExcelFieldAnnotation;
import com.zsmall.common.domain.dto.ExcelBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 库存上传DTO
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@Data
@EqualsAndHashCode(callSuper=false)
@AllArgsConstructor
@NoArgsConstructor
public class StockUploadDTO extends ExcelBaseDTO {

    @ExcelFieldAnnotation(required = true)
    private String productSkuCode;
    @ExcelFieldAnnotation(required = true)
    private String warehouseSystemCode;
    @ExcelFieldAnnotation(required = true)
    private Integer quantity;
    @ExcelFieldAnnotation(required = true)
    private String dropShippingQuantity;


}
