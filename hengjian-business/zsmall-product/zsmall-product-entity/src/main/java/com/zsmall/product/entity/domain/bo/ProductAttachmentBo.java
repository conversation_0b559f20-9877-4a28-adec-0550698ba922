package com.zsmall.product.entity.domain.bo;

import com.zsmall.common.domain.SortEntity;
import com.zsmall.product.entity.domain.ProductAttachment;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品SPU附件业务对象 product_attachment
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductAttachment.class, reverseConvertGenerate = false)
public class ProductAttachmentBo extends SortEntity {


    /**
     * 存储对象主键
     */
    private Long ossId;

    /**
     * 商品SPU表主键
     */
    private Long productId;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件原名
     */
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    private Long attachmentSort;

    /**
     * 附件类型
     */
    private String attachmentType;


}
