package com.zsmall.product.entity.domain.bo;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.UserShippingCart;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 用户购物车业务对象 user_shipping_cart
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserShippingCart.class, reverseConvertGenerate = false)
public class UserShippingCartBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户主键
     */
    private Long userId;

    /**
     * product编号
     */
    private String productCode;

    /**
     * 是否收藏 (默认取消)
     */
    private Boolean collected = false;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 加入购物车时的图片URL
     */
    private String beforeImageShowUrl;

    /**
     * 加入购物车时的库存数量
     */
    private Integer beforeStockQuantity;

    /**
     * 加入购物车时的自提价（取平台自提价）
     */
    private BigDecimal beforePickUpPrice;

    /**
     * 加入购物车时的代发价（取平台代发价）
     */
    private BigDecimal beforeDropShippingPrice;

    /**
     * 加入购物车时的建议零售价
     */
    private BigDecimal beforeMsrp;

    /**
     * 支持的物流方式
     */
    private String supportedLogistics;
}
