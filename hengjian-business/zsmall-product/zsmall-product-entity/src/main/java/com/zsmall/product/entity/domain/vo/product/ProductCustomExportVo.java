package com.zsmall.product.entity.domain.vo.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import com.zsmall.common.enums.product.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 响应体-商品自定义导出
 *
 * <AUTHOR>
 * @date 2023/8/8
 */
@Data
public class ProductCustomExportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 供应商ID */
    public String supplierId;

    /** 商品类型 */
    public String productType;

    /** 商品编号 */
    public String productCode;

    /** 商品名称 */
    public String productName;

    /** 商品分类 */
    public String productCategory;

    /** 商品标签 */
    public String productLabel;

    /** 创建时间 */
    public Date createTime;

    /** Sku */
    public String sku;

    /** Erp Sku */
    public String erpSku;

    /** Item No. */
    public String productSkuCode;

    /** 商品规格 */
    public String skuSpec;

    /** 自提价 */
    public BigDecimal pickUpPrice;

    /** 代发价 */
    public BigDecimal dropShippingPrice;

    /** 建议零售价 */
    public BigDecimal msrp;

    /** 库存数量 */
    public Integer stockQuantity;

    /** 库存管理方 */
    public StockManagerEnum stockManager;

    /** 审核状态 */
    public ProductVerifyStateEnum reviewState;

    /** 上/下架 */
    public ShelfStateEnum shelfState;

    /** 禁售渠道 */
    public JSONArray forbiddenChannel;

    /** 支持的物流 */
    public SupportedLogisticsEnum supportedLogistics;

    /** 长度 */
    public BigDecimal length;

    /** 宽度 */
    public BigDecimal width;

    /** 高度 */
    public BigDecimal height;

    /** 尺寸单位 */
    public LengthUnitEnum lengthUnit;

    /** 重量 */
    public BigDecimal weight;

    /** 重量单位 */
    public WeightUnitEnum weightUnit;

    /** 打包长度 */
    public BigDecimal packLength;

    /** 打包宽度 */
    public BigDecimal packWidth;

    /** 打包高度 */
    public BigDecimal packHeight;

    /** 打包尺寸单位 */
    public LengthUnitEnum packLengthUnit;

    /** 打包重量 */
    public BigDecimal packWeight;

    /** 打包重量单位 */
    public WeightUnitEnum packWeightUnit;

    /** 图片URL */
    public List<String> skuShowUrl;

    /** SKU销售状态 */
    public SkuShelfStateEnum skuShelfState;

    /** 供应商邮箱 */
    public String supplierEmail;

    /** 仓库名称 */
    public String warehouseName;

    /** 仓库编码 */
    public String warehouseCode;

    /** 仓库系统编号 */
    public String warehouseSystemCode;

    /** 供应商手机号 */
    public String phoneNumber;

    /** 商品链接 */
    public String productLink;

    /** 首图URL */
    public String productShowUrl;

    /** 产品单价 */
    public BigDecimal unitPrice;

    /** 操作费 */
    public BigDecimal operateFee;

    /** 尾程派送费 */
    public BigDecimal finalDeliveryFee;

    public String getCreateTime() {
        return DateUtil.formatDateTime(createTime);
    }

    public String getStockManager(String language) {
        return stockManager.getByLocale(language);
    }

    public String getReviewState(String language) {
        return reviewState.getByLocale(language);
    }

    public String getShelfState(String language) {
        return shelfState.getByLocale(language);
    }

    public String getForbiddenChannel() {
        return CollUtil.join(forbiddenChannel, ",");
    }

    public String getSupportedLogistics(String language) {
        return supportedLogistics.getByLocale(language);
    }

    public String getSkuShelfState(String language) {
        return skuShelfState.getByLocale(language);
    }

    public String getSkuShowUrl() {
        return CollUtil.join(skuShowUrl, "\n");
    }
}
