package com.zsmall.product.entity.domain.vo.category;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.hengjian.common.translation.annotation.Translation;
import com.hengjian.common.translation.constant.TransConstant;
import com.zsmall.product.entity.domain.ProductCategory;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;



/**
 * 商品分类视图对象 product_category
 *
 * <AUTHOR> Li
 * @date 2023-05-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductCategory.class)
public class ProductCategoryVo implements Serializable {


    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 父级分类主键
     */
    @ExcelProperty(value = "父级分类主键")
    private Long parentId;

    /**
     * 类目等级
     */
    @ExcelProperty(value = "类目等级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "category_level")
    private Integer categoryLevel;

    /**
     * 分类名称（主名称）
     */
    @ExcelProperty(value = "分类名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "主=名称")
    private String categoryName;

    /**
     * 分类名称（英文名称）
     */
    @ExcelIgnore
    private String categoryEnglishName;

    /**
     * 分类其他语种名称（格式：key-语种，value-对应语种分类名）
     */
    @ExcelProperty(value = "分类其他语种名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "格=式：key-语种，value-对应语种分类名")
    private JSONObject categoryOtherName;

    /**
     * 分类排序号
     */
    @ExcelProperty(value = "分类排序号")
    private Integer categorySort;

    /**
     * 大图存储桶Id
     */
    private Long imageOssId;
    /**
     * 图标存储桶Id
     */
    private Long iconOssId;

    @Translation(type = TransConstant.OSS_ID_TO_URL)
    private Long ossId = 1660866864065622018l;

    /**
     * 分类大图存储路径
     */
    @ExcelProperty(value = "分类大图存储路径")
    private String categoryImageSavePath;

    /**
     * 分类大图展示地址
     */
    @ExcelProperty(value = "分类大图展示地址")
    private String categoryImageShowUrl;

    /**
     * 分类图标存储路径
     */
    @ExcelProperty(value = "分类图标存储路径")
    private String categoryIconSavePath;

    /**
     * 分类图标展示地址
     */
    @ExcelProperty(value = "分类图标展示地址")
    private String categoryIconShowUrl;

    /**
     * 分类状态（0-停用，1-启用等）
     */
    @ExcelProperty(value = "分类状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "common_state")
    private Integer categoryState;


}
