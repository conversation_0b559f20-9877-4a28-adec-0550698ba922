package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 商品分类-商品全局属性关联对象 product_category_global_attribute
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_category_global_attribute")
public class ProductCategoryGlobalAttribute extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联的商品全局属性表主键
     */
    private Long globalAttributeId;

    /**
     * 关联的商品分类表主键
     */
    private Long productCategoryId;

    /**
     * 是否必填：0-否，1-是
     */
    private Boolean isRequired;

    /**
     * 自定义值（json数组）
     */
    private String customValues;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
