package com.zsmall.product.entity.domain.vo.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/17 13:46
 */
@Data
public class RuleLevelSkuPriceVo {

    private Long id;

    private Long siteId;

    private String currencyCode;

    private String currencySymbol;


    private Long dictCode;

//    private BigDecimal price;

//    @ApiModelProperty("0:自提价格 1:代发价格 ")
//    private Integer type;

    private Long ruleLevelPriceId;

    private Long levelId;
    @ApiModelProperty("自提价格 ")
    private BigDecimal pickUpPrice;
    @ApiModelProperty("代发价格")
    private BigDecimal dropPrice;
    private String levelName;
}
