package com.zsmall.product.entity.domain.dto.productSkuPrice;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品价格变更Body
 * <AUTHOR>
 */
@Data
public class ProductPriceChangeDto {

  private String productSkuCode;

  private BigDecimal before_dropShippingPrice;

  private BigDecimal after_dropShippingPrice;

  private BigDecimal before_pickUpPrice;

  private BigDecimal after_pickUpPrice;

  private BigDecimal before_msrp;

  private BigDecimal after_msrp;

  private BigDecimal before_unitPrice;

  private BigDecimal after_unitPrice;

  private BigDecimal before_operationFee;

  private BigDecimal after_operationFee;

  private BigDecimal before_finalDeliveryFee;

  private BigDecimal after_finalDeliveryFee;

  private BigDecimal before_fob;

  private BigDecimal after_fob;

  private Long before_ruleId;

  private Long after_ruleId;

  private List<String> tenantIdList;

  private String supplierTenantId;

  private String effectiveTime;

}
