package com.zsmall.product.entity.domain.dto.product;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-11
 **/
@Data
public class ExportProductMdDTO {

  private List<String> createDate;

  private Date startTime;

  private Date endTime;

  private String queryType;

  private String queryValue;

  private String shelfType;

  private String inventoryManager;

  private String verifyType;

  private Integer labelId;

  private Boolean pickUp;

  private Boolean notForSale;

}
