package com.zsmall.product.entity.domain.dto.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-upc信息
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-upc信息")
public class ReqUpcPageBody {

    @Schema(title = "UPC码")
    private String upc;

    @Schema(title = "使用状态：已使用--used、未使用--unused")
    private String stateType;

    @Schema(title = "渠道类型")
    private String channelType;

}
