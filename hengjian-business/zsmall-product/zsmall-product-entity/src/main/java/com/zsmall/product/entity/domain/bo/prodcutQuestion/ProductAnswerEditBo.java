package com.zsmall.product.entity.domain.bo.prodcutQuestion;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 编辑回复
 */
@Data
@EqualsAndHashCode
public class ProductAnswerEditBo {

    /**
     * 问题编码
     */
    @NotBlank(message = "{zsmall.productQA.answerCodeIsBlank}", groups = { AddGroup.class, EditGroup.class })
    private String answerCode;

    /**
     * 回复内容
     */
    @NotBlank(message = "{zsmall.productQA.textAnswersError}", groups = { AddGroup.class, EditGroup.class })
    private String answer;

}
