package com.zsmall.product.entity.domain.bo.productSku;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.product.entity.domain.*;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;


/**
 * 商品SKU视图对象 product_sku
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductSku.class, reverseConvertGenerate = false)
public class ProductSkuImportBo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 商品SPU表主键
     */
    private Long productId;

    /**
     * 商品SPU唯一编号
     */
    private String productCode;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 最小库存单位（Sku）
     */
    private String sku;

    /**
     * 系统库存单位
     */
    private String erpSku;

    /**
     * 商品统一代码（UPC）
     */
    private String upc;

    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;

    /**
     * Sku库存总数
     */
    private Integer stockTotal;

    /**
     * 库存管理方（区分自有仓库管理还是第三方平台管理）
     */
    private StockManagerEnum stockManager;

    /**
     * Sku货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
    private ShelfStateEnum shelfState;

    /**
     * Sku审核状态
     */
    private ProductVerifyStateEnum verifyState;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 铺货渠道主键
     */
    private Long channelId;

    /**
     * 铺货渠道类型
     */
    private ChannelTypeEnum channelType;

    // 做过价格更新
    private boolean isUpdated;
    /**
     * 携带用
     */
    private ProductSkuDetail skuDetail;
    private ProductSkuPrice skuPrice;
    private List<ProductSkuStock> skuStockList;
    private List<ProductSkuAttribute> skuAttributeList;
    private List<ProductSkuAttachment> skuAttachmentList;
    private List<RuleLevelProductPrice> ruleLevelProductPrices = new ArrayList<>();
    private List<RuleLevelProductPrice> ruleLevelProductPricesForUpdate = new ArrayList<>();


    /**
     * 功能描述：添加规则价格
     *
     * @param ruleLevelProductPrice 规则级产品价格
     * <AUTHOR>
     * @date 2024/07/11
     */
    public void addRulePrice(RuleLevelProductPrice ruleLevelProductPrice) {
        ruleLevelProductPrices.add(ruleLevelProductPrice);
    }

    /**
     * 功能描述：为upate添加规则价格
     *
     * @param ruleLevelProductPrice 规则级产品价格
     * <AUTHOR>
     * @date 2024/07/11
     */
    public void addRulePriceForUpate(RuleLevelProductPrice ruleLevelProductPrice) {
        ruleLevelProductPricesForUpdate.add(ruleLevelProductPrice);
    }
}
