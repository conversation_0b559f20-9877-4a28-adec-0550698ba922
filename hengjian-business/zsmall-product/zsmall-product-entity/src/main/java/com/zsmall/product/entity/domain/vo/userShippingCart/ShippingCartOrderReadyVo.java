package com.zsmall.product.entity.domain.vo.userShippingCart;

import com.zsmall.system.entity.domain.vo.TenantShippingAddressVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 响应体-购物车下单准备
 *
 * <AUTHOR>
 * @date 2023/9/1
 */
@Data
public class ShippingCartOrderReadyVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址列表
     */
    private List<TenantShippingAddressVo> addressList;

    /**
     * 钱包余额
     */
    private BigDecimal walletBalance;

}
