package com.zsmall.product.entity.domain.vo;

import cn.hutool.json.JSONArray;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.product.entity.domain.ProductGlobalAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 商品全局属性视图对象 product_global_attribute
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductGlobalAttribute.class)
public class ProductGlobalAttributeVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 属性名称（主名称）
     */
    @ExcelProperty(value = "属性名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "主=名称")
    private String attributeName;

    /**
     * 属性值（字符串数组）
     */
    @ExcelProperty(value = "属性值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "字=符串数组")
    private JSONArray attributeValues;

    /**
     * 属性备注
     */
    @ExcelProperty(value = "属性备注")
    private String attributeNotes;

    /**
     * 属性作用域（所有场景、仅通用规格，仅可选规格等）
     */
    @ExcelProperty(value = "属性作用域", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "biz_attribute_scope")
    private String attributeScope;

    /**
     * 属性状态（0-停用，1-启用等）
     */
    @ExcelProperty(value = "属性状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "biz_global_state")
    private Integer attributeState;

    /**
     * 是否支持自定义值（0-否，1-是）
     */
    @ExcelProperty(value = "是否支持自定义值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "yes_no")
    private Boolean isSupportCustom;

    /**
     * 是否是基础属性（0-否，1-是）
     */
    @ExcelProperty(value = "是否是基础属性", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "yes_no")
    private Boolean isBasicAttribute;

    /**
     * 绑定分类（AllCategory-所有分类，SpecifyCategory-指定分类）
     */
    @ExcelProperty(value = "绑定分类（AllCategory-所有分类，SpecifyCategory-指定分类）", converter = ExcelDictConvert.class)
    private String bindingCategory;

    /**
     * 指定分类主键数组
     */
    @ExcelProperty(value = "指定分类主键数组", converter = ExcelDictConvert.class)
    private List<Long> specifyCategoryIds;

    /**
     * 是否必填
     */
    @ExcelProperty(value = "是否必填", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "yes_no")
    private Boolean isRequired;

}
