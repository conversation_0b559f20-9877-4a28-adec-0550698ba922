package com.zsmall.product.entity.domain.dto.warningMessage;

import com.baomidou.mybatisplus.annotation.IEnum;

import lombok.Getter;

/**
 * 预警消息类型枚举
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Getter
public  enum WarningMessageTypeEnum implements IEnum<Integer> {

    /**
     * 库存预警
     */
    SKU_STOCK_WARNING(1);

    private Integer code;

    WarningMessageTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return this.code;
    }
}
