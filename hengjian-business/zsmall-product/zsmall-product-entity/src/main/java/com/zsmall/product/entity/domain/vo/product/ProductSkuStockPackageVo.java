package com.zsmall.product.entity.domain.vo.product;

import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 响应体-商品资料包SKU库存信息
 *
 * <AUTHOR>
 * @date 2023/8/24
 */
@Data
@AllArgsConstructor
public class ProductSkuStockPackageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(order = 0)
    @ExcelI18nFormat(code = "zsmall.excel.productSkuCode")
    private String productSkuCode;

    @ExcelProperty(order = 10)
    @ExcelI18nFormat(code = "zsmall.excel.stockTotal")
    private Integer stockTotal;

    @ExcelProperty(order = 20)
    @ExcelI18nFormat(code = "zsmall.excel.warehouseSystemCode")
    private String warehouseSystemCode;

}
