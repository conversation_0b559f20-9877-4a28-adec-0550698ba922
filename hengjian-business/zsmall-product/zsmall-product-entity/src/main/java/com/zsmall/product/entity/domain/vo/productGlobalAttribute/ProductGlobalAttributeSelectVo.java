package com.zsmall.product.entity.domain.vo.productGlobalAttribute;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 商品全局属性视图对象（提供给下拉选使用）
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@Data
@AllArgsConstructor
public class ProductGlobalAttributeSelectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 可选规格
     */
    private List<ProductGlobalAttributeSimpleVo> optionalSpec;

    /**
     * 通用规格
     */
    private List<ProductGlobalAttributeSimpleVo> genericSpec;

}
