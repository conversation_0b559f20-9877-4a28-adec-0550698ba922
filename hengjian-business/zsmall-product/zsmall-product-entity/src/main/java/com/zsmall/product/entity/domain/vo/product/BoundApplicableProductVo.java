package com.zsmall.product.entity.domain.vo.product;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 用于接收已绑定定价公式及与当前公式比较信息
 * <AUTHOR>
 * @date 2022/11/1 9:16
 */
@Data
public class BoundApplicableProductVo {

  /**
   * 公式名称
   */
  private String ruleName;
  /**
   * 代发价
   */
  private BigDecimal dropShippingPrice;

  /**
   * 自提价
   */
  private BigDecimal pickUpPrice;

  /**
   * 涨幅 %（代发价）
   */
  private String dropShippingPriceIncrease;

  /**
   * 涨幅类型（代发价）
   */
  private String dropShippingPriceIncreaseType;

  /**
   *涨幅类型（自提价）
   */
  private String pickUpPriceIncrease;

  /**
   * 涨幅类型（自提价）
   */
  private String pickUpPriceIncreaseType;
}
