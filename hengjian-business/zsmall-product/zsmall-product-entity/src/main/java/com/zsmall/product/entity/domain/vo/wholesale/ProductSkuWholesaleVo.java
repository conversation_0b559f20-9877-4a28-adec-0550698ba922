package com.zsmall.product.entity.domain.vo.wholesale;

import com.zsmall.product.entity.domain.bo.wholesale.ProductSkuWholesaleBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 通用参数-国外现货批发商品sku信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProductSkuWholesaleVo extends ProductSkuWholesaleBo {

    /**
     * 主图片展示地址
     */
    private String imageShowUrl;

    /**
     * itemNo
     */
    private String itemNo;

    /**
     *发货国家
     */
    private String shippingFrom;
    /**
     *是否允许销售（若SKU开启了全渠道管控，只有指定的用户才能销售）
     */
    private boolean allowSale = true;
    /**
     *sku价格信息
     */
    private List<TieredPrice> tieredPricesList;

    /**
     * sku价格信息
     */
    @Data
    public static class TieredPrice {

        /**
         * 原始单价（供货商）
         */
        private BigDecimal originUnitPrice;

        /**
         * 平台单价（员工、分销商）
         */
        private BigDecimal platformUnitPrice;

        /**
         * 订金单价（员工、分销商）
         */
        private BigDecimal platformDepositUnitPrice;

        /**
         * 订金单价（供应商）
         */
        private BigDecimal depositUnitPrice;

        /**
         * 最小起订量
         */
        private Integer minimumQuantity;

        /**
         * 预估操作费
         */
        private BigDecimal estimatedOperationalFee;

        /**
         * 预估运费
         */
        private BigDecimal estimatedShoppingFee;

        /**
         * 预估处理时间(天)
         */
        private Integer dealDate;

    }
}
