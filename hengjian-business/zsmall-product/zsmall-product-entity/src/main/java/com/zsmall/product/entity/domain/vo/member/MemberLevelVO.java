package com.zsmall.product.entity.domain.vo.member;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.zsmall.product.entity.domain.member.MemberLevel;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/7 10:36
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MemberLevel.class)
public class MemberLevelVO implements Serializable {
    @ApiModelProperty("id,非序号")
    private Long id;
    /**
     * 等级名称
     */
//    @Size(max= 255,message="编码长度不能超过255")
//    @ApiModelProperty(value = "等级名称")
//    @Length(max= 255,message="编码长度不能超过255")
//    private String levelName;
    /**
     * 等级启用状态
     */
    @NotNull(message="[等级启用状态]不能为空,0开启1关闭")
    @ApiModelProperty(value = "等级启用状态",required = true)
    private Integer status;
    /**
     * icon
     */
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("icon")
    @Length(max= 255,message="编码长度不能超过255")
    private String showUrl;

    /**
     * 租户标识
     */
    @NotBlank(message="[租户标识]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("租户标识")
    @Length(max= 20,message="编码长度不能超过20")
    private String tenantId;

    @ApiModelProperty(value = "字典编号",required = true)
    private Long dictCode;

    private String levelName;
}
