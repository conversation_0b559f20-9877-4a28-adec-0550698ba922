package com.zsmall.product.entity.domain.vo.product;

import com.zsmall.common.domain.vo.SkuWarehouseBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应信息-SKU仓库配置
 * <AUTHOR>
 */
@Data
@NoArgsConstructor

@Schema(name = "响应信息-SKU仓库配置")
public class RespSkuWarehouseListBody {

  @Schema(title = "仓库配置集合")
  private List<SkuWarehouseBody> warehouseConfigs;

}
