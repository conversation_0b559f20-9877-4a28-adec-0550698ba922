package com.zsmall.product.entity.domain.bo.productChannelControl;

import com.hengjian.common.mybatis.core.domain.NoParamBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求体-切换管控类型
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=false)
@NoArgsConstructor
public class SwitchControlTypeBo extends NoParamBaseEntity {

    /**
     * 商品ItemNo
     */
    private String productSkuCode;

    /**
     * 是否全渠道管控
     */
    private Boolean allChannelControl;

}
