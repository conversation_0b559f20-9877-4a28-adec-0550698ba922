package com.zsmall.product.entity.domain.vo.productGlobalAttribute;

import cn.hutool.json.JSONArray;
import com.zsmall.product.entity.domain.ProductGlobalAttribute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 商品全局属性简略对象
 *
 * <AUTHOR>
 * @date 2023/5/31
 */
@Data
@AutoMapper(target = ProductGlobalAttribute.class)
public class ProductGlobalAttributeSimpleVo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 属性名称（主名称）
     */
    private String attributeName;

    /**
     * 是否支持自定义值
     */
    private Boolean isSupportCustom;

    /**
     * 是否必填
     */
    private Boolean isRequired;

    /**
     * 属性值（字符串数组）
     */
    private JSONArray attributeValues;

}
