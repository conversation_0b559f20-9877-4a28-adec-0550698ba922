package com.zsmall.warehouse.entity.domain;

import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.hengjian.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 管理端仓库信息对象 warehouse_admin_info
 *
 * <AUTHOR> Li
 * @date 2024-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warehouse_admin_info")
public class WarehouseAdminInfo extends NoDeptBaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 仓库类型
     */
    private Integer warehouseType;
    /**
     * 仓库类型描述
     */
    private String warehouseTypeName;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库编号
     */
    private String warehouseCode;

    /**
     * 仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 仓库状态（0-停用，1-启用等）
     */
    private Integer warehouseState;

    /**
     * 是否支持第三方物流账号（0-否，1-是）
     */
    private Integer supportLogisticsAccount;

    /**
     * 地区表主键（国家）
     */
    private Long countryId;

    /**
     * 国家名文本
     */
    private String country;

    /**
     * 地区表主键（州/省）
     */
    private Long stateId;

    /**
     * 州/省名文本
     */
    private String state;

    /**
     * 地区表主键（市县）
     */
    private Long cityId;

    /**
     * 市县名文本
     */
    private String city;

    /**
     * 详细地址1
     */
    private String address1;

    /**
     * 详细地址2
     */
    private String address2;

    /**
     * 仓库邮编
     */
    private String zipCode;

    /**
     * 仓库管理者姓名
     */
    private String managerName;

    /**
     * 仓库管理者联系电话
     */
    private String managerPhone;

    /**
     * 经度
     */
    private Long longitude;

    /**
     * 纬度
     */
    private Long latitude;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private Long migrationId;
}
