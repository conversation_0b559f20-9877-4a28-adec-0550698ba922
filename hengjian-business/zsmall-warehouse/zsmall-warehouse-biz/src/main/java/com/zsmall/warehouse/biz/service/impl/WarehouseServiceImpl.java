package com.zsmall.warehouse.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.iservice.IProductActivityStockItemService;
import com.zsmall.activity.entity.iservice.IProductActivityStockService;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.order.biz.test.service.impl.WarehouseServiceV2Impl;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.product.entity.util.ProductCodeGenerator;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import com.zsmall.warehouse.biz.service.WarehouseService;
import com.zsmall.warehouse.biz.support.WarehouseSupport;
import com.zsmall.warehouse.biz.util.WarehouseCodeGenerator;
import com.zsmall.warehouse.entity.domain.*;
import com.zsmall.warehouse.entity.domain.bo.warehouse.WarehouseAddressBo;
import com.zsmall.warehouse.entity.domain.bo.warehouse.WarehouseBo;
import com.zsmall.warehouse.entity.domain.bo.warehouse.WarehouseIntactInfoBo;
import com.zsmall.warehouse.entity.domain.vo.WarehouseBizArkConfigVo;
import com.zsmall.warehouse.entity.domain.vo.warehouse.*;
import com.zsmall.warehouse.entity.iservice.*;
import com.zsmall.warehouse.entity.mapper.WarehouseMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 仓库管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WarehouseServiceImpl implements WarehouseService {

    private final WarehouseCodeGenerator warehouseCodeGenerator;

    private final IWarehouseService iWarehouseService;
    private final IWorldLocationService iWorldLocationService;
    private final IWarehouseAddressService iWarehouseAddressService;

    private final IWarehouseBizArkConfigService iWarehouseBizArkConfigService;
    private final ILogisticsTemplateService iLogisticsTemplateService;
    private final IWarehouseAdminService iWarehouseAdminService;
    private final IProductSkuService iProductSkuService;
    private final ProductCodeGenerator productCodeGenerator;
    private final WarehouseServiceV2Impl warehouseServiceV2Impl;
    private final WarehouseSupport warehouseSupport;
    private final IProductSkuStockService iProductSkuStockService;
    private final IProductActivityStockService iProductActivityStockService;
    private final IProductActivityStockItemService iProductActivityStockItemService;
    private final WarehouseMapper warehouseMapper;
    private final IWarehouseAdminDeliveryCountryService warehouseAdminDeliveryCountryService;
    private final IWarehouseDeliveryCountryService warehouseDeliveryCountryService;

    /**
     * 查询仓库完整信息
     *
     * @param warehouseId
     */
    @Override
    public WarehouseIntactInfoVo queryIntactInfoById(Long warehouseId) {
        log.info("查询仓库完整信息 id = {}", warehouseId);
        WarehouseVo warehouseVo = iWarehouseService.selectVoById(warehouseId);
        WarehouseAddressVo warehouseAddressVo = iWarehouseAddressService.queryByWarehouseId(warehouseId);
        WarehouseIntactInfoVo warehouseIntactInfoVo = BeanUtil.copyProperties(warehouseVo, WarehouseIntactInfoVo.class);
        BeanUtil.copyProperties(warehouseAddressVo, warehouseIntactInfoVo, "id");
        return warehouseIntactInfoVo;
    }

    /**
     * 查询仓库管理列表
     */
    @Override
    public TableDataInfo<WarehouseVo> queryPageList(WarehouseBo bo, PageQuery pageQuery) {
        return iWarehouseService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询仓库管理列表（提供给下拉选使用）
     *
     * @param bo
     */
    @Override
    public List<WarehouseSelectVo> queryListForSelect(WarehouseBo bo) throws RStatusCodeException {
        String warehouseType = bo.getWarehouseType();
        LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(warehouseType)) {
            lqw.eq(Warehouse::getWarehouseType, WarehouseTypeEnum.valueOf(warehouseType));
        }
        lqw.eq(Warehouse::getWarehouseState, GlobalStateEnum.Valid);
        lqw.groupBy(Warehouse::getWarehouseSystemCode);
        List<Warehouse> warehouses = iWarehouseService.list(lqw);

        List<WarehouseSelectVo> voList = BeanUtil.copyToList(warehouses, WarehouseSelectVo.class);
        for (WarehouseSelectVo warehouseSelectVo : voList) {
            List<LogisticsTemplate> templateList = iLogisticsTemplateService.queryByWarehouse(warehouseSelectVo.getWarehouseSystemCode());
            if (CollUtil.isNotEmpty(templateList)) {
                List<WarehouseSelectVo.LogisticsTemplateSelectVo> templateSelectVoList = BeanUtil.copyToList(templateList, WarehouseSelectVo.LogisticsTemplateSelectVo.class);
                warehouseSelectVo.setLogisticsTemplateSelect(templateSelectVoList);
            }
        }

        return voList;
    }

    /**
     * 新增仓库管理
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public Boolean insertByBo(WarehouseIntactInfoBo bo) throws RStatusCodeException {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        // 删除仓库列表缓存
        this.deleteWarehouseListCache();
        String tenantId = LoginHelper.getTenantId();
        String boState = bo.getState();
        Long countryId = bo.getCountryId();
        Long stateId = bo.getStateId();
        String warehouseCode = bo.getWarehouseCode();

        boolean existsed = iWarehouseService.existsWarehouseCode(tenantId, warehouseCode, bo.getWarehouseType(), null);
        if (existsed) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS);
        }

        WorldLocationVo country = iWorldLocationService.queryById(countryId);
        // String countryName = country.getLocationOtherName().getStr("en_US");
        bo.setCountry(country.getLocationCode());

        String locationCode = country.getLocationCode();
        if (stateId != null && StrUtil.isBlank(boState)) {
            WorldLocationVo state = iWorldLocationService.queryById(stateId);
            locationCode = state.getLocationCode();
            bo.setState(state.getLocationOtherName().getStr("en_US"));
        }

        Warehouse add = MapstructUtils.convert(bo, Warehouse.class);
        String warehouseSystemCode = warehouseCodeGenerator.codeGenerate(BusinessCodeEnum.WarehouseSystemCode, locationCode);
        add.setWarehouseState(GlobalStateEnum.Valid.getCode());
        add.setWarehouseSystemCode(warehouseSystemCode);

        WarehouseAddressBo addWarehouseAddressBo = BeanUtil.toBean(bo, WarehouseAddressBo.class);
        addWarehouseAddressBo.setId(null);
        addWarehouseAddressBo.setWarehouseSystemCode(warehouseSystemCode);

        boolean flag = iWarehouseService.save(add);
        if (flag) {
            addWarehouseAddressBo.setWarehouseId(add.getId());
            iWarehouseAddressService.insertByBo(addWarehouseAddressBo);

            WarehouseTypeEnum warehouseType = add.getWarehouseType();
            if (WarehouseTypeEnum.BizArk.equals(warehouseType)) {
                WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(tenantId);
                if (bizArkConfig == null) {
                    bizArkConfig = new WarehouseBizArkConfig();
                }

                bizArkConfig.setBizArkApiUrl(bo.getBizArkApiUrl());
                bizArkConfig.setBizArkSecretKey(bo.getBizArkSecretKey());
                bizArkConfig.setBizArkChannelAccount(StrUtil.emptyToNull(bo.getBizArkChannelAccount()));
                iWarehouseBizArkConfigService.saveOrUpdate(bizArkConfig);
            }
        }
        return flag;
    }

    /**
     * 修改仓库管理
     */
    @Override
    public Boolean updateByBo(WarehouseIntactInfoBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        // 删除仓库列表缓存
        this.deleteWarehouseListCache();
        String tenantId = LoginHelper.getTenantId();
        String boState = bo.getState();
        Long countryId = bo.getCountryId();
        Long stateId = bo.getStateId();
        Long warehouseId = bo.getId();
        String warehouseCode = bo.getWarehouseCode();

        boolean existsed = iWarehouseService.existsWarehouseCode(tenantId, warehouseCode, bo.getWarehouseType(), warehouseId);
        if (existsed) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS);
        }

        WorldLocationVo country = iWorldLocationService.queryById(countryId);
        // String countryName = country.getLocationOtherName().getStr("en_US");
        bo.setCountry(country.getLocationCode());

        if (stateId != null && StrUtil.isBlank(boState)) {
            WorldLocationVo state = iWorldLocationService.queryById(stateId);
            bo.setState(state.getLocationOtherName().getStr("en_US"));
        }

        Warehouse update = MapstructUtils.convert(bo, Warehouse.class);
        boolean flag = iWarehouseService.updateById(update);
        if (flag) {
            WarehouseAddressVo warehouseAddressVo = iWarehouseAddressService.queryByWarehouseId(warehouseId);
            BeanUtil.copyProperties(bo, warehouseAddressVo, "id");
            WarehouseAddressBo warehouseAddressBo = BeanUtil.toBean(warehouseAddressVo, WarehouseAddressBo.class);
            flag = iWarehouseAddressService.updateByBo(warehouseAddressBo);

            WarehouseTypeEnum warehouseType = update.getWarehouseType();
            if (WarehouseTypeEnum.BizArk.equals(warehouseType)) {
                WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(tenantId);
                if (bizArkConfig == null) {
                    bizArkConfig = new WarehouseBizArkConfig();
                }

                bizArkConfig.setBizArkApiUrl(bo.getBizArkApiUrl());
                bizArkConfig.setBizArkSecretKey(bo.getBizArkSecretKey());
                bizArkConfig.setBizArkChannelAccount(StrUtil.emptyToNull(bo.getBizArkChannelAccount()));
                iWarehouseBizArkConfigService.saveOrUpdate(bizArkConfig);
            }
        }
        return flag;
    }

    /**
     * 批量删除仓库管理
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);

        for (Long id : ids) {
            Warehouse warehouse = iWarehouseService.getById(id);
            if (warehouse != null) {
                String warehouseSystemCode = warehouse.getWarehouseSystemCode();
                // 删除仓库需要判断有没有绑定了普通商品、锁货圈货活动、批发活动

                Long stockCount = iProductSkuStockService.countValidByWarehouseSystemCode(warehouseSystemCode);
                Long actStockCount = iProductActivityStockService.countValidByWarehouseSystemCode(warehouseSystemCode);
                Long actItemStockCount = iProductActivityStockItemService.countValidByWarehouseSystemCode(warehouseSystemCode);
                if (stockCount > 0 || actStockCount > 0 || actItemStockCount > 0) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.WAREHOUSE_IS_ALREADY_IN_USE);
                }
            }
        }

        // 删除仓库列表缓存
        this.deleteWarehouseListCache();
        return iWarehouseService.deleteWithValidByIds(ids);
    }

    /**
     * 查询第三方仓库配置
     * @return
     */
    @Override
    public R<ThirdWarehouseConfigVo> queryThirdWarehouseConfig() {
        ThirdWarehouseConfigVo vo = new ThirdWarehouseConfigVo();
        String tenantId = LoginHelper.getTenantId();
        StockManagerEnum[] values = StockManagerEnum.values();
        for (StockManagerEnum stockManagerEnum : values) {
            if (StockManagerEnum.BizArk.equals(stockManagerEnum)) {
                WarehouseBizArkConfig warehouseBizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(tenantId);
                WarehouseBizArkConfigVo bizArkConfigVo = BeanUtil.toBean(warehouseBizArkConfig, WarehouseBizArkConfigVo.class);
                vo.setBizArk(bizArkConfigVo);
            }
        }
        return R.ok(vo);
    }

    @Override
    public List<WarehouseAdminVo> getWarehouseAdminInfo() {
        List<WarehouseAdminInfo> warehouseAdminInfoList = iWarehouseAdminService.listWarehouseAdminInfo();
        List<WarehouseAdminVo> warehouseAdminVoList = new ArrayList<>();
        if(CollUtil.isNotEmpty(warehouseAdminInfoList)){
            for(WarehouseAdminInfo warehouseAdminInfo : warehouseAdminInfoList){
                WarehouseAdminVo warehouseAdminVo = new WarehouseAdminVo();
                BeanUtils.copyProperties(warehouseAdminInfo, warehouseAdminVo);
                StringBuffer allAddresses = new StringBuffer();
                allAddresses.append(warehouseAdminInfo.getState()).append(warehouseAdminInfo.getCity()).append(warehouseAdminInfo.getAddress1()).append(warehouseAdminInfo.getAddress2());
                warehouseAdminVo.setAllAddresses(allAddresses.toString());
                warehouseAdminVoList.add(warehouseAdminVo);
            }
        }
        return warehouseAdminVoList;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public void saveWarehouse(WarehouseInsertVo warehouseInsertVo) {
        List<WarehouseAdminVo> warehouseAdminVoList = warehouseInsertVo.getWarehouseAdminVoList();
        String warehouseType = warehouseInsertVo.getWarehouseType();
        if(CollUtil.isNotEmpty(warehouseAdminVoList)){
            LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
            // 删除仓库列表缓存
            this.deleteWarehouseListCache();
            String tenantId = LoginHelper.getTenantId();
            for(WarehouseAdminVo warehouseAdminVo : warehouseAdminVoList){

                boolean existsed = iWarehouseService.existsWarehouseCode(tenantId, warehouseAdminVo.getWarehouseCode(), warehouseAdminVo.getWarehouseType(), null);
                if (existsed) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS);
                }
                boolean existsedByWarehouseName = iWarehouseService.existsWarehouseName(tenantId, warehouseAdminVo.getWarehouseCode(),warehouseAdminVo.getWarehouseName(), null);
                if (existsedByWarehouseName) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.THE_SAME_WAREHOUSE_CODE_EXISTS);
                }
                warehouseAdminVo.setWarehouseType(warehouseType);
                warehouseAdminVo.setTenantId(tenantId);
                Warehouse add = MapstructUtils.convert(warehouseAdminVo, Warehouse.class);
                add.setId(null);
                add.setWarehouseSystemCode(warehouseCodeGenerator.codeGenerate(BusinessCodeEnum.WarehouseSystemCode, "supplier"));
                add.setCreateBy(loginUser.getUserId());
                add.setCreateTime(new Date());
                // 地址处理
                WorldLocationVo country = iWorldLocationService.queryById(warehouseAdminVo.getCountryId());
                warehouseAdminVo.setCountry(country.getLocationCode());
                add.setCountry(country.getLocationCode());

                if (null != warehouseAdminVo.getStateId() && StrUtil.isBlank(warehouseAdminVo.getState())) {
                    WorldLocationVo state = iWorldLocationService.queryById(warehouseAdminVo.getStateId());
                    warehouseAdminVo.setState(state.getLocationOtherName().getStr("en_US"));
                }

                WarehouseAddressBo addWarehouseAddressBo = BeanUtil.toBean(warehouseAdminVo, WarehouseAddressBo.class);
                addWarehouseAddressBo.setId(null);
                addWarehouseAddressBo.setWarehouseSystemCode(add.getWarehouseSystemCode());
                addWarehouseAddressBo.setCreateBy(loginUser.getUserId());
                addWarehouseAddressBo.setCreateTime(new Date());

                boolean flag = iWarehouseService.save(add);
                if (flag) {
                    addWarehouseAddressBo.setWarehouseId(add.getId());
                    iWarehouseAddressService.insertByBo(addWarehouseAddressBo);
                    //供应商仓库插入成功后,继续插入对应超管仓库的支持配送国家
                    LambdaQueryWrapper<WarehouseAdminDeliveryCountry> q = new LambdaQueryWrapper<>();
                    q.eq(WarehouseAdminDeliveryCountry::getWarehouseAdminInfoId,warehouseAdminVo.getId());
                    List<WarehouseAdminDeliveryCountry> list =TenantHelper.ignore(()->warehouseAdminDeliveryCountryService.list(q)) ;
                    list.forEach(s->{
                        WarehouseDeliveryCountry w = new WarehouseDeliveryCountry();
                        w.setCountryCode(s.getCountryCode());
                        w.setWarehouseId(add.getId());
                        w.setWorldLocationId(s.getWorldLocationId());
                        w.setCountryNameEn(s.getCountryNameEn());
                        w.setCountryNameZh(s.getCountryNameZh());
                        warehouseDeliveryCountryService.save(w);
                    });
                }
                // 新增产品sku库存数据
                warehouseSupport.addProductSkuStockDataAndPullInventoryAsync(tenantId,add.getWarehouseSystemCode(),loginUser.getUserId());
            }
            // 刷新库存信息
//            warehouseServiceV2Impl.pullInventoryAsync();
        }
    }

    @Override
    @Transactional
    public R<Void> enableOrDisableWarehouse(Long id, Integer warehouseState) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        String tenantId = loginUser.getTenantId();
        Long userId = loginUser.getUserId();
        Warehouse warehouse = iWarehouseService.getById(id);
        String warehouseSystemCode = null;
        if (warehouse != null) {
            warehouseSystemCode = warehouse.getWarehouseSystemCode();
        }
        if(StringUtils.isEmpty(warehouseSystemCode)){
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WAREHOUSE_NOT_EXIST);
        }
        // 停用
        if(GlobalStateEnum.Invalid.getValue().equals(warehouseState)){
            if (warehouse != null) {
                // 判断仓库下面是否有商品库存
                Long stockCount = iProductSkuStockService.countInventoryByWarehouseSystemCode(tenantId, warehouseSystemCode);
                if (null != stockCount && stockCount > 0) {
                    return R.fail(501, "该仓库下有关联的商品库存，无法禁用!");
                }
                LambdaUpdateWrapper<Warehouse> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.set(Warehouse::getWarehouseState,warehouseState).eq(Warehouse::getId,id);
                iWarehouseService.getBaseMapper().update(warehouse,lambdaUpdateWrapper);
                // 产品库存信息修改
                LambdaUpdateWrapper<ProductSkuStock> lambdaUpdateProductSkuStockWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateProductSkuStockWrapper.set(ProductSkuStock::getDelFlag,"2").set(ProductSkuStock::getUpdateBy,userId).set(ProductSkuStock::getUpdateTime,new Date()).eq(ProductSkuStock::getTenantId,tenantId).eq(ProductSkuStock::getWarehouseSystemCode,warehouseSystemCode);
                iProductSkuStockService.update(lambdaUpdateProductSkuStockWrapper);
            }
        }
        // 启用
        if(GlobalStateEnum.Valid.getValue().equals(warehouseState)){
            // 判断超管仓库是否被禁用，禁用状态下供应商仓库无法启用
                LambdaQueryWrapper<WarehouseAdminInfo> lqw = Wrappers.lambdaQuery();
                lqw.eq(WarehouseAdminInfo::getDelFlag, "0");
                lqw.eq(WarehouseAdminInfo::getWarehouseCode,warehouse.getWarehouseCode());
                WarehouseAdminInfo warehouseAdminInfo = TenantHelper.ignore(() ->iWarehouseAdminService.getBaseMapper().selectOne(lqw));
                if(null == warehouseAdminInfo){
                    return R.fail(501, "仓库不存在，无法启用!");
                }
                if(null != warehouseAdminInfo.getWarehouseState() && warehouseAdminInfo.getWarehouseState().equals(0)){
                    return R.fail(501, "仓库已经被禁用，无法启用!");
                }
                // 新增产品库存信息
                warehouseSupport.addProductSkuStockDataAndPullInventoryAsync(tenantId,warehouseSystemCode,userId);
                // 修改数据
                LambdaUpdateWrapper<Warehouse> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.set(Warehouse::getWarehouseState,warehouseState).eq(Warehouse::getId,id);
                iWarehouseService.getBaseMapper().update(warehouse,lambdaUpdateWrapper);
        }
        return R.ok();
    }

    @Override
    public List<WarehouseSelectVo> warehouseSelect() {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        LambdaQueryWrapper<Warehouse> lqw = Wrappers.lambdaQuery();
        lqw.eq(Warehouse::getWarehouseState, GlobalStateEnum.Valid).eq(Warehouse::getTenantId,loginUser.getTenantId());
        List<Warehouse> warehouses = iWarehouseService.list(lqw);
        List<WarehouseSelectVo> voList = BeanUtil.copyToList(warehouses, WarehouseSelectVo.class);
        return voList;
    }


    /**
     * 清除仓库List缓存
     */
    private void deleteWarehouseListCache() {
        Boolean existsed = RedisUtils.hasKey(RedisConstants.ZSMALL_SUPPLIER_WAREHOUSE);
        if (existsed) {
            RedisUtils.deleteObject(RedisConstants.ZSMALL_SUPPLIER_WAREHOUSE);
        }
    }
}
