package com.zsmall.marketplace.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.activity.entity.domain.bo.productActivity.ParticipateActivityBo;
import com.zsmall.activity.entity.domain.vo.productActivity.MpProductActivityVo;
import com.zsmall.marketplace.domain.bo.AddToFavoritesBo;
import com.zsmall.marketplace.domain.bo.MpProductSearchBo;
import com.zsmall.marketplace.domain.bo.mpWholesale.MpWholesaleProductBo;
import com.zsmall.marketplace.domain.bo.product.RecentlyProductBo;
import com.zsmall.marketplace.domain.vo.mpWholesale.MpWholesaleProductDetailVo;
import com.zsmall.marketplace.domain.vo.mpWholesale.MpWholesaleProductPageVo;
import com.zsmall.marketplace.domain.vo.product.*;
import com.zsmall.marketplace.service.MpProductService;
import com.zsmall.product.entity.domain.vo.product.ProductSkuSimpleVo;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Marketplace商品相关
 *
 * <AUTHOR>
 * @date 2023/7/25
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/mp/product")
public class MpProductController {

    private final MpProductService mpProductService;

    /**
     * 初始化ElasticSearch商品数据
     */
    @RepeatSubmit
    @GetMapping("/initEsProduct")
    public R<Void> initEsProduct() {
        return mpProductService.initEsProduct();
    }

    /**
     * 搜索最近商品
     */
    @SaIgnore
    @PostMapping("/searchRecentlyProduct")
    public R<List<MpProductVo>> searchRecentlyProduct(@RequestBody RecentlyProductBo bo) {
        return mpProductService.searchRecentlyProduct(bo);
    }

    /**
     * 查询商品
     */
    @SaIgnore
    @PostMapping("/searchProductPage")
    public R<MpProductListVo> searchProductPage(@RequestBody MpProductSearchBo bo) throws Exception {
        return mpProductService.searchProductPage(bo);
    }

    /**
     * 查询批发商品
     */
    @SaIgnore
    @PostMapping("/searchWholesaleProductPage")
    public MpWholesaleProductPageVo searchWholesaleProductPage(@RequestBody MpWholesaleProductBo bo) {
        return mpProductService.searchWholesaleProductPage(bo);
    }

    /**
     * 查询分类树
     */
    @GetMapping("/searchProductCategoryTree")
    public R<List<MpProductCategoryVo>> searchProductCategoryTree() {
        return mpProductService.searchProductCategoryTree();
    }

    /**
     * 获取商品详情
     */
    @SaIgnore
    @GetMapping("/getProductDetail")
    public R<MpProductDetailVo> getProductDetail(String productCode,String site) throws Exception {
        return mpProductService.getProductDetail(productCode,site);
    }

    /**
     * 获取批发商品详情
     */
    @SaIgnore
    @GetMapping("/getWholesaleProductDetail")
    public R<MpWholesaleProductDetailVo> getWholesaleProductDetail(String productCode) {
        return mpProductService.getWholesaleProductDetail(productCode);
    }

    /** 获取商品问答列表 */
    @SaIgnore
    @GetMapping("/getProductQAPage")
    public TableDataInfo<MpProductQuestionVo> getProductQAPage(String productSkuCode, String question, PageQuery pageQuery) throws Exception {
        return mpProductService.getProductQAPage(productSkuCode, question, pageQuery);
    }

    /**
     * 分销商参加商品活动
     */
    @PostMapping("/participateProductActivity")
    public R<Void> participateProductActivity(@RequestBody ParticipateActivityBo bo) {
        return mpProductService.participateProductActivity(bo);
    }

    /**
     * 查询商品可参与的活动
     */
    @GetMapping("/queryMpActivity")
    public R<MpProductActivityVo> queryMpActivity(String productSkuCode) {
        return mpProductService.queryMpActivity(productSkuCode);
    }

    /**
     * 获取商品SKU详情
     */
    @SaIgnore
    @RepeatSubmit(interval = 1000)
    @GetMapping("/sku/{productSkuCode}")
    public R<ProductSkuSimpleVo> getProductSkuSimpleDetail(@NotEmpty(message = "{zsmall.productQA.productSkuCodeIsnullError}")
                                                           @PathVariable String productSkuCode) {
        return R.ok(mpProductService.getProductSkuSimpleDetail(productSkuCode));
    }

    /**
     * 加入收藏夹
     */
    @PostMapping("/addToFavorites")
    @Transactional
    public R<Void> addToFavorites(@RequestBody @Validated AddToFavoritesBo bo) {
        return mpProductService.addToFavorites(bo);
    }

    /**
     * 清洗收藏夹历史数据
     * @param bo
     * @return
     */
    @PostMapping("/dealTenantFavoritesData")
    @Transactional
    public void dealTenantFavoritesData() {
        mpProductService.dealTenantFavoritesData();
    }

}
