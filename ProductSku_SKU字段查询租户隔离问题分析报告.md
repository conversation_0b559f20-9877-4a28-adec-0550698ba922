# ProductSku SKU字段查询租户隔离问题分析报告

## 问题概述

在ProductSku类中，sku字段作为最小库存单位标识，在业务中出现了相同sku对应不同ProductSkuCode的情况。当涉及到sku字段查询时，如果没有正确拼接tenant_id进行租户隔离，可能导致selectOne查询返回多个结果，从而引发MyBatis-Plus异常。

## 问题背景

- **实体类**: `com.zsmall.product.entity.domain.ProductSku`
- **问题字段**: `sku` (最小库存单位)
- **风险**: 相同sku值存在于不同租户时，selectOne()可能返回多个结果
- **影响范围**: 所有涉及sku字段查询的业务模块

## 问题查询分类

### 1. IProductSkuService中的问题方法

#### 1.1 queryBySku方法 - 高风险
**文件位置**: `hengjian-business/zsmall-product/zsmall-product-entity/src/main/java/com/zsmall/product/entity/iservice/IProductSkuService.java:235-241`

```java
public ProductSku queryBySku(String sku) {
    log.info("通过sku查询商品SKU, sku = {}", sku);
    LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
    lqw.eq(ProductSku::getSku, sku);
    lqw.eq(ProductSku::getDelFlag,0);
    return TenantHelper.ignore(() -> baseMapper.selectOne(lqw), TenantType.Manager, TenantType.Distributor);
}
```

**问题分析**:
- 使用了TenantHelper.ignore()但只针对Manager和Distributor租户类型
- 对Supplier租户类型没有进行租户隔离
- 当Supplier租户中存在相同sku时，可能返回多个结果

**修复建议**:
```java
public ProductSku queryBySku(String sku) {
    log.info("通过sku查询商品SKU, sku = {}", sku);
    LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
    lqw.eq(ProductSku::getSku, sku);
    lqw.eq(ProductSku::getDelFlag,0);
    // 添加租户隔离条件
    lqw.eq(ProductSku::getTenantId, LoginHelper.getTenantId());
    return baseMapper.selectOne(lqw);
}
```

### 2. 订单模块中的问题查询

#### 2.1 OrderItemServiceImpl中的sku查询 - 高风险
**文件位置**: `hengjian-business/zsmall-order/zsmall-order-biz/src/main/java/com/zsmall/order/biz/service/impl/OrderItemServiceImpl.java:1221`

```java
productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, sellerSku)));
```

**问题分析**:
- 使用TenantHelper.ignore()忽略了租户隔离
- 直接基于sku字段查询，可能返回多个结果
- 在订单处理中可能导致数据混乱

**修复建议**:
```java
// 方案1: 添加租户条件
LambdaQueryWrapper<ProductSku> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(ProductSku::getSku, sellerSku);
wrapper.eq(ProductSku::getTenantId, LoginHelper.getTenantId());
productSku = productSkuService.getOne(wrapper);

// 方案2: 使用ProductSkuCode替代sku查询
productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, sellerSku)));
```

#### 2.2 OrderItemServiceImpl中的其他sku查询 - 高风险
**文件位置**: 
- `hengjian-business/zsmall-order/zsmall-order-biz/src/main/java/com/zsmall/order/biz/service/impl/OrderItemServiceImpl.java:1389`
- `hengjian-business/zsmall-order/zsmall-order-biz/src/main/java/com/zsmall/order/biz/service/impl/OrderItemServiceImpl.java:1555`

```java
// 第1389行
ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, sellerSku)));

// 第1555行
productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, sellerSku)));
```

**问题分析**: 同上述问题，需要添加租户隔离条件

### 3. 产品模块中的问题查询

#### 3.1 RuleLevelProductPriceServiceImpl中的sku查询 - 高风险
**文件位置**: `hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/service/impl/RuleLevelProductPriceServiceImpl.java:1112`

```java
ProductSku one = iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, sku).last("limit 1"));
```

**问题分析**:
- 虽然使用了`.last("limit 1")`来限制结果，但这不是正确的解决方案
- 没有进行租户隔离，可能查询到其他租户的数据
- 在价格计算中可能导致数据错误

**修复建议**:
```java
LambdaQueryWrapper<ProductSku> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(ProductSku::getSku, sku);
wrapper.eq(ProductSku::getTenantId, LoginHelper.getTenantId());
ProductSku one = iProductSkuService.getOne(wrapper);
```

#### 3.2 RuleLevelProductPriceServiceImpl中的另一个sku查询 - 高风险
**文件位置**: `hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/service/impl/RuleLevelProductPriceServiceImpl.java:1546`

```java
one = iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, dto.getSku()));
```

**问题分析**: 同上述问题，需要添加租户隔离条件

#### 3.3 ProductSkuServiceImpl中的正确实现 - 参考示例
**文件位置**: `hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/service/impl/ProductSkuServiceImpl.java:147-152`

```java
@Override
public ProductSku getProductSkuBySellerSku(String sellerSku, String tenantId) {
    LambdaQueryWrapper<ProductSku> skuLambdaQueryWrapper = new LambdaQueryWrapper<>();
    skuLambdaQueryWrapper.eq(ProductSku::getSku,sellerSku).eq(ProductSku::getDelFlag,0);
    skuLambdaQueryWrapper.eq(ProductSku::getTenantId,tenantId);
    return iProductSkuService.getOne(skuLambdaQueryWrapper);
}
```

**正确实现**: 此方法正确添加了tenantId条件，可以作为其他查询的参考模板

### 4. 市场模块中的问题查询

#### 4.1 MpProductServiceImpl中的sku查询 - 中风险
**文件位置**: `hengjian-business/zsmall-marketplace/src/main/java/com/zsmall/marketplace/service/impl/MpProductServiceImpl.java:341`

```java
List<ProductSku> list = TenantHelper.ignore(()->iProductSkuService.list(new LambdaQueryWrapper<>(ProductSku.class).eq(ProductSku::getSku, finalQueryValue).eq(ProductSku::getDelFlag,0)));
```

**问题分析**:
- 使用了list()方法而不是getOne()，风险相对较低
- 但仍然没有进行租户隔离，可能查询到其他租户的数据
- 在商品搜索中可能影响用户体验

**修复建议**:
```java
LambdaQueryWrapper<ProductSku> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(ProductSku::getSku, finalQueryValue);
wrapper.eq(ProductSku::getDelFlag, 0);
wrapper.eq(ProductSku::getTenantId, LoginHelper.getTenantId());
List<ProductSku> list = iProductSkuService.list(wrapper);
```

### 5. 计算模块中的问题查询

#### 5.1 DeliveryFeeV2Utils中的sku查询 - 高风险
**文件位置**: `hengjian-business/zsmall-calculate/zsmall-calculate-entity/src/main/java/com/zsmall/calculate/entity/util/DeliveryFeeV2Utils.java:240`

```java
LambdaQueryWrapper<ProductSku> p = new LambdaQueryWrapper<>();
p.eq(ProductSku::getSku, sku);
p.eq(ProductSku::getDelFlag,0);
p.eq(ProductSku::getShelfState, ShelfStateEnum.OnShelf.name());
p.eq(ProductSku::getTenantId, requests.get(0).getSupplierTenantId());
ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(p));
```

**问题分析**:
- 此查询实际上已经正确添加了tenantId条件
- 但使用了TenantHelper.ignore()，可能绕过了租户隔离机制
- 在运费计算中可能导致数据错误

**修复建议**:
```java
LambdaQueryWrapper<ProductSku> p = new LambdaQueryWrapper<>();
p.eq(ProductSku::getSku, sku);
p.eq(ProductSku::getDelFlag,0);
p.eq(ProductSku::getShelfState, ShelfStateEnum.OnShelf.name());
p.eq(ProductSku::getTenantId, requests.get(0).getSupplierTenantId());
ProductSku productSku = iProductSkuService.getOne(p);
```

### 6. XML映射文件中的问题查询

#### 6.1 ProductSkuMapper.xml中的sku查询 - 中风险
**文件位置**: `hengjian-business/zsmall-product/zsmall-product-entity/src/main/resources/mapper/ProductSkuMapper.xml:398`

```xml
<if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'Sku')">
    AND sku.sku LIKE CONCAT('%', #{queryValue}, '%')
</if>
```

**问题分析**:
- XML中的查询没有包含tenant_id条件
- 在分页查询中可能返回其他租户的数据
- 影响商品管理页面的数据展示

**修复建议**:
```xml
<if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryType, 'Sku')">
    AND sku.sku LIKE CONCAT('%', #{queryValue}, '%')
    AND sku.tenant_id = #{tenantId}
</if>
```

## 风险等级评估

### 高风险 (需要立即修复)
1. IProductSkuService.queryBySku()方法
2. OrderItemServiceImpl中的sku查询
3. RuleLevelProductPriceServiceImpl中的sku查询
4. DeliveryFeeV2Utils中的sku查询

### 中风险 (建议尽快修复)
1. MpProductServiceImpl中的sku查询
2. ProductSkuMapper.xml中的sku查询

## 修复优先级建议

### 第一优先级
- 修复IProductSkuService.queryBySku()方法，确保所有租户类型都有正确的租户隔离
- 修复订单模块中的sku查询，避免订单处理中的数据混乱

### 第二优先级
- 修复产品模块中的sku查询，确保价格计算的准确性
- 修复计算模块中的sku查询，确保运费计算的准确性

### 第三优先级
- 修复市场模块和XML映射文件中的sku查询，提升用户体验

## 通用修复方案

### 方案1: 添加租户条件
```java
LambdaQueryWrapper<ProductSku> wrapper = new LambdaQueryWrapper<>();
wrapper.eq(ProductSku::getSku, sku);
wrapper.eq(ProductSku::getDelFlag, 0);
wrapper.eq(ProductSku::getTenantId, LoginHelper.getTenantId());
ProductSku productSku = iProductSkuService.getOne(wrapper);
```

### 方案2: 使用ProductSkuCode替代
```java
// 如果业务逻辑允许，优先使用ProductSkuCode进行查询
ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
```

### 方案3: 创建专用的租户隔离查询方法
```java
public ProductSku queryBySkuWithTenant(String sku, String tenantId) {
    LambdaQueryWrapper<ProductSku> lqw = Wrappers.lambdaQuery();
    lqw.eq(ProductSku::getSku, sku);
    lqw.eq(ProductSku::getDelFlag, 0);
    lqw.eq(ProductSku::getTenantId, tenantId);
    return baseMapper.selectOne(lqw);
}
```

## 总结

本报告识别了项目中所有涉及ProductSku sku字段查询但没有正确拼接tenant_id的问题。这些问题可能导致selectOne查询返回多个结果，引发系统异常。建议按照优先级逐步修复这些问题，确保租户数据隔离的完整性和系统稳定性。

修复过程中需要注意：
1. 确保所有修复都经过充分测试
2. 考虑业务逻辑的兼容性
3. 优先修复高风险问题
4. 建立代码审查机制，防止类似问题再次出现

