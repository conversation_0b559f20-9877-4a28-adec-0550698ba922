package com.hengjian;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 启动程序
 *
 * <AUTHOR> Li
 */

@ConfigurationPropertiesScan
@SpringBootApplication
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
public class DistributionApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(DistributionApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        ConfigurableApplicationContext run = application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  HengJian-Distribution启动成功   ლ(´ڡ`ლ)ﾞ");

    }

}
