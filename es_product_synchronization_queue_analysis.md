# es_product_synchronization.queue 队列分析报告

## 概述

本报告分析了项目中 `es_product_synchronization.queue` 队列的使用情况，并识别了导致消息堆积的潜在问题。

## 队列基本信息

### 队列定义
- **队列名称**: `es_product_synchronization.queue`
- **定义位置**: `hengjian-extend/hengjian-stream-mq/src/main/java/com/hengjian/stream/mq/constant/RabbitMqConstant.java:156`
- **交换机**: `MULTICHANNEL_SEND_EXCHANGE = "multichannel_send_exchange"`
- **消息类型**: ElasticSearch商品数据同步

## 消息生产者分析

### 主要发送位置

#### 1. EsProductSupport.productSkuUpload() - SKU维度
```java
// 位置: hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/support/EsProductSupport.java:124
public void productSkuUpload(String... productSkuCodes) {
    if (ArrayUtil.isNotEmpty(productSkuCodes)) {
        Date date = new Date();
        MessageDto messageDto = new MessageDto(DateUtil.format(date, "yyyy-MM-dd_HH:mm:ss.SSSS"));
        
        Map<String, Object> data = new HashMap<>();
        data.put("productSkuCodes", List.of(productSkuCodes));
        
        messageDto.setMsgSource("Es Product Push");
        messageDto.setData(data);
        log.info("[ES商品上传],发送消息,内容:{}", JSON.toJSONString(messageDto));
        rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE, 
                                    RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE, 
                                    JSON.toJSONString(messageDto));
    }
}
```

#### 2. EsProductSupport.productUpload() - SPU维度
```java
// 位置: hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/support/EsProductSupport.java:180
public void productUpload(String... productCodes) {
    if (ArrayUtil.isNotEmpty(productCodes)) {
        Date date = new Date();
        MessageDto messageDto = new MessageDto(DateUtil.format(date, "yyyy-MM-dd_HH:mm:ss.SSSS"));
        
        Map<String, Object> data = new HashMap<>();
        data.put("productCodes", List.of(productCodes));
        
        messageDto.setMsgSource("Es Product Push");
        messageDto.setData(data);
        log.info("[ES商品上传],发送消息,内容:{}", JSON.toJSONString(messageDto));
        rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE, 
                                    RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE, 
                                    JSON.toJSONString(messageDto));
    }
}
```

#### 3. BizArkServiceImpl.productUpload() - 仓库模块
```java
// 位置: hengjian-business/zsmall-warehouse/zsmall-warehouse-biz/src/main/java/com/zsmall/warehouse/biz/factory/impl/BizArkServiceImpl.java:292
public void productUpload(String... productCodes) {
    if (ArrayUtil.isNotEmpty(productCodes)) {
        Date date = new Date();
        MessageDto messageDto = new MessageDto(DateUtil.format(date, "yyyy-MM-dd_HH:mm:ss.SSSS"));
        
        Map<String, Object> data = new HashMap<>();
        data.put("productCodes", List.of(productCodes));
        
        messageDto.setMsgSource("Es Product Push");
        messageDto.setData(data);
        rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE, 
                                    RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE, 
                                    JSON.toJSONString(messageDto));
    }
}
```

## 消息消费者分析

### 主要消费者
```java
// 位置: hengjian-business/zsmall-product/zsmall-product-biz/src/main/java/com/zsmall/product/biz/mq/ProductEsSynchronizationListener.java
@RabbitHandler
@RabbitListener(queues = RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE)
public void productEsSynchronizationListener(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) 
        throws IOException, InterruptedException {
    //休息1秒，保证上游事务已经提交
    Thread.sleep(1000);
    String messageContext = new String(message.getBody());
    log.info("【消费者 - ElasticSearch商品数据推送】通道: {}, 收到数据: {}", 
             QueueConstants.QueueName.ES_PRODUCT_PUSH, messageContext);
    try {
        MessageDto messageDto = JSONUtil.toBean(messageContext, MessageDto.class);
        String msgId = messageDto.getMsgId();
        Map<String, Object> data = (Map<String, Object>) messageDto.getData();
        List<String> productCodes = (List<String>) data.get("productCodes");
        List<String> productSkuCodes = (List<String>) data.get("productSkuCodes");
        
        if (CollUtil.isNotEmpty(productCodes)) {
            esProductSupport.productUploadForConsumer(productCodes);
        }
        if (CollUtil.isNotEmpty(productSkuCodes)) {
            esProductSupport.productSkuUploadForConsumer(productSkuCodes);
        }
    } catch (Exception e) {
        log.error("【消费者 - ElasticSearch商品数据推送】发生异常：{}", e.getMessage(), e);
    }finally {
        channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
    }
}
```

## 触发ES同步的业务场景

### 1. 商品保存/更新
- **位置**: `ProductServiceImpl.saveProduct()` - 新增商品时
- **位置**: `ProductServiceImpl.updateProduct()` - 更新商品时
- **触发条件**: 商品信息变更

### 2. 商品上下架
- **位置**: `ProductServiceImpl.updateProductSkuShelfState()` - 商品状态变更
- **触发条件**: SKU上下架状态改变

### 3. 库存同步任务
- **位置**: `ProductSupport.createSyncTask()` - 创建库存同步任务
- **触发条件**: 库存变更需要同步到各渠道

### 4. 事件监听机制
- **位置**: `ProductSkuEventListener.listenInEsProductUpload()` - 监听ES上传事件
- **触发条件**: 发布 `EsProductUploadEvent` 事件

### 5. 手动推送
- **位置**: `ProductServiceImpl.manualProductPush()` - 手动推送商品
- **触发条件**: 管理员手动操作

### 6. 批量初始化
- **位置**: `MpProductServiceImpl.initEsProduct()` - 初始化ES商品数据
- **触发条件**: 系统初始化或数据重建

## 潜在的消息堆积问题

### 1. 🚨 性能瓶颈问题

#### 强制延迟问题
```java
// 问题代码：每个消息处理前强制等待1秒
Thread.sleep(1000);
```
**影响**: 严重降低消费速度，每小时最多处理3600条消息

#### 复杂处理逻辑
- ES数据同步涉及复杂的数据库查询
- 需要处理商品、SKU、价格、库存等多维度数据
- 单个消息处理时间较长

### 2. 🚨 并发配置问题

#### 生产环境配置过低
```yaml
# application-prod-hj.yml
listener:
  simple:
    acknowledge-mode: manual
    concurrency: 5          # 并发度过低
    auto-startup: true
    max-concurrency: 20     # 最大并发度过低
```

#### 不同环境配置对比
| 环境 | 并发度 | 最大并发度 |
|------|--------|------------|
| 开发环境 | 10 | 50 |
| 生产环境 | 5 | 20 |

### 3. 🚨 重复消息问题

#### 多场景触发同一商品同步
- 商品更新 → 触发ES同步
- 库存变更 → 触发ES同步  
- 价格变更 → 触发ES同步
- 状态变更 → 触发ES同步

**结果**: 同一商品可能在短时间内产生多条重复消息

### 4. 🚨 消息确认机制问题

#### 手动确认模式风险
```java
// 手动确认，如果处理失败可能导致消息丢失或重复
channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
```

### 5. 🚨 批量处理效率问题
- 当前逐个商品处理，没有批量优化
- 大量商品更新时会产生大量单独消息
- 缺乏消息合并机制

## 优化建议

### 1. 立即优化 (高优先级)

#### 移除强制延迟
```java
// 移除这行代码
// Thread.sleep(1000);

// 替换为更优雅的事务同步机制
@Transactional(propagation = Propagation.REQUIRES_NEW)
public void processMessage() {
    // 处理逻辑
}
```

#### 提高并发配置
```yaml
listener:
  simple:
    acknowledge-mode: manual
    concurrency: 15         # 提高并发度
    auto-startup: true
    max-concurrency: 50     # 提高最大并发度
```

### 2. 中期优化 (中优先级)

#### 实现消息去重
```java
// 添加消息去重逻辑
private final Set<String> processedMessages = ConcurrentHashMap.newKeySet();

public void processMessage(MessageDto messageDto) {
    String messageKey = generateMessageKey(messageDto);
    if (processedMessages.contains(messageKey)) {
        log.info("消息已处理，跳过: {}", messageKey);
        return;
    }
    processedMessages.add(messageKey);
    // 处理逻辑
}
```

#### 批量处理优化
```java
// 实现批量消息处理
@RabbitListener(queues = RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE, 
               containerFactory = "batchListenerContainerFactory")
public void batchProcessMessages(List<Message> messages) {
    // 批量处理逻辑
}
```

### 3. 长期优化 (低优先级)

#### 消息合并机制
- 实现相同商品的消息合并
- 设置合并时间窗口
- 减少重复处理

#### 监控和告警
```java
// 添加队列监控
@Component
public class QueueMonitor {
    
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void monitorQueue() {
        // 检查队列长度
        // 检查消费速度
        // 发送告警
    }
}
```

## 监控指标建议

### 关键指标
1. **队列长度**: 实时监控队列中待处理消息数量
2. **消费速度**: 每分钟/每小时处理的消息数量
3. **处理时间**: 单个消息的平均处理时间
4. **错误率**: 消息处理失败的比例
5. **重复率**: 重复消息的比例

### 告警阈值
- 队列长度 > 1000 条
- 消费速度 < 100 条/分钟
- 错误率 > 5%
- 平均处理时间 > 5 秒

## 总结

`es_product_synchronization.queue` 队列的消息堆积问题主要由以下因素造成：

1. **性能瓶颈**: 强制1秒延迟严重影响处理速度
2. **并发不足**: 生产环境并发配置过低
3. **重复消息**: 多业务场景触发重复同步
4. **处理复杂**: ES同步逻辑复杂耗时

**建议优先解决强制延迟和并发配置问题**，这两个问题的解决可以立即显著提升队列处理能力。

---
*报告生成时间: 2024-12-19*
*分析范围: hengjian-distribution 项目*
