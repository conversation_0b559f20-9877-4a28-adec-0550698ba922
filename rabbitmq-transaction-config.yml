# RabbitMQ事务支持配置示例
# 将以下配置添加到你的application.yml中

spring:
  rabbitmq:
    # 基础连接配置
    addresses: *************
    port: 5672
    username: guest
    password: ccxP^p@!8xhj
    virtual-host: /
    publisher-returns: true
    
    # 启用事务支持
    template:
      mandatory: true
      # 启用通道事务
      channelTransacted: true
      
    # 消费者配置
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 10
        auto-startup: true
        max-concurrency: 50
        # 启用事务
        transaction-size: 1
        
    # 发布者确认模式（与事务二选一）
    # publisher-confirm-type: correlated
    
# 注意：channelTransacted 和 publisher-confirm-type 不能同时使用
# 事务模式性能较低但一致性更强
# 确认模式性能较高但需要额外的确认处理逻辑
